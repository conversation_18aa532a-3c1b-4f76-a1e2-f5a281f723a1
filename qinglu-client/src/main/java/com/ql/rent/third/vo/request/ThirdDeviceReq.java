package com.ql.rent.third.vo.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ThirdDeviceReq {

    public static final String IDS_ID  = "idsId";

    public static final String SN  = "sn";

    public static final String CODE  = "code";

    public static final String PLATE_NUMBER  = "plateNumber";

    public static final String VIN  = "vin";

    public static final String SIGN  = "sign";

    /**
     * 设备sn
     */
    private String sn;

    /**
     * 设备code
     */
    private String code;

    /**
     * 车牌号
     */
    private String plateNumber;

    /**
     * 车辆vin
     */
    private String vin;

    /**
     *
     */
    private String sign;

}

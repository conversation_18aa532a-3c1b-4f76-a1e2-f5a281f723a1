package com.ql.rent.third.vo.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString
public class CarDeviceQuery {

    /**
     * 车辆id
     */
    private Long vehicleId;

    /**
     * 设备编号
     */
    private String deviceNo;

    /**
     * 查询条件:开始时间
     */
    private Date startTime;


    /**
     * 查询条件:结束时间
     */
    private Date endTime;

}

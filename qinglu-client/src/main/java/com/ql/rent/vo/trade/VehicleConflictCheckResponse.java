package com.ql.rent.vo.trade;

import com.ql.rent.vo.vehicle.VehicleInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 检查车辆改排后库存冲突响应
 *
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Data
@ApiModel("检查车辆改排后库存冲突响应")
public class VehicleConflictCheckResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否存在库存冲突")
    private Boolean hasStockConflict;

    @ApiModelProperty(value = "是否存在合同冲突")
    private Boolean hasContractConflict;

    @ApiModelProperty(value = "合同冲突的订单列表")
    private List<Long> contractConflictOrderList;

    @ApiModelProperty(value = "目标车辆信息")
    private VehicleInfoVO targetVehicleInfo;
}

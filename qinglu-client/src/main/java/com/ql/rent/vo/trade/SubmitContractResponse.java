package com.ql.rent.vo.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 手动上报违章转移合同响应
 *
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Data
@ApiModel("手动上报违章转移合同响应")
public class SubmitContractResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "三方合同ID")
    private String contractId;

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "合同状态 -2计划上报 -1表示上报失败 0表示上报中，1表示已上报，2已终止, 3已取消")
    private Integer status;

    @ApiModelProperty(value = "响应码")
    private String postCode;

    @ApiModelProperty(value = "状态说明")
    private String postMsg;
}

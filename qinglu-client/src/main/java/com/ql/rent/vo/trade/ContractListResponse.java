package com.ql.rent.vo.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 查询违章转移合同列表响应
 *
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Data
@ApiModel("查询违章转移合同列表响应")
public class ContractListResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总记录数")
    private Long total;

    @ApiModelProperty(value = "合同列表")
    private List<TransferContractStatusVo> list;
}

package com.ql.rent.param.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 查询违章转移合同列表请求参数
 *
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Data
@ApiModel("查询违章转移合同列表请求参数")
public class ContractListRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单ID（可选）")
    private Long orderId;

    @ApiModelProperty(value = "车辆ID（可选）")
    private Long vehicleId;

    @ApiModelProperty(value = "合同状态（可选）")
    private Integer status;

    @ApiModelProperty(value = "合同开始时间范围-开始（可选）")
    private Date startBeginTime;

    @ApiModelProperty(value = "合同开始时间范围-结束（可选）")
    private Date endBeginTime;

    @ApiModelProperty(value = "合同结束时间范围-开始（可选）")
    private Date startEndTime;

    @ApiModelProperty(value = "合同结束时间范围-结束（可选）")
    private Date endEndTime;

    @ApiModelProperty(value = "页码，从1开始", example = "1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小", example = "20")
    private Integer pageSize = 20;
}

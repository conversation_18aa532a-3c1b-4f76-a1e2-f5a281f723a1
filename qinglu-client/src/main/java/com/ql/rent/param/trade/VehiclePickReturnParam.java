package com.ql.rent.param.trade;

import com.ql.rent.vo.trade.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc
 * @time 2022-11-15 01:52
 */
@Data
@ToString
@ApiModel("取车参数")
public class VehiclePickReturnParam implements Serializable {

    private static final long serialVersionUID = 8162903876553930181L;

    // 取还相同参数
    @ApiModelProperty(value = "id", hidden = true)
    private Long id;

    @ApiModelProperty("订单id")
    private Long orderId;

    @ApiModelProperty("里程")
    private Integer mileage;

    @ApiModelProperty("油升数")
    private Integer oilLiter;

    @ApiModelProperty("最大油量")
    private Integer maxOilLiter;

    @ApiModelProperty("取/还车时间")
    private Date prTime;

    @ApiModelProperty(value = "附件列表", required = true)
    private List<VehiclePickReturnAttVO> attList;

    @ApiModelProperty(value = "合同相关参数")
    private PickReturnContractParam contract;

    @ApiModelProperty(value = "验车单数据")
    private VehicleInspectionVO inspection;

    // -------------取车特定参数-------------------
    @ApiModelProperty(value = "新增的附加产品id", required = false)
    private List<Long> addServiceIdList;

    @ApiModelProperty(value = "新增的保险产品id", required = false)
    private List<Long> insuranceServiceIdList;

    @ApiModelProperty("押金相关参数, 押金未支付的场景需要传入")
    private DepositParam deposit;

    // ---------还车特定参数---------
    @ApiModelProperty(value = "扣费方式:还车时用到;取车，该字段为空; 1线上（平台代扣） 2线下")
    private Byte deductionPayType;

    @ApiModelProperty(value = "退费方式:还车时用到;取车，该字段为空1线上（平台代收） 2线下")
    private Byte refundPayType;

    /**
     * 支付方式，1，支付宝付款码；99，其他
     */
    private Byte payKind;

    @ApiModelProperty("还车时 扣费明细")
    private List<VehicleReturnExpenseItemVO> returnDeductionItemList;

    @ApiModelProperty("还车时 退费明细")
    private List<VehicleReturnExpenseItemVO> returnRefundItemList;

    @ApiModelProperty(value = " 还车时 车损记录")
    private VehicleDamageOrderVO damageOrder;

    @ApiModelProperty(value = "还车时 违章记录 ")
    private VehicleIllegalOrderParam illegalOrder;

    @ApiModelProperty(value = "电子合同是否盖章 电子合同验车信息接口使用.首次传0，二次传1", required = true)
    private Byte contractSign;

    @ApiModelProperty(value = "里程附件")
    private VehiclePickReturnAttVO mileageAtt;

    @ApiModelProperty(value = "取车时指定的车辆ID（用于改排车辆）")
    private Long pickupVehicleId;

    @Data
    public static class PickReturnContractParam {

        @ApiModelProperty(value = "签署合同url")
        private String contractUrl;

        @ApiModelProperty(value = "验车单图片url")
        private String inspectionUrl;
    }

    /**
     * 是否开放第三方
     */
    private boolean isOpenThird = false;
}

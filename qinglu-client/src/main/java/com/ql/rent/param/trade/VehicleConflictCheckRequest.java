package com.ql.rent.param.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 检查车辆改排后库存冲突请求参数
 *
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Data
@ApiModel("检查车辆改排后库存冲突请求参数")
public class VehicleConflictCheckRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单ID", required = true)
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @ApiModelProperty(value = "目标车牌", required = true)
    @NotBlank(message = "目标车牌不能为空")
    private String targetLicense;

    @ApiModelProperty(value = "取车时间", required = true)
    @NotNull(message = "取车时间不能为空")
    private Date pickupTime;
}

package com.ql.rent.param.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 违章转移合同状态查询参数
 *
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Data
@ApiModel("违章转移合同状态查询参数")
public class TransferContractStatusQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商家ID")
    private Long merchantId;

    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    @ApiModelProperty(value = "车辆ID")
    private Long vehicleId;

    @ApiModelProperty(value = "合同状态")
    private Integer status;

    @ApiModelProperty(value = "合同开始时间范围-开始")
    private Date startBeginTime;

    @ApiModelProperty(value = "合同开始时间范围-结束")
    private Date endBeginTime;

    @ApiModelProperty(value = "合同结束时间范围-开始")
    private Date startEndTime;

    @ApiModelProperty(value = "合同结束时间范围-结束")
    private Date endEndTime;

    @ApiModelProperty(value = "页码，从1开始")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小")
    private Integer pageSize = 20;

    @ApiModelProperty(value = "偏移量")
    private Integer offset;

    @ApiModelProperty(value = "限制数量")
    private Integer limit;

    /**
     * 计算偏移量
     */
    public Integer getOffset() {
        if (pageNum != null && pageSize != null && pageNum > 0) {
            return (pageNum - 1) * pageSize;
        }
        return 0;
    }

    /**
     * 获取限制数量
     */
    public Integer getLimit() {
        return pageSize != null ? pageSize : 20;
    }
}

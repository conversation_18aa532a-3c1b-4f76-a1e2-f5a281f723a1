package com.ql.rent.param.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 手动上报违章转移合同请求参数
 *
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Data
@ApiModel("手动上报违章转移合同请求参数")
public class SubmitContractRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单ID", required = true)
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @ApiModelProperty(value = "合同开始时间", required = true)
    @NotNull(message = "合同开始时间不能为空")
    private Date beginTime;

    @ApiModelProperty(value = "合同结束时间", required = true)
    @NotNull(message = "合同结束时间不能为空")
    private Date endTime;

    @ApiModelProperty(value = "车辆ID（可选，不提供则使用订单默认车辆）")
    private Long vehicleId;
}

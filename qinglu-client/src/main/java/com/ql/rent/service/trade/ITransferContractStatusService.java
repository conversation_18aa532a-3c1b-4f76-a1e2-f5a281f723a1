package com.ql.rent.service.trade;

import com.ql.rent.dto.trade.TransferReportSummaryDTO;
import com.ql.rent.param.trade.ContractListRequest;
import com.ql.rent.param.trade.SubmitContractRequest;
import com.ql.rent.param.trade.TransferContractStatusParam;
import com.ql.rent.param.trade.TransferContractStatusQuery;
import com.ql.rent.param.trade.VehicleConflictCheckRequest;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.trade.ContractListResponse;
import com.ql.rent.vo.trade.QueryIllegalTransferAuthVO;
import com.ql.rent.vo.trade.SubmitContractResponse;
import com.ql.rent.vo.trade.TransferContractStatusVo;
import com.ql.rent.vo.trade.VehicleConflictCheckResponse;

import java.util.Date;
import java.util.List;

public interface ITransferContractStatusService {

    /**
     * 匹配转移合同，获取违章的转移状态
     * @param merchantId
     * @param vehicleId
     * @param illegalTime
     * @return
     */
    TransferContractStatusVo getTransferStatus(Long merchantId, Long vehicleId, Long orderId, Date illegalTime);

    /**
     * 保存违章转移合同状态表
     */
    Result<Boolean> saveTransferContractStatus(TransferContractStatusParam param);

    /**
     * 查询合同
     */
    Result<List<TransferContractStatusVo>> getTransferContractStatus(TransferContractStatusParam param);

    /**
     * 查询当前订单的最新可能成功或已经成功的合同
     */
    TransferContractStatusVo getLatestContract(Long merchantId, Long vehicleId, Long orderId);

    /**
     * 录入合同
     */
    Result<Boolean> submitContract(Long orderId, Date beginTime, Date endTime);

    /**
     * 终止合同 return true表示有终止合同
     * orderId 或 vehicleId不能为空
     * abortDate=null时，取当前时间
     * orderId != null && needSubmit=true,表示订单的合同可以补充
     */
    Boolean abortAllContract(Long orderId, Long vehicleId, Date abortDate, boolean needSubmit);

    /**
     * 拉取更新合同(定时任务)
     */
    void syncContractByJob();

    /**
     * 续期 录入合同(定时任务)
     */
    void submitDelayContractByJob();

    /**
     * 作废合同
     */
    Result<Boolean> cancelContract(String contractId);

    Result<Boolean> submitContract(Long orderId, Date beginTime, Date endTime, Long vehicleId);

    /**
     * 测试上报公众号消息推送
     */
    void testPushTransferMsg(Long orderId, String code, Date beginTime, Date endTime, String account);

    Result<Boolean> authorizeIllegal(Long orderId, LoginVo loginVo);

    Result<QueryIllegalTransferAuthVO> queryIllegalTransferAuth(Long orderId, Long merchantId);

    /**
     * if (是有效违章转移订单的车辆 && 订单状态在已排车 && 订单的身份证错误)
     *   异步发送订单身份证不正确企业微信消息
     */
    void sendOrderIdCardErrorMsg(Long orderId);

    /**
     * 统计转移上报情况(定时任务)
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    TransferReportSummaryDTO getTransferReportSummary(Date startTime, Date endTime);

    /**
     * 执行转移上报统计任务并发送企业微信消息
     */
    void executeTransferReportTask();

    /**
     * 执行违章月度账单推送任务
     */
    void executeViolationMonthlyBillTask();

    /**
     * 违章登录成功后自动重试失败的合同
     * @param account 122账号
     */
    void retryFailedContractsOnLoginSuccess(String account);

    // ==================== 新增的违章转移API接口方法 ====================

    /**
     * 手动上报违章转移合同
     *
     * @param merchantId 商家ID
     * @param request    上报请求参数
     * @return 上报结果
     */
    Result<SubmitContractResponse> submitContractByApi(Long merchantId, SubmitContractRequest request);

    /**
     * 查询违章转移合同列表
     *
     * @param merchantId 商家ID
     * @param request    查询请求参数
     * @return 合同列表
     */
    Result<ContractListResponse> getContractListByApi(Long merchantId, ContractListRequest request);

    /**
     * 查询违章转移合同列表（内部使用）
     *
     * @param query 查询参数
     * @return 合同列表
     */
    Result<List<TransferContractStatusVo>> listTransferContractStatus(TransferContractStatusQuery query);

    /**
     * 检查车辆改排后库存冲突
     *
     * @param merchantId 商家ID
     * @param request    检查请求参数
     * @return 冲突检查结果
     */
    Result<VehicleConflictCheckResponse> checkVehicleConflict(Long merchantId, VehicleConflictCheckRequest request);
}

package com.ql.rent.service.trade;

import com.ql.rent.param.trade.ContractListRequest;
import com.ql.rent.param.trade.SubmitContractRequest;
import com.ql.rent.param.trade.TransferContractStatusQuery;
import com.ql.rent.param.trade.VehicleConflictCheckRequest;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.trade.ContractListResponse;
import com.ql.rent.vo.trade.SubmitContractResponse;
import com.ql.rent.vo.trade.TransferContractStatusVo;
import com.ql.rent.vo.trade.VehicleConflictCheckResponse;

import java.util.List;

/**
 * 违章转移服务接口
 *
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
public interface IIllegalTransferService {

    /**
     * 手动上报违章转移合同
     *
     * @param merchantId 商家ID
     * @param request    上报请求参数
     * @return 上报结果
     */
    Result<SubmitContractResponse> submitContract(Long merchantId, SubmitContractRequest request);

    /**
     * 查询违章转移合同列表
     *
     * @param merchantId 商家ID
     * @param request    查询请求参数
     * @return 合同列表
     */
    Result<ContractListResponse> getContractList(Long merchantId, ContractListRequest request);

    /**
     * 查询违章转移合同列表（内部使用）
     *
     * @param query 查询参数
     * @return 合同列表
     */
    Result<List<TransferContractStatusVo>> listTransferContractStatus(TransferContractStatusQuery query);

    /**
     * 检查车辆改排后库存冲突
     *
     * @param merchantId 商家ID
     * @param request    检查请求参数
     * @return 冲突检查结果
     */
    Result<VehicleConflictCheckResponse> checkVehicleConflict(Long merchantId, VehicleConflictCheckRequest request);
}

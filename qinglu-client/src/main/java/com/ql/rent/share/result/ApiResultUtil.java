package com.ql.rent.share.result;

import com.ql.enums.ResultCode;
import com.ql.rent.share.exception.BizException;
import org.apache.commons.lang3.StringUtils;

public class ApiResultUtil {
    private ApiResultUtil() {
    }

    public static ResultMap defaultResult() {
        return new ResultMap();
    }

    public static ResultMap successResult(Object data) {
        ResultMap result = defaultResult();
        result.setData(data);
        return result;
    }

    public static ResultMap failResult(String errorMessage) {
        return failResult("0", errorMessage, false);
    }

    public static ResultMap failResult(Throwable e) {
        // 如果抛出的是业务异常BizException,一起抛出对应的错误码
        if (e instanceof BizException) {
            BizException bizException = (BizException)e;
            return failResult(StringUtils.defaultString(bizException.getCode(), "0"),
                StringUtils.defaultString(e.getMessage(), "服务器异常"));
        }
        return failResult(e.getMessage());
//        return failResult("服务器异常");
    }

    public static ResultMap failResult(String errorCode, String errorMessage) {
        return failResult(errorCode, errorMessage, false);
    }

    public static ResultMap failResult(ResultCode resultCode) {
        return failResult(resultCode.getCode(), resultCode.getMessage(), false);
    }

    public static ResultMap failResult(String errorCode, String errorMessage, boolean isI18n) {
        ResultMap result = defaultResult();
        result.setSuccess(false);
        if (isI18n) {
            result.setI18nCode(errorCode);
        } else {
            result.setErrorCode(errorCode);
        }

        result.setMessage(errorMessage);
        return result;
    }

    public static ResultMap failResult(String errorCode, String i18nCode, String errorMessage) {
        ResultMap result = defaultResult();
        result.setSuccess(false);
        result.setErrorCode(errorCode);
        result.setI18nCode(i18nCode);
        result.setMessage(errorMessage);
        return result;
    }

    public static boolean isBlank(String str) {
        int strLen;
        if (str != null && (strLen = str.length()) != 0) {
            for (int i = 0; i < strLen; ++i) {
                if (!Character.isWhitespace(str.charAt(i))) {
                    return false;
                }
            }

            return true;
        } else {
            return true;
        }
    }
}
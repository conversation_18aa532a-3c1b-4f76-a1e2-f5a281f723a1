<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ql</groupId>
        <artifactId>qinglu-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../qinglu-parent</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>qinglu-api-aggregate</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <maven.deploy.skip>true</maven.deploy.skip>

        <spring-cloud-starter-openfeign.version>3.1.4</spring-cloud-starter-openfeign.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.15</version>
        </dependency>

        <dependency>
            <groupId>com.ql</groupId>
            <artifactId>qinglu-api-aggregate-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

         <dependency>
            <groupId>com.qinglusaas</groupId>
            <artifactId>logutil-spring</artifactId>
        </dependency>


        <!-- https://mvnrepository.com/artifact/org.springframework.cloud/spring-cloud-starter-openfeign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>${spring-cloud-starter-openfeign.version}</version>
        </dependency>
    </dependencies>
</project>
package com.ql.rent.api.aggregate.service;

import com.ql.rent.api.aggregate.model.dto.*;
import com.ql.rent.service.store.IThirdStoreService;
import com.ql.rent.service.trade.IThirdOrderService;
import com.ql.rent.service.vehicle.IThirdVehicleService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2022/11/3 21:09
 */
@Service
public class VehicleAggregateDomainService {
    private final IThirdOrderService orderService;
    private final IThirdStoreService storeService;
    private final IThirdVehicleService vehicleService;

    public VehicleAggregateDomainService(IThirdOrderService orderService, IThirdStoreService storeService, IThirdVehicleService vehicleService) {
        this.orderService = orderService;
        this.storeService = storeService;
        this.vehicleService = vehicleService;
    }

    public List<ServiceItemDTO> getServiceItems(Long merchantId, Long channelId, StorePairDTO pairDTO, VehicleModelUniDTO vehicleModelId, PointDTO pickUpPoint, PointDTO returnPoint, Date pickUpTime, Date returnTime) {
        List<ServiceItemDTO> storeServiceItems = storeService.selectStoreService(merchantId, channelId, pairDTO, pickUpPoint, returnPoint, pickUpTime, returnTime);
        StorePairVehicleModelUniDTO storePairVehicleModelUniDTO = storeService.selectVehicleModelUni(merchantId, channelId, new StorePairAndVehicleModelDTO(pairDTO, vehicleModelId.getVehicleModelId()), pickUpTime, returnTime);
        List<ServiceItemDTO> vehicleModelUniServiceItem = vehicleService.selectModelService(merchantId, vehicleModelId, channelId, pickUpTime, returnTime, false);
        List<ServiceItemDTO> itemDTOS = new ArrayList<>();
        Optional.ofNullable(storeServiceItems).ifPresent(item -> {
            itemDTOS.addAll(item);
        });
        Optional.ofNullable(storePairVehicleModelUniDTO).ifPresent(item -> {
            if (null != item.getVehicleModelItems()) {
                itemDTOS.addAll(item.getVehicleModelItems());
            }
        });
        Optional.ofNullable(vehicleModelUniServiceItem).ifPresent(item -> {
            itemDTOS.addAll(item);
        });
        return itemDTOS;
    }


    public VehicleModelPriceAbbrDTO getModelPrice(Long merchantId, Long channelId, StorePairDTO pairDTO, VehicleModelUniDTO vehicleModelId, List<DailyPriceDTO> vehicleModelDailyPrice,
                                                  PointDTO pickUpPoint, PointDTO returnPoint, Date pickUpTime, Date returnTime, List<String> addServiceIdList, List<ActivityAndCouponDTO> activityAndCouponList, int childrenSeat, boolean platformCal) {
        return this.getModelPrice(merchantId, channelId, pairDTO, vehicleModelId, vehicleModelDailyPrice, pickUpPoint, returnPoint, pickUpTime, returnTime,
                addServiceIdList, Collections.emptyList(), activityAndCouponList, childrenSeat, false);
    }

    /**
     * 取得车型算价结果
     *
     * @param pairDTO
     * @param vehicleModelId
     * @param vehicleModelDailyPrice 价格日历
     * @param pickUpPoint
     * @param returnPoint
     * @param pickUpTime
     * @param returnTime
     * @param platformCal
     * @return
     */
    public VehicleModelPriceAbbrDTO getModelPrice(Long merchantId, Long channelId, StorePairDTO pairDTO, VehicleModelUniDTO vehicleModelId, List<DailyPriceDTO> vehicleModelDailyPrice,
                                                  PointDTO pickUpPoint, PointDTO returnPoint, Date pickUpTime, Date returnTime, List<String> addServiceIdList,
                                                  List<String> couponCodeList, List<ActivityAndCouponDTO> activityAndCouponDTOList, int childrenSeat, boolean platformCal) {
        List<ServiceItemDTO> serviceItemDTOList = this.getServiceItems(merchantId, channelId, pairDTO, vehicleModelId, pickUpPoint, returnPoint, pickUpTime, returnTime);
        VehicleModelPriceCalDTO vehicleModelPriceCal = new VehicleModelPriceCalDTO();
        vehicleModelPriceCal.setChildrenSeatNum(childrenSeat);
        vehicleModelPriceCal.setId(vehicleModelId.getVehicleModelId());
        vehicleModelPriceCal.setMerchantId(merchantId);
        vehicleModelPriceCal.setStoreId(vehicleModelId.getStoreId());
        vehicleModelPriceCal.setChannelId(channelId);
        vehicleModelPriceCal.setVehicleModelId(vehicleModelId.getVehicleModelId());
        vehicleModelPriceCal.setPriceDailyList(vehicleModelDailyPrice);
        vehicleModelPriceCal.setServiceItemList(serviceItemDTOList);
        vehicleModelPriceCal.setSelectServiceIdList(addServiceIdList);
        vehicleModelPriceCal.setCouponCodeList(couponCodeList);
        vehicleModelPriceCal.setActivityAndCouponDTOList(activityAndCouponDTOList);
        vehicleModelPriceCal.setSelfServiceReturn(vehicleModelId.getSelfServiceReturn());
        List<StoreChargeDTO<List<DailyPriceListDTO>>> storeChargeList = vehicleService.getStoreCharge(merchantId, channelId, Arrays.asList(vehicleModelId.getStoreId()), pickUpTime,  returnTime, null, "", platformCal);
        Map<Long, List<DailyPriceListDTO>> storeChargeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(storeChargeList)) {
            for (StoreChargeDTO<List<DailyPriceListDTO>> storeChargeDTO : storeChargeList) {
                storeChargeMap.put(storeChargeDTO.getStoreId(), storeChargeDTO.getData());
            }
        }
        return orderService.priceCalForThird(pickUpTime.getTime(), returnTime.getTime(), vehicleModelPriceCal, storeChargeMap).getModel();
    }

}

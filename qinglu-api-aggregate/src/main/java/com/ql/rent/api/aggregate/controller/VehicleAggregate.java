package com.ql.rent.api.aggregate.controller;

import com.alibaba.fastjson.JSON;
import com.ql.dto.vehicle.VehicleInfoDTO;
import com.ql.rent.api.aggregate.model.dto.DailyPriceDTO;
import com.ql.rent.api.aggregate.model.dto.VehicleModelUniDTO;
import com.ql.rent.api.aggregate.model.remote.ctrip.dto.CtripSkuPriceInfoDTO;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.request.GetSkuPriceInfoRequest;
import com.ql.rent.api.aggregate.model.request.*;
import com.ql.rent.api.aggregate.model.response.RenewalPriceDetailResp;
import com.ql.rent.api.aggregate.model.response.StoreVehicleModelPriceResp;
import com.ql.rent.api.aggregate.model.response.VehicleModelPriceDetailResp;
import com.ql.rent.api.aggregate.service.VehicleAggregateService;
import com.ql.rent.api.aggregate.vo.response.SaasResponse;
import com.ql.rent.common.ITokenService;
import com.ql.rent.constant.RedisConstant;
import com.ql.rent.enums.trade.OrderSourceEnum;
import com.ql.rent.param.vehicle.*;
import com.ql.rent.service.price.ICtripPriceService;
import com.ql.rent.service.trade.IThirdOrderService;
import com.ql.rent.param.vehicle.VehicleBindQueryParam;
import com.ql.rent.service.vehicle.ICtripVehicleService;
import com.ql.rent.service.vehicle.IThirdBusyService;
import com.ql.rent.service.vehicle.IThirdVehicleService;
import com.ql.rent.service.vehicle.*;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.common.VehicleModelGroupVO;
import com.ql.rent.vo.vehicle.BaseVehicleModelVO;
import com.ql.rent.vo.vehicle.ThirdVehicleMatchVo;
import com.ql.rent.vo.vehicle.VehicleAttachDTO;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;

/**
 * 车辆产品相关Api接口
 *
 * <AUTHOR>
 * @Date 2022/10/26 22:31
 */
@Slf4j
@RestController
@RequestMapping("/aggregate/v1/vehicle")
public class VehicleAggregate {

    private final VehicleAggregateService vehicleAggregateService;

    public VehicleAggregate(VehicleAggregateService vehicleAggregateService) {
        this.vehicleAggregateService = vehicleAggregateService;
    }

    @Resource
    private IThirdBusyService thirdBusyService;

    @Resource
    private IThirdOrderService iThirdOrderService;

    @Resource
    private ICtripVehicleService ctripVehicleService;

    @Resource
    private ICtripPriceService ctripPriceService;

    @Resource
    private ITokenService tokenService;
    @Resource
    private IThirdVehicleService thirdVehicleService;

    @Resource
    private IVehicleModelGroupService iVehicleModelGroupService;

    @Resource
    private IVehicleModelService iVehicleModelService;


    @RequestMapping(path = "/merchant/{id}/store-vehicle-model-price/xjTest", method = {RequestMethod.GET, RequestMethod.POST})
    public SaasResponse<StoreVehicleModelPriceResp> xjTest(@PathVariable("id") Long merchantId,
                                                                                 @RequestBody StoreVehicleModelPriceReq req,
                                                                                 HttpServletRequest request) {
        LoginVo loginVo = tokenService.getUserByRequest(request);
        if (loginVo.getMerchantId().compareTo(merchantId) != 0) {
            return SaasResponse.builder().build().failed("无权操作");
        }
        return SaasResponse.builder().build().success(vehicleAggregateService.storeVehicleModelPriceSearch(merchantId, req));
    }

    @RequestMapping(path = "/merchant/{id}/store-vehicle-model-price/search", method = {RequestMethod.GET, RequestMethod.POST})
    public SaasResponse<StoreVehicleModelPriceResp> storeVehicleModelPriceSearch(@PathVariable("id") Long merchantId,
                                                                                 @RequestBody
                                                                                 StoreVehicleModelPriceReq req) {
        Span.current().updateName("获取门店车型价格列表");
        try {
            iThirdOrderService.transferOrder(merchantId, req.getChannelId(), req.getOriginReq(), RedisConstant.MsgTopic.ORDER_TRANSFER_INQUIRE);
            StoreVehicleModelPriceResp resp = vehicleAggregateService.storeVehicleModelPriceSearch(merchantId, req);
            if (resp == null) {
                Span.current().setStatus(StatusCode.ERROR);
                log.error("====third====storeVehicleModelPriceSearch merchantId={},req={}", merchantId, JSON.toJSONString(req));
                return SaasResponse.builder().build().failed("-1", "获取门店车型价格失败");
            } else {
                Span.current().addEvent("collectSearchPriceLog start");
                vehicleAggregateService.collectSearchPriceLog(req, resp, merchantId);
                Span.current().addEvent("collectSearchPriceLog end");
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(resp);
            }
        } finally {
//             //暂时打印大于500ms的请求
//            long rt = System.currentTimeMillis() - startTime;
//            if (rt > 500L) {
//                log.info("====third====storeVehicleModelPriceSearch rt:{}ms, merchantId={}, req={}",
//                    rt, merchantId, JSON.toJSONString(req));
//            }
            Span.current().end();
        }
    }

    @PostMapping(path = "/merchant/{merchantId}/insurance-policy")
    public SaasResponse<StoreVehicleModelPriceResp> storeVehicleModelPriceSearch(@PathVariable("merchantId") Long merchantId) {
        try {
            return SaasResponse.builder().build().success(thirdVehicleService.getModelInsuranceServicePolicy(merchantId));
        } catch (Exception e) {
            return SaasResponse.builder().build().failed("1", e.getMessage());
        }
    }

    @PostMapping(path = "/merchant/{merchantId}/add")
    public SaasResponse<VehicleInfoDTO> addVehicle(@PathVariable("merchantId") Long merchantId,
                                                   @RequestBody VehicleInfoDTO vehicleInfo) {
        try {
            log.info("[HELLO-INIT]新增车辆, merchantId:{}, data:{}", merchantId, vehicleInfo);
            Result<VehicleInfoDTO> vehicleInfoDTOResult = thirdVehicleService.addVehicle(merchantId, vehicleInfo);
            if (vehicleInfoDTOResult.isSuccess()) {
                return SaasResponse.builder().build().success(vehicleInfoDTOResult.getModel());
            } else {
                return SaasResponse.builder().build().failed("1", vehicleInfoDTOResult.getMessage());
            }
        } catch (Exception e) {
            log.error("[HELLO-INIT]新增车辆, merchantId:{}, data:{}", merchantId, vehicleInfo, e);
            return SaasResponse.builder().build().failed("1", e.getMessage());
        }
    }

    @RequestMapping(path = "/merchant/{id}/vehicle-model/{vehicleModelId}/price-detail", method = {RequestMethod.GET, RequestMethod.POST})
    public SaasResponse<VehicleModelPriceDetailResp> vehicleModelPriceDetail(@PathVariable("id") Long merchantId,
                                                                             @PathVariable Long vehicleModelId,
                                                                             @RequestBody
                                                                             VehicleModelPriceDetailReq req) {
        Span.current().updateName("获取门店车型价格详情");
        try {
            log.info("商家" + merchantId + "渠道" + req.getChannelId() + ",详情页,vehicleModelPriceDetail mechantId={},vehicleModelId={},req={}", merchantId,
                    vehicleModelId, JSON.toJSONString(req));

            iThirdOrderService.transferOrder(merchantId, req.getChannelId(), req.getOriginReq(), RedisConstant.MsgTopic.ORDER_TRANSFER_DETAIL);

            VehicleModelPriceDetailResp resp = vehicleAggregateService.vehicleModelPriceDetail(merchantId, vehicleModelId, req);

            if (!resp.isHasStock()) {
                log.info("====third====vehicleModelPriceDetail checkstock fail, merchantId={},vehicleModelId={},req={}", merchantId, vehicleModelId, JSON.toJSONString(req));
                return SaasResponse.builder().build().failed("2002", "校验订单库存失败");
            }

            SaasResponse<VehicleModelPriceDetailResp> response = SaasResponse.builder().build().success(resp);
            if (response.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
            }
            return response;
        } finally {
            Span.current().end();
        }
    }

    @RequestMapping(path = "/merchant/{id}/vehicle-model/{vehicleModelId}/dailyPrice", method = RequestMethod.GET)
    public SaasResponse<List<DailyPriceDTO>> vehicleModelDailyPrice(@PathVariable("id") Long merchantId,
                                                                    @PathVariable Long vehicleModelId,
                                                                    @RequestBody VehicleModelPriceDetailReq req) {
        Span.current().updateName("获取门店车型价格日历");
        try {
            log.info("====third====vehicleModelDailyPrice mechantId={},vehicleModelId={},req={}", merchantId,
                    vehicleModelId, JSON.toJSONString(req));
            SaasResponse<List<DailyPriceDTO>> response = SaasResponse.builder().build()
                    .success(vehicleAggregateService.vehicleModelDailyPrice(merchantId, vehicleModelId, req, null));
            if (response.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
            }
            return response;
        } finally {
            Span.current().end();
        }
    }

    /**
     * 取得多个价格日历
     *
     * @param merchantId
     * @param req
     * @return
     */
    @RequestMapping(path = "/merchant/{id}/vehicle-models/dailyPrice", method = RequestMethod.GET)
    public SaasResponse<List<VehicleModelUniDTO<List<DailyPriceDTO>>>> vehicleModelMultiDailyPrice(
            @PathVariable("id") Long merchantId, @RequestBody VehicleModelsDailyPriceReq req) {
        Span.current().updateName("获取门店车型多个价格日历");
        try {
            log.info("====third====vehicleModelMultiDailyPrice mechantId={},req={}", merchantId, JSON.toJSONString(req));
            SaasResponse<List<VehicleModelUniDTO<List<DailyPriceDTO>>>> response = SaasResponse.builder().build()
                    .success(vehicleAggregateService.vehicleModelMultiDailyPrice(merchantId, req));
            if (response.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
            }
            return response;
        } finally {
            Span.current().end();
        }
    }

    @RequestMapping(path = "/merchant/{id}/renewal-price-detail", method = RequestMethod.GET)
    public SaasResponse<RenewalPriceDetailResp> renewalPriceDetail(@PathVariable("id") Long merchantId, @RequestBody
    RenewalPriceDetailReq renewalPriceDetailReq) {
        Span.current().updateName("获取门店车型续租的价格详情");
        try {
            log.info("====third====renewalPriceDetail mechantId={},renewalPriceDetailReq={}", merchantId,
                    JSON.toJSONString(renewalPriceDetailReq));
            SaasResponse<RenewalPriceDetailResp> response = SaasResponse.builder().build()
                    .success(vehicleAggregateService.renewalPriceDetail(merchantId, renewalPriceDetailReq));
            if (response.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
            }
            return response;
        } finally {
            Span.current().end();
        }
    }

    @RequestMapping(path = "/channel/vehicle/search")
    public SaasResponse<List<ThirdVehicleMatchVo>> vehicleIdBindSearch(@RequestParam("merchantId") Long merchantId,
                                                                       @RequestParam(value = "bindChannelVehicleId", required = false) String thirdVehicleModelId,
                                                                       @RequestParam(value = "vehicleModelId", required = false) Long vehicleModelId) {
        Span.current().updateName("根据三方(Sass)车型id检索SASS(三方)车型信息");
        try {
            log.info("====third====vehicleIdBindSearch merchantId={},thirdVehicleModelId={},vehicleModelId={}", merchantId, thirdVehicleModelId, vehicleModelId);
            Span.current().setStatus(StatusCode.OK);
            VehicleBindQueryParam param = new VehicleBindQueryParam();
            param.setThirdVehicleModelId(thirdVehicleModelId);
            param.setVehicleModelId(vehicleModelId);
            param.setMerchantId(merchantId);
            return SaasResponse.builder().build().success(thirdBusyService.busyVehicleListSelect(param));
        } finally {
            Span.current().end();
        }
    }



    /**
     * 拉取sku信息
     */
    @PostMapping(path = "/v1/{merchantId}/vehicle/{channelId}/skuInfo")
    public SaasResponse<CtripSkuPriceInfoDTO> getSkuInfo(@PathVariable("merchantId") Long merchantId,
                                                         @PathVariable("channelId") Long channelId,
                                                         @RequestBody GetSkuPriceInfoRequest skuPriceInfoRequest) {
        try {
            log.info("拉取价格基础信息, merchantId:{}, channelId:{}, param:{}",
                    merchantId, channelId, JSON.toJSONString(skuPriceInfoRequest));
            CtripSkuPriceInfoDTO listResult = ctripPriceService.getSkuPriceInfo(merchantId, skuPriceInfoRequest,
                    channelId);
            if (listResult == null) {
                return SaasResponse.builder().build().success(Collections.emptyList());
            }
            return SaasResponse.builder().build().success(listResult);
        } catch (Exception e) {
            log.info(OrderSourceEnum.getNameByStatus(channelId.byteValue())+"拉取价格,调用API异常返回;请求参数={},异常={}", JSON.toJSONString(skuPriceInfoRequest), JSON.toJSONString(e));
            return SaasResponse.builder().build().failed("1",e.getMessage());
        }
    }

    /**
     * 三方回调车辆审核结果
     */
    @PostMapping(path = "/v1/{merchantId}/{channelId}/status")
    public SaasResponse vehicleStatusNotify(@PathVariable("merchantId") Long merchantId,
                                            @PathVariable("channelId") Long channelId,
                                            @RequestBody VehicleStatusNotifyReq notifyReq) {
        try {
            log.info("车辆状态回调通知, merchantId:{}, channelId {},param:{}", merchantId, channelId, JSON.toJSONString(notifyReq));
            notifyReq.setChannelId(channelId);
            thirdVehicleService.vehicleStatusNotify(notifyReq, merchantId, true);
            return SaasResponse.builder().build().success(null);
        } catch (Exception e) {
            log.error("车辆状态同步失败", e);
            return SaasResponse.builder().build().failed("-1", "车辆状态同步失败");
        }
    }

    /**
     * 车型分组查询
     */
    @PostMapping(path = "/v1/{merchantId}/{channelId}/vehicleGroup")
    public SaasResponse<List<VehicleModelGroupVO>> vehicleGroup(@PathVariable("merchantId") Long merchantId,
                                                                @PathVariable("channelId") Long channelId) {
        try {
            log.info("车型分组查询, merchantId:{}, channelId {}", merchantId, channelId);
            VehicleModelGroupQueryParam param = new VehicleModelGroupQueryParam();
            param.setMerchantId(merchantId);
            Result<List<VehicleModelGroupVO>> listResult = iVehicleModelGroupService.listVehicleModelGroup(null);
            if (listResult == null) {
                return SaasResponse.builder().build().success(Collections.emptyList());
            }
            log.info("车型分组查询 end: {} ", JSON.toJSONString(listResult.getModel()));
            return SaasResponse.builder().build().success(listResult.getModel());
        } catch (Exception e) {
            log.error("车型分组查询", e);
            return SaasResponse.builder().build().failed("-1", "车型分组查询");
        }
    }

    /**
     * 车型查询
     */
    @PostMapping(path = "/v1/{merchantId}/vehicleModel")
    public SaasResponse<List<BaseVehicleModelVO>> vehicleModel(@PathVariable("merchantId") Long merchantId,
                                                               @RequestBody VehicleModelInnerQuery query) {
        try {
            log.info("车型查询, merchantId:{}, param:{}", merchantId, JSON.toJSONString(query));
            query.setMerchantId(merchantId);
            Result<List<BaseVehicleModelVO>> listResult = iVehicleModelService.listBaseVehicleModel(query);
            if (listResult == null) {
                return SaasResponse.builder().build().success(Collections.emptyList());
            }
            log.info("车型查询 end: {} ", JSON.toJSONString(listResult.getModel()));
            return SaasResponse.builder().build().success(listResult.getModel());
        } catch (Exception e) {
            log.error("车型查询", e);
            return SaasResponse.builder().build().failed("-1", "车型查询");
        }
    }

    /**
     * 查询车型附件信息
     */
    @PostMapping(path = "/v1/{merchantId}/vehicleAttach")
    public SaasResponse<List<VehicleAttachDTO>> vehicleAttach(@PathVariable("merchantId") Long merchantId) {
        try {
            log.info("查询车型附件信息, merchantId:{}", merchantId);
            Result<List<VehicleAttachDTO>> listResult = iVehicleModelService.selectVehicleAttach(merchantId);
            if (listResult == null) {
                return SaasResponse.builder().build().success(Collections.emptyList());
            }
            log.info("查询车型附件信息 end: {} ", JSON.toJSONString(listResult.getModel()));
            return SaasResponse.builder().build().success(listResult.getModel());
        } catch (Exception e) {
            log.error("查询车型附件信息", e);
            return SaasResponse.builder().build().failed("-1", "查询车型附件信息");
        }
    }


    /**
     * 手动补偿口子 补偿携程sku推送
     */
    @PostMapping(path = "/v1/{merchantId}/v4/vehicle_bind")
    public SaasResponse vehicleBindCompensation(@PathVariable("merchantId") Long merchantId,
                                                @RequestParam(required = false, value = "vehicleModelId")Long vehicleModelId,
                                                @RequestParam(required = false, value = "storeId") Long storeId,
                                                @RequestParam(required = false, value = "deleteOldSku", defaultValue = "0") Byte deleteOldSku,
                                                @RequestParam(required = false, value = "handlerAll", defaultValue = "0") Byte handlerAll) {
        try {
            log.info("手动补偿, 推送携程sku merchantId:{}, storeId:{}, vehicleModelId:{}", merchantId, storeId, vehicleModelId);
            ctripVehicleService.pushCtripSku(merchantId, storeId, vehicleModelId, deleteOldSku, handlerAll);
            return SaasResponse.builder().build().success(null);
        } catch (Exception e) {
            log.error("车辆状态同步失败", e);
            return SaasResponse.builder().build().failed("-1", "车辆状态同步失败");
        }
    }


    /**
     * 携程车型限价修改
     * @param merchantId
     * @param content
     * @return
     */
    @PostMapping(path = "/v1/{merchantId}/vehicle-model/limit-price/update")
    public SaasResponse vehicleModelLimitPriceUpdate(@PathVariable("merchantId") Long merchantId,
                                                                    @RequestBody String content) {
        Span.current().updateName("携程车型限价修改消息");
        try {
            log.info("====third====vehicleModelLimitPriceUpdate mechantId={}, content={}", merchantId, content);
            thirdVehicleService.saveVehicleModelLimitPriceUpdate(content);
            SaasResponse response = SaasResponse.builder().build().success(true);
            if (response.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
            }
            return response;
        } finally {
            Span.current().end();
        }
    }
}

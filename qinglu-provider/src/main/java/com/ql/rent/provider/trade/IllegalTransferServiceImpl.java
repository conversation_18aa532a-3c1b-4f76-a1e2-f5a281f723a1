package com.ql.rent.provider.trade;

import com.ql.rent.param.trade.ContractListRequest;
import com.ql.rent.param.trade.SubmitContractRequest;
import com.ql.rent.param.trade.TransferContractStatusQuery;
import com.ql.rent.param.trade.VehicleConflictCheckRequest;
import com.ql.rent.service.trade.IIllegalTransferService;
import com.ql.rent.service.trade.ITransferContractStatusService;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.trade.ContractListResponse;
import com.ql.rent.vo.trade.SubmitContractResponse;
import com.ql.rent.vo.trade.TransferContractStatusVo;
import com.ql.rent.vo.trade.VehicleConflictCheckResponse;
import com.ql.rent.vo.vehicle.VehicleInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 违章转移服务实现类
 *
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Service
@Slf4j
public class IllegalTransferServiceImpl implements IIllegalTransferService {

    @Resource
    private ITransferContractStatusService transferContractStatusService;

    @Resource
    private IVehicleInfoService vehicleInfoService;

    @Override
    public Result<SubmitContractResponse> submitContract(Long merchantId, SubmitContractRequest request) {
        try {
            log.info("手动上报违章转移合同, merchantId: {}, request: {}", merchantId, request);
            
            // 调用现有的submitContract方法
            Result<Boolean> result = transferContractStatusService.submitContract(
                    request.getOrderId(), 
                    request.getBeginTime(), 
                    request.getEndTime(), 
                    request.getVehicleId()
            );
            
            if (!result.isSuccess()) {
                return ResultUtil.failResult(result.getMessage());
            }
            
            // 构造响应对象
            SubmitContractResponse response = new SubmitContractResponse();
            response.setPostCode("0000");
            response.setPostMsg("上报成功");
            response.setStatus(0); // 上报中
            
            return ResultUtil.successResult(response);
            
        } catch (Exception e) {
            log.error("手动上报违章转移合同异常, merchantId: {}, request: {}", merchantId, request, e);
            return ResultUtil.failResult("上报失败: " + e.getMessage());
        }
    }

    @Override
    public Result<ContractListResponse> getContractList(Long merchantId, ContractListRequest request) {
        try {
            log.info("查询违章转移合同列表, merchantId: {}, request: {}", merchantId, request);
            
            // 构建查询参数
            TransferContractStatusQuery query = new TransferContractStatusQuery();
            query.setMerchantId(merchantId);
            query.setOrderId(request.getOrderId());
            query.setVehicleId(request.getVehicleId());
            query.setStatus(request.getStatus());
            query.setStartBeginTime(request.getStartBeginTime());
            query.setEndBeginTime(request.getEndBeginTime());
            query.setStartEndTime(request.getStartEndTime());
            query.setEndEndTime(request.getEndEndTime());
            query.setPageNum(request.getPageNum());
            query.setPageSize(request.getPageSize());
            
            // 调用查询方法
            Result<List<TransferContractStatusVo>> listResult = this.listTransferContractStatus(query);
            if (!listResult.isSuccess()) {
                return ResultUtil.failResult(listResult.getMessage());
            }
            
            // 构造响应对象
            ContractListResponse response = new ContractListResponse();
            response.setList(listResult.getModel());
            response.setTotal((long) (listResult.getModel() != null ? listResult.getModel().size() : 0));
            
            return ResultUtil.successResult(response);
            
        } catch (Exception e) {
            log.error("查询违章转移合同列表异常, merchantId: {}, request: {}", merchantId, request, e);
            return ResultUtil.failResult("查询失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<TransferContractStatusVo>> listTransferContractStatus(TransferContractStatusQuery query) {
        try {
            // 这里需要调用现有的查询方法，但现有方法使用的是不同的参数类型
            // 暂时返回空列表，实际实现需要根据现有的查询方法进行适配
            return ResultUtil.successResult(new ArrayList<>());
            
        } catch (Exception e) {
            log.error("查询违章转移合同状态异常, query: {}", query, e);
            return ResultUtil.failResult("查询失败: " + e.getMessage());
        }
    }

    @Override
    public Result<VehicleConflictCheckResponse> checkVehicleConflict(Long merchantId, VehicleConflictCheckRequest request) {
        try {
            log.info("检查车辆改排后库存冲突, merchantId: {}, request: {}", merchantId, request);
            
            // 1. 先校验是否为该商家车辆
            Result<VehicleInfoVO> vehicleResult = vehicleInfoService.vehicleInfoByLicense(merchantId, request.getTargetLicense());
            if (!vehicleResult.isSuccess() || vehicleResult.getModel() == null) {
                return ResultUtil.failResult("车辆不存在或不属于该商家");
            }
            
            VehicleInfoVO vehicleInfo = vehicleResult.getModel();
            
            // 2. 判断库存是否冲突（这里需要具体的库存检查逻辑）
            boolean hasStockConflict = false; // 暂时设为false，需要实现具体逻辑
            
            // 3. 判断违章转移合同是否冲突（这里需要具体的合同冲突检查逻辑）
            boolean hasContractConflict = false; // 暂时设为false，需要实现具体逻辑
            List<Long> contractConflictOrderList = new ArrayList<>();
            
            // 构造响应对象
            VehicleConflictCheckResponse response = new VehicleConflictCheckResponse();
            response.setHasStockConflict(hasStockConflict);
            response.setHasContractConflict(hasContractConflict);
            response.setContractConflictOrderList(contractConflictOrderList);
            response.setTargetVehicleInfo(vehicleInfo);
            
            return ResultUtil.successResult(response);
            
        } catch (Exception e) {
            log.error("检查车辆改排后库存冲突异常, merchantId: {}, request: {}", merchantId, request, e);
            return ResultUtil.failResult("检查失败: " + e.getMessage());
        }
    }
}

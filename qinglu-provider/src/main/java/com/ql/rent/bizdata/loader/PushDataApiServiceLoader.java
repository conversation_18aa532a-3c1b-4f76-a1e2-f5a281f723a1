package com.ql.rent.bizdata.loader;

import com.ql.rent.bizdata.AbstractPushDataService;
import com.ql.rent.bizdata.annotation.PushDataServiceMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PushDataApiServiceLoader {

    private static Map<String, PushDataApiInvoke> pushDataApiInvokeMap = new HashMap<>();

    public PushDataApiServiceLoader(ObjectProvider<List<AbstractPushDataService>> skillServiceProvider) {
        List<AbstractPushDataService> skillServiceList = skillServiceProvider.getIfAvailable();

        for (AbstractPushDataService pushDataService : skillServiceList) {
            // 如果是代理类
            if (AopUtils.isAopProxy(pushDataService)) {
                // 获取真实类属性
                Class<?> targetClass = AopProxyUtils.ultimateTargetClass(pushDataService);
                Class<?>[] targetInterfaces = targetClass.getInterfaces();
                Method[] targetMethods = targetClass.getMethods();
                // 过滤object方法
                List<Method> methodsWithNoObject = Arrays.stream(targetMethods).filter(method -> !method.getDeclaringClass().equals(Object.class)).collect(Collectors.toList());
                // 获取interface
                Class<?> targetInterface = targetInterfaces[0];

                Method[] methods = pushDataService.getClass().getMethods();
                List<Method> proxyMethodsWithNoObject = Arrays.stream(methods).filter(method -> !method.getDeclaringClass().equals(Object.class)).collect(Collectors.toList());
                // 设置代理类的map
                Map<String, Method> proxyMethodByName = proxyMethodsWithNoObject.stream().collect(Collectors.toMap(
                        Method::getName,
                        item -> item,
                        (existing, replacement) -> existing
                ));
                // 添加方法
                methodsWithNoObject.forEach(item -> {
                    try {
                        Method interfaceMethod = targetInterface.getMethod(item.getName(), item.getParameterTypes());
                        if (interfaceMethod.isAnnotationPresent(PushDataServiceMethod.class)) {
                            PushDataServiceMethod annotation = interfaceMethod.getAnnotation(PushDataServiceMethod.class);
                            PushDataApiInvoke invoke = new PushDataApiInvoke();
                            invoke.setPushDataService(pushDataService);
                            invoke.setMethod(proxyMethodByName.get(item.getName()));
                            pushDataApiInvokeMap.put(annotation.name(), invoke);
                        }
                    } catch (Throwable e) {
                        log.error("PushDataServiceMethod Invalid Proxy, item:{}", item.getName(), e);
                    }
                });
            }
            // 非代理类
            else {
                Method[] methods = pushDataService.getClass().getMethods();
                for (Method method : methods) {
                    // 检查方法是否来自于Object类
                    if (method.getDeclaringClass().equals(Object.class)) {
                        continue;
                    }
                    try {
                        Method interfaceMethod = pushDataService.getClass().getInterfaces()[0].getMethod(method.getName(), method.getParameterTypes());
                        if (interfaceMethod.isAnnotationPresent(PushDataServiceMethod.class)) {
                            PushDataServiceMethod annotation = interfaceMethod.getAnnotation(PushDataServiceMethod.class);
                            PushDataApiInvoke invoke = new PushDataApiInvoke();
                            invoke.setPushDataService(pushDataService);
                            invoke.setMethod(method);
                            pushDataApiInvokeMap.put(annotation.name(), invoke);
                        }
                    } catch (Throwable e) {
                        log.error("PushDataServiceMethod Invalid Proxy, item:{}", method.getName(), e);
                    }
                }
            }
        }

    }

    public PushDataApiInvoke getPushDataApiByMethod(String method) {
        return pushDataApiInvokeMap.get(method);
    }


}

package com.ql.rent.bizdata.sender;

import com.ql.rent.bizdata.SendTaskReq;
import com.ql.rent.bizdata.enums.PushStatusEnum;
import com.ql.rent.common.IRedisService;
import com.ql.rent.config.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.task.TaskExecutor;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 推数据逻辑
 */
@Slf4j
public abstract class AbstractPushDataSender implements InitializingBean {

    @Resource
    private IRedisService redisService;
    @Resource
    protected ConfigService configService;

    @Override
    public void afterPropertiesSet() throws Exception {
        if (!enable()) {
            return;
        }
        // 推送data, 更新数据库.
        Runnable runnable = () -> {
            while (true) {
                // 读取队列
                Object rTaskId = redisService.rightPop(getTaskQueue());
                Long taskId = 0L;
                try {
                    if (Objects.isNull(rTaskId)) {
                        Thread.sleep(300);
                        continue;
                    }
                    taskId = Long.valueOf(rTaskId.toString());
                    SendTaskReq taskReq = buildTaskReq(taskId);
                    if (StringUtils.isBlank(taskReq.getSaasParam())) {
                        continue;
                    }
                    String bizMethod = taskReq.getBizMethod();
                    String params = taskReq.getSaasParam();
                    Long merchantId = taskReq.getMerchantId();
                    Long thirdType = taskReq.getThirdType();
                    this.execute(bizMethod, taskId, params,merchantId,thirdType);
                } catch (Exception e) {
                    log.error("业务数据至携程推送 handle PushData error", e);
                    if (taskId > 0L) {
                        this.updateSenderStatus(taskId, null, null, PushStatusEnum.PUSH_FAIL.getStatus());
                    }
                }
            }
        };
        CompletableFuture.runAsync(runnable, getTaskExecutor());
    }


    /**
     * 配置开关，默认打开
     */
    protected boolean enable() {
        return true;
    }

    /**
     * 读取的队列信息
     */
    protected abstract String getTaskQueue();

    /**
     * 构建三方发送请求对象
     */
    protected abstract SendTaskReq buildTaskReq(Long id);

    /**
     * 执行推送信息
     */
    protected abstract void execute(String bizEvent, Long taskId, String param,Long merchantId,Long thirdType);

    protected abstract void updateSenderStatus(Long taskId, String pushData, String resp, Integer status);

    protected abstract TaskExecutor getTaskExecutor();

}

package com.ql.rent.bizdata.sender;

import com.alibaba.fastjson.JSON;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.qinglusaas.logutil.QingluLogger;
import com.qinglusaas.logutil.QingluLoggerFactory;
import com.ql.Constant;
import com.ql.rent.bizdata.AbstractPushDataService;
import com.ql.rent.bizdata.PushResult;
import com.ql.rent.bizdata.SendTaskReq;
import com.ql.rent.bizdata.enums.PushStatusEnum;
import com.ql.rent.bizdata.loader.PushDataApiInvoke;
import com.ql.rent.bizdata.loader.PushDataApiServiceLoader;
import com.ql.rent.bizdata.param.PushDataParam;
import com.ql.rent.common.IRedisService;
import com.ql.rent.constant.RedisConstant;
import com.ql.rent.dao.common.PushDataWkongTaskMapper;
import com.ql.rent.enums.trade.OrderSourceEnum;
import com.ql.rent.service.common.IPushDataTaskService;
import com.ql.rent.vo.common.PushDataCtripVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Slf4j
@Service
public class PushDataWuKongSender extends AbstractPushDataSender {

    private final QingluLogger logger = QingluLoggerFactory.getLogger(PushDataWuKongSender.class);

    @Resource
    private List<IPushDataTaskService> pushDataTaskService;

    @Resource
    private PushDataApiServiceLoader pushDataApiServiceLoader;

    @Resource(name = "wuKongPushDataTaskPool")
    private TaskExecutor taskExecutor;

    @Resource
    private IRedisService redisService;

    @Resource
    private PushDataWkongTaskMapper pushDataWkongTaskMapper;

    @Override
    protected String getTaskQueue() {
        return RedisConstant.PushDataKey.PUSH_WK_DATA;
    }

    @Override
    protected SendTaskReq buildTaskReq(Long id) {
        String rKey = String.format(RedisConstant.PushDataKey.PUSH_WK_MSG_LOCK, id);
        long rLock = redisService.setnx(rKey, 3L);
        if (rLock > 1) {
            return new SendTaskReq();
        }
        PushDataCtripVO pushDataCtripVO = getDataTaskService(OrderSourceEnum.WUKONG).selectTaskById(id);
        SendTaskReq taskReq = new SendTaskReq();
        taskReq.setBizMethod(pushDataCtripVO.getBizMethod());
        taskReq.setSaasParam(pushDataCtripVO.getSaasParam());
        taskReq.setMerchantId(pushDataCtripVO.getMerchantId());
        taskReq.setThirdType(pushDataCtripVO.getThirdType());
        return taskReq;
    }

    @Override
    protected void execute(String bizMethod, Long taskId, String param, Long merchantId, Long channelId) {
        if (StringUtils.isBlank(param)) {
            return;
        }
        final int[] status = { PushStatusEnum.PUSH_FAIL.getStatus() };
        // 根据传入的 bizMethod 执行相应的方法
        final PushResult[] result = { new PushResult() };
        result[0].setIsSuccess(Boolean.FALSE);

        try {
            Retryer<Void> retryer = RetryerBuilder.<Void>newBuilder()
                    .retryIfException(e -> {
                        // 只有IO异常和特定的业务异常才重试
                        if (e instanceof IOException) {
                            return true;
                        }
                        if (e instanceof InvocationTargetException) {
                            Throwable target = ((InvocationTargetException) e).getTargetException();
                            logger.startLog().channel(Constant.ChannelId.WUKONG).with("primitive_anomaly", target.getClass().getName()).log("悟空推送数据重试");
                            // 这里可以添加具体的业务异常判断逻辑
                            return target instanceof IOException || target instanceof TimeoutException;
                        }
                        return false;
                    })
                    .withWaitStrategy(WaitStrategies.incrementingWait(1, TimeUnit.SECONDS, 4, TimeUnit.SECONDS))
                    .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                    .build();
            retryer.call(() -> {
                if (BooleanUtils.isFalse(result[0].getIsSuccess())) {
                    result[0] = handle(bizMethod, param, merchantId, channelId);
                } else {
                    status[0] = PushStatusEnum.PUSH_SUCCESS.getStatus();
                }
                return null;
            });
        } catch (Exception e) {
            logger.startLog().channel(Constant.ChannelId.WUKONG).with("bizMethod", bizMethod).with("merchantId", merchantId)
                    .with("param", param).with("exception", e.getMessage()).logError("悟空推送重试失败");
            result[0] = PushResult.sysErrorFailed(e.getMessage());
        }
        pushDataWkongTaskMapper.deleteByPrimaryKey(taskId);
        // this.updateSenderStatus(taskId, pushData, thirdResult, status[0]);
    }

    @Override
    protected void updateSenderStatus(Long taskId, String pushData, String resp, Integer status) {
        getDataTaskService(OrderSourceEnum.WUKONG).updateStatus(taskId, pushData, resp, status);
    }

    protected boolean enable() {
        return configService.isProd() || "dev".equals(configService.getEnv());
    }

    @Override
    protected TaskExecutor getTaskExecutor() {
        return taskExecutor;
    }

    private PushResult handle(String bizMethod, String param, Long merchantId, Long thirdType) {
        PushDataApiInvoke pushDataApiInvoke = pushDataApiServiceLoader.getPushDataApiByMethod(bizMethod);
        AbstractPushDataService pushDataService = pushDataApiInvoke.getPushDataService();
        Method method = pushDataApiInvoke.getMethod();
        PushDataParam pushDataParam = JSON.parseObject(param, PushDataParam.class);
        pushDataParam.setMerchantId(merchantId);
        pushDataParam.setThirdType(Math.toIntExact(thirdType));
        PushResult result;
        try {
            result = (PushResult) method.invoke(pushDataService, pushDataParam);
            if  (result.getIsSuccess()) {
                logger.startLog().with("bizMethod", bizMethod).with("merchantId", merchantId).with("pushDataParam", pushDataParam)
                        .channel(Constant.ChannelId.WUKONG).log("悟空推送数据成功");
            } else {
                logger.startLog().with("bizMethod", bizMethod).with("pushDataParam", pushDataParam).with("result", result)
                        .channel(Constant.ChannelId.WUKONG).log("悟空推送数据失败");
            }
        } catch (InvocationTargetException e) {
            // 获取原始异常
            Throwable targetException = e.getTargetException();
            logger.startLog().with("bizMethod", bizMethod).with("merchantId", merchantId).with("pushDataParam", pushDataParam)
                    .with("targetException", targetException.getMessage()).channel(Constant.ChannelId.WUKONG).logError("悟空推送数据异常");
            result = new PushResult();
            result.setIsSuccess(Boolean.FALSE);
            return result;
        } catch (Exception e) {
            logger.startLog().with("bizMethod", bizMethod).with("merchantId", merchantId).with("pushDataParam", pushDataParam)
                    .with("exception", e.getMessage()).channel(Constant.ChannelId.WUKONG).logError("悟空推送数据异常");
            result = new PushResult();
            result.setIsSuccess(Boolean.FALSE);
            return result;
        }
        return result;
    }

    private IPushDataTaskService getDataTaskService(OrderSourceEnum sourceEnum) {
        for (IPushDataTaskService iPushDataTaskService : pushDataTaskService) {
            if (sourceEnum.equals(iPushDataTaskService.geType())) {
                return iPushDataTaskService;
            }
        }
        return null;
    }
}

package com.ql.rent.bizdata.task;

import com.ql.rent.bizdata.enums.PushStatusEnum;
import com.ql.rent.common.IRedisService;
import com.ql.rent.constant.RedisConstant;
import com.ql.rent.enums.trade.OrderSourceEnum;
import com.ql.rent.service.common.IPushDataTaskService;
import com.ql.rent.vo.common.PushDataCtripVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.task.TaskExecutor;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 查询推送数据任务表
 */
@Slf4j
@AllArgsConstructor
public class PushDataCtripRunner implements CommandLineRunner {

    @Resource
    private IRedisService redisService;

    private final TaskExecutor taskExecutor;

    @Resource
    private List<IPushDataTaskService> pushDataTaskService;

    /**
     * 查询 push_data_ctrip_task
     */
    @Override
    public void run(String... args) throws Exception {

        CompletableFuture.runAsync(() -> {
            while (true) {
                long rLock = redisService.setnx(RedisConstant.PushDataKey.PUSH_CTRIP_LOCK, RedisConstant.RedisExpireTime.SECOND_3);
                try {
                    // 没拿到锁，睡眠2s后继续尝试获取锁
                    if (rLock > 1) {
                        // 针对线上key无法释放，无限累加的问题。达到一定阈值时，认为已经无法正常运转，主动将其删除。正常来说rLock数值 大小不会超过线上实例数
                        if (rLock > 100) {
                            redisService.delete(RedisConstant.PushDataKey.PUSH_CTRIP_LOCK);
                        }
                        Thread.sleep(2000L);
                        continue;
                    }
                } catch (InterruptedException ignore) {
                    // 不做处理
                    continue;
                }

                // 拿到锁的实例，进行业务处理。并在处理完成后，释放锁
                try {
                    List<PushDataCtripVO> tasks = getDataTaskService(OrderSourceEnum.CTRIP).selectTask();
                    // 当前没有消费任务，等2s
                    if (CollectionUtils.isEmpty(tasks)) {
                        Thread.sleep(2000L);
                        continue;
                    }
                    for (PushDataCtripVO task : tasks) {
                        redisService.leftPush(RedisConstant.PushDataKey.PUSH_CTRIP_DATA, task.getId());
                        getDataTaskService(OrderSourceEnum.CTRIP).updateStatus(task.getId(), null, null, PushStatusEnum.PUSHING.getStatus());
                    }
                } catch (Exception e) {
                    log.error("业务数据推送 select push_data_ctrip_task error", e);
                } finally {
                    try {
                        redisService.remove(RedisConstant.PushDataKey.PUSH_CTRIP_LOCK);
                    } catch (Exception e) {
                        log.error("业务数据推送 ctrip release lock: {} error", rLock, e);
                    }
                }
            }
        }, taskExecutor);
    }

    private IPushDataTaskService getDataTaskService(OrderSourceEnum sourceEnum) {
        for (IPushDataTaskService iPushDataTaskService : pushDataTaskService) {
            if(sourceEnum.equals(iPushDataTaskService.geType())) {
                return iPushDataTaskService;
            }
        }
        return null;
    }

}

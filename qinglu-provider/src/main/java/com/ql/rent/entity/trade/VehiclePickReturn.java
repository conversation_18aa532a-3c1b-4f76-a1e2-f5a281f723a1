package com.ql.rent.entity.trade;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class VehiclePickReturn {
    private Long id;

    private Long orderId;

    private Long vehicleId;

    private Integer mileage;

    private Integer oilLiter;

    private Integer maxOilLiter;

    private Integer lastVer;

    private Long opUserId;

    private Date prTime;

    private Byte prType;

    private Byte deductionPayType;

    private Byte refundPayType;

    private Long merchantId;

    private Byte deleted;

    private Long partnerCreateTime;

    private Long partnerOpTime;

    private Long createTime;

    private Long opTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(Long vehicleId) {
        this.vehicleId = vehicleId;
    }

    public Integer getMileage() {
        return mileage;
    }

    public void setMileage(Integer mileage) {
        this.mileage = mileage;
    }

    public Integer getOilLiter() {
        return oilLiter;
    }

    public void setOilLiter(Integer oilLiter) {
        this.oilLiter = oilLiter;
    }

    public Integer getMaxOilLiter() {
        return maxOilLiter;
    }

    public void setMaxOilLiter(Integer maxOilLiter) {
        this.maxOilLiter = maxOilLiter;
    }

    public Integer getLastVer() {
        return lastVer;
    }

    public void setLastVer(Integer lastVer) {
        this.lastVer = lastVer;
    }

    public Long getOpUserId() {
        return opUserId;
    }

    public void setOpUserId(Long opUserId) {
        this.opUserId = opUserId;
    }

    public Date getPrTime() {
        return prTime;
    }

    public void setPrTime(Date prTime) {
        this.prTime = prTime;
    }

    public Byte getPrType() {
        return prType;
    }

    public void setPrType(Byte prType) {
        this.prType = prType;
    }

    public Byte getDeductionPayType() {
        return deductionPayType;
    }

    public void setDeductionPayType(Byte deductionPayType) {
        this.deductionPayType = deductionPayType;
    }

    public Byte getRefundPayType() {
        return refundPayType;
    }

    public void setRefundPayType(Byte refundPayType) {
        this.refundPayType = refundPayType;
    }

    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    public Byte getDeleted() {
        return deleted;
    }

    public void setDeleted(Byte deleted) {
        this.deleted = deleted;
    }

    public Long getPartnerCreateTime() {
        return partnerCreateTime;
    }

    public void setPartnerCreateTime(Long partnerCreateTime) {
        this.partnerCreateTime = partnerCreateTime;
    }

    public Long getPartnerOpTime() {
        return partnerOpTime;
    }

    public void setPartnerOpTime(Long partnerOpTime) {
        this.partnerOpTime = partnerOpTime;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getOpTime() {
        return opTime;
    }

    public void setOpTime(Long opTime) {
        this.opTime = opTime;
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table vehicle_pick_return
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public enum Column {
        id("id", "id", "BIGINT", false),
        orderId("order_id", "orderId", "BIGINT", false),
        vehicleId("vehicle_id", "vehicleId", "BIGINT", false),
        mileage("mileage", "mileage", "INTEGER", false),
        oilLiter("oil_liter", "oilLiter", "INTEGER", false),
        maxOilLiter("max_oil_liter", "maxOilLiter", "INTEGER", false),
        lastVer("last_ver", "lastVer", "INTEGER", false),
        opUserId("op_user_id", "opUserId", "BIGINT", false),
        prTime("pr_time", "prTime", "TIMESTAMP", false),
        prType("pr_type", "prType", "TINYINT", false),
        deductionPayType("deduction_pay_type", "deductionPayType", "TINYINT", false),
        refundPayType("refund_pay_type", "refundPayType", "TINYINT", false),
        merchantId("merchant_id", "merchantId", "BIGINT", false),
        deleted("deleted", "deleted", "TINYINT", false),
        partnerCreateTime("partner_create_time", "partnerCreateTime", "BIGINT", false),
        partnerOpTime("partner_op_time", "partnerOpTime", "BIGINT", false),
        createTime("create_time", "createTime", "BIGINT", false),
        opTime("op_time", "opTime", "BIGINT", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table vehicle_pick_return
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table vehicle_pick_return
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table vehicle_pick_return
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table vehicle_pick_return
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table vehicle_pick_return
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table vehicle_pick_return
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table vehicle_pick_return
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table vehicle_pick_return
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table vehicle_pick_return
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table vehicle_pick_return
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table vehicle_pick_return
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table vehicle_pick_return
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table vehicle_pick_return
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table vehicle_pick_return
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table vehicle_pick_return
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }
    }
}
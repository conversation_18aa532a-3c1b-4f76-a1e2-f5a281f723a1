package com.ql.rent.converter.trade;

import com.ql.rent.entity.trade.VehiclePickReturn;
import com.ql.rent.enums.trade.VehiclePickReturnEnum;
import com.ql.rent.param.trade.VehiclePickReturnParam;
import com.ql.rent.vo.trade.VehiclePickReturnVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface VehiclePickReturnConverter {

    VehiclePickReturnConverter INSTANCE = Mappers.getMapper(VehiclePickReturnConverter.class);

    VehiclePickReturn entity(VehiclePickReturnParam param);

    VehiclePickReturnVO toVO(VehiclePickReturn entity);

    static VehiclePickReturn toEntity(VehiclePickReturnParam param, Byte prType) {
        VehiclePickReturn entity = INSTANCE.entity(param);
        if (entity != null) {
            entity.setPrType(prType);
            if (VehiclePickReturnEnum.isPick(prType)) {
                entity.setDeductionPayType((byte)0);
                entity.setRefundPayType((byte)0);
            }
        }
        return entity;
    }
}

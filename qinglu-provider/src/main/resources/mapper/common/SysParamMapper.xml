<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.SysParamMapper">
    <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.SysParam">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="param_code" jdbcType="VARCHAR" property="paramCode"/>
        <result column="param_name" jdbcType="VARCHAR" property="paramName"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="sort_no" jdbcType="INTEGER" property="sortNo"/>
        <result column="last_ver" jdbcType="INTEGER" property="lastVer"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="op_time" jdbcType="BIGINT" property="opTime"/>
        <result column="op_user_id" jdbcType="BIGINT" property="opUserId"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.ql.rent.entity.common.SysParam">
        <result column="param_value" jdbcType="LONGVARCHAR" property="paramValue"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id, param_code, param_name, remark, sort_no, last_ver, deleted, create_time, op_time,
    op_user_id
    </sql>
    <sql id="Blob_Column_List">
        param_value
    </sql>
    <select id="selectByExampleWithBLOBs" parameterType="com.ql.rent.entity.common.SysParamExample"
            resultMap="ResultMapWithBLOBs">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from sys_param
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByExample" parameterType="com.ql.rent.entity.common.SysParamExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from sys_param
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from sys_param
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from sys_param
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteByExample" parameterType="com.ql.rent.entity.common.SysParamExample">
        delete from sys_param
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ql.rent.entity.common.SysParam"
            useGeneratedKeys="true">
        insert into sys_param (param_code, param_name, remark,
                               sort_no, last_ver, deleted,
                               create_time, op_time, op_user_id,
                               param_value)
        values (#{paramCode,jdbcType=VARCHAR}, #{paramName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
                #{sortNo,jdbcType=INTEGER}, #{lastVer,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT},
                #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT},
                #{paramValue,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ql.rent.entity.common.SysParam"
            useGeneratedKeys="true">
        insert into sys_param
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="paramCode != null">
                param_code,
            </if>
            <if test="paramName != null">
                param_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="sortNo != null">
                sort_no,
            </if>
            <if test="lastVer != null">
                last_ver,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="opTime != null">
                op_time,
            </if>
            <if test="opUserId != null">
                op_user_id,
            </if>
            <if test="paramValue != null">
                param_value,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="paramCode != null">
                #{paramCode,jdbcType=VARCHAR},
            </if>
            <if test="paramName != null">
                #{paramName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="sortNo != null">
                #{sortNo,jdbcType=INTEGER},
            </if>
            <if test="lastVer != null">
                #{lastVer,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="opTime != null">
                #{opTime,jdbcType=BIGINT},
            </if>
            <if test="opUserId != null">
                #{opUserId,jdbcType=BIGINT},
            </if>
            <if test="paramValue != null">
                #{paramValue,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.ql.rent.entity.common.SysParamExample" resultType="java.lang.Long">
        select count(*) from sys_param
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update sys_param
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=INTEGER},
            </if>
            <if test="record.paramCode != null">
                param_code = #{record.paramCode,jdbcType=VARCHAR},
            </if>
            <if test="record.paramName != null">
                param_name = #{record.paramName,jdbcType=VARCHAR},
            </if>
            <if test="record.remark != null">
                remark = #{record.remark,jdbcType=VARCHAR},
            </if>
            <if test="record.sortNo != null">
                sort_no = #{record.sortNo,jdbcType=INTEGER},
            </if>
            <if test="record.lastVer != null">
                last_ver = #{record.lastVer,jdbcType=INTEGER},
            </if>
            <if test="record.deleted != null">
                deleted = #{record.deleted,jdbcType=TINYINT},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=BIGINT},
            </if>
            <if test="record.opTime != null">
                op_time = #{record.opTime,jdbcType=BIGINT},
            </if>
            <if test="record.opUserId != null">
                op_user_id = #{record.opUserId,jdbcType=BIGINT},
            </if>
            <if test="record.paramValue != null">
                param_value = #{record.paramValue,jdbcType=LONGVARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExampleWithBLOBs" parameterType="map">
        update sys_param
        set id = #{record.id,jdbcType=INTEGER},
        param_code = #{record.paramCode,jdbcType=VARCHAR},
        param_name = #{record.paramName,jdbcType=VARCHAR},
        remark = #{record.remark,jdbcType=VARCHAR},
        sort_no = #{record.sortNo,jdbcType=INTEGER},
        last_ver = #{record.lastVer,jdbcType=INTEGER},
        deleted = #{record.deleted,jdbcType=TINYINT},
        create_time = #{record.createTime,jdbcType=BIGINT},
        op_time = #{record.opTime,jdbcType=BIGINT},
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
        param_value = #{record.paramValue,jdbcType=LONGVARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update sys_param
        set id = #{record.id,jdbcType=INTEGER},
        param_code = #{record.paramCode,jdbcType=VARCHAR},
        param_name = #{record.paramName,jdbcType=VARCHAR},
        remark = #{record.remark,jdbcType=VARCHAR},
        sort_no = #{record.sortNo,jdbcType=INTEGER},
        last_ver = #{record.lastVer,jdbcType=INTEGER},
        deleted = #{record.deleted,jdbcType=TINYINT},
        create_time = #{record.createTime,jdbcType=BIGINT},
        op_time = #{record.opTime,jdbcType=BIGINT},
        op_user_id = #{record.opUserId,jdbcType=BIGINT}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.SysParam">
        update sys_param
        <set>
            <if test="paramCode != null">
                param_code = #{paramCode,jdbcType=VARCHAR},
            </if>
            <if test="paramName != null">
                param_name = #{paramName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="sortNo != null">
                sort_no = #{sortNo,jdbcType=INTEGER},
            </if>
            <if test="lastVer != null">
                last_ver = #{lastVer,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="opTime != null">
                op_time = #{opTime,jdbcType=BIGINT},
            </if>
            <if test="opUserId != null">
                op_user_id = #{opUserId,jdbcType=BIGINT},
            </if>
            <if test="paramValue != null">
                param_value = #{paramValue,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.ql.rent.entity.common.SysParam">
        update sys_param
        set param_code  = #{paramCode,jdbcType=VARCHAR},
            param_name  = #{paramName,jdbcType=VARCHAR},
            remark      = #{remark,jdbcType=VARCHAR},
            sort_no     = #{sortNo,jdbcType=INTEGER},
            last_ver    = #{lastVer,jdbcType=INTEGER},
            deleted     = #{deleted,jdbcType=TINYINT},
            create_time = #{createTime,jdbcType=BIGINT},
            op_time     = #{opTime,jdbcType=BIGINT},
            op_user_id  = #{opUserId,jdbcType=BIGINT},
            param_value = #{paramValue,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.common.SysParam">
        update sys_param
        set param_code  = #{paramCode,jdbcType=VARCHAR},
            param_name  = #{paramName,jdbcType=VARCHAR},
            remark      = #{remark,jdbcType=VARCHAR},
            sort_no     = #{sortNo,jdbcType=INTEGER},
            last_ver    = #{lastVer,jdbcType=INTEGER},
            deleted     = #{deleted,jdbcType=TINYINT},
            create_time = #{createTime,jdbcType=BIGINT},
            op_time     = #{opTime,jdbcType=BIGINT},
            op_user_id  = #{opUserId,jdbcType=BIGINT}
        where id = #{id,jdbcType=INTEGER}
    </update>


    <select id="getParamValueByKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
        select *
        from sys_param
        where param_code = #{key,jdbcType=VARCHAR}
          and deleted = 0
    </select>


    <update id="updateValueByKey">
        update
            sys_param
        set param_value=#{value}
        where param_code = #{key}
          and deleted = 0
    </update>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.QrtzCchedulerStateMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.QrtzCchedulerState">
    <id column="SCHED_NAME" jdbcType="VARCHAR" property="schedName" />
    <id column="INSTANCE_NAME" jdbcType="VARCHAR" property="instanceName" />
    <result column="LAST_CHECKIN_TIME" jdbcType="BIGINT" property="lastCheckinTime" />
    <result column="CHECKIN_INTERVAL" jdbcType="BIGINT" property="checkinInterval" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    SCHED_NAME, INSTANCE_NAME, LAST_CHECKIN_TIME, CHECKIN_INTERVAL
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.common.QrtzCchedulerStateExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from qrtz_scheduler_state
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="com.ql.rent.entity.common.QrtzCchedulerStateKey" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from qrtz_scheduler_state
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and INSTANCE_NAME = #{instanceName,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.ql.rent.entity.common.QrtzCchedulerStateKey">
    delete from qrtz_scheduler_state
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and INSTANCE_NAME = #{instanceName,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.common.QrtzCchedulerStateExample">
    delete from qrtz_scheduler_state
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.common.QrtzCchedulerState">
    insert into qrtz_scheduler_state (SCHED_NAME, INSTANCE_NAME, LAST_CHECKIN_TIME, 
      CHECKIN_INTERVAL)
    values (#{schedName,jdbcType=VARCHAR}, #{instanceName,jdbcType=VARCHAR}, #{lastCheckinTime,jdbcType=BIGINT}, 
      #{checkinInterval,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.common.QrtzCchedulerState">
    insert into qrtz_scheduler_state
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="schedName != null">
        SCHED_NAME,
      </if>
      <if test="instanceName != null">
        INSTANCE_NAME,
      </if>
      <if test="lastCheckinTime != null">
        LAST_CHECKIN_TIME,
      </if>
      <if test="checkinInterval != null">
        CHECKIN_INTERVAL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="schedName != null">
        #{schedName,jdbcType=VARCHAR},
      </if>
      <if test="instanceName != null">
        #{instanceName,jdbcType=VARCHAR},
      </if>
      <if test="lastCheckinTime != null">
        #{lastCheckinTime,jdbcType=BIGINT},
      </if>
      <if test="checkinInterval != null">
        #{checkinInterval,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.common.QrtzCchedulerStateExample" resultType="java.lang.Long">
    select count(*) from qrtz_scheduler_state
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update qrtz_scheduler_state
    <set>
      <if test="record.schedName != null">
        SCHED_NAME = #{record.schedName,jdbcType=VARCHAR},
      </if>
      <if test="record.instanceName != null">
        INSTANCE_NAME = #{record.instanceName,jdbcType=VARCHAR},
      </if>
      <if test="record.lastCheckinTime != null">
        LAST_CHECKIN_TIME = #{record.lastCheckinTime,jdbcType=BIGINT},
      </if>
      <if test="record.checkinInterval != null">
        CHECKIN_INTERVAL = #{record.checkinInterval,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update qrtz_scheduler_state
    set SCHED_NAME = #{record.schedName,jdbcType=VARCHAR},
      INSTANCE_NAME = #{record.instanceName,jdbcType=VARCHAR},
      LAST_CHECKIN_TIME = #{record.lastCheckinTime,jdbcType=BIGINT},
      CHECKIN_INTERVAL = #{record.checkinInterval,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.QrtzCchedulerState">
    update qrtz_scheduler_state
    <set>
      <if test="lastCheckinTime != null">
        LAST_CHECKIN_TIME = #{lastCheckinTime,jdbcType=BIGINT},
      </if>
      <if test="checkinInterval != null">
        CHECKIN_INTERVAL = #{checkinInterval,jdbcType=BIGINT},
      </if>
    </set>
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and INSTANCE_NAME = #{instanceName,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.common.QrtzCchedulerState">
    update qrtz_scheduler_state
    set LAST_CHECKIN_TIME = #{lastCheckinTime,jdbcType=BIGINT},
      CHECKIN_INTERVAL = #{checkinInterval,jdbcType=BIGINT}
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and INSTANCE_NAME = #{instanceName,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into qrtz_scheduler_state
    (SCHED_NAME, INSTANCE_NAME, LAST_CHECKIN_TIME, CHECKIN_INTERVAL)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.schedName,jdbcType=VARCHAR}, #{item.instanceName,jdbcType=VARCHAR}, #{item.lastCheckinTime,jdbcType=BIGINT}, 
        #{item.checkinInterval,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into qrtz_scheduler_state (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'SCHED_NAME'.toString() == column.value">
          #{item.schedName,jdbcType=VARCHAR}
        </if>
        <if test="'INSTANCE_NAME'.toString() == column.value">
          #{item.instanceName,jdbcType=VARCHAR}
        </if>
        <if test="'LAST_CHECKIN_TIME'.toString() == column.value">
          #{item.lastCheckinTime,jdbcType=BIGINT}
        </if>
        <if test="'CHECKIN_INTERVAL'.toString() == column.value">
          #{item.checkinInterval,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.PushMsgMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.PushMsg">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="msg" jdbcType="VARCHAR" property="msg" />
    <result column="readed" jdbcType="TINYINT" property="readed" />
    <result column="source_type" jdbcType="INTEGER" property="sourceType" />
    <result column="source_id" jdbcType="BIGINT" property="sourceId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, title, msg, readed, source_type, source_id, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.common.PushMsgExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from push_msg
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from push_msg
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from push_msg
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.common.PushMsgExample">
    delete from push_msg
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.common.PushMsg">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into push_msg (user_id, title, msg, 
      readed, source_type, source_id, 
      create_time, op_time)
    values (#{userId,jdbcType=BIGINT}, #{title,jdbcType=VARCHAR}, #{msg,jdbcType=VARCHAR}, 
      #{readed,jdbcType=TINYINT}, #{sourceType,jdbcType=INTEGER}, #{sourceId,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.common.PushMsg">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into push_msg
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="msg != null">
        msg,
      </if>
      <if test="readed != null">
        readed,
      </if>
      <if test="sourceType != null">
        source_type,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="msg != null">
        #{msg,jdbcType=VARCHAR},
      </if>
      <if test="readed != null">
        #{readed,jdbcType=TINYINT},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=INTEGER},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.common.PushMsgExample" resultType="java.lang.Long">
    select count(*) from push_msg
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update push_msg
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.msg != null">
        msg = #{record.msg,jdbcType=VARCHAR},
      </if>
      <if test="record.readed != null">
        readed = #{record.readed,jdbcType=TINYINT},
      </if>
      <if test="record.sourceType != null">
        source_type = #{record.sourceType,jdbcType=INTEGER},
      </if>
      <if test="record.sourceId != null">
        source_id = #{record.sourceId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update push_msg
    set id = #{record.id,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      title = #{record.title,jdbcType=VARCHAR},
      msg = #{record.msg,jdbcType=VARCHAR},
      readed = #{record.readed,jdbcType=TINYINT},
      source_type = #{record.sourceType,jdbcType=INTEGER},
      source_id = #{record.sourceId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.PushMsg">
    update push_msg
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="msg != null">
        msg = #{msg,jdbcType=VARCHAR},
      </if>
      <if test="readed != null">
        readed = #{readed,jdbcType=TINYINT},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType,jdbcType=INTEGER},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.common.PushMsg">
    update push_msg
    set user_id = #{userId,jdbcType=BIGINT},
      title = #{title,jdbcType=VARCHAR},
      msg = #{msg,jdbcType=VARCHAR},
      readed = #{readed,jdbcType=TINYINT},
      source_type = #{sourceType,jdbcType=INTEGER},
      source_id = #{sourceId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into push_msg
    (user_id, title, msg, readed, source_type, source_id, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.userId,jdbcType=BIGINT}, #{item.title,jdbcType=VARCHAR}, #{item.msg,jdbcType=VARCHAR}, 
        #{item.readed,jdbcType=TINYINT}, #{item.sourceType,jdbcType=INTEGER}, #{item.sourceId,jdbcType=BIGINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into push_msg (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=BIGINT}
        </if>
        <if test="'title'.toString() == column.value">
          #{item.title,jdbcType=VARCHAR}
        </if>
        <if test="'msg'.toString() == column.value">
          #{item.msg,jdbcType=VARCHAR}
        </if>
        <if test="'readed'.toString() == column.value">
          #{item.readed,jdbcType=TINYINT}
        </if>
        <if test="'source_type'.toString() == column.value">
          #{item.sourceType,jdbcType=INTEGER}
        </if>
        <if test="'source_id'.toString() == column.value">
          #{item.sourceId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
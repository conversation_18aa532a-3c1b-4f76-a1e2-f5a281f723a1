<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.CalendarHolidayMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.CalendarHoliday">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="year" jdbcType="INTEGER" property="year" />
    <result column="holiday_date_start" jdbcType="TIMESTAMP" property="holidayDateStart" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="holiday_type" jdbcType="VARCHAR" property="holidayType" />
    <result column="holiday_date_end" jdbcType="TIMESTAMP" property="holidayDateEnd" />
    <result column="ctrip_holiday_id" jdbcType="VARCHAR" property="ctripHolidayId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, year, holiday_date_start, name, holiday_type, holiday_date_end, ctrip_holiday_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.common.CalendarHolidayExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from calendar_holiday
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from calendar_holiday
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from calendar_holiday
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.common.CalendarHolidayExample">
    delete from calendar_holiday
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.common.CalendarHoliday">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into calendar_holiday (year, holiday_date_start, name, 
      holiday_type, holiday_date_end, ctrip_holiday_id
      )
    values (#{year,jdbcType=INTEGER}, #{holidayDateStart,jdbcType=TIMESTAMP}, #{name,jdbcType=VARCHAR}, 
      #{holidayType,jdbcType=VARCHAR}, #{holidayDateEnd,jdbcType=TIMESTAMP}, #{ctripHolidayId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.common.CalendarHoliday">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into calendar_holiday
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="year != null">
        year,
      </if>
      <if test="holidayDateStart != null">
        holiday_date_start,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="holidayType != null">
        holiday_type,
      </if>
      <if test="holidayDateEnd != null">
        holiday_date_end,
      </if>
      <if test="ctripHolidayId != null">
        ctrip_holiday_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="year != null">
        #{year,jdbcType=INTEGER},
      </if>
      <if test="holidayDateStart != null">
        #{holidayDateStart,jdbcType=TIMESTAMP},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="holidayType != null">
        #{holidayType,jdbcType=VARCHAR},
      </if>
      <if test="holidayDateEnd != null">
        #{holidayDateEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="ctripHolidayId != null">
        #{ctripHolidayId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.common.CalendarHolidayExample" resultType="java.lang.Long">
    select count(*) from calendar_holiday
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update calendar_holiday
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.year != null">
        year = #{record.year,jdbcType=INTEGER},
      </if>
      <if test="record.holidayDateStart != null">
        holiday_date_start = #{record.holidayDateStart,jdbcType=TIMESTAMP},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.holidayType != null">
        holiday_type = #{record.holidayType,jdbcType=VARCHAR},
      </if>
      <if test="record.holidayDateEnd != null">
        holiday_date_end = #{record.holidayDateEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ctripHolidayId != null">
        ctrip_holiday_id = #{record.ctripHolidayId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update calendar_holiday
    set id = #{record.id,jdbcType=BIGINT},
      year = #{record.year,jdbcType=INTEGER},
      holiday_date_start = #{record.holidayDateStart,jdbcType=TIMESTAMP},
      name = #{record.name,jdbcType=VARCHAR},
      holiday_type = #{record.holidayType,jdbcType=VARCHAR},
      holiday_date_end = #{record.holidayDateEnd,jdbcType=TIMESTAMP},
      ctrip_holiday_id = #{record.ctripHolidayId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.CalendarHoliday">
    update calendar_holiday
    <set>
      <if test="year != null">
        year = #{year,jdbcType=INTEGER},
      </if>
      <if test="holidayDateStart != null">
        holiday_date_start = #{holidayDateStart,jdbcType=TIMESTAMP},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="holidayType != null">
        holiday_type = #{holidayType,jdbcType=VARCHAR},
      </if>
      <if test="holidayDateEnd != null">
        holiday_date_end = #{holidayDateEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="ctripHolidayId != null">
        ctrip_holiday_id = #{ctripHolidayId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.common.CalendarHoliday">
    update calendar_holiday
    set year = #{year,jdbcType=INTEGER},
      holiday_date_start = #{holidayDateStart,jdbcType=TIMESTAMP},
      name = #{name,jdbcType=VARCHAR},
      holiday_type = #{holidayType,jdbcType=VARCHAR},
      holiday_date_end = #{holidayDateEnd,jdbcType=TIMESTAMP},
      ctrip_holiday_id = #{ctripHolidayId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into calendar_holiday
    (year, holiday_date_start, name, holiday_type, holiday_date_end, ctrip_holiday_id
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.year,jdbcType=INTEGER}, #{item.holidayDateStart,jdbcType=TIMESTAMP}, #{item.name,jdbcType=VARCHAR}, 
        #{item.holidayType,jdbcType=VARCHAR}, #{item.holidayDateEnd,jdbcType=TIMESTAMP}, 
        #{item.ctripHolidayId,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into calendar_holiday (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'year'.toString() == column.value">
          #{item.year,jdbcType=INTEGER}
        </if>
        <if test="'holiday_date_start'.toString() == column.value">
          #{item.holidayDateStart,jdbcType=TIMESTAMP}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'holiday_type'.toString() == column.value">
          #{item.holidayType,jdbcType=VARCHAR}
        </if>
        <if test="'holiday_date_end'.toString() == column.value">
          #{item.holidayDateEnd,jdbcType=TIMESTAMP}
        </if>
        <if test="'ctrip_holiday_id'.toString() == column.value">
          #{item.ctripHolidayId,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
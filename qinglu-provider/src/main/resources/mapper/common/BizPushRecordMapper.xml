<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.BizPushRecordMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.BizPushRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="saas_trace_id" jdbcType="VARCHAR" property="saasTraceId" />
    <result column="ctrip_req_id" jdbcType="VARCHAR" property="ctripReqId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="biz_id" jdbcType="BIGINT" property="bizId" />
    <result column="biz_method" jdbcType="VARCHAR" property="bizMethod" />
    <result column="rep_result" jdbcType="VARCHAR" property="repResult" />
    <result column="response_time" jdbcType="BIGINT" property="responseTime" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="message" jdbcType="VARCHAR" property="message" />
    <result column="result" jdbcType="VARCHAR" property="result" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.ql.rent.entity.common.BizPushRecord">
    <result column="saas_param" jdbcType="LONGVARCHAR" property="saasParam" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, channel_id, saas_trace_id, ctrip_req_id, store_id, biz_id, biz_method, 
    rep_result, response_time, user_id, code, message, result
  </sql>
  <sql id="Blob_Column_List">
    saas_param
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.ql.rent.entity.common.BizPushRecordExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from biz_push_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.ql.rent.entity.common.BizPushRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from biz_push_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from biz_push_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from biz_push_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.common.BizPushRecordExample">
    delete from biz_push_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.common.BizPushRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into biz_push_record (merchant_id, channel_id, saas_trace_id, 
      ctrip_req_id, store_id, biz_id, 
      biz_method, rep_result, response_time, 
      user_id, code, message, 
      result, saas_param)
    values (#{merchantId,jdbcType=BIGINT}, #{channelId,jdbcType=BIGINT}, #{saasTraceId,jdbcType=VARCHAR}, 
      #{ctripReqId,jdbcType=VARCHAR}, #{storeId,jdbcType=BIGINT}, #{bizId,jdbcType=BIGINT}, 
      #{bizMethod,jdbcType=VARCHAR}, #{repResult,jdbcType=VARCHAR}, #{responseTime,jdbcType=BIGINT}, 
      #{userId,jdbcType=BIGINT}, #{code,jdbcType=VARCHAR}, #{message,jdbcType=VARCHAR}, 
      #{result,jdbcType=VARCHAR}, #{saasParam,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.common.BizPushRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into biz_push_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="saasTraceId != null">
        saas_trace_id,
      </if>
      <if test="ctripReqId != null">
        ctrip_req_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="bizMethod != null">
        biz_method,
      </if>
      <if test="repResult != null">
        rep_result,
      </if>
      <if test="responseTime != null">
        response_time,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="message != null">
        message,
      </if>
      <if test="result != null">
        result,
      </if>
      <if test="saasParam != null">
        saas_param,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="saasTraceId != null">
        #{saasTraceId,jdbcType=VARCHAR},
      </if>
      <if test="ctripReqId != null">
        #{ctripReqId,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizMethod != null">
        #{bizMethod,jdbcType=VARCHAR},
      </if>
      <if test="repResult != null">
        #{repResult,jdbcType=VARCHAR},
      </if>
      <if test="responseTime != null">
        #{responseTime,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        #{message,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="saasParam != null">
        #{saasParam,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.common.BizPushRecordExample" resultType="java.lang.Long">
    select count(*) from biz_push_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update biz_push_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=BIGINT},
      </if>
      <if test="record.saasTraceId != null">
        saas_trace_id = #{record.saasTraceId,jdbcType=VARCHAR},
      </if>
      <if test="record.ctripReqId != null">
        ctrip_req_id = #{record.ctripReqId,jdbcType=VARCHAR},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=BIGINT},
      </if>
      <if test="record.bizMethod != null">
        biz_method = #{record.bizMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.repResult != null">
        rep_result = #{record.repResult,jdbcType=VARCHAR},
      </if>
      <if test="record.responseTime != null">
        response_time = #{record.responseTime,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.message != null">
        message = #{record.message,jdbcType=VARCHAR},
      </if>
      <if test="record.result != null">
        result = #{record.result,jdbcType=VARCHAR},
      </if>
      <if test="record.saasParam != null">
        saas_param = #{record.saasParam,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update biz_push_record
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      channel_id = #{record.channelId,jdbcType=BIGINT},
      saas_trace_id = #{record.saasTraceId,jdbcType=VARCHAR},
      ctrip_req_id = #{record.ctripReqId,jdbcType=VARCHAR},
      store_id = #{record.storeId,jdbcType=BIGINT},
      biz_id = #{record.bizId,jdbcType=BIGINT},
      biz_method = #{record.bizMethod,jdbcType=VARCHAR},
      rep_result = #{record.repResult,jdbcType=VARCHAR},
      response_time = #{record.responseTime,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      code = #{record.code,jdbcType=VARCHAR},
      message = #{record.message,jdbcType=VARCHAR},
      result = #{record.result,jdbcType=VARCHAR},
      saas_param = #{record.saasParam,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update biz_push_record
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      channel_id = #{record.channelId,jdbcType=BIGINT},
      saas_trace_id = #{record.saasTraceId,jdbcType=VARCHAR},
      ctrip_req_id = #{record.ctripReqId,jdbcType=VARCHAR},
      store_id = #{record.storeId,jdbcType=BIGINT},
      biz_id = #{record.bizId,jdbcType=BIGINT},
      biz_method = #{record.bizMethod,jdbcType=VARCHAR},
      rep_result = #{record.repResult,jdbcType=VARCHAR},
      response_time = #{record.responseTime,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      code = #{record.code,jdbcType=VARCHAR},
      message = #{record.message,jdbcType=VARCHAR},
      result = #{record.result,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.BizPushRecord">
    update biz_push_record
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="saasTraceId != null">
        saas_trace_id = #{saasTraceId,jdbcType=VARCHAR},
      </if>
      <if test="ctripReqId != null">
        ctrip_req_id = #{ctripReqId,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizMethod != null">
        biz_method = #{bizMethod,jdbcType=VARCHAR},
      </if>
      <if test="repResult != null">
        rep_result = #{repResult,jdbcType=VARCHAR},
      </if>
      <if test="responseTime != null">
        response_time = #{responseTime,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        message = #{message,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        result = #{result,jdbcType=VARCHAR},
      </if>
      <if test="saasParam != null">
        saas_param = #{saasParam,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.ql.rent.entity.common.BizPushRecord">
    update biz_push_record
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      channel_id = #{channelId,jdbcType=BIGINT},
      saas_trace_id = #{saasTraceId,jdbcType=VARCHAR},
      ctrip_req_id = #{ctripReqId,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=BIGINT},
      biz_id = #{bizId,jdbcType=BIGINT},
      biz_method = #{bizMethod,jdbcType=VARCHAR},
      rep_result = #{repResult,jdbcType=VARCHAR},
      response_time = #{responseTime,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      code = #{code,jdbcType=VARCHAR},
      message = #{message,jdbcType=VARCHAR},
      result = #{result,jdbcType=VARCHAR},
      saas_param = #{saasParam,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.common.BizPushRecord">
    update biz_push_record
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      channel_id = #{channelId,jdbcType=BIGINT},
      saas_trace_id = #{saasTraceId,jdbcType=VARCHAR},
      ctrip_req_id = #{ctripReqId,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=BIGINT},
      biz_id = #{bizId,jdbcType=BIGINT},
      biz_method = #{bizMethod,jdbcType=VARCHAR},
      rep_result = #{repResult,jdbcType=VARCHAR},
      response_time = #{responseTime,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      code = #{code,jdbcType=VARCHAR},
      message = #{message,jdbcType=VARCHAR},
      result = #{result,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into biz_push_record
    (merchant_id, channel_id, saas_trace_id, ctrip_req_id, store_id, biz_id, biz_method, 
      rep_result, response_time, user_id, code, message, result, saas_param)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.channelId,jdbcType=BIGINT}, #{item.saasTraceId,jdbcType=VARCHAR}, 
        #{item.ctripReqId,jdbcType=VARCHAR}, #{item.storeId,jdbcType=BIGINT}, #{item.bizId,jdbcType=BIGINT}, 
        #{item.bizMethod,jdbcType=VARCHAR}, #{item.repResult,jdbcType=VARCHAR}, #{item.responseTime,jdbcType=BIGINT}, 
        #{item.userId,jdbcType=BIGINT}, #{item.code,jdbcType=VARCHAR}, #{item.message,jdbcType=VARCHAR}, 
        #{item.result,jdbcType=VARCHAR}, #{item.saasParam,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into biz_push_record (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'channel_id'.toString() == column.value">
          #{item.channelId,jdbcType=BIGINT}
        </if>
        <if test="'saas_trace_id'.toString() == column.value">
          #{item.saasTraceId,jdbcType=VARCHAR}
        </if>
        <if test="'ctrip_req_id'.toString() == column.value">
          #{item.ctripReqId,jdbcType=VARCHAR}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'biz_id'.toString() == column.value">
          #{item.bizId,jdbcType=BIGINT}
        </if>
        <if test="'biz_method'.toString() == column.value">
          #{item.bizMethod,jdbcType=VARCHAR}
        </if>
        <if test="'rep_result'.toString() == column.value">
          #{item.repResult,jdbcType=VARCHAR}
        </if>
        <if test="'response_time'.toString() == column.value">
          #{item.responseTime,jdbcType=BIGINT}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=BIGINT}
        </if>
        <if test="'code'.toString() == column.value">
          #{item.code,jdbcType=VARCHAR}
        </if>
        <if test="'message'.toString() == column.value">
          #{item.message,jdbcType=VARCHAR}
        </if>
        <if test="'result'.toString() == column.value">
          #{item.result,jdbcType=VARCHAR}
        </if>
        <if test="'saas_param'.toString() == column.value">
          #{item.saasParam,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
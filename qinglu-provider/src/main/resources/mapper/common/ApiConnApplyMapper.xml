<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.ApiConnApplyMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.ApiConnApply">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="detail_message" jdbcType="VARCHAR" property="detailMessage" />
    <result column="display_message" jdbcType="VARCHAR" property="displayMessage" />
    <result column="step" jdbcType="INTEGER" property="step" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, channel_id, merchant_id, detail_message, display_message, step, status, last_ver, 
    deleted, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.common.ApiConnApplyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from api_conn_apply
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from api_conn_apply
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.common.ApiConnApply">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into api_conn_apply (channel_id, merchant_id, detail_message, 
      display_message, step, status, 
      last_ver, deleted, create_time, 
      op_time)
    values (#{channelId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, #{detailMessage,jdbcType=VARCHAR}, 
      #{displayMessage,jdbcType=VARCHAR}, #{step,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{lastVer,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.common.ApiConnApply">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into api_conn_apply
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="detailMessage != null">
        detail_message,
      </if>
      <if test="displayMessage != null">
        display_message,
      </if>
      <if test="step != null">
        step,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="detailMessage != null">
        #{detailMessage,jdbcType=VARCHAR},
      </if>
      <if test="displayMessage != null">
        #{displayMessage,jdbcType=VARCHAR},
      </if>
      <if test="step != null">
        #{step,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.common.ApiConnApplyExample" resultType="java.lang.Long">
    select count(*) from api_conn_apply
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update api_conn_apply
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.detailMessage != null">
        detail_message = #{record.detailMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.displayMessage != null">
        display_message = #{record.displayMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.step != null">
        step = #{record.step,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update api_conn_apply
    set id = #{record.id,jdbcType=BIGINT},
      channel_id = #{record.channelId,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      detail_message = #{record.detailMessage,jdbcType=VARCHAR},
      display_message = #{record.displayMessage,jdbcType=VARCHAR},
      step = #{record.step,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.ApiConnApply">
    update api_conn_apply
    <set>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="detailMessage != null">
        detail_message = #{detailMessage,jdbcType=VARCHAR},
      </if>
      <if test="displayMessage != null">
        display_message = #{displayMessage,jdbcType=VARCHAR},
      </if>
      <if test="step != null">
        step = #{step,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.common.ApiConnApply">
    update api_conn_apply
    set channel_id = #{channelId,jdbcType=BIGINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      detail_message = #{detailMessage,jdbcType=VARCHAR},
      display_message = #{displayMessage,jdbcType=VARCHAR},
      step = #{step,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      last_ver = #{lastVer,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into api_conn_apply
    (channel_id, merchant_id, detail_message, display_message, step, status, last_ver, 
      deleted, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.channelId,jdbcType=BIGINT}, #{item.merchantId,jdbcType=BIGINT}, #{item.detailMessage,jdbcType=VARCHAR}, 
        #{item.displayMessage,jdbcType=VARCHAR}, #{item.step,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER}, 
        #{item.lastVer,jdbcType=INTEGER}, #{item.deleted,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into api_conn_apply (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'channel_id'.toString() == column.value">
          #{item.channelId,jdbcType=BIGINT}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'detail_message'.toString() == column.value">
          #{item.detailMessage,jdbcType=VARCHAR}
        </if>
        <if test="'display_message'.toString() == column.value">
          #{item.displayMessage,jdbcType=VARCHAR}
        </if>
        <if test="'step'.toString() == column.value">
          #{item.step,jdbcType=INTEGER}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.CtripHourlyMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.CtripHourly">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="start_hour" jdbcType="INTEGER" property="startHour" />
    <result column="end_hour" jdbcType="INTEGER" property="endHour" />
    <result column="scene" jdbcType="TINYINT" property="scene" />
    <result column="charge_value" jdbcType="INTEGER" property="chargeValue" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, start_hour, end_hour, scene, charge_value, deleted, last_ver, create_time, op_time, 
    op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.common.CtripHourlyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from ctrip_hourly
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ctrip_hourly
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.common.CtripHourly">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ctrip_hourly (start_hour, end_hour, scene, 
      charge_value, deleted, last_ver, 
      create_time, op_time, op_user_id
      )
    values (#{startHour,jdbcType=INTEGER}, #{endHour,jdbcType=INTEGER}, #{scene,jdbcType=TINYINT}, 
      #{chargeValue,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER}, 
      #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.common.CtripHourly">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ctrip_hourly
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="startHour != null">
        start_hour,
      </if>
      <if test="endHour != null">
        end_hour,
      </if>
      <if test="scene != null">
        scene,
      </if>
      <if test="chargeValue != null">
        charge_value,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="startHour != null">
        #{startHour,jdbcType=INTEGER},
      </if>
      <if test="endHour != null">
        #{endHour,jdbcType=INTEGER},
      </if>
      <if test="scene != null">
        #{scene,jdbcType=TINYINT},
      </if>
      <if test="chargeValue != null">
        #{chargeValue,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.common.CtripHourlyExample" resultType="java.lang.Long">
    select count(*) from ctrip_hourly
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ctrip_hourly
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.startHour != null">
        start_hour = #{record.startHour,jdbcType=INTEGER},
      </if>
      <if test="record.endHour != null">
        end_hour = #{record.endHour,jdbcType=INTEGER},
      </if>
      <if test="record.scene != null">
        scene = #{record.scene,jdbcType=TINYINT},
      </if>
      <if test="record.chargeValue != null">
        charge_value = #{record.chargeValue,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ctrip_hourly
    set id = #{record.id,jdbcType=BIGINT},
      start_hour = #{record.startHour,jdbcType=INTEGER},
      end_hour = #{record.endHour,jdbcType=INTEGER},
      scene = #{record.scene,jdbcType=TINYINT},
      charge_value = #{record.chargeValue,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.CtripHourly">
    update ctrip_hourly
    <set>
      <if test="startHour != null">
        start_hour = #{startHour,jdbcType=INTEGER},
      </if>
      <if test="endHour != null">
        end_hour = #{endHour,jdbcType=INTEGER},
      </if>
      <if test="scene != null">
        scene = #{scene,jdbcType=TINYINT},
      </if>
      <if test="chargeValue != null">
        charge_value = #{chargeValue,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.common.CtripHourly">
    update ctrip_hourly
    set start_hour = #{startHour,jdbcType=INTEGER},
      end_hour = #{endHour,jdbcType=INTEGER},
      scene = #{scene,jdbcType=TINYINT},
      charge_value = #{chargeValue,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into ctrip_hourly
    (start_hour, end_hour, scene, charge_value, deleted, last_ver, create_time, op_time, 
      op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.startHour,jdbcType=INTEGER}, #{item.endHour,jdbcType=INTEGER}, #{item.scene,jdbcType=TINYINT}, 
        #{item.chargeValue,jdbcType=INTEGER}, #{item.deleted,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into ctrip_hourly (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'start_hour'.toString() == column.value">
          #{item.startHour,jdbcType=INTEGER}
        </if>
        <if test="'end_hour'.toString() == column.value">
          #{item.endHour,jdbcType=INTEGER}
        </if>
        <if test="'scene'.toString() == column.value">
          #{item.scene,jdbcType=TINYINT}
        </if>
        <if test="'charge_value'.toString() == column.value">
          #{item.chargeValue,jdbcType=INTEGER}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
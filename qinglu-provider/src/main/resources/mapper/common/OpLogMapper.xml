<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.OpLogMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.OpLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="module" jdbcType="VARCHAR" property="module" />
    <result column="operation_type" jdbcType="VARCHAR" property="operationType" />
    <result column="level1" jdbcType="VARCHAR" property="level1" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="vehicle_model_id" jdbcType="VARCHAR" property="vehicleModelId" />
    <result column="channel_id" jdbcType="VARCHAR" property="channelId" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="key_id" jdbcType="VARCHAR" property="keyId" />
    <result column="trace" jdbcType="VARCHAR" property="trace" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="op_user_name" jdbcType="VARCHAR" property="opUserName" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.ql.rent.entity.common.OpLogWithBLOBs">
    <result column="different" jdbcType="LONGVARCHAR" property="different" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, module, operation_type, level1, merchant_id, store_id, vehicle_model_id, channel_id, 
    source, key_id, trace, create_time, op_user_id, op_user_name
  </sql>
  <sql id="Blob_Column_List">
    different
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.ql.rent.entity.common.OpLogExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from op_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.ql.rent.entity.common.OpLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from op_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from op_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from op_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.common.OpLogExample">
    delete from op_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.common.OpLogWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into op_log (module, operation_type, level1, 
      merchant_id, store_id, vehicle_model_id, 
      channel_id, source, key_id, 
      trace, create_time, op_user_id, 
      op_user_name, different)
    values (#{module,jdbcType=VARCHAR}, #{operationType,jdbcType=VARCHAR}, #{level1,jdbcType=VARCHAR}, 
      #{merchantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{vehicleModelId,jdbcType=VARCHAR}, 
      #{channelId,jdbcType=VARCHAR}, #{source,jdbcType=TINYINT}, #{keyId,jdbcType=VARCHAR}, 
      #{trace,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}, 
      #{opUserName,jdbcType=VARCHAR}, #{different,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.common.OpLogWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into op_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="module != null">
        module,
      </if>
      <if test="operationType != null">
        operation_type,
      </if>
      <if test="level1 != null">
        level1,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="keyId != null">
        key_id,
      </if>
      <if test="trace != null">
        trace,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="opUserName != null">
        op_user_name,
      </if>
      <if test="different != null">
        different,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="module != null">
        #{module,jdbcType=VARCHAR},
      </if>
      <if test="operationType != null">
        #{operationType,jdbcType=VARCHAR},
      </if>
      <if test="level1 != null">
        #{level1,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="keyId != null">
        #{keyId,jdbcType=VARCHAR},
      </if>
      <if test="trace != null">
        #{trace,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="opUserName != null">
        #{opUserName,jdbcType=VARCHAR},
      </if>
      <if test="different != null">
        #{different,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.common.OpLogExample" resultType="java.lang.Long">
    select count(*) from op_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update op_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.module != null">
        module = #{record.module,jdbcType=VARCHAR},
      </if>
      <if test="record.operationType != null">
        operation_type = #{record.operationType,jdbcType=VARCHAR},
      </if>
      <if test="record.level1 != null">
        level1 = #{record.level1,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=VARCHAR},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=TINYINT},
      </if>
      <if test="record.keyId != null">
        key_id = #{record.keyId,jdbcType=VARCHAR},
      </if>
      <if test="record.trace != null">
        trace = #{record.trace,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.opUserName != null">
        op_user_name = #{record.opUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.different != null">
        different = #{record.different,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update op_log
    set id = #{record.id,jdbcType=BIGINT},
      module = #{record.module,jdbcType=VARCHAR},
      operation_type = #{record.operationType,jdbcType=VARCHAR},
      level1 = #{record.level1,jdbcType=VARCHAR},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=VARCHAR},
      channel_id = #{record.channelId,jdbcType=VARCHAR},
      source = #{record.source,jdbcType=TINYINT},
      key_id = #{record.keyId,jdbcType=VARCHAR},
      trace = #{record.trace,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      op_user_name = #{record.opUserName,jdbcType=VARCHAR},
      different = #{record.different,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update op_log
    set id = #{record.id,jdbcType=BIGINT},
      module = #{record.module,jdbcType=VARCHAR},
      operation_type = #{record.operationType,jdbcType=VARCHAR},
      level1 = #{record.level1,jdbcType=VARCHAR},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=VARCHAR},
      channel_id = #{record.channelId,jdbcType=VARCHAR},
      source = #{record.source,jdbcType=TINYINT},
      key_id = #{record.keyId,jdbcType=VARCHAR},
      trace = #{record.trace,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      op_user_name = #{record.opUserName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.OpLogWithBLOBs">
    update op_log
    <set>
      <if test="module != null">
        module = #{module,jdbcType=VARCHAR},
      </if>
      <if test="operationType != null">
        operation_type = #{operationType,jdbcType=VARCHAR},
      </if>
      <if test="level1 != null">
        level1 = #{level1,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=TINYINT},
      </if>
      <if test="keyId != null">
        key_id = #{keyId,jdbcType=VARCHAR},
      </if>
      <if test="trace != null">
        trace = #{trace,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="opUserName != null">
        op_user_name = #{opUserName,jdbcType=VARCHAR},
      </if>
      <if test="different != null">
        different = #{different,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.ql.rent.entity.common.OpLogWithBLOBs">
    update op_log
    set module = #{module,jdbcType=VARCHAR},
      operation_type = #{operationType,jdbcType=VARCHAR},
      level1 = #{level1,jdbcType=VARCHAR},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      vehicle_model_id = #{vehicleModelId,jdbcType=VARCHAR},
      channel_id = #{channelId,jdbcType=VARCHAR},
      source = #{source,jdbcType=TINYINT},
      key_id = #{keyId,jdbcType=VARCHAR},
      trace = #{trace,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      op_user_name = #{opUserName,jdbcType=VARCHAR},
      different = #{different,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.common.OpLog">
    update op_log
    set module = #{module,jdbcType=VARCHAR},
      operation_type = #{operationType,jdbcType=VARCHAR},
      level1 = #{level1,jdbcType=VARCHAR},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      vehicle_model_id = #{vehicleModelId,jdbcType=VARCHAR},
      channel_id = #{channelId,jdbcType=VARCHAR},
      source = #{source,jdbcType=TINYINT},
      key_id = #{keyId,jdbcType=VARCHAR},
      trace = #{trace,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      op_user_name = #{opUserName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into op_log
    (module, operation_type, level1, merchant_id, store_id, vehicle_model_id, channel_id, 
      source, key_id, trace, create_time, op_user_id, op_user_name, different)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.module,jdbcType=VARCHAR}, #{item.operationType,jdbcType=VARCHAR}, #{item.level1,jdbcType=VARCHAR}, 
        #{item.merchantId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.vehicleModelId,jdbcType=VARCHAR}, 
        #{item.channelId,jdbcType=VARCHAR}, #{item.source,jdbcType=TINYINT}, #{item.keyId,jdbcType=VARCHAR}, 
        #{item.trace,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT}, 
        #{item.opUserName,jdbcType=VARCHAR}, #{item.different,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into op_log (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'module'.toString() == column.value">
          #{item.module,jdbcType=VARCHAR}
        </if>
        <if test="'operation_type'.toString() == column.value">
          #{item.operationType,jdbcType=VARCHAR}
        </if>
        <if test="'level1'.toString() == column.value">
          #{item.level1,jdbcType=VARCHAR}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=VARCHAR}
        </if>
        <if test="'channel_id'.toString() == column.value">
          #{item.channelId,jdbcType=VARCHAR}
        </if>
        <if test="'source'.toString() == column.value">
          #{item.source,jdbcType=TINYINT}
        </if>
        <if test="'key_id'.toString() == column.value">
          #{item.keyId,jdbcType=VARCHAR}
        </if>
        <if test="'trace'.toString() == column.value">
          #{item.trace,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'op_user_name'.toString() == column.value">
          #{item.opUserName,jdbcType=VARCHAR}
        </if>
        <if test="'different'.toString() == column.value">
          #{item.different,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>


</mapper>
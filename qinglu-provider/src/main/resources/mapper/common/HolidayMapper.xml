<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.HolidayMapper">
    <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.Holiday">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="contry" jdbcType="INTEGER" property="contry"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
    id, name, contry
  </sql>
    <select id="selectByExample" parameterType="com.ql.rent.entity.common.HolidayExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from holiday
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from holiday
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from holiday
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <delete id="deleteByExample" parameterType="com.ql.rent.entity.common.HolidayExample">
        delete from holiday
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.ql.rent.entity.common.Holiday">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into holiday (name, contry)
        values (#{name,jdbcType=VARCHAR}, #{contry,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" parameterType="com.ql.rent.entity.common.Holiday">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into holiday
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                name,
            </if>
            <if test="contry != null">
                contry,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="contry != null">
                #{contry,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.ql.rent.entity.common.HolidayExample" resultType="java.lang.Long">
        select count(*) from holiday
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update holiday
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.name != null">
                name = #{record.name,jdbcType=VARCHAR},
            </if>
            <if test="record.contry != null">
                contry = #{record.contry,jdbcType=INTEGER},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update holiday
        set id = #{record.id,jdbcType=BIGINT},
        name = #{record.name,jdbcType=VARCHAR},
        contry = #{record.contry,jdbcType=INTEGER}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.Holiday">
        update holiday
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="contry != null">
                contry = #{contry,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.common.Holiday">
    update holiday
    set name = #{name,jdbcType=VARCHAR},
      contry = #{contry,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        insert into holiday
        (name, contry)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.name,jdbcType=VARCHAR}, #{item.contry,jdbcType=INTEGER})
        </foreach>
    </insert>
    <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        insert into holiday (
        <foreach collection="selective" item="column" separator=",">
            ${column.escapedColumnName}
        </foreach>
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            <foreach collection="selective" item="column" separator=",">
                <if test="'name'.toString() == column.value">
                    #{item.name,jdbcType=VARCHAR}
                </if>
                <if test="'contry'.toString() == column.value">
                    #{item.contry,jdbcType=INTEGER}
                </if>
            </foreach>
            )
        </foreach>
    </insert>
</mapper>
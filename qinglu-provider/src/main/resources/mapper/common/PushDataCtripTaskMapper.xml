<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.PushDataCtripTaskMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.PushDataCtripTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="biz_method" jdbcType="VARCHAR" property="bizMethod" />
    <result column="third_type" jdbcType="BIGINT" property="thirdType" />
    <result column="push_status" jdbcType="TINYINT" property="pushStatus" />
    <result column="retry_times" jdbcType="INTEGER" property="retryTimes" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.ql.rent.entity.common.PushDataCtripTaskWithBLOBs">
    <result column="saas_param" jdbcType="LONGVARCHAR" property="saasParam" />
    <result column="push_param" jdbcType="LONGVARCHAR" property="pushParam" />
    <result column="rep_result" jdbcType="LONGVARCHAR" property="repResult" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, biz_method, third_type, push_status, retry_times, create_time, op_time
  </sql>
  <sql id="Blob_Column_List">
    saas_param, push_param, rep_result
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.ql.rent.entity.common.PushDataCtripTaskExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from push_data_ctrip_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.ql.rent.entity.common.PushDataCtripTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from push_data_ctrip_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from push_data_ctrip_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from push_data_ctrip_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.common.PushDataCtripTaskExample">
    delete from push_data_ctrip_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.common.PushDataCtripTaskWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into push_data_ctrip_task (merchant_id, biz_method, third_type, 
      push_status, retry_times, create_time, 
      op_time, saas_param, push_param, 
      rep_result)
    values (#{merchantId,jdbcType=BIGINT}, #{bizMethod,jdbcType=VARCHAR}, #{thirdType,jdbcType=BIGINT}, 
      #{pushStatus,jdbcType=TINYINT}, #{retryTimes,jdbcType=INTEGER}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{saasParam,jdbcType=LONGVARCHAR}, #{pushParam,jdbcType=LONGVARCHAR}, 
      #{repResult,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.common.PushDataCtripTaskWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into push_data_ctrip_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="bizMethod != null">
        biz_method,
      </if>
      <if test="thirdType != null">
        third_type,
      </if>
      <if test="pushStatus != null">
        push_status,
      </if>
      <if test="retryTimes != null">
        retry_times,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="saasParam != null">
        saas_param,
      </if>
      <if test="pushParam != null">
        push_param,
      </if>
      <if test="repResult != null">
        rep_result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="bizMethod != null">
        #{bizMethod,jdbcType=VARCHAR},
      </if>
      <if test="thirdType != null">
        #{thirdType,jdbcType=BIGINT},
      </if>
      <if test="pushStatus != null">
        #{pushStatus,jdbcType=TINYINT},
      </if>
      <if test="retryTimes != null">
        #{retryTimes,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="saasParam != null">
        #{saasParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="pushParam != null">
        #{pushParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="repResult != null">
        #{repResult,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.common.PushDataCtripTaskExample" resultType="java.lang.Long">
    select count(*) from push_data_ctrip_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update push_data_ctrip_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.bizMethod != null">
        biz_method = #{record.bizMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdType != null">
        third_type = #{record.thirdType,jdbcType=BIGINT},
      </if>
      <if test="record.pushStatus != null">
        push_status = #{record.pushStatus,jdbcType=TINYINT},
      </if>
      <if test="record.retryTimes != null">
        retry_times = #{record.retryTimes,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.saasParam != null">
        saas_param = #{record.saasParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.pushParam != null">
        push_param = #{record.pushParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.repResult != null">
        rep_result = #{record.repResult,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update push_data_ctrip_task
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      biz_method = #{record.bizMethod,jdbcType=VARCHAR},
      third_type = #{record.thirdType,jdbcType=BIGINT},
      push_status = #{record.pushStatus,jdbcType=TINYINT},
      retry_times = #{record.retryTimes,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      saas_param = #{record.saasParam,jdbcType=LONGVARCHAR},
      push_param = #{record.pushParam,jdbcType=LONGVARCHAR},
      rep_result = #{record.repResult,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update push_data_ctrip_task
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      biz_method = #{record.bizMethod,jdbcType=VARCHAR},
      third_type = #{record.thirdType,jdbcType=BIGINT},
      push_status = #{record.pushStatus,jdbcType=TINYINT},
      retry_times = #{record.retryTimes,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.PushDataCtripTaskWithBLOBs">
    update push_data_ctrip_task
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="bizMethod != null">
        biz_method = #{bizMethod,jdbcType=VARCHAR},
      </if>
      <if test="thirdType != null">
        third_type = #{thirdType,jdbcType=BIGINT},
      </if>
      <if test="pushStatus != null">
        push_status = #{pushStatus,jdbcType=TINYINT},
      </if>
      <if test="retryTimes != null">
        retry_times = #{retryTimes,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="saasParam != null">
        saas_param = #{saasParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="pushParam != null">
        push_param = #{pushParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="repResult != null">
        rep_result = #{repResult,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.ql.rent.entity.common.PushDataCtripTaskWithBLOBs">
    update push_data_ctrip_task
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      biz_method = #{bizMethod,jdbcType=VARCHAR},
      third_type = #{thirdType,jdbcType=BIGINT},
      push_status = #{pushStatus,jdbcType=TINYINT},
      retry_times = #{retryTimes,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      saas_param = #{saasParam,jdbcType=LONGVARCHAR},
      push_param = #{pushParam,jdbcType=LONGVARCHAR},
      rep_result = #{repResult,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.common.PushDataCtripTask">
    update push_data_ctrip_task
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      biz_method = #{bizMethod,jdbcType=VARCHAR},
      third_type = #{thirdType,jdbcType=BIGINT},
      push_status = #{pushStatus,jdbcType=TINYINT},
      retry_times = #{retryTimes,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into push_data_ctrip_task
    (merchant_id, biz_method, third_type, push_status, retry_times, create_time, op_time, 
      saas_param, push_param, rep_result)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.bizMethod,jdbcType=VARCHAR}, #{item.thirdType,jdbcType=BIGINT}, 
        #{item.pushStatus,jdbcType=TINYINT}, #{item.retryTimes,jdbcType=INTEGER}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.saasParam,jdbcType=LONGVARCHAR}, #{item.pushParam,jdbcType=LONGVARCHAR}, 
        #{item.repResult,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into push_data_ctrip_task (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'biz_method'.toString() == column.value">
          #{item.bizMethod,jdbcType=VARCHAR}
        </if>
        <if test="'third_type'.toString() == column.value">
          #{item.thirdType,jdbcType=BIGINT}
        </if>
        <if test="'push_status'.toString() == column.value">
          #{item.pushStatus,jdbcType=TINYINT}
        </if>
        <if test="'retry_times'.toString() == column.value">
          #{item.retryTimes,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'saas_param'.toString() == column.value">
          #{item.saasParam,jdbcType=LONGVARCHAR}
        </if>
        <if test="'push_param'.toString() == column.value">
          #{item.pushParam,jdbcType=LONGVARCHAR}
        </if>
        <if test="'rep_result'.toString() == column.value">
          #{item.repResult,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
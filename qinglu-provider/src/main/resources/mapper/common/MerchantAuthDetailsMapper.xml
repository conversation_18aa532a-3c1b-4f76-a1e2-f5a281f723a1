<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.MerchantAuthDetailsMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.MerchantAuthDetails">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="channel_id" jdbcType="TINYINT" property="channelId" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="payee_user_no" jdbcType="VARCHAR" property="payeeUserNo" />
    <result column="app_auth_token" jdbcType="VARCHAR" property="appAuthToken" />
    <result column="encrypt_key" jdbcType="VARCHAR" property="encryptKey" />
    <result column="free_deposit_service_no" jdbcType="VARCHAR" property="freeDepositServiceNo" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, channel_id, app_id, payee_user_no, app_auth_token, encrypt_key, 
    free_deposit_service_no, deleted, create_time, op_time, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.common.MerchantAuthDetailsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from merchant_auth_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_auth_details
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.common.MerchantAuthDetails">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into merchant_auth_details (merchant_id, channel_id, app_id, 
      payee_user_no, app_auth_token, encrypt_key, 
      free_deposit_service_no, deleted, create_time, 
      op_time, op_user_id)
    values (#{merchantId,jdbcType=BIGINT}, #{channelId,jdbcType=TINYINT}, #{appId,jdbcType=VARCHAR}, 
      #{payeeUserNo,jdbcType=VARCHAR}, #{appAuthToken,jdbcType=VARCHAR}, #{encryptKey,jdbcType=VARCHAR}, 
      #{freeDepositServiceNo,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.common.MerchantAuthDetails">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into merchant_auth_details
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="payeeUserNo != null">
        payee_user_no,
      </if>
      <if test="appAuthToken != null">
        app_auth_token,
      </if>
      <if test="encryptKey != null">
        encrypt_key,
      </if>
      <if test="freeDepositServiceNo != null">
        free_deposit_service_no,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=TINYINT},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="payeeUserNo != null">
        #{payeeUserNo,jdbcType=VARCHAR},
      </if>
      <if test="appAuthToken != null">
        #{appAuthToken,jdbcType=VARCHAR},
      </if>
      <if test="encryptKey != null">
        #{encryptKey,jdbcType=VARCHAR},
      </if>
      <if test="freeDepositServiceNo != null">
        #{freeDepositServiceNo,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.common.MerchantAuthDetailsExample" resultType="java.lang.Long">
    select count(*) from merchant_auth_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update merchant_auth_details
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=TINYINT},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeUserNo != null">
        payee_user_no = #{record.payeeUserNo,jdbcType=VARCHAR},
      </if>
      <if test="record.appAuthToken != null">
        app_auth_token = #{record.appAuthToken,jdbcType=VARCHAR},
      </if>
      <if test="record.encryptKey != null">
        encrypt_key = #{record.encryptKey,jdbcType=VARCHAR},
      </if>
      <if test="record.freeDepositServiceNo != null">
        free_deposit_service_no = #{record.freeDepositServiceNo,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update merchant_auth_details
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      channel_id = #{record.channelId,jdbcType=TINYINT},
      app_id = #{record.appId,jdbcType=VARCHAR},
      payee_user_no = #{record.payeeUserNo,jdbcType=VARCHAR},
      app_auth_token = #{record.appAuthToken,jdbcType=VARCHAR},
      encrypt_key = #{record.encryptKey,jdbcType=VARCHAR},
      free_deposit_service_no = #{record.freeDepositServiceNo,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.MerchantAuthDetails">
    update merchant_auth_details
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=TINYINT},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="payeeUserNo != null">
        payee_user_no = #{payeeUserNo,jdbcType=VARCHAR},
      </if>
      <if test="appAuthToken != null">
        app_auth_token = #{appAuthToken,jdbcType=VARCHAR},
      </if>
      <if test="encryptKey != null">
        encrypt_key = #{encryptKey,jdbcType=VARCHAR},
      </if>
      <if test="freeDepositServiceNo != null">
        free_deposit_service_no = #{freeDepositServiceNo,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.common.MerchantAuthDetails">
    update merchant_auth_details
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      channel_id = #{channelId,jdbcType=TINYINT},
      app_id = #{appId,jdbcType=VARCHAR},
      payee_user_no = #{payeeUserNo,jdbcType=VARCHAR},
      app_auth_token = #{appAuthToken,jdbcType=VARCHAR},
      encrypt_key = #{encryptKey,jdbcType=VARCHAR},
      free_deposit_service_no = #{freeDepositServiceNo,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into merchant_auth_details
    (merchant_id, channel_id, app_id, payee_user_no, app_auth_token, encrypt_key, free_deposit_service_no, 
      deleted, create_time, op_time, op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.channelId,jdbcType=TINYINT}, #{item.appId,jdbcType=VARCHAR}, 
        #{item.payeeUserNo,jdbcType=VARCHAR}, #{item.appAuthToken,jdbcType=VARCHAR}, #{item.encryptKey,jdbcType=VARCHAR}, 
        #{item.freeDepositServiceNo,jdbcType=VARCHAR}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into merchant_auth_details (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'channel_id'.toString() == column.value">
          #{item.channelId,jdbcType=TINYINT}
        </if>
        <if test="'app_id'.toString() == column.value">
          #{item.appId,jdbcType=VARCHAR}
        </if>
        <if test="'payee_user_no'.toString() == column.value">
          #{item.payeeUserNo,jdbcType=VARCHAR}
        </if>
        <if test="'app_auth_token'.toString() == column.value">
          #{item.appAuthToken,jdbcType=VARCHAR}
        </if>
        <if test="'encrypt_key'.toString() == column.value">
          #{item.encryptKey,jdbcType=VARCHAR}
        </if>
        <if test="'free_deposit_service_no'.toString() == column.value">
          #{item.freeDepositServiceNo,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.AreaMapper">
    <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.Area">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="alias_name" jdbcType="VARCHAR" property="aliasName"/>
        <result column="depth" jdbcType="TINYINT" property="depth"/>
        <result column="parent_code" jdbcType="VARCHAR" property="parentCode"/>
        <result column="inital_pinyin" jdbcType="CHAR" property="initalPinyin"/>
        <result column="short_pinyin" jdbcType="VARCHAR" property="shortPinyin"/>
        <result column="pinyin" jdbcType="VARCHAR" property="pinyin"/>
        <result column="english_name" jdbcType="VARCHAR" property="englishName"/>
        <result column="lat" jdbcType="DECIMAL" property="lat"/>
        <result column="lng" jdbcType="DECIMAL" property="lng"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id, code, name, alias_name, depth, parent_code, inital_pinyin, short_pinyin, pinyin,
        english_name, lat, lng
    </sql>
    <select id="selectByExample" parameterType="com.ql.rent.entity.common.AreaExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from area
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from area
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from area
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <delete id="deleteByExample" parameterType="com.ql.rent.entity.common.AreaExample">
        delete from area
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.ql.rent.entity.common.Area">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into area (code, name, alias_name,
        depth, parent_code, inital_pinyin,
        short_pinyin, pinyin, english_name,
        lat, lng)
        values (#{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{aliasName,jdbcType=VARCHAR},
        #{depth,jdbcType=TINYINT}, #{parentCode,jdbcType=VARCHAR}, #{initalPinyin,jdbcType=CHAR},
        #{shortPinyin,jdbcType=VARCHAR}, #{pinyin,jdbcType=VARCHAR}, #{englishName,jdbcType=VARCHAR},
        #{lat,jdbcType=DECIMAL}, #{lng,jdbcType=DECIMAL})
    </insert>
    <insert id="insertSelective" parameterType="com.ql.rent.entity.common.Area">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">
                code,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="aliasName != null">
                alias_name,
            </if>
            <if test="depth != null">
                depth,
            </if>
            <if test="parentCode != null">
                parent_code,
            </if>
            <if test="initalPinyin != null">
                inital_pinyin,
            </if>
            <if test="shortPinyin != null">
                short_pinyin,
            </if>
            <if test="pinyin != null">
                pinyin,
            </if>
            <if test="englishName != null">
                english_name,
            </if>
            <if test="lat != null">
                lat,
            </if>
            <if test="lng != null">
                lng,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="aliasName != null">
                #{aliasName,jdbcType=VARCHAR},
            </if>
            <if test="depth != null">
                #{depth,jdbcType=TINYINT},
            </if>
            <if test="parentCode != null">
                #{parentCode,jdbcType=VARCHAR},
            </if>
            <if test="initalPinyin != null">
                #{initalPinyin,jdbcType=CHAR},
            </if>
            <if test="shortPinyin != null">
                #{shortPinyin,jdbcType=VARCHAR},
            </if>
            <if test="pinyin != null">
                #{pinyin,jdbcType=VARCHAR},
            </if>
            <if test="englishName != null">
                #{englishName,jdbcType=VARCHAR},
            </if>
            <if test="lat != null">
                #{lat,jdbcType=DECIMAL},
            </if>
            <if test="lng != null">
                #{lng,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.ql.rent.entity.common.AreaExample" resultType="java.lang.Long">
        select count(*) from area
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update area
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.code != null">
                code = #{record.code,jdbcType=VARCHAR},
            </if>
            <if test="record.name != null">
                name = #{record.name,jdbcType=VARCHAR},
            </if>
            <if test="record.aliasName != null">
                alias_name = #{record.aliasName,jdbcType=VARCHAR},
            </if>
            <if test="record.depth != null">
                depth = #{record.depth,jdbcType=TINYINT},
            </if>
            <if test="record.parentCode != null">
                parent_code = #{record.parentCode,jdbcType=VARCHAR},
            </if>
            <if test="record.initalPinyin != null">
                inital_pinyin = #{record.initalPinyin,jdbcType=CHAR},
            </if>
            <if test="record.shortPinyin != null">
                short_pinyin = #{record.shortPinyin,jdbcType=VARCHAR},
            </if>
            <if test="record.pinyin != null">
                pinyin = #{record.pinyin,jdbcType=VARCHAR},
            </if>
            <if test="record.englishName != null">
                english_name = #{record.englishName,jdbcType=VARCHAR},
            </if>
            <if test="record.lat != null">
                lat = #{record.lat,jdbcType=DECIMAL},
            </if>
            <if test="record.lng != null">
                lng = #{record.lng,jdbcType=DECIMAL},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update area
        set id = #{record.id,jdbcType=BIGINT},
        code = #{record.code,jdbcType=VARCHAR},
        name = #{record.name,jdbcType=VARCHAR},
        alias_name = #{record.aliasName,jdbcType=VARCHAR},
        depth = #{record.depth,jdbcType=TINYINT},
        parent_code = #{record.parentCode,jdbcType=VARCHAR},
        inital_pinyin = #{record.initalPinyin,jdbcType=CHAR},
        short_pinyin = #{record.shortPinyin,jdbcType=VARCHAR},
        pinyin = #{record.pinyin,jdbcType=VARCHAR},
        english_name = #{record.englishName,jdbcType=VARCHAR},
        lat = #{record.lat,jdbcType=DECIMAL},
        lng = #{record.lng,jdbcType=DECIMAL}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.Area">
        update area
        <set>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="aliasName != null">
                alias_name = #{aliasName,jdbcType=VARCHAR},
            </if>
            <if test="depth != null">
                depth = #{depth,jdbcType=TINYINT},
            </if>
            <if test="parentCode != null">
                parent_code = #{parentCode,jdbcType=VARCHAR},
            </if>
            <if test="initalPinyin != null">
                inital_pinyin = #{initalPinyin,jdbcType=CHAR},
            </if>
            <if test="shortPinyin != null">
                short_pinyin = #{shortPinyin,jdbcType=VARCHAR},
            </if>
            <if test="pinyin != null">
                pinyin = #{pinyin,jdbcType=VARCHAR},
            </if>
            <if test="englishName != null">
                english_name = #{englishName,jdbcType=VARCHAR},
            </if>
            <if test="lat != null">
                lat = #{lat,jdbcType=DECIMAL},
            </if>
            <if test="lng != null">
                lng = #{lng,jdbcType=DECIMAL},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.common.Area">
    update area
    set code = #{code,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      alias_name = #{aliasName,jdbcType=VARCHAR},
      depth = #{depth,jdbcType=TINYINT},
      parent_code = #{parentCode,jdbcType=VARCHAR},
      inital_pinyin = #{initalPinyin,jdbcType=CHAR},
      short_pinyin = #{shortPinyin,jdbcType=VARCHAR},
      pinyin = #{pinyin,jdbcType=VARCHAR},
      english_name = #{englishName,jdbcType=VARCHAR},
      lat = #{lat,jdbcType=DECIMAL},
      lng = #{lng,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        insert into area
        (code, name, alias_name, depth, parent_code, inital_pinyin, short_pinyin, pinyin,
        english_name, lat, lng)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.code,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.aliasName,jdbcType=VARCHAR},
            #{item.depth,jdbcType=TINYINT}, #{item.parentCode,jdbcType=VARCHAR}, #{item.initalPinyin,jdbcType=CHAR},
            #{item.shortPinyin,jdbcType=VARCHAR}, #{item.pinyin,jdbcType=VARCHAR}, #{item.englishName,jdbcType=VARCHAR},
            #{item.lat,jdbcType=DECIMAL}, #{item.lng,jdbcType=DECIMAL})
        </foreach>
    </insert>
    <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        insert into area (
        <foreach collection="selective" item="column" separator=",">
            ${column.escapedColumnName}
        </foreach>
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            <foreach collection="selective" item="column" separator=",">
                <if test="'code'.toString() == column.value">
                    #{item.code,jdbcType=VARCHAR}
                </if>
                <if test="'name'.toString() == column.value">
                    #{item.name,jdbcType=VARCHAR}
                </if>
                <if test="'alias_name'.toString() == column.value">
                    #{item.aliasName,jdbcType=VARCHAR}
                </if>
                <if test="'depth'.toString() == column.value">
                    #{item.depth,jdbcType=TINYINT}
                </if>
                <if test="'parent_code'.toString() == column.value">
                    #{item.parentCode,jdbcType=VARCHAR}
                </if>
                <if test="'inital_pinyin'.toString() == column.value">
                    #{item.initalPinyin,jdbcType=CHAR}
                </if>
                <if test="'short_pinyin'.toString() == column.value">
                    #{item.shortPinyin,jdbcType=VARCHAR}
                </if>
                <if test="'pinyin'.toString() == column.value">
                    #{item.pinyin,jdbcType=VARCHAR}
                </if>
                <if test="'english_name'.toString() == column.value">
                    #{item.englishName,jdbcType=VARCHAR}
                </if>
                <if test="'lat'.toString() == column.value">
                    #{item.lat,jdbcType=DECIMAL}
                </if>
                <if test="'lng'.toString() == column.value">
                    #{item.lng,jdbcType=DECIMAL}
                </if>
            </foreach>
            )
        </foreach>
    </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.QrtzCalendarsMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.QrtzCalendars">
    <id column="SCHED_NAME" jdbcType="VARCHAR" property="schedName" />
    <id column="CALENDAR_NAME" jdbcType="VARCHAR" property="calendarName" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.ql.rent.entity.common.QrtzCalendars">
    <result column="CALENDAR" jdbcType="LONGVARBINARY" property="calendar" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    SCHED_NAME, CALENDAR_NAME
  </sql>
  <sql id="Blob_Column_List">
    CALENDAR
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.ql.rent.entity.common.QrtzCalendarsExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from qrtz_calendars
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.ql.rent.entity.common.QrtzCalendarsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from qrtz_calendars
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="com.ql.rent.entity.common.QrtzCalendarsKey" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from qrtz_calendars
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and CALENDAR_NAME = #{calendarName,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.ql.rent.entity.common.QrtzCalendarsKey">
    delete from qrtz_calendars
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and CALENDAR_NAME = #{calendarName,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.common.QrtzCalendarsExample">
    delete from qrtz_calendars
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.common.QrtzCalendars">
    insert into qrtz_calendars (SCHED_NAME, CALENDAR_NAME, CALENDAR
      )
    values (#{schedName,jdbcType=VARCHAR}, #{calendarName,jdbcType=VARCHAR}, #{calendar,jdbcType=LONGVARBINARY}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.common.QrtzCalendars">
    insert into qrtz_calendars
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="schedName != null">
        SCHED_NAME,
      </if>
      <if test="calendarName != null">
        CALENDAR_NAME,
      </if>
      <if test="calendar != null">
        CALENDAR,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="schedName != null">
        #{schedName,jdbcType=VARCHAR},
      </if>
      <if test="calendarName != null">
        #{calendarName,jdbcType=VARCHAR},
      </if>
      <if test="calendar != null">
        #{calendar,jdbcType=LONGVARBINARY},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.common.QrtzCalendarsExample" resultType="java.lang.Long">
    select count(*) from qrtz_calendars
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update qrtz_calendars
    <set>
      <if test="record.schedName != null">
        SCHED_NAME = #{record.schedName,jdbcType=VARCHAR},
      </if>
      <if test="record.calendarName != null">
        CALENDAR_NAME = #{record.calendarName,jdbcType=VARCHAR},
      </if>
      <if test="record.calendar != null">
        CALENDAR = #{record.calendar,jdbcType=LONGVARBINARY},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update qrtz_calendars
    set SCHED_NAME = #{record.schedName,jdbcType=VARCHAR},
      CALENDAR_NAME = #{record.calendarName,jdbcType=VARCHAR},
      CALENDAR = #{record.calendar,jdbcType=LONGVARBINARY}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update qrtz_calendars
    set SCHED_NAME = #{record.schedName,jdbcType=VARCHAR},
      CALENDAR_NAME = #{record.calendarName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.QrtzCalendars">
    update qrtz_calendars
    <set>
      <if test="calendar != null">
        CALENDAR = #{calendar,jdbcType=LONGVARBINARY},
      </if>
    </set>
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and CALENDAR_NAME = #{calendarName,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.ql.rent.entity.common.QrtzCalendars">
    update qrtz_calendars
    set CALENDAR = #{calendar,jdbcType=LONGVARBINARY}
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and CALENDAR_NAME = #{calendarName,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into qrtz_calendars
    (SCHED_NAME, CALENDAR_NAME, CALENDAR)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.schedName,jdbcType=VARCHAR}, #{item.calendarName,jdbcType=VARCHAR}, #{item.calendar,jdbcType=LONGVARBINARY}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into qrtz_calendars (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'SCHED_NAME'.toString() == column.value">
          #{item.schedName,jdbcType=VARCHAR}
        </if>
        <if test="'CALENDAR_NAME'.toString() == column.value">
          #{item.calendarName,jdbcType=VARCHAR}
        </if>
        <if test="'CALENDAR'.toString() == column.value">
          #{item.calendar,jdbcType=LONGVARBINARY}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
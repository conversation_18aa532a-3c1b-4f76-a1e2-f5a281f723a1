<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.ApiConnMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.ApiConn">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="conn_type" jdbcType="TINYINT" property="connType" />
    <result column="appkey" jdbcType="VARCHAR" property="appkey" />
    <result column="appsecret" jdbcType="VARCHAR" property="appsecret" />
    <result column="channel_shared_key" jdbcType="VARCHAR" property="channelSharedKey" />
    <result column="channel_vendor_code" jdbcType="VARCHAR" property="channelVendorCode" />
    <result column="channel_url" jdbcType="VARCHAR" property="channelUrl" />
    <result column="channel_reg_phone" jdbcType="VARCHAR" property="channelRegPhone" />
    <result column="channel_reg_email" jdbcType="VARCHAR" property="channelRegEmail" />
    <result column="channel_reg_user" jdbcType="VARCHAR" property="channelRegUser" />
    <result column="channel_reg_pass" jdbcType="VARCHAR" property="channelRegPass" />
    <result column="channel_reg_interface" jdbcType="VARCHAR" property="channelRegInterface" />
    <result column="channel_create_user_id" jdbcType="VARCHAR" property="channelCreateUserId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="api_status" jdbcType="TINYINT" property="apiStatus" />
    <result column="api_type" jdbcType="TINYINT" property="apiType" />
    <result column="ctrip_push_status" jdbcType="TINYINT" property="ctripPushStatus" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, channel_id, conn_type, appkey, appsecret, channel_shared_key, channel_vendor_code, 
    channel_url, channel_reg_phone, channel_reg_email, channel_reg_user, channel_reg_pass, 
    channel_reg_interface, channel_create_user_id, status, api_status, api_type, ctrip_push_status, 
    last_ver, create_time, op_time, extra
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.common.ApiConnExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from api_conn
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from api_conn
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.common.ApiConn">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into api_conn (merchant_id, channel_id, conn_type, 
      appkey, appsecret, channel_shared_key, 
      channel_vendor_code, channel_url, channel_reg_phone, 
      channel_reg_email, channel_reg_user, channel_reg_pass, 
      channel_reg_interface, channel_create_user_id, 
      status, api_status, api_type, 
      ctrip_push_status, last_ver, create_time, 
      op_time, extra)
    values (#{merchantId,jdbcType=BIGINT}, #{channelId,jdbcType=BIGINT}, #{connType,jdbcType=TINYINT}, 
      #{appkey,jdbcType=VARCHAR}, #{appsecret,jdbcType=VARCHAR}, #{channelSharedKey,jdbcType=VARCHAR}, 
      #{channelVendorCode,jdbcType=VARCHAR}, #{channelUrl,jdbcType=VARCHAR}, #{channelRegPhone,jdbcType=VARCHAR}, 
      #{channelRegEmail,jdbcType=VARCHAR}, #{channelRegUser,jdbcType=VARCHAR}, #{channelRegPass,jdbcType=VARCHAR}, 
      #{channelRegInterface,jdbcType=VARCHAR}, #{channelCreateUserId,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{apiStatus,jdbcType=TINYINT}, #{apiType,jdbcType=TINYINT}, 
      #{ctripPushStatus,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{extra,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.common.ApiConn">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into api_conn
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="connType != null">
        conn_type,
      </if>
      <if test="appkey != null">
        appkey,
      </if>
      <if test="appsecret != null">
        appsecret,
      </if>
      <if test="channelSharedKey != null">
        channel_shared_key,
      </if>
      <if test="channelVendorCode != null">
        channel_vendor_code,
      </if>
      <if test="channelUrl != null">
        channel_url,
      </if>
      <if test="channelRegPhone != null">
        channel_reg_phone,
      </if>
      <if test="channelRegEmail != null">
        channel_reg_email,
      </if>
      <if test="channelRegUser != null">
        channel_reg_user,
      </if>
      <if test="channelRegPass != null">
        channel_reg_pass,
      </if>
      <if test="channelRegInterface != null">
        channel_reg_interface,
      </if>
      <if test="channelCreateUserId != null">
        channel_create_user_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="apiStatus != null">
        api_status,
      </if>
      <if test="apiType != null">
        api_type,
      </if>
      <if test="ctripPushStatus != null">
        ctrip_push_status,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="connType != null">
        #{connType,jdbcType=TINYINT},
      </if>
      <if test="appkey != null">
        #{appkey,jdbcType=VARCHAR},
      </if>
      <if test="appsecret != null">
        #{appsecret,jdbcType=VARCHAR},
      </if>
      <if test="channelSharedKey != null">
        #{channelSharedKey,jdbcType=VARCHAR},
      </if>
      <if test="channelVendorCode != null">
        #{channelVendorCode,jdbcType=VARCHAR},
      </if>
      <if test="channelUrl != null">
        #{channelUrl,jdbcType=VARCHAR},
      </if>
      <if test="channelRegPhone != null">
        #{channelRegPhone,jdbcType=VARCHAR},
      </if>
      <if test="channelRegEmail != null">
        #{channelRegEmail,jdbcType=VARCHAR},
      </if>
      <if test="channelRegUser != null">
        #{channelRegUser,jdbcType=VARCHAR},
      </if>
      <if test="channelRegPass != null">
        #{channelRegPass,jdbcType=VARCHAR},
      </if>
      <if test="channelRegInterface != null">
        #{channelRegInterface,jdbcType=VARCHAR},
      </if>
      <if test="channelCreateUserId != null">
        #{channelCreateUserId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="apiStatus != null">
        #{apiStatus,jdbcType=TINYINT},
      </if>
      <if test="apiType != null">
        #{apiType,jdbcType=TINYINT},
      </if>
      <if test="ctripPushStatus != null">
        #{ctripPushStatus,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.common.ApiConnExample" resultType="java.lang.Long">
    select count(*) from api_conn
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update api_conn
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=BIGINT},
      </if>
      <if test="record.connType != null">
        conn_type = #{record.connType,jdbcType=TINYINT},
      </if>
      <if test="record.appkey != null">
        appkey = #{record.appkey,jdbcType=VARCHAR},
      </if>
      <if test="record.appsecret != null">
        appsecret = #{record.appsecret,jdbcType=VARCHAR},
      </if>
      <if test="record.channelSharedKey != null">
        channel_shared_key = #{record.channelSharedKey,jdbcType=VARCHAR},
      </if>
      <if test="record.channelVendorCode != null">
        channel_vendor_code = #{record.channelVendorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.channelUrl != null">
        channel_url = #{record.channelUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.channelRegPhone != null">
        channel_reg_phone = #{record.channelRegPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.channelRegEmail != null">
        channel_reg_email = #{record.channelRegEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.channelRegUser != null">
        channel_reg_user = #{record.channelRegUser,jdbcType=VARCHAR},
      </if>
      <if test="record.channelRegPass != null">
        channel_reg_pass = #{record.channelRegPass,jdbcType=VARCHAR},
      </if>
      <if test="record.channelRegInterface != null">
        channel_reg_interface = #{record.channelRegInterface,jdbcType=VARCHAR},
      </if>
      <if test="record.channelCreateUserId != null">
        channel_create_user_id = #{record.channelCreateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.apiStatus != null">
        api_status = #{record.apiStatus,jdbcType=TINYINT},
      </if>
      <if test="record.apiType != null">
        api_type = #{record.apiType,jdbcType=TINYINT},
      </if>
      <if test="record.ctripPushStatus != null">
        ctrip_push_status = #{record.ctripPushStatus,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update api_conn
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      channel_id = #{record.channelId,jdbcType=BIGINT},
      conn_type = #{record.connType,jdbcType=TINYINT},
      appkey = #{record.appkey,jdbcType=VARCHAR},
      appsecret = #{record.appsecret,jdbcType=VARCHAR},
      channel_shared_key = #{record.channelSharedKey,jdbcType=VARCHAR},
      channel_vendor_code = #{record.channelVendorCode,jdbcType=VARCHAR},
      channel_url = #{record.channelUrl,jdbcType=VARCHAR},
      channel_reg_phone = #{record.channelRegPhone,jdbcType=VARCHAR},
      channel_reg_email = #{record.channelRegEmail,jdbcType=VARCHAR},
      channel_reg_user = #{record.channelRegUser,jdbcType=VARCHAR},
      channel_reg_pass = #{record.channelRegPass,jdbcType=VARCHAR},
      channel_reg_interface = #{record.channelRegInterface,jdbcType=VARCHAR},
      channel_create_user_id = #{record.channelCreateUserId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      api_status = #{record.apiStatus,jdbcType=TINYINT},
      api_type = #{record.apiType,jdbcType=TINYINT},
      ctrip_push_status = #{record.ctripPushStatus,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      extra = #{record.extra,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.ApiConn">
    update api_conn
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="connType != null">
        conn_type = #{connType,jdbcType=TINYINT},
      </if>
      <if test="appkey != null">
        appkey = #{appkey,jdbcType=VARCHAR},
      </if>
      <if test="appsecret != null">
        appsecret = #{appsecret,jdbcType=VARCHAR},
      </if>
      <if test="channelSharedKey != null">
        channel_shared_key = #{channelSharedKey,jdbcType=VARCHAR},
      </if>
      <if test="channelVendorCode != null">
        channel_vendor_code = #{channelVendorCode,jdbcType=VARCHAR},
      </if>
      <if test="channelUrl != null">
        channel_url = #{channelUrl,jdbcType=VARCHAR},
      </if>
      <if test="channelRegPhone != null">
        channel_reg_phone = #{channelRegPhone,jdbcType=VARCHAR},
      </if>
      <if test="channelRegEmail != null">
        channel_reg_email = #{channelRegEmail,jdbcType=VARCHAR},
      </if>
      <if test="channelRegUser != null">
        channel_reg_user = #{channelRegUser,jdbcType=VARCHAR},
      </if>
      <if test="channelRegPass != null">
        channel_reg_pass = #{channelRegPass,jdbcType=VARCHAR},
      </if>
      <if test="channelRegInterface != null">
        channel_reg_interface = #{channelRegInterface,jdbcType=VARCHAR},
      </if>
      <if test="channelCreateUserId != null">
        channel_create_user_id = #{channelCreateUserId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="apiStatus != null">
        api_status = #{apiStatus,jdbcType=TINYINT},
      </if>
      <if test="apiType != null">
        api_type = #{apiType,jdbcType=TINYINT},
      </if>
      <if test="ctripPushStatus != null">
        ctrip_push_status = #{ctripPushStatus,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.common.ApiConn">
    update api_conn
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      channel_id = #{channelId,jdbcType=BIGINT},
      conn_type = #{connType,jdbcType=TINYINT},
      appkey = #{appkey,jdbcType=VARCHAR},
      appsecret = #{appsecret,jdbcType=VARCHAR},
      channel_shared_key = #{channelSharedKey,jdbcType=VARCHAR},
      channel_vendor_code = #{channelVendorCode,jdbcType=VARCHAR},
      channel_url = #{channelUrl,jdbcType=VARCHAR},
      channel_reg_phone = #{channelRegPhone,jdbcType=VARCHAR},
      channel_reg_email = #{channelRegEmail,jdbcType=VARCHAR},
      channel_reg_user = #{channelRegUser,jdbcType=VARCHAR},
      channel_reg_pass = #{channelRegPass,jdbcType=VARCHAR},
      channel_reg_interface = #{channelRegInterface,jdbcType=VARCHAR},
      channel_create_user_id = #{channelCreateUserId,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      api_status = #{apiStatus,jdbcType=TINYINT},
      api_type = #{apiType,jdbcType=TINYINT},
      ctrip_push_status = #{ctripPushStatus,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      extra = #{extra,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into api_conn
    (merchant_id, channel_id, conn_type, appkey, appsecret, channel_shared_key, channel_vendor_code, 
      channel_url, channel_reg_phone, channel_reg_email, channel_reg_user, channel_reg_pass, 
      channel_reg_interface, channel_create_user_id, status, api_status, api_type, ctrip_push_status, 
      last_ver, create_time, op_time, extra)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.channelId,jdbcType=BIGINT}, #{item.connType,jdbcType=TINYINT}, 
        #{item.appkey,jdbcType=VARCHAR}, #{item.appsecret,jdbcType=VARCHAR}, #{item.channelSharedKey,jdbcType=VARCHAR}, 
        #{item.channelVendorCode,jdbcType=VARCHAR}, #{item.channelUrl,jdbcType=VARCHAR}, 
        #{item.channelRegPhone,jdbcType=VARCHAR}, #{item.channelRegEmail,jdbcType=VARCHAR}, 
        #{item.channelRegUser,jdbcType=VARCHAR}, #{item.channelRegPass,jdbcType=VARCHAR}, 
        #{item.channelRegInterface,jdbcType=VARCHAR}, #{item.channelCreateUserId,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=TINYINT}, #{item.apiStatus,jdbcType=TINYINT}, #{item.apiType,jdbcType=TINYINT}, 
        #{item.ctripPushStatus,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.extra,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into api_conn (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'channel_id'.toString() == column.value">
          #{item.channelId,jdbcType=BIGINT}
        </if>
        <if test="'conn_type'.toString() == column.value">
          #{item.connType,jdbcType=TINYINT}
        </if>
        <if test="'appkey'.toString() == column.value">
          #{item.appkey,jdbcType=VARCHAR}
        </if>
        <if test="'appsecret'.toString() == column.value">
          #{item.appsecret,jdbcType=VARCHAR}
        </if>
        <if test="'channel_shared_key'.toString() == column.value">
          #{item.channelSharedKey,jdbcType=VARCHAR}
        </if>
        <if test="'channel_vendor_code'.toString() == column.value">
          #{item.channelVendorCode,jdbcType=VARCHAR}
        </if>
        <if test="'channel_url'.toString() == column.value">
          #{item.channelUrl,jdbcType=VARCHAR}
        </if>
        <if test="'channel_reg_phone'.toString() == column.value">
          #{item.channelRegPhone,jdbcType=VARCHAR}
        </if>
        <if test="'channel_reg_email'.toString() == column.value">
          #{item.channelRegEmail,jdbcType=VARCHAR}
        </if>
        <if test="'channel_reg_user'.toString() == column.value">
          #{item.channelRegUser,jdbcType=VARCHAR}
        </if>
        <if test="'channel_reg_pass'.toString() == column.value">
          #{item.channelRegPass,jdbcType=VARCHAR}
        </if>
        <if test="'channel_reg_interface'.toString() == column.value">
          #{item.channelRegInterface,jdbcType=VARCHAR}
        </if>
        <if test="'channel_create_user_id'.toString() == column.value">
          #{item.channelCreateUserId,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'api_status'.toString() == column.value">
          #{item.apiStatus,jdbcType=TINYINT}
        </if>
        <if test="'api_type'.toString() == column.value">
          #{item.apiType,jdbcType=TINYINT}
        </if>
        <if test="'ctrip_push_status'.toString() == column.value">
          #{item.ctripPushStatus,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'extra'.toString() == column.value">
          #{item.extra,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
  <select id="selectAllMerchantList"  resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM
    api_conn
    where channel_id = 2
    and status = 3
    and conn_type = 1;
  </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.QrtzConTriggersMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.QrtzConTriggers">
    <id column="SCHED_NAME" jdbcType="VARCHAR" property="schedName" />
    <id column="TRIGGER_NAME" jdbcType="VARCHAR" property="triggerName" />
    <id column="TRIGGER_GROUP" jdbcType="VARCHAR" property="triggerGroup" />
    <result column="CRON_EXPRESSION" jdbcType="VARCHAR" property="cronExpression" />
    <result column="TIME_ZONE_ID" jdbcType="VARCHAR" property="timeZoneId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP, CRON_EXPRESSION, TIME_ZONE_ID
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.common.QrtzConTriggersExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from qrtz_cron_triggers
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="com.ql.rent.entity.common.QrtzConTriggersKey" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from qrtz_cron_triggers
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and TRIGGER_NAME = #{triggerName,jdbcType=VARCHAR}
      and TRIGGER_GROUP = #{triggerGroup,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.ql.rent.entity.common.QrtzConTriggersKey">
    delete from qrtz_cron_triggers
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and TRIGGER_NAME = #{triggerName,jdbcType=VARCHAR}
      and TRIGGER_GROUP = #{triggerGroup,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.common.QrtzConTriggersExample">
    delete from qrtz_cron_triggers
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.common.QrtzConTriggers">
    insert into qrtz_cron_triggers (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP, 
      CRON_EXPRESSION, TIME_ZONE_ID)
    values (#{schedName,jdbcType=VARCHAR}, #{triggerName,jdbcType=VARCHAR}, #{triggerGroup,jdbcType=VARCHAR}, 
      #{cronExpression,jdbcType=VARCHAR}, #{timeZoneId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.common.QrtzConTriggers">
    insert into qrtz_cron_triggers
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="schedName != null">
        SCHED_NAME,
      </if>
      <if test="triggerName != null">
        TRIGGER_NAME,
      </if>
      <if test="triggerGroup != null">
        TRIGGER_GROUP,
      </if>
      <if test="cronExpression != null">
        CRON_EXPRESSION,
      </if>
      <if test="timeZoneId != null">
        TIME_ZONE_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="schedName != null">
        #{schedName,jdbcType=VARCHAR},
      </if>
      <if test="triggerName != null">
        #{triggerName,jdbcType=VARCHAR},
      </if>
      <if test="triggerGroup != null">
        #{triggerGroup,jdbcType=VARCHAR},
      </if>
      <if test="cronExpression != null">
        #{cronExpression,jdbcType=VARCHAR},
      </if>
      <if test="timeZoneId != null">
        #{timeZoneId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.common.QrtzConTriggersExample" resultType="java.lang.Long">
    select count(*) from qrtz_cron_triggers
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update qrtz_cron_triggers
    <set>
      <if test="record.schedName != null">
        SCHED_NAME = #{record.schedName,jdbcType=VARCHAR},
      </if>
      <if test="record.triggerName != null">
        TRIGGER_NAME = #{record.triggerName,jdbcType=VARCHAR},
      </if>
      <if test="record.triggerGroup != null">
        TRIGGER_GROUP = #{record.triggerGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.cronExpression != null">
        CRON_EXPRESSION = #{record.cronExpression,jdbcType=VARCHAR},
      </if>
      <if test="record.timeZoneId != null">
        TIME_ZONE_ID = #{record.timeZoneId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update qrtz_cron_triggers
    set SCHED_NAME = #{record.schedName,jdbcType=VARCHAR},
      TRIGGER_NAME = #{record.triggerName,jdbcType=VARCHAR},
      TRIGGER_GROUP = #{record.triggerGroup,jdbcType=VARCHAR},
      CRON_EXPRESSION = #{record.cronExpression,jdbcType=VARCHAR},
      TIME_ZONE_ID = #{record.timeZoneId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.QrtzConTriggers">
    update qrtz_cron_triggers
    <set>
      <if test="cronExpression != null">
        CRON_EXPRESSION = #{cronExpression,jdbcType=VARCHAR},
      </if>
      <if test="timeZoneId != null">
        TIME_ZONE_ID = #{timeZoneId,jdbcType=VARCHAR},
      </if>
    </set>
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and TRIGGER_NAME = #{triggerName,jdbcType=VARCHAR}
      and TRIGGER_GROUP = #{triggerGroup,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.common.QrtzConTriggers">
    update qrtz_cron_triggers
    set CRON_EXPRESSION = #{cronExpression,jdbcType=VARCHAR},
      TIME_ZONE_ID = #{timeZoneId,jdbcType=VARCHAR}
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and TRIGGER_NAME = #{triggerName,jdbcType=VARCHAR}
      and TRIGGER_GROUP = #{triggerGroup,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into qrtz_cron_triggers
    (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP, CRON_EXPRESSION, TIME_ZONE_ID)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.schedName,jdbcType=VARCHAR}, #{item.triggerName,jdbcType=VARCHAR}, #{item.triggerGroup,jdbcType=VARCHAR}, 
        #{item.cronExpression,jdbcType=VARCHAR}, #{item.timeZoneId,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into qrtz_cron_triggers (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'SCHED_NAME'.toString() == column.value">
          #{item.schedName,jdbcType=VARCHAR}
        </if>
        <if test="'TRIGGER_NAME'.toString() == column.value">
          #{item.triggerName,jdbcType=VARCHAR}
        </if>
        <if test="'TRIGGER_GROUP'.toString() == column.value">
          #{item.triggerGroup,jdbcType=VARCHAR}
        </if>
        <if test="'CRON_EXPRESSION'.toString() == column.value">
          #{item.cronExpression,jdbcType=VARCHAR}
        </if>
        <if test="'TIME_ZONE_ID'.toString() == column.value">
          #{item.timeZoneId,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.PullDataTaskMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.PullDataTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="merchant_name" jdbcType="VARCHAR" property="merchantName" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="vehicle_model_name" jdbcType="VARCHAR" property="vehicleModelName" />
    <result column="pull_status" jdbcType="TINYINT" property="pullStatus" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="op_user_name" jdbcType="VARCHAR" property="opUserName" />
    <result column="start_time" jdbcType="BIGINT" property="startTime" />
    <result column="end_time" jdbcType="BIGINT" property="endTime" />
    <result column="note" jdbcType="VARCHAR" property="note" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, merchant_name, vehicle_model_id, vehicle_model_name, pull_status, 
    op_user_id, op_user_name, start_time, end_time, note
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.common.PullDataTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from pull_data_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pull_data_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pull_data_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.common.PullDataTaskExample">
    delete from pull_data_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.common.PullDataTask">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pull_data_task (merchant_id, merchant_name, vehicle_model_id, 
      vehicle_model_name, pull_status, op_user_id, 
      op_user_name, start_time, end_time, 
      note)
    values (#{merchantId,jdbcType=BIGINT}, #{merchantName,jdbcType=VARCHAR}, #{vehicleModelId,jdbcType=BIGINT}, 
      #{vehicleModelName,jdbcType=VARCHAR}, #{pullStatus,jdbcType=TINYINT}, #{opUserId,jdbcType=BIGINT}, 
      #{opUserName,jdbcType=VARCHAR}, #{startTime,jdbcType=BIGINT}, #{endTime,jdbcType=BIGINT}, 
      #{note,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.common.PullDataTask">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pull_data_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="merchantName != null">
        merchant_name,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name,
      </if>
      <if test="pullStatus != null">
        pull_status,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="opUserName != null">
        op_user_name,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="note != null">
        note,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="merchantName != null">
        #{merchantName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelName != null">
        #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="pullStatus != null">
        #{pullStatus,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="opUserName != null">
        #{opUserName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=BIGINT},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.common.PullDataTaskExample" resultType="java.lang.Long">
    select count(*) from pull_data_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update pull_data_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantName != null">
        merchant_name = #{record.merchantName,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelName != null">
        vehicle_model_name = #{record.vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="record.pullStatus != null">
        pull_status = #{record.pullStatus,jdbcType=TINYINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.opUserName != null">
        op_user_name = #{record.opUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=BIGINT},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=BIGINT},
      </if>
      <if test="record.note != null">
        note = #{record.note,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update pull_data_task
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      merchant_name = #{record.merchantName,jdbcType=VARCHAR},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      vehicle_model_name = #{record.vehicleModelName,jdbcType=VARCHAR},
      pull_status = #{record.pullStatus,jdbcType=TINYINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      op_user_name = #{record.opUserName,jdbcType=VARCHAR},
      start_time = #{record.startTime,jdbcType=BIGINT},
      end_time = #{record.endTime,jdbcType=BIGINT},
      note = #{record.note,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.PullDataTask">
    update pull_data_task
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="merchantName != null">
        merchant_name = #{merchantName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="pullStatus != null">
        pull_status = #{pullStatus,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="opUserName != null">
        op_user_name = #{opUserName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=BIGINT},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.common.PullDataTask">
    update pull_data_task
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      merchant_name = #{merchantName,jdbcType=VARCHAR},
      vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      pull_status = #{pullStatus,jdbcType=TINYINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      op_user_name = #{opUserName,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=BIGINT},
      end_time = #{endTime,jdbcType=BIGINT},
      note = #{note,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into pull_data_task
    (merchant_id, merchant_name, vehicle_model_id, vehicle_model_name, pull_status, op_user_id, 
      op_user_name, start_time, end_time, note)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.merchantName,jdbcType=VARCHAR}, #{item.vehicleModelId,jdbcType=BIGINT}, 
        #{item.vehicleModelName,jdbcType=VARCHAR}, #{item.pullStatus,jdbcType=TINYINT}, 
        #{item.opUserId,jdbcType=BIGINT}, #{item.opUserName,jdbcType=VARCHAR}, #{item.startTime,jdbcType=BIGINT}, 
        #{item.endTime,jdbcType=BIGINT}, #{item.note,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into pull_data_task (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'merchant_name'.toString() == column.value">
          #{item.merchantName,jdbcType=VARCHAR}
        </if>
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_model_name'.toString() == column.value">
          #{item.vehicleModelName,jdbcType=VARCHAR}
        </if>
        <if test="'pull_status'.toString() == column.value">
          #{item.pullStatus,jdbcType=TINYINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'op_user_name'.toString() == column.value">
          #{item.opUserName,jdbcType=VARCHAR}
        </if>
        <if test="'start_time'.toString() == column.value">
          #{item.startTime,jdbcType=BIGINT}
        </if>
        <if test="'end_time'.toString() == column.value">
          #{item.endTime,jdbcType=BIGINT}
        </if>
        <if test="'note'.toString() == column.value">
          #{item.note,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
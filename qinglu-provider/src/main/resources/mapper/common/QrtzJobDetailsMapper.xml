<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.QrtzJobDetailsMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.QrtzJobDetails">
    <id column="SCHED_NAME" jdbcType="VARCHAR" property="schedName" />
    <id column="JOB_NAME" jdbcType="VARCHAR" property="jobName" />
    <id column="JOB_GROUP" jdbcType="VARCHAR" property="jobGroup" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="JOB_CLASS_NAME" jdbcType="VARCHAR" property="jobClassName" />
    <result column="IS_DURABLE" jdbcType="BIT" property="isDurable" />
    <result column="IS_NONCONCURRENT" jdbcType="BIT" property="isNonconcurrent" />
    <result column="IS_UPDATE_DATA" jdbcType="BIT" property="isUpdateData" />
    <result column="REQUESTS_RECOVERY" jdbcType="BIT" property="requestsRecovery" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.ql.rent.entity.common.QrtzJobDetails">
    <result column="JOB_DATA" jdbcType="LONGVARBINARY" property="jobData" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    SCHED_NAME, JOB_NAME, JOB_GROUP, DESCRIPTION, JOB_CLASS_NAME, IS_DURABLE, IS_NONCONCURRENT, 
    IS_UPDATE_DATA, REQUESTS_RECOVERY
  </sql>
  <sql id="Blob_Column_List">
    JOB_DATA
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.ql.rent.entity.common.QrtzJobDetailsExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from qrtz_job_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.ql.rent.entity.common.QrtzJobDetailsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from qrtz_job_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="com.ql.rent.entity.common.QrtzJobDetailsKey" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from qrtz_job_details
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and JOB_NAME = #{jobName,jdbcType=VARCHAR}
      and JOB_GROUP = #{jobGroup,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.ql.rent.entity.common.QrtzJobDetailsKey">
    delete from qrtz_job_details
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and JOB_NAME = #{jobName,jdbcType=VARCHAR}
      and JOB_GROUP = #{jobGroup,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.common.QrtzJobDetailsExample">
    delete from qrtz_job_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.common.QrtzJobDetails">
    insert into qrtz_job_details (SCHED_NAME, JOB_NAME, JOB_GROUP, 
      DESCRIPTION, JOB_CLASS_NAME, IS_DURABLE, 
      IS_NONCONCURRENT, IS_UPDATE_DATA, REQUESTS_RECOVERY, 
      JOB_DATA)
    values (#{schedName,jdbcType=VARCHAR}, #{jobName,jdbcType=VARCHAR}, #{jobGroup,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{jobClassName,jdbcType=VARCHAR}, #{isDurable,jdbcType=BIT}, 
      #{isNonconcurrent,jdbcType=BIT}, #{isUpdateData,jdbcType=BIT}, #{requestsRecovery,jdbcType=BIT}, 
      #{jobData,jdbcType=LONGVARBINARY})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.common.QrtzJobDetails">
    insert into qrtz_job_details
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="schedName != null">
        SCHED_NAME,
      </if>
      <if test="jobName != null">
        JOB_NAME,
      </if>
      <if test="jobGroup != null">
        JOB_GROUP,
      </if>
      <if test="description != null">
        DESCRIPTION,
      </if>
      <if test="jobClassName != null">
        JOB_CLASS_NAME,
      </if>
      <if test="isDurable != null">
        IS_DURABLE,
      </if>
      <if test="isNonconcurrent != null">
        IS_NONCONCURRENT,
      </if>
      <if test="isUpdateData != null">
        IS_UPDATE_DATA,
      </if>
      <if test="requestsRecovery != null">
        REQUESTS_RECOVERY,
      </if>
      <if test="jobData != null">
        JOB_DATA,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="schedName != null">
        #{schedName,jdbcType=VARCHAR},
      </if>
      <if test="jobName != null">
        #{jobName,jdbcType=VARCHAR},
      </if>
      <if test="jobGroup != null">
        #{jobGroup,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="jobClassName != null">
        #{jobClassName,jdbcType=VARCHAR},
      </if>
      <if test="isDurable != null">
        #{isDurable,jdbcType=BIT},
      </if>
      <if test="isNonconcurrent != null">
        #{isNonconcurrent,jdbcType=BIT},
      </if>
      <if test="isUpdateData != null">
        #{isUpdateData,jdbcType=BIT},
      </if>
      <if test="requestsRecovery != null">
        #{requestsRecovery,jdbcType=BIT},
      </if>
      <if test="jobData != null">
        #{jobData,jdbcType=LONGVARBINARY},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.common.QrtzJobDetailsExample" resultType="java.lang.Long">
    select count(*) from qrtz_job_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update qrtz_job_details
    <set>
      <if test="record.schedName != null">
        SCHED_NAME = #{record.schedName,jdbcType=VARCHAR},
      </if>
      <if test="record.jobName != null">
        JOB_NAME = #{record.jobName,jdbcType=VARCHAR},
      </if>
      <if test="record.jobGroup != null">
        JOB_GROUP = #{record.jobGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        DESCRIPTION = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.jobClassName != null">
        JOB_CLASS_NAME = #{record.jobClassName,jdbcType=VARCHAR},
      </if>
      <if test="record.isDurable != null">
        IS_DURABLE = #{record.isDurable,jdbcType=BIT},
      </if>
      <if test="record.isNonconcurrent != null">
        IS_NONCONCURRENT = #{record.isNonconcurrent,jdbcType=BIT},
      </if>
      <if test="record.isUpdateData != null">
        IS_UPDATE_DATA = #{record.isUpdateData,jdbcType=BIT},
      </if>
      <if test="record.requestsRecovery != null">
        REQUESTS_RECOVERY = #{record.requestsRecovery,jdbcType=BIT},
      </if>
      <if test="record.jobData != null">
        JOB_DATA = #{record.jobData,jdbcType=LONGVARBINARY},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update qrtz_job_details
    set SCHED_NAME = #{record.schedName,jdbcType=VARCHAR},
      JOB_NAME = #{record.jobName,jdbcType=VARCHAR},
      JOB_GROUP = #{record.jobGroup,jdbcType=VARCHAR},
      DESCRIPTION = #{record.description,jdbcType=VARCHAR},
      JOB_CLASS_NAME = #{record.jobClassName,jdbcType=VARCHAR},
      IS_DURABLE = #{record.isDurable,jdbcType=BIT},
      IS_NONCONCURRENT = #{record.isNonconcurrent,jdbcType=BIT},
      IS_UPDATE_DATA = #{record.isUpdateData,jdbcType=BIT},
      REQUESTS_RECOVERY = #{record.requestsRecovery,jdbcType=BIT},
      JOB_DATA = #{record.jobData,jdbcType=LONGVARBINARY}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update qrtz_job_details
    set SCHED_NAME = #{record.schedName,jdbcType=VARCHAR},
      JOB_NAME = #{record.jobName,jdbcType=VARCHAR},
      JOB_GROUP = #{record.jobGroup,jdbcType=VARCHAR},
      DESCRIPTION = #{record.description,jdbcType=VARCHAR},
      JOB_CLASS_NAME = #{record.jobClassName,jdbcType=VARCHAR},
      IS_DURABLE = #{record.isDurable,jdbcType=BIT},
      IS_NONCONCURRENT = #{record.isNonconcurrent,jdbcType=BIT},
      IS_UPDATE_DATA = #{record.isUpdateData,jdbcType=BIT},
      REQUESTS_RECOVERY = #{record.requestsRecovery,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.QrtzJobDetails">
    update qrtz_job_details
    <set>
      <if test="description != null">
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
      <if test="jobClassName != null">
        JOB_CLASS_NAME = #{jobClassName,jdbcType=VARCHAR},
      </if>
      <if test="isDurable != null">
        IS_DURABLE = #{isDurable,jdbcType=BIT},
      </if>
      <if test="isNonconcurrent != null">
        IS_NONCONCURRENT = #{isNonconcurrent,jdbcType=BIT},
      </if>
      <if test="isUpdateData != null">
        IS_UPDATE_DATA = #{isUpdateData,jdbcType=BIT},
      </if>
      <if test="requestsRecovery != null">
        REQUESTS_RECOVERY = #{requestsRecovery,jdbcType=BIT},
      </if>
      <if test="jobData != null">
        JOB_DATA = #{jobData,jdbcType=LONGVARBINARY},
      </if>
    </set>
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and JOB_NAME = #{jobName,jdbcType=VARCHAR}
      and JOB_GROUP = #{jobGroup,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.ql.rent.entity.common.QrtzJobDetails">
    update qrtz_job_details
    set DESCRIPTION = #{description,jdbcType=VARCHAR},
      JOB_CLASS_NAME = #{jobClassName,jdbcType=VARCHAR},
      IS_DURABLE = #{isDurable,jdbcType=BIT},
      IS_NONCONCURRENT = #{isNonconcurrent,jdbcType=BIT},
      IS_UPDATE_DATA = #{isUpdateData,jdbcType=BIT},
      REQUESTS_RECOVERY = #{requestsRecovery,jdbcType=BIT},
      JOB_DATA = #{jobData,jdbcType=LONGVARBINARY}
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and JOB_NAME = #{jobName,jdbcType=VARCHAR}
      and JOB_GROUP = #{jobGroup,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.common.QrtzJobDetails">
    update qrtz_job_details
    set DESCRIPTION = #{description,jdbcType=VARCHAR},
      JOB_CLASS_NAME = #{jobClassName,jdbcType=VARCHAR},
      IS_DURABLE = #{isDurable,jdbcType=BIT},
      IS_NONCONCURRENT = #{isNonconcurrent,jdbcType=BIT},
      IS_UPDATE_DATA = #{isUpdateData,jdbcType=BIT},
      REQUESTS_RECOVERY = #{requestsRecovery,jdbcType=BIT}
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and JOB_NAME = #{jobName,jdbcType=VARCHAR}
      and JOB_GROUP = #{jobGroup,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into qrtz_job_details
    (SCHED_NAME, JOB_NAME, JOB_GROUP, DESCRIPTION, JOB_CLASS_NAME, IS_DURABLE, IS_NONCONCURRENT, 
      IS_UPDATE_DATA, REQUESTS_RECOVERY, JOB_DATA)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.schedName,jdbcType=VARCHAR}, #{item.jobName,jdbcType=VARCHAR}, #{item.jobGroup,jdbcType=VARCHAR}, 
        #{item.description,jdbcType=VARCHAR}, #{item.jobClassName,jdbcType=VARCHAR}, #{item.isDurable,jdbcType=BIT}, 
        #{item.isNonconcurrent,jdbcType=BIT}, #{item.isUpdateData,jdbcType=BIT}, #{item.requestsRecovery,jdbcType=BIT}, 
        #{item.jobData,jdbcType=LONGVARBINARY})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into qrtz_job_details (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'SCHED_NAME'.toString() == column.value">
          #{item.schedName,jdbcType=VARCHAR}
        </if>
        <if test="'JOB_NAME'.toString() == column.value">
          #{item.jobName,jdbcType=VARCHAR}
        </if>
        <if test="'JOB_GROUP'.toString() == column.value">
          #{item.jobGroup,jdbcType=VARCHAR}
        </if>
        <if test="'DESCRIPTION'.toString() == column.value">
          #{item.description,jdbcType=VARCHAR}
        </if>
        <if test="'JOB_CLASS_NAME'.toString() == column.value">
          #{item.jobClassName,jdbcType=VARCHAR}
        </if>
        <if test="'IS_DURABLE'.toString() == column.value">
          #{item.isDurable,jdbcType=BIT}
        </if>
        <if test="'IS_NONCONCURRENT'.toString() == column.value">
          #{item.isNonconcurrent,jdbcType=BIT}
        </if>
        <if test="'IS_UPDATE_DATA'.toString() == column.value">
          #{item.isUpdateData,jdbcType=BIT}
        </if>
        <if test="'REQUESTS_RECOVERY'.toString() == column.value">
          #{item.requestsRecovery,jdbcType=BIT}
        </if>
        <if test="'JOB_DATA'.toString() == column.value">
          #{item.jobData,jdbcType=LONGVARBINARY}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
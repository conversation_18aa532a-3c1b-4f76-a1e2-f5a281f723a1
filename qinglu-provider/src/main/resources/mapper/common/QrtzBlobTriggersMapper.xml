<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.QrtzBlobTriggersMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.QrtzBlobTriggers">
    <id column="SCHED_NAME" jdbcType="VARCHAR" property="schedName" />
    <id column="TRIGGER_NAME" jdbcType="VARCHAR" property="triggerName" />
    <id column="TRIGGER_GROUP" jdbcType="VARCHAR" property="triggerGroup" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.ql.rent.entity.common.QrtzBlobTriggers">
    <result column="BLOB_DATA" jdbcType="LONGVARBINARY" property="blobData" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP
  </sql>
  <sql id="Blob_Column_List">
    BLOB_DATA
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.ql.rent.entity.common.QrtzBlobTriggersExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from qrtz_blob_triggers
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.ql.rent.entity.common.QrtzBlobTriggersExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from qrtz_blob_triggers
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="com.ql.rent.entity.common.QrtzBlobTriggersKey" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from qrtz_blob_triggers
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and TRIGGER_NAME = #{triggerName,jdbcType=VARCHAR}
      and TRIGGER_GROUP = #{triggerGroup,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.ql.rent.entity.common.QrtzBlobTriggersKey">
    delete from qrtz_blob_triggers
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and TRIGGER_NAME = #{triggerName,jdbcType=VARCHAR}
      and TRIGGER_GROUP = #{triggerGroup,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.common.QrtzBlobTriggersExample">
    delete from qrtz_blob_triggers
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.common.QrtzBlobTriggers">
    insert into qrtz_blob_triggers (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP, 
      BLOB_DATA)
    values (#{schedName,jdbcType=VARCHAR}, #{triggerName,jdbcType=VARCHAR}, #{triggerGroup,jdbcType=VARCHAR}, 
      #{blobData,jdbcType=LONGVARBINARY})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.common.QrtzBlobTriggers">
    insert into qrtz_blob_triggers
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="schedName != null">
        SCHED_NAME,
      </if>
      <if test="triggerName != null">
        TRIGGER_NAME,
      </if>
      <if test="triggerGroup != null">
        TRIGGER_GROUP,
      </if>
      <if test="blobData != null">
        BLOB_DATA,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="schedName != null">
        #{schedName,jdbcType=VARCHAR},
      </if>
      <if test="triggerName != null">
        #{triggerName,jdbcType=VARCHAR},
      </if>
      <if test="triggerGroup != null">
        #{triggerGroup,jdbcType=VARCHAR},
      </if>
      <if test="blobData != null">
        #{blobData,jdbcType=LONGVARBINARY},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.common.QrtzBlobTriggersExample" resultType="java.lang.Long">
    select count(*) from qrtz_blob_triggers
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update qrtz_blob_triggers
    <set>
      <if test="record.schedName != null">
        SCHED_NAME = #{record.schedName,jdbcType=VARCHAR},
      </if>
      <if test="record.triggerName != null">
        TRIGGER_NAME = #{record.triggerName,jdbcType=VARCHAR},
      </if>
      <if test="record.triggerGroup != null">
        TRIGGER_GROUP = #{record.triggerGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.blobData != null">
        BLOB_DATA = #{record.blobData,jdbcType=LONGVARBINARY},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update qrtz_blob_triggers
    set SCHED_NAME = #{record.schedName,jdbcType=VARCHAR},
      TRIGGER_NAME = #{record.triggerName,jdbcType=VARCHAR},
      TRIGGER_GROUP = #{record.triggerGroup,jdbcType=VARCHAR},
      BLOB_DATA = #{record.blobData,jdbcType=LONGVARBINARY}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update qrtz_blob_triggers
    set SCHED_NAME = #{record.schedName,jdbcType=VARCHAR},
      TRIGGER_NAME = #{record.triggerName,jdbcType=VARCHAR},
      TRIGGER_GROUP = #{record.triggerGroup,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.QrtzBlobTriggers">
    update qrtz_blob_triggers
    <set>
      <if test="blobData != null">
        BLOB_DATA = #{blobData,jdbcType=LONGVARBINARY},
      </if>
    </set>
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and TRIGGER_NAME = #{triggerName,jdbcType=VARCHAR}
      and TRIGGER_GROUP = #{triggerGroup,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.ql.rent.entity.common.QrtzBlobTriggers">
    update qrtz_blob_triggers
    set BLOB_DATA = #{blobData,jdbcType=LONGVARBINARY}
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and TRIGGER_NAME = #{triggerName,jdbcType=VARCHAR}
      and TRIGGER_GROUP = #{triggerGroup,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into qrtz_blob_triggers
    (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP, BLOB_DATA)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.schedName,jdbcType=VARCHAR}, #{item.triggerName,jdbcType=VARCHAR}, #{item.triggerGroup,jdbcType=VARCHAR}, 
        #{item.blobData,jdbcType=LONGVARBINARY})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into qrtz_blob_triggers (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'SCHED_NAME'.toString() == column.value">
          #{item.schedName,jdbcType=VARCHAR}
        </if>
        <if test="'TRIGGER_NAME'.toString() == column.value">
          #{item.triggerName,jdbcType=VARCHAR}
        </if>
        <if test="'TRIGGER_GROUP'.toString() == column.value">
          #{item.triggerGroup,jdbcType=VARCHAR}
        </if>
        <if test="'BLOB_DATA'.toString() == column.value">
          #{item.blobData,jdbcType=LONGVARBINARY}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
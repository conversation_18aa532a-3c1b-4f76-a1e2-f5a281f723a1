<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.common.QrtzSimpleTriggersMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.common.QrtzSimpleTriggers">
    <id column="SCHED_NAME" jdbcType="VARCHAR" property="schedName" />
    <id column="TRIGGER_NAME" jdbcType="VARCHAR" property="triggerName" />
    <id column="TRIGGER_GROUP" jdbcType="VARCHAR" property="triggerGroup" />
    <result column="REPEAT_COUNT" jdbcType="BIGINT" property="repeatCount" />
    <result column="REPEAT_INTERVAL" jdbcType="BIGINT" property="repeatInterval" />
    <result column="TIMES_TRIGGERED" jdbcType="BIGINT" property="timesTriggered" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP, REPEAT_COUNT, REPEAT_INTERVAL, TIMES_TRIGGERED
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.common.QrtzSimpleTriggersExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from qrtz_simple_triggers
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="com.ql.rent.entity.common.QrtzSimpleTriggersKey" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from qrtz_simple_triggers
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and TRIGGER_NAME = #{triggerName,jdbcType=VARCHAR}
      and TRIGGER_GROUP = #{triggerGroup,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.ql.rent.entity.common.QrtzSimpleTriggersKey">
    delete from qrtz_simple_triggers
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and TRIGGER_NAME = #{triggerName,jdbcType=VARCHAR}
      and TRIGGER_GROUP = #{triggerGroup,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.common.QrtzSimpleTriggersExample">
    delete from qrtz_simple_triggers
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.common.QrtzSimpleTriggers">
    insert into qrtz_simple_triggers (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP, 
      REPEAT_COUNT, REPEAT_INTERVAL, TIMES_TRIGGERED
      )
    values (#{schedName,jdbcType=VARCHAR}, #{triggerName,jdbcType=VARCHAR}, #{triggerGroup,jdbcType=VARCHAR}, 
      #{repeatCount,jdbcType=BIGINT}, #{repeatInterval,jdbcType=BIGINT}, #{timesTriggered,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.common.QrtzSimpleTriggers">
    insert into qrtz_simple_triggers
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="schedName != null">
        SCHED_NAME,
      </if>
      <if test="triggerName != null">
        TRIGGER_NAME,
      </if>
      <if test="triggerGroup != null">
        TRIGGER_GROUP,
      </if>
      <if test="repeatCount != null">
        REPEAT_COUNT,
      </if>
      <if test="repeatInterval != null">
        REPEAT_INTERVAL,
      </if>
      <if test="timesTriggered != null">
        TIMES_TRIGGERED,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="schedName != null">
        #{schedName,jdbcType=VARCHAR},
      </if>
      <if test="triggerName != null">
        #{triggerName,jdbcType=VARCHAR},
      </if>
      <if test="triggerGroup != null">
        #{triggerGroup,jdbcType=VARCHAR},
      </if>
      <if test="repeatCount != null">
        #{repeatCount,jdbcType=BIGINT},
      </if>
      <if test="repeatInterval != null">
        #{repeatInterval,jdbcType=BIGINT},
      </if>
      <if test="timesTriggered != null">
        #{timesTriggered,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.common.QrtzSimpleTriggersExample" resultType="java.lang.Long">
    select count(*) from qrtz_simple_triggers
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update qrtz_simple_triggers
    <set>
      <if test="record.schedName != null">
        SCHED_NAME = #{record.schedName,jdbcType=VARCHAR},
      </if>
      <if test="record.triggerName != null">
        TRIGGER_NAME = #{record.triggerName,jdbcType=VARCHAR},
      </if>
      <if test="record.triggerGroup != null">
        TRIGGER_GROUP = #{record.triggerGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.repeatCount != null">
        REPEAT_COUNT = #{record.repeatCount,jdbcType=BIGINT},
      </if>
      <if test="record.repeatInterval != null">
        REPEAT_INTERVAL = #{record.repeatInterval,jdbcType=BIGINT},
      </if>
      <if test="record.timesTriggered != null">
        TIMES_TRIGGERED = #{record.timesTriggered,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update qrtz_simple_triggers
    set SCHED_NAME = #{record.schedName,jdbcType=VARCHAR},
      TRIGGER_NAME = #{record.triggerName,jdbcType=VARCHAR},
      TRIGGER_GROUP = #{record.triggerGroup,jdbcType=VARCHAR},
      REPEAT_COUNT = #{record.repeatCount,jdbcType=BIGINT},
      REPEAT_INTERVAL = #{record.repeatInterval,jdbcType=BIGINT},
      TIMES_TRIGGERED = #{record.timesTriggered,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.common.QrtzSimpleTriggers">
    update qrtz_simple_triggers
    <set>
      <if test="repeatCount != null">
        REPEAT_COUNT = #{repeatCount,jdbcType=BIGINT},
      </if>
      <if test="repeatInterval != null">
        REPEAT_INTERVAL = #{repeatInterval,jdbcType=BIGINT},
      </if>
      <if test="timesTriggered != null">
        TIMES_TRIGGERED = #{timesTriggered,jdbcType=BIGINT},
      </if>
    </set>
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and TRIGGER_NAME = #{triggerName,jdbcType=VARCHAR}
      and TRIGGER_GROUP = #{triggerGroup,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.common.QrtzSimpleTriggers">
    update qrtz_simple_triggers
    set REPEAT_COUNT = #{repeatCount,jdbcType=BIGINT},
      REPEAT_INTERVAL = #{repeatInterval,jdbcType=BIGINT},
      TIMES_TRIGGERED = #{timesTriggered,jdbcType=BIGINT}
    where SCHED_NAME = #{schedName,jdbcType=VARCHAR}
      and TRIGGER_NAME = #{triggerName,jdbcType=VARCHAR}
      and TRIGGER_GROUP = #{triggerGroup,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into qrtz_simple_triggers
    (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP, REPEAT_COUNT, REPEAT_INTERVAL, TIMES_TRIGGERED
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.schedName,jdbcType=VARCHAR}, #{item.triggerName,jdbcType=VARCHAR}, #{item.triggerGroup,jdbcType=VARCHAR}, 
        #{item.repeatCount,jdbcType=BIGINT}, #{item.repeatInterval,jdbcType=BIGINT}, #{item.timesTriggered,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into qrtz_simple_triggers (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'SCHED_NAME'.toString() == column.value">
          #{item.schedName,jdbcType=VARCHAR}
        </if>
        <if test="'TRIGGER_NAME'.toString() == column.value">
          #{item.triggerName,jdbcType=VARCHAR}
        </if>
        <if test="'TRIGGER_GROUP'.toString() == column.value">
          #{item.triggerGroup,jdbcType=VARCHAR}
        </if>
        <if test="'REPEAT_COUNT'.toString() == column.value">
          #{item.repeatCount,jdbcType=BIGINT}
        </if>
        <if test="'REPEAT_INTERVAL'.toString() == column.value">
          #{item.repeatInterval,jdbcType=BIGINT}
        </if>
        <if test="'TIMES_TRIGGERED'.toString() == column.value">
          #{item.timesTriggered,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
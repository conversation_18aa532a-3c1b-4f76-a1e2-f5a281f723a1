<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.OrderInstalmentMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.OrderInstalment">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="instalment_no" jdbcType="INTEGER" property="instalmentNo" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="init_plan_amount" jdbcType="INTEGER" property="initPlanAmount" />
    <result column="plan_amount" jdbcType="INTEGER" property="planAmount" />
    <result column="actual_amount" jdbcType="INTEGER" property="actualAmount" />
    <result column="instalment_status" jdbcType="TINYINT" property="instalmentStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, instalment_no, start_date, end_date, init_plan_amount, plan_amount, actual_amount, instalment_status,
    remark, last_ver, op_user_id, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.OrderInstalmentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from order_instalment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_instalment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_instalment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.OrderInstalmentExample">
    delete from order_instalment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.OrderInstalment">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_instalment (order_id, instalment_no, start_date, 
      end_date, init_plan_amount, plan_amount, actual_amount,
      instalment_status, remark, last_ver, 
      op_user_id, create_time, op_time
      )
    values (#{orderId,jdbcType=BIGINT}, #{instalmentNo,jdbcType=INTEGER}, #{startDate,jdbcType=TIMESTAMP}, 
      #{endDate,jdbcType=TIMESTAMP}, #{initPlanAmount,jdbcType=INTEGER}, #{planAmount,jdbcType=INTEGER}, #{actualAmount,jdbcType=INTEGER},
      #{instalmentStatus,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, #{lastVer,jdbcType=INTEGER}, 
      #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.OrderInstalment">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_instalment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="instalmentNo != null">
        instalment_no,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="initPlanAmount != null">
        init_plan_amount,
      </if>
      <if test="planAmount != null">
        plan_amount,
      </if>
      <if test="actualAmount != null">
        actual_amount,
      </if>
      <if test="instalmentStatus != null">
        instalment_status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="instalmentNo != null">
        #{instalmentNo,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="initPlanAmount != null">
        #{initPlanAmount,jdbcType=INTEGER},
      </if>
      <if test="planAmount != null">
        #{planAmount,jdbcType=INTEGER},
      </if>
      <if test="actualAmount != null">
        #{actualAmount,jdbcType=INTEGER},
      </if>
      <if test="instalmentStatus != null">
        #{instalmentStatus,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.OrderInstalmentExample" resultType="java.lang.Long">
    select count(*) from order_instalment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_instalment
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.instalmentNo != null">
        instalment_no = #{record.instalmentNo,jdbcType=INTEGER},
      </if>
      <if test="record.startDate != null">
        start_date = #{record.startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.initPlanAmount != null">
        init_plan_amount = #{record.initPlanAmount,jdbcType=INTEGER},
      </if>
      <if test="record.planAmount != null">
        plan_amount = #{record.planAmount,jdbcType=INTEGER},
      </if>
      <if test="record.actualAmount != null">
        actual_amount = #{record.actualAmount,jdbcType=INTEGER},
      </if>
      <if test="record.instalmentStatus != null">
        instalment_status = #{record.instalmentStatus,jdbcType=TINYINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_instalment
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      instalment_no = #{record.instalmentNo,jdbcType=INTEGER},
      start_date = #{record.startDate,jdbcType=TIMESTAMP},
      end_date = #{record.endDate,jdbcType=TIMESTAMP},
      init_plan_amount = #{record.initPlanAmount,jdbcType=INTEGER},
      plan_amount = #{record.planAmount,jdbcType=INTEGER},
      actual_amount = #{record.actualAmount,jdbcType=INTEGER},
      instalment_status = #{record.instalmentStatus,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.OrderInstalment">
    update order_instalment
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="instalmentNo != null">
        instalment_no = #{instalmentNo,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="initPlanAmount != null">
        init_plan_amount = #{initPlanAmount,jdbcType=INTEGER},
      </if>
      <if test="planAmount != null">
        plan_amount = #{planAmount,jdbcType=INTEGER},
      </if>
      <if test="actualAmount != null">
        actual_amount = #{actualAmount,jdbcType=INTEGER},
      </if>
      <if test="instalmentStatus != null">
        instalment_status = #{instalmentStatus,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.OrderInstalment">
    update order_instalment
    set order_id = #{orderId,jdbcType=BIGINT},
      instalment_no = #{instalmentNo,jdbcType=INTEGER},
      start_date = #{startDate,jdbcType=TIMESTAMP},
      end_date = #{endDate,jdbcType=TIMESTAMP},
      init_plan_amount = #{initPlanAmount,jdbcType=INTEGER},
      plan_amount = #{planAmount,jdbcType=INTEGER},
      actual_amount = #{actualAmount,jdbcType=INTEGER},
      instalment_status = #{instalmentStatus,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_instalment
    (order_id, instalment_no, start_date, end_date, init_plan_amount, plan_amount, actual_amount, instalment_status,
      remark, last_ver, op_user_id, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=BIGINT}, #{item.instalmentNo,jdbcType=INTEGER}, #{item.startDate,jdbcType=TIMESTAMP}, 
        #{item.endDate,jdbcType=TIMESTAMP}, #{item.initPlanAmount,jdbcType=INTEGER}, #{item.planAmount,jdbcType=INTEGER}, #{item.actualAmount,jdbcType=INTEGER},
        #{item.instalmentStatus,jdbcType=TINYINT}, #{item.remark,jdbcType=VARCHAR}, #{item.lastVer,jdbcType=INTEGER}, 
        #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_instalment (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'instalment_no'.toString() == column.value">
          #{item.instalmentNo,jdbcType=INTEGER}
        </if>
        <if test="'start_date'.toString() == column.value">
          #{item.startDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'end_date'.toString() == column.value">
          #{item.endDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'init_plan_amount'.toString() == column.value">
          #{item.initPlanAmount,jdbcType=INTEGER}
        </if>
        <if test="'plan_amount'.toString() == column.value">
          #{item.planAmount,jdbcType=INTEGER}
        </if>
        <if test="'actual_amount'.toString() == column.value">
          #{item.actualAmount,jdbcType=INTEGER}
        </if>
        <if test="'instalment_status'.toString() == column.value">
          #{item.instalmentStatus,jdbcType=TINYINT}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.InvoiceInfoMapper">
    <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.InvoiceDetailInfo">
        <!--@mbg.generated-->
        <!--@Table invoice_detail_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode"/>
        <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo"/>
        <result column="invoice_amount" jdbcType="BIGINT" property="invoiceAmount"/>
        <result column="invoice_status" jdbcType="TINYINT" property="invoiceStatus"/>
        <result column="invoice_type" jdbcType="TINYINT" property="invoiceType"/>
        <result column="invoice_body" jdbcType="VARCHAR" property="invoiceBody"/>
        <result column="invoice_title" jdbcType="VARCHAR" property="invoiceTitle"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="tax_rate" jdbcType="DECIMAL" property="taxRate"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="uscc" jdbcType="VARCHAR" property="uscc"/>
        <result column="open_bank" jdbcType="VARCHAR" property="openBank"/>
        <result column="open_account" jdbcType="VARCHAR" property="openAccount"/>
        <result column="register_address" jdbcType="VARCHAR" property="registerAddress"/>
        <result column="register_phone" jdbcType="VARCHAR" property="registerPhone"/>
        <result column="bill_link_address" jdbcType="VARCHAR" property="billLinkAddress"/>
        <result column="dishonor_reason" jdbcType="VARCHAR" property="dishonorReason"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="op_time" jdbcType="BIGINT" property="opTime"/>
        <result column="last_ver" jdbcType="INTEGER" property="lastVer"/>
        <result column="red_application_id" jdbcType="VARCHAR" property="redApplicationId"/>
        <result column="red_application_no" jdbcType="VARCHAR" property="redApplicationNo"/>
        <result column="serial_no" jdbcType="VARCHAR" property="serialNo"/>
        <result column="merchant_id" jdbcType="BIGINT" property="merchantId"/>
        <result column="invoice_nature" jdbcType="TINYINT" property="invoiceNature"/>
        <result column="old_invoice_code" jdbcType="VARCHAR" property="oldInvoiceCode"/>
        <result column="old_invoice_no" jdbcType="VARCHAR" property="oldInvoiceNo"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, invoice_code, invoice_no, invoice_amount, invoice_status, invoice_type, invoice_body,
        invoice_title, content, tax_rate, email, remark, uscc, open_bank, open_account, register_address,
        register_phone, bill_link_address, dishonor_reason, create_time, op_time, last_ver,
        red_application_id, merchant_id, invoice_nature, old_invoice_code, old_invoice_no,serial_no,red_application_no
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from invoice_detail_info
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from invoice_detail_info
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ql.rent.entity.trade.InvoiceDetailInfo"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into invoice_detail_info (invoice_code, invoice_no, invoice_amount,
        invoice_status, invoice_type, invoice_body,
        invoice_title, content, tax_rate,
        email, remark, uscc,
        open_bank, open_account, register_address,
        register_phone, bill_link_address, dishonor_reason,
        create_time, op_time, last_ver,
        red_application_id, merchant_id, invoice_nature,
        old_invoice_code, old_invoice_no,red_application_no,serial_no)
        values (#{invoiceCode,jdbcType=VARCHAR}, #{invoiceNo,jdbcType=VARCHAR}, #{invoiceAmount,jdbcType=BIGINT},
        #{invoiceStatus,jdbcType=TINYINT}, #{invoiceType,jdbcType=TINYINT}, #{invoiceBody,jdbcType=VARCHAR},
        #{invoiceTitle,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{taxRate,jdbcType=DECIMAL},
        #{email,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{uscc,jdbcType=VARCHAR},
        #{openBank,jdbcType=VARCHAR}, #{openAccount,jdbcType=VARCHAR}, #{registerAddress,jdbcType=VARCHAR},
        #{registerPhone,jdbcType=VARCHAR}, #{billLinkAddress,jdbcType=VARCHAR}, #{dishonorReason,jdbcType=VARCHAR},
        #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, #{lastVer,jdbcType=INTEGER},
        #{redApplicationId,jdbcType=VARCHAR}, #{merchantId,jdbcType=BIGINT}, #{invoiceNature,jdbcType=TINYINT},
        #{oldInvoiceCode,jdbcType=VARCHAR}, #{oldInvoiceNo,jdbcType=VARCHAR}, #{redApplicationNo,jdbcType=VARCHAR},
        #{serialNo,jdbcType=VARCHAR})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ql.rent.entity.trade.InvoiceDetailInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into invoice_detail_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceNo != null">
                invoice_no,
            </if>
            <if test="invoiceCode != null">
                invoice_code,
            </if>
            <if test="invoiceAmount != null">
                invoice_amount,
            </if>
            <if test="invoiceStatus != null">
                invoice_status,
            </if>
            <if test="invoiceType != null">
                invoice_type,
            </if>
            <if test="invoiceBody != null">
                invoice_body,
            </if>
            <if test="invoiceTitle != null">
                invoice_title,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="taxRate != null">
                tax_rate,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="uscc != null">
                uscc,
            </if>
            <if test="openBank != null">
                open_bank,
            </if>
            <if test="openAccount != null">
                open_account,
            </if>
            <if test="registerAddress != null">
                register_address,
            </if>
            <if test="registerPhone != null">
                register_phone,
            </if>
            <if test="billLinkAddress != null">
                bill_link_address,
            </if>
            <if test="dishonorReason != null">
                dishonor_reason,
            </if>
            <if test="merchantId != null">
                merchant_id,
            </if>
            <if test="invoiceNature != null">
                invoice_nature,
            </if>
            <if test="oldInvoiceCode != null">
                old_invoice_code,
            </if>
            <if test="oldInvoiceNo != null">
                old_invoice_no,
            </if>
            <if test="redApplicationId != null">
                red_application_id,
            </if>
            <if test="redApplicationNo != null">
                red_application_no,
            </if>
            <if test="serialNo != null">
                serial_no,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="opTime != null">
                op_time,
            </if>
            <if test="lastVer != null">
                last_ver,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="invoiceNo != null">
                #{invoiceNo,jdbcType=VARCHAR},
            </if>
            <if test="invoiceCode != null">
                #{invoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="invoiceAmount != null">
                #{invoiceAmount,jdbcType=BIGINT},
            </if>
            <if test="invoiceStatus != null">
                #{invoiceStatus,jdbcType=TINYINT},
            </if>
            <if test="invoiceType != null">
                #{invoiceType,jdbcType=TINYINT},
            </if>
            <if test="invoiceBody != null">
                #{invoiceBody,jdbcType=VARCHAR},
            </if>
            <if test="invoiceTitle != null">
                #{invoiceTitle,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="taxRate != null">
                #{taxRate,jdbcType=DECIMAL},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="uscc != null">
                #{uscc,jdbcType=VARCHAR},
            </if>
            <if test="openBank != null">
                #{openBank,jdbcType=VARCHAR},
            </if>
            <if test="openAccount != null">
                #{openAccount,jdbcType=VARCHAR},
            </if>
            <if test="registerAddress != null">
                #{registerAddress,jdbcType=VARCHAR},
            </if>
            <if test="registerPhone != null">
                #{registerPhone,jdbcType=VARCHAR},
            </if>
            <if test="billLinkAddress != null">
                #{billLinkAddress,jdbcType=VARCHAR},
            </if>
            <if test="dishonorReason != null">
                #{dishonorReason,jdbcType=VARCHAR},
            </if>
            <if test="merchantId != null">
                #{merchantId,jdbcType=BIGINT},
            </if>
            <if test="invoiceNature != null">
                #{invoiceNature,jdbcType=TINYINT},
            </if>
            <if test="oldInvoiceCode != null">
                #{oldInvoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="oldInvoiceNo != null">
                #{oldInvoiceNo,jdbcType=VARCHAR},
            </if>
            <if test="redApplicationId != null">
                #{redApplicationId,jdbcType=VARCHAR},
            </if>
            <if test="redApplicationNo != null">
                #{redApplicationNo,jdbcType=VARCHAR},
            </if>
            <if test="serialNo != null">
                #{serialNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="opTime != null">
                #{opTime,jdbcType=BIGINT},
            </if>
            <if test="lastVer != null">
                #{lastVer,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.InvoiceDetailInfo">
        <!--@mbg.generated-->
        update invoice_detail_info
        set invoice_code = #{invoiceCode,jdbcType=VARCHAR},
        invoice_no = #{invoiceNo,jdbcType=VARCHAR},
        invoice_amount = #{invoiceAmount,jdbcType=BIGINT},
        invoice_status = #{invoiceStatus,jdbcType=TINYINT},
        invoice_type = #{invoiceType,jdbcType=TINYINT},
        invoice_body = #{invoiceBody,jdbcType=VARCHAR},
        invoice_title = #{invoiceTitle,jdbcType=VARCHAR},
        content = #{content,jdbcType=VARCHAR},
        tax_rate = #{taxRate,jdbcType=DECIMAL},
        email = #{email,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        uscc = #{uscc,jdbcType=VARCHAR},
        open_bank = #{openBank,jdbcType=VARCHAR},
        open_account = #{openAccount,jdbcType=VARCHAR},
        register_address = #{registerAddress,jdbcType=VARCHAR},
        register_phone = #{registerPhone,jdbcType=VARCHAR},
        bill_link_address = #{billLinkAddress,jdbcType=VARCHAR},
        dishonor_reason = #{dishonorReason,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=BIGINT},
        op_time = #{opTime,jdbcType=BIGINT},
        last_ver = #{lastVer,jdbcType=INTEGER},
        merchant_id = #{merchantId,jdbcType=BIGINT},
        invoice_nature = #{invoiceNature,jdbcType=TINYINT},
        old_invoice_code = #{oldInvoiceCode,jdbcType=VARCHAR},
        serial_no = #{serialNo,jdbcType=VARCHAR},
        red_application_id = #{redApplicationId,jdbcType=VARCHAR},
        old_invoice_no = #{oldInvoiceNo,jdbcType=VARCHAR},
        red_application_no = #{redApplicationNo,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.InvoiceDetailInfo">
        <!--@mbg.generated-->
        update invoice_detail_info
        <set>
            <if test="invoiceCode != null">
                invoice_code = #{invoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="invoiceNo != null">
                invoice_no = #{invoiceNo,jdbcType=VARCHAR},
            </if>
            <if test="invoiceAmount != null">
                invoice_amount = #{invoiceAmount,jdbcType=BIGINT},
            </if>
            <if test="invoiceStatus != null">
                invoice_status = #{invoiceStatus,jdbcType=TINYINT},
            </if>
            <if test="invoiceType != null">
                invoice_type = #{invoiceType,jdbcType=TINYINT},
            </if>
            <if test="invoiceBody != null">
                invoice_body = #{invoiceBody,jdbcType=VARCHAR},
            </if>
            <if test="invoiceTitle != null">
                invoice_title = #{invoiceTitle,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="taxRate != null">
                tax_rate = #{taxRate,jdbcType=DECIMAL},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="uscc != null">
                uscc = #{uscc,jdbcType=VARCHAR},
            </if>
            <if test="openBank != null">
                open_bank = #{openBank,jdbcType=VARCHAR},
            </if>
            <if test="openAccount != null">
                open_account = #{openAccount,jdbcType=VARCHAR},
            </if>
            <if test="registerAddress != null">
                register_address = #{registerAddress,jdbcType=VARCHAR},
            </if>
            <if test="registerPhone != null">
                register_phone = #{registerPhone,jdbcType=VARCHAR},
            </if>
            <if test="billLinkAddress != null">
                bill_link_address = #{billLinkAddress,jdbcType=VARCHAR},
            </if>
            <if test="dishonorReason != null">
                dishonor_reason = #{dishonorReason,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="opTime != null">
                op_time = #{opTime,jdbcType=BIGINT},
            </if>
            <if test="lastVer != null">
                last_ver = #{lastVer,jdbcType=INTEGER},
            </if>
            <if test="redApplicationId != null">
                red_application_id = #{redApplicationId,jdbcType=VARCHAR},
            </if>
            <if test="merchantId != null">
                merchant_id = #{merchantId,jdbcType=BIGINT},
            </if>
            <if test="invoiceNature != null">
                invoice_nature = #{invoiceNature,jdbcType=TINYINT},
            </if>
            <if test="oldInvoiceCode != null">
                old_invoice_code = #{oldInvoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="oldInvoiceNo != null">
                old_invoice_no = #{oldInvoiceNo,jdbcType=VARCHAR},
            </if>
            <if test="redApplicationNo != null">
                red_application_no = #{redApplicationNo,jdbcType=VARCHAR},
            </if>
            <if test="serialNo != null">
                serial_no = #{serialNo,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="countByQuery" resultType="java.lang.Integer" parameterType="com.ql.rent.param.trade.InvoiceInfoQuery">
        select count(id)
        from invoice_detail_info
        where 1 = 1
        <if test="startTime != null">
            <![CDATA[
            and create_time >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[
            and create_time <= #{endTime}
            ]]>
        </if>
        <if test="merchantId != null">
            and merchant_id = #{merchantId}
        </if>
        <if test="invoiceNo != null">
            and invoice_no = #{invoiceNo}
        </if>
        <if test="invoiceType != null">
            and invoice_type = #{invoiceType}
        </if>
    </select>

    <select id="selectByQuery" parameterType="com.ql.rent.param.trade.InvoiceInfoQuery" resultMap="BaseResultMap">
        select *
        from invoice_detail_info
        where 1 = 1
        <if test="startTime != null">
            <![CDATA[
            and create_time >= #{startTime}
            ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[
            and create_time <= #{endTime}
            ]]>
        </if>
        <if test="invoiceNo != null">
            and invoice_no = #{invoiceNo}
        </if>
        <if test="merchantId != null">
            and merchant_id = #{merchantId}
        </if>
        <if test="invoiceType != null">
            and invoice_type = #{invoiceType}
        </if>
        order by create_time desc
        <if test="pageIndex != null and pageSize != null">
            limit #{startPos},#{pageSize}
        </if>
    </select>

    <select id="getWaitRedList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from invoice_detail_info
        where invoice_type in (7, 8)
        and invoice_status = 3
        and red_application_id is not null
    </select>

    <select id="selectByInvoiceNo" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from invoice_detail_info
        where invoice_no = #{invoiceNo,jdbcType=VARCHAR}
    </select>

    <select id="selectByOldInvoiceNo" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from invoice_detail_info
        where old_invoice_no = #{oldInvoiceNo,jdbcType=VARCHAR}
    </select>

    <select id="selectByRedApplicationNo" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from invoice_detail_info
        where red_application_no = #{redApplicationNo,jdbcType=VARCHAR}
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.OrderSelfPickReturnOperationMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.OrderSelfPickReturnOperation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="device_no" jdbcType="VARCHAR" property="deviceNo" />
    <result column="op_type" jdbcType="TINYINT" property="opType" />
    <result column="start_time" jdbcType="BIGINT" property="startTime" />
    <result column="end_time" jdbcType="BIGINT" property="endTime" />
    <result column="energy_type" jdbcType="TINYINT" property="energyType" />
    <result column="max_oil_liter" jdbcType="INTEGER" property="maxOilLiter" />
    <result column="oil_liter" jdbcType="DECIMAL" property="oilLiter" />
    <result column="oil_percent" jdbcType="DECIMAL" property="oilPercent" />
    <result column="oil_unit_price" jdbcType="BIGINT" property="oilUnitPrice" />
    <result column="max_mileage" jdbcType="INTEGER" property="maxMileage" />
    <result column="remind_mileage" jdbcType="INTEGER" property="remindMileage" />
    <result column="mileage_unit_price" jdbcType="BIGINT" property="mileageUnitPrice" />
    <result column="battery_percent" jdbcType="DECIMAL" property="batteryPercent" />
    <result column="servicePrice" jdbcType="BIGINT" property="serviceprice" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, vehicle_id, vehicle_model_id, device_no, op_type, start_time, end_time, 
    energy_type, max_oil_liter, oil_liter, oil_percent, oil_unit_price, max_mileage, 
    remind_mileage, mileage_unit_price, battery_percent, servicePrice, extra, deleted, 
    op_user_id, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.OrderSelfPickReturnOperationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from order_self_pick_return_operation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_self_pick_return_operation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_self_pick_return_operation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.OrderSelfPickReturnOperationExample">
    delete from order_self_pick_return_operation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.OrderSelfPickReturnOperation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_self_pick_return_operation (order_id, vehicle_id, vehicle_model_id, 
      device_no, op_type, start_time, 
      end_time, energy_type, max_oil_liter, 
      oil_liter, oil_percent, oil_unit_price, 
      max_mileage, remind_mileage, mileage_unit_price, 
      battery_percent, servicePrice, extra, 
      deleted, op_user_id, create_time, 
      op_time)
    values (#{orderId,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, #{vehicleModelId,jdbcType=BIGINT}, 
      #{deviceNo,jdbcType=VARCHAR}, #{opType,jdbcType=TINYINT}, #{startTime,jdbcType=BIGINT}, 
      #{endTime,jdbcType=BIGINT}, #{energyType,jdbcType=TINYINT}, #{maxOilLiter,jdbcType=INTEGER}, 
      #{oilLiter,jdbcType=DECIMAL}, #{oilPercent,jdbcType=DECIMAL}, #{oilUnitPrice,jdbcType=BIGINT}, 
      #{maxMileage,jdbcType=INTEGER}, #{remindMileage,jdbcType=INTEGER}, #{mileageUnitPrice,jdbcType=BIGINT}, 
      #{batteryPercent,jdbcType=DECIMAL}, #{serviceprice,jdbcType=BIGINT}, #{extra,jdbcType=VARCHAR}, 
      #{deleted,jdbcType=TINYINT}, #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.OrderSelfPickReturnOperation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_self_pick_return_operation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="deviceNo != null">
        device_no,
      </if>
      <if test="opType != null">
        op_type,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="energyType != null">
        energy_type,
      </if>
      <if test="maxOilLiter != null">
        max_oil_liter,
      </if>
      <if test="oilLiter != null">
        oil_liter,
      </if>
      <if test="oilPercent != null">
        oil_percent,
      </if>
      <if test="oilUnitPrice != null">
        oil_unit_price,
      </if>
      <if test="maxMileage != null">
        max_mileage,
      </if>
      <if test="remindMileage != null">
        remind_mileage,
      </if>
      <if test="mileageUnitPrice != null">
        mileage_unit_price,
      </if>
      <if test="batteryPercent != null">
        battery_percent,
      </if>
      <if test="serviceprice != null">
        servicePrice,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="deviceNo != null">
        #{deviceNo,jdbcType=VARCHAR},
      </if>
      <if test="opType != null">
        #{opType,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=BIGINT},
      </if>
      <if test="energyType != null">
        #{energyType,jdbcType=TINYINT},
      </if>
      <if test="maxOilLiter != null">
        #{maxOilLiter,jdbcType=INTEGER},
      </if>
      <if test="oilLiter != null">
        #{oilLiter,jdbcType=DECIMAL},
      </if>
      <if test="oilPercent != null">
        #{oilPercent,jdbcType=DECIMAL},
      </if>
      <if test="oilUnitPrice != null">
        #{oilUnitPrice,jdbcType=BIGINT},
      </if>
      <if test="maxMileage != null">
        #{maxMileage,jdbcType=INTEGER},
      </if>
      <if test="remindMileage != null">
        #{remindMileage,jdbcType=INTEGER},
      </if>
      <if test="mileageUnitPrice != null">
        #{mileageUnitPrice,jdbcType=BIGINT},
      </if>
      <if test="batteryPercent != null">
        #{batteryPercent,jdbcType=DECIMAL},
      </if>
      <if test="serviceprice != null">
        #{serviceprice,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.OrderSelfPickReturnOperationExample" resultType="java.lang.Long">
    select count(*) from order_self_pick_return_operation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_self_pick_return_operation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.deviceNo != null">
        device_no = #{record.deviceNo,jdbcType=VARCHAR},
      </if>
      <if test="record.opType != null">
        op_type = #{record.opType,jdbcType=TINYINT},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=BIGINT},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=BIGINT},
      </if>
      <if test="record.energyType != null">
        energy_type = #{record.energyType,jdbcType=TINYINT},
      </if>
      <if test="record.maxOilLiter != null">
        max_oil_liter = #{record.maxOilLiter,jdbcType=INTEGER},
      </if>
      <if test="record.oilLiter != null">
        oil_liter = #{record.oilLiter,jdbcType=DECIMAL},
      </if>
      <if test="record.oilPercent != null">
        oil_percent = #{record.oilPercent,jdbcType=DECIMAL},
      </if>
      <if test="record.oilUnitPrice != null">
        oil_unit_price = #{record.oilUnitPrice,jdbcType=BIGINT},
      </if>
      <if test="record.maxMileage != null">
        max_mileage = #{record.maxMileage,jdbcType=INTEGER},
      </if>
      <if test="record.remindMileage != null">
        remind_mileage = #{record.remindMileage,jdbcType=INTEGER},
      </if>
      <if test="record.mileageUnitPrice != null">
        mileage_unit_price = #{record.mileageUnitPrice,jdbcType=BIGINT},
      </if>
      <if test="record.batteryPercent != null">
        battery_percent = #{record.batteryPercent,jdbcType=DECIMAL},
      </if>
      <if test="record.serviceprice != null">
        servicePrice = #{record.serviceprice,jdbcType=BIGINT},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_self_pick_return_operation
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      device_no = #{record.deviceNo,jdbcType=VARCHAR},
      op_type = #{record.opType,jdbcType=TINYINT},
      start_time = #{record.startTime,jdbcType=BIGINT},
      end_time = #{record.endTime,jdbcType=BIGINT},
      energy_type = #{record.energyType,jdbcType=TINYINT},
      max_oil_liter = #{record.maxOilLiter,jdbcType=INTEGER},
      oil_liter = #{record.oilLiter,jdbcType=DECIMAL},
      oil_percent = #{record.oilPercent,jdbcType=DECIMAL},
      oil_unit_price = #{record.oilUnitPrice,jdbcType=BIGINT},
      max_mileage = #{record.maxMileage,jdbcType=INTEGER},
      remind_mileage = #{record.remindMileage,jdbcType=INTEGER},
      mileage_unit_price = #{record.mileageUnitPrice,jdbcType=BIGINT},
      battery_percent = #{record.batteryPercent,jdbcType=DECIMAL},
      servicePrice = #{record.serviceprice,jdbcType=BIGINT},
      extra = #{record.extra,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.OrderSelfPickReturnOperation">
    update order_self_pick_return_operation
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="deviceNo != null">
        device_no = #{deviceNo,jdbcType=VARCHAR},
      </if>
      <if test="opType != null">
        op_type = #{opType,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=BIGINT},
      </if>
      <if test="energyType != null">
        energy_type = #{energyType,jdbcType=TINYINT},
      </if>
      <if test="maxOilLiter != null">
        max_oil_liter = #{maxOilLiter,jdbcType=INTEGER},
      </if>
      <if test="oilLiter != null">
        oil_liter = #{oilLiter,jdbcType=DECIMAL},
      </if>
      <if test="oilPercent != null">
        oil_percent = #{oilPercent,jdbcType=DECIMAL},
      </if>
      <if test="oilUnitPrice != null">
        oil_unit_price = #{oilUnitPrice,jdbcType=BIGINT},
      </if>
      <if test="maxMileage != null">
        max_mileage = #{maxMileage,jdbcType=INTEGER},
      </if>
      <if test="remindMileage != null">
        remind_mileage = #{remindMileage,jdbcType=INTEGER},
      </if>
      <if test="mileageUnitPrice != null">
        mileage_unit_price = #{mileageUnitPrice,jdbcType=BIGINT},
      </if>
      <if test="batteryPercent != null">
        battery_percent = #{batteryPercent,jdbcType=DECIMAL},
      </if>
      <if test="serviceprice != null">
        servicePrice = #{serviceprice,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.OrderSelfPickReturnOperation">
    update order_self_pick_return_operation
    set order_id = #{orderId,jdbcType=BIGINT},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      device_no = #{deviceNo,jdbcType=VARCHAR},
      op_type = #{opType,jdbcType=TINYINT},
      start_time = #{startTime,jdbcType=BIGINT},
      end_time = #{endTime,jdbcType=BIGINT},
      energy_type = #{energyType,jdbcType=TINYINT},
      max_oil_liter = #{maxOilLiter,jdbcType=INTEGER},
      oil_liter = #{oilLiter,jdbcType=DECIMAL},
      oil_percent = #{oilPercent,jdbcType=DECIMAL},
      oil_unit_price = #{oilUnitPrice,jdbcType=BIGINT},
      max_mileage = #{maxMileage,jdbcType=INTEGER},
      remind_mileage = #{remindMileage,jdbcType=INTEGER},
      mileage_unit_price = #{mileageUnitPrice,jdbcType=BIGINT},
      battery_percent = #{batteryPercent,jdbcType=DECIMAL},
      servicePrice = #{serviceprice,jdbcType=BIGINT},
      extra = #{extra,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_self_pick_return_operation
    (order_id, vehicle_id, vehicle_model_id, device_no, op_type, start_time, end_time, 
      energy_type, max_oil_liter, oil_liter, oil_percent, oil_unit_price, max_mileage, 
      remind_mileage, mileage_unit_price, battery_percent, servicePrice, extra, deleted, 
      op_user_id, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=BIGINT}, #{item.vehicleId,jdbcType=BIGINT}, #{item.vehicleModelId,jdbcType=BIGINT}, 
        #{item.deviceNo,jdbcType=VARCHAR}, #{item.opType,jdbcType=TINYINT}, #{item.startTime,jdbcType=BIGINT}, 
        #{item.endTime,jdbcType=BIGINT}, #{item.energyType,jdbcType=TINYINT}, #{item.maxOilLiter,jdbcType=INTEGER}, 
        #{item.oilLiter,jdbcType=DECIMAL}, #{item.oilPercent,jdbcType=DECIMAL}, #{item.oilUnitPrice,jdbcType=BIGINT}, 
        #{item.maxMileage,jdbcType=INTEGER}, #{item.remindMileage,jdbcType=INTEGER}, #{item.mileageUnitPrice,jdbcType=BIGINT}, 
        #{item.batteryPercent,jdbcType=DECIMAL}, #{item.serviceprice,jdbcType=BIGINT}, 
        #{item.extra,jdbcType=VARCHAR}, #{item.deleted,jdbcType=TINYINT}, #{item.opUserId,jdbcType=BIGINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_self_pick_return_operation (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_id'.toString() == column.value">
          #{item.vehicleId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'device_no'.toString() == column.value">
          #{item.deviceNo,jdbcType=VARCHAR}
        </if>
        <if test="'op_type'.toString() == column.value">
          #{item.opType,jdbcType=TINYINT}
        </if>
        <if test="'start_time'.toString() == column.value">
          #{item.startTime,jdbcType=BIGINT}
        </if>
        <if test="'end_time'.toString() == column.value">
          #{item.endTime,jdbcType=BIGINT}
        </if>
        <if test="'energy_type'.toString() == column.value">
          #{item.energyType,jdbcType=TINYINT}
        </if>
        <if test="'max_oil_liter'.toString() == column.value">
          #{item.maxOilLiter,jdbcType=INTEGER}
        </if>
        <if test="'oil_liter'.toString() == column.value">
          #{item.oilLiter,jdbcType=DECIMAL}
        </if>
        <if test="'oil_percent'.toString() == column.value">
          #{item.oilPercent,jdbcType=DECIMAL}
        </if>
        <if test="'oil_unit_price'.toString() == column.value">
          #{item.oilUnitPrice,jdbcType=BIGINT}
        </if>
        <if test="'max_mileage'.toString() == column.value">
          #{item.maxMileage,jdbcType=INTEGER}
        </if>
        <if test="'remind_mileage'.toString() == column.value">
          #{item.remindMileage,jdbcType=INTEGER}
        </if>
        <if test="'mileage_unit_price'.toString() == column.value">
          #{item.mileageUnitPrice,jdbcType=BIGINT}
        </if>
        <if test="'battery_percent'.toString() == column.value">
          #{item.batteryPercent,jdbcType=DECIMAL}
        </if>
        <if test="'servicePrice'.toString() == column.value">
          #{item.serviceprice,jdbcType=BIGINT}
        </if>
        <if test="'extra'.toString() == column.value">
          #{item.extra,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.OrderDetailMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.OrderDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="service_id" jdbcType="BIGINT" property="serviceId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="quantity" jdbcType="DOUBLE" property="quantity" />
    <result column="price" jdbcType="INTEGER" property="price" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="pay_mode" jdbcType="TINYINT" property="payMode" />
    <result column="pay_Kind" jdbcType="TINYINT" property="payKind" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="pay_status" jdbcType="TINYINT" property="payStatus" />
    <result column="source_order_id" jdbcType="VARCHAR" property="sourceOrderId" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="order_time" jdbcType="BIGINT" property="orderTime" />
    <result column="order_op_time" jdbcType="BIGINT" property="orderOpTime" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="parent_order_id" jdbcType="BIGINT" property="parentOrderId" />
    <result column="order_source" jdbcType="TINYINT" property="orderSource" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, store_id, service_id, name, quantity, price, amount, pay_mode, pay_kind, order_type,
    order_status, pay_status, source_order_id, extra, last_ver, op_user_id, order_time, 
    order_op_time, create_time, op_time, parent_order_id, order_source
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.OrderDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.OrderDetailExample">
    delete from order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.OrderDetail">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_detail (order_id, store_id, service_id, 
      name, quantity, price, 
      amount, pay_mode, pay_kind, order_type,
      order_status, pay_status, source_order_id, 
      extra, last_ver, op_user_id, 
      order_time, order_op_time, create_time, 
      op_time, parent_order_id, order_source
      )
    values (#{orderId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{serviceId,jdbcType=BIGINT}, 
      #{name,jdbcType=VARCHAR}, #{quantity,jdbcType=DOUBLE}, #{price,jdbcType=INTEGER}, 
      #{amount,jdbcType=INTEGER}, #{payMode,jdbcType=TINYINT}, #{payKind,jdbcType=TINYINT},#{orderType,jdbcType=TINYINT},
      #{orderStatus,jdbcType=TINYINT}, #{payStatus,jdbcType=TINYINT}, #{sourceOrderId,jdbcType=VARCHAR}, 
      #{extra,jdbcType=VARCHAR}, #{lastVer,jdbcType=INTEGER}, #{opUserId,jdbcType=BIGINT}, 
      #{orderTime,jdbcType=BIGINT}, #{orderOpTime,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{parentOrderId,jdbcType=BIGINT}, #{orderSource,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.OrderDetail">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="payMode != null">
        pay_mode,
      </if>
      <if test="payKind != null">
        pay_kind,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="payStatus != null">
        pay_status,
      </if>
      <if test="sourceOrderId != null">
        source_order_id,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="orderTime != null">
        order_time,
      </if>
      <if test="orderOpTime != null">
        order_op_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="parentOrderId != null">
        parent_order_id,
      </if>
      <if test="orderSource != null">
        order_source,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=DOUBLE},
      </if>
      <if test="price != null">
        #{price,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="payMode != null">
        #{payMode,jdbcType=TINYINT},
      </if>
      <if test="payKind != null">
        #{payKind,jdbcType=TINYINT},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=TINYINT},
      </if>
      <if test="sourceOrderId != null">
        #{sourceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="orderTime != null">
        #{orderTime,jdbcType=BIGINT},
      </if>
      <if test="orderOpTime != null">
        #{orderOpTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="parentOrderId != null">
        #{parentOrderId,jdbcType=BIGINT},
      </if>
      <if test="orderSource != null">
        #{orderSource,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.OrderDetailExample" resultType="java.lang.Long">
    select count(*) from order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.serviceId != null">
        service_id = #{record.serviceId,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=DOUBLE},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=INTEGER},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=INTEGER},
      </if>
      <if test="record.payMode != null">
        pay_mode = #{record.payMode,jdbcType=TINYINT},
      </if>
      <if test="record.payKind != null">
        pay_kind = #{record.payKind,jdbcType=TINYINT},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=TINYINT},
      </if>
      <if test="record.orderStatus != null">
        order_status = #{record.orderStatus,jdbcType=TINYINT},
      </if>
      <if test="record.payStatus != null">
        pay_status = #{record.payStatus,jdbcType=TINYINT},
      </if>
      <if test="record.sourceOrderId != null">
        source_order_id = #{record.sourceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.orderTime != null">
        order_time = #{record.orderTime,jdbcType=BIGINT},
      </if>
      <if test="record.orderOpTime != null">
        order_op_time = #{record.orderOpTime,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.parentOrderId != null">
        parent_order_id = #{record.parentOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.orderSource != null">
        order_source = #{record.orderSource,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_detail
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      service_id = #{record.serviceId,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      quantity = #{record.quantity,jdbcType=DOUBLE},
      price = #{record.price,jdbcType=INTEGER},
      amount = #{record.amount,jdbcType=INTEGER},
      pay_mode = #{record.payMode,jdbcType=TINYINT},
      pay_kind = #{record.payKind,jdbcType=TINYINT},
      order_type = #{record.orderType,jdbcType=TINYINT},
      order_status = #{record.orderStatus,jdbcType=TINYINT},
      pay_status = #{record.payStatus,jdbcType=TINYINT},
      source_order_id = #{record.sourceOrderId,jdbcType=VARCHAR},
      extra = #{record.extra,jdbcType=VARCHAR},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      order_time = #{record.orderTime,jdbcType=BIGINT},
      order_op_time = #{record.orderOpTime,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      parent_order_id = #{record.parentOrderId,jdbcType=BIGINT},
      order_source = #{record.orderSource,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.OrderDetail">
    update order_detail
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=DOUBLE},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=INTEGER},
      </if>
      <if test="payMode != null">
        pay_mode = #{payMode,jdbcType=TINYINT},
      </if>
      <if test="payKind!= null">
        pay_kind = #{payKind,jdbcType=TINYINT},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="payStatus != null">
        pay_status = #{payStatus,jdbcType=TINYINT},
      </if>
      <if test="sourceOrderId != null">
        source_order_id = #{sourceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="orderTime != null">
        order_time = #{orderTime,jdbcType=BIGINT},
      </if>
      <if test="orderOpTime != null">
        order_op_time = #{orderOpTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="parentOrderId != null">
        parent_order_id = #{parentOrderId,jdbcType=BIGINT},
      </if>
      <if test="orderSource != null">
        order_source = #{orderSource,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.OrderDetail">
    update order_detail
    set order_id = #{orderId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      service_id = #{serviceId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      quantity = #{quantity,jdbcType=DOUBLE},
      price = #{price,jdbcType=INTEGER},
      amount = #{amount,jdbcType=INTEGER},
      pay_mode = #{payMode,jdbcType=TINYINT},
      pay_kind = #{payKind,jdbcType=TINYINT},
      order_type = #{orderType,jdbcType=TINYINT},
      order_status = #{orderStatus,jdbcType=TINYINT},
      pay_status = #{payStatus,jdbcType=TINYINT},
      source_order_id = #{sourceOrderId,jdbcType=VARCHAR},
      extra = #{extra,jdbcType=VARCHAR},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      order_time = #{orderTime,jdbcType=BIGINT},
      order_op_time = #{orderOpTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      parent_order_id = #{parentOrderId,jdbcType=BIGINT},
      order_source = #{orderSource,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_detail
    (order_id, store_id, service_id, name, quantity, price, amount, pay_mode, pay_kind, order_type,
      order_status, pay_status, source_order_id, extra, last_ver, op_user_id, order_time, 
      order_op_time, create_time, op_time, parent_order_id, order_source)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.serviceId,jdbcType=BIGINT}, 
        #{item.name,jdbcType=VARCHAR}, #{item.quantity,jdbcType=DOUBLE}, #{item.price,jdbcType=INTEGER}, 
        #{item.amount,jdbcType=INTEGER}, #{item.payMode,jdbcType=TINYINT}, #{item.payKind,jdbcType=TINYINT},#{item.orderType,jdbcType=TINYINT},
        #{item.orderStatus,jdbcType=TINYINT}, #{item.payStatus,jdbcType=TINYINT}, #{item.sourceOrderId,jdbcType=VARCHAR}, 
        #{item.extra,jdbcType=VARCHAR}, #{item.lastVer,jdbcType=INTEGER}, #{item.opUserId,jdbcType=BIGINT}, 
        #{item.orderTime,jdbcType=BIGINT}, #{item.orderOpTime,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.parentOrderId,jdbcType=BIGINT}, #{item.orderSource,jdbcType=TINYINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_detail (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'service_id'.toString() == column.value">
          #{item.serviceId,jdbcType=BIGINT}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'quantity'.toString() == column.value">
          #{item.quantity,jdbcType=DOUBLE}
        </if>
        <if test="'price'.toString() == column.value">
          #{item.price,jdbcType=INTEGER}
        </if>
        <if test="'amount'.toString() == column.value">
          #{item.amount,jdbcType=INTEGER}
        </if>
        <if test="'pay_mode'.toString() == column.value">
          #{item.payMode,jdbcType=TINYINT}
        </if>
        <if test="'pay_kind'.toString() == column.value">
          #{item.payKind,jdbcType=TINYINT}
        </if>
        <if test="'order_type'.toString() == column.value">
          #{item.orderType,jdbcType=TINYINT}
        </if>
        <if test="'order_status'.toString() == column.value">
          #{item.orderStatus,jdbcType=TINYINT}
        </if>
        <if test="'pay_status'.toString() == column.value">
          #{item.payStatus,jdbcType=TINYINT}
        </if>
        <if test="'source_order_id'.toString() == column.value">
          #{item.sourceOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'extra'.toString() == column.value">
          #{item.extra,jdbcType=VARCHAR}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'order_time'.toString() == column.value">
          #{item.orderTime,jdbcType=BIGINT}
        </if>
        <if test="'order_op_time'.toString() == column.value">
          #{item.orderOpTime,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'parent_order_id'.toString() == column.value">
          #{item.parentOrderId,jdbcType=BIGINT}
        </if>
        <if test="'order_source'.toString() == column.value">
          #{item.orderSource,jdbcType=TINYINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
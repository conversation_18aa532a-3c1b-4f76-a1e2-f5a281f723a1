<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.RerentOrderMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.RerentOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="pay_amount" jdbcType="INTEGER" property="payAmount" />
    <result column="receivable_amount" jdbcType="INTEGER" property="receivableAmount" />
    <result column="pay_mode" jdbcType="TINYINT" property="payMode" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="pay_status" jdbcType="TINYINT" property="payStatus" />
    <result column="source_order_id" jdbcType="VARCHAR" property="sourceOrderId" />
    <result column="source_inner_order_id" jdbcType="VARCHAR" property="sourceInnerOrderId" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="order_time" jdbcType="BIGINT" property="orderTime" />
    <result column="order_op_time" jdbcType="BIGINT" property="orderOpTime" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="parent_order_id" jdbcType="BIGINT" property="parentOrderId" />
    <result column="order_source" jdbcType="TINYINT" property="orderSource" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, order_id, store_id, start_time, end_time, pay_amount, receivable_amount, pay_mode,
    order_status, pay_status, source_order_id, source_inner_order_id, last_ver, op_user_id, order_time, order_op_time,
    create_time, op_time, parent_order_id, order_source, extra
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.RerentOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from rerent_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from rerent_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from rerent_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.RerentOrderExample">
    delete from rerent_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.RerentOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into rerent_order (order_no, order_id, store_id, start_time,
      end_time, pay_amount, receivable_amount, 
      pay_mode, order_status, pay_status, 
      source_order_id, source_inner_order_id, last_ver, op_user_id,
      order_time, order_op_time, create_time, 
      op_time, parent_order_id, order_source, extra
      )
    values (#{orderNo,jdbcType=VARCHAR}, #{orderId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{startTime,jdbcType=TIMESTAMP},
      #{endTime,jdbcType=TIMESTAMP}, #{payAmount,jdbcType=INTEGER}, #{receivableAmount,jdbcType=INTEGER}, 
      #{payMode,jdbcType=TINYINT}, #{orderStatus,jdbcType=TINYINT}, #{payStatus,jdbcType=TINYINT}, 
      #{sourceOrderId,jdbcType=VARCHAR}, #{sourceInnerOrderId,jdbcType=VARCHAR}, #{lastVer,jdbcType=INTEGER}, #{opUserId,jdbcType=BIGINT},
      #{orderTime,jdbcType=BIGINT}, #{orderOpTime,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{parentOrderId,jdbcType=BIGINT}, #{orderSource,jdbcType=TINYINT}, #{extra,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.RerentOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into rerent_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="payAmount != null">
        pay_amount,
      </if>
      <if test="receivableAmount != null">
        receivable_amount,
      </if>
      <if test="payMode != null">
        pay_mode,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="payStatus != null">
        pay_status,
      </if>
      <if test="sourceOrderId != null">
        source_order_id,
      </if>
      <if test="sourceInnerOrderId != null">
        source_inner_order_id,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="orderTime != null">
        order_time,
      </if>
      <if test="orderOpTime != null">
        order_op_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="parentOrderId != null">
        parent_order_id,
      </if>
      <if test="orderSource != null">
        order_source,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payAmount != null">
        #{payAmount,jdbcType=INTEGER},
      </if>
      <if test="receivableAmount != null">
        #{receivableAmount,jdbcType=INTEGER},
      </if>
      <if test="payMode != null">
        #{payMode,jdbcType=TINYINT},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=TINYINT},
      </if>
      <if test="sourceOrderId != null">
        #{sourceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="sourceInnerOrderId != null">
        #{sourceInnerOrderId,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="orderTime != null">
        #{orderTime,jdbcType=BIGINT},
      </if>
      <if test="orderOpTime != null">
        #{orderOpTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="parentOrderId != null">
        #{parentOrderId,jdbcType=BIGINT},
      </if>
      <if test="orderSource != null">
        #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.RerentOrderExample" resultType="java.lang.Long">
    select count(*) from rerent_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update rerent_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.payAmount != null">
        pay_amount = #{record.payAmount,jdbcType=INTEGER},
      </if>
      <if test="record.receivableAmount != null">
        receivable_amount = #{record.receivableAmount,jdbcType=INTEGER},
      </if>
      <if test="record.payMode != null">
        pay_mode = #{record.payMode,jdbcType=TINYINT},
      </if>
      <if test="record.orderStatus != null">
        order_status = #{record.orderStatus,jdbcType=TINYINT},
      </if>
      <if test="record.payStatus != null">
        pay_status = #{record.payStatus,jdbcType=TINYINT},
      </if>
      <if test="record.sourceOrderId != null">
        source_order_id = #{record.sourceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceInnerOrderId != null">
        source_inner_order_id = #{record.sourceInnerOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.orderTime != null">
        order_time = #{record.orderTime,jdbcType=BIGINT},
      </if>
      <if test="record.orderOpTime != null">
        order_op_time = #{record.orderOpTime,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.parentOrderId != null">
        parent_order_id = #{record.parentOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.orderSource != null">
        order_source = #{record.orderSource,jdbcType=TINYINT},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update rerent_order
    set id = #{record.id,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      pay_amount = #{record.payAmount,jdbcType=INTEGER},
      receivable_amount = #{record.receivableAmount,jdbcType=INTEGER},
      pay_mode = #{record.payMode,jdbcType=TINYINT},
      order_status = #{record.orderStatus,jdbcType=TINYINT},
      pay_status = #{record.payStatus,jdbcType=TINYINT},
      source_order_id = #{record.sourceOrderId,jdbcType=VARCHAR},
      source_inner_order_id = #{record.sourceInnerOrderId,jdbcType=VARCHAR},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      order_time = #{record.orderTime,jdbcType=BIGINT},
      order_op_time = #{record.orderOpTime,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      parent_order_id = #{record.parentOrderId,jdbcType=BIGINT},
      order_source = #{record.orderSource,jdbcType=TINYINT},
      extra = #{record.extra,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.RerentOrder">
    update rerent_order
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payAmount != null">
        pay_amount = #{payAmount,jdbcType=INTEGER},
      </if>
      <if test="receivableAmount != null">
        receivable_amount = #{receivableAmount,jdbcType=INTEGER},
      </if>
      <if test="payMode != null">
        pay_mode = #{payMode,jdbcType=TINYINT},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="payStatus != null">
        pay_status = #{payStatus,jdbcType=TINYINT},
      </if>
      <if test="sourceOrderId != null">
        source_order_id = #{sourceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="sourceInnerOrderId != null">
        source_inner_order_id = #{sourceInnerOrderId,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="orderTime != null">
        order_time = #{orderTime,jdbcType=BIGINT},
      </if>
      <if test="orderOpTime != null">
        order_op_time = #{orderOpTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="parentOrderId != null">
        parent_order_id = #{parentOrderId,jdbcType=BIGINT},
      </if>
      <if test="orderSource != null">
        order_source = #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.RerentOrder">
    update rerent_order
    set order_no = #{orderNo,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      pay_amount = #{payAmount,jdbcType=INTEGER},
      receivable_amount = #{receivableAmount,jdbcType=INTEGER},
      pay_mode = #{payMode,jdbcType=TINYINT},
      order_status = #{orderStatus,jdbcType=TINYINT},
      pay_status = #{payStatus,jdbcType=TINYINT},
      source_order_id = #{sourceOrderId,jdbcType=VARCHAR},
      source_inner_order_id = #{sourceInnerOrderId,jdbcType=VARCHAR},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      order_time = #{orderTime,jdbcType=BIGINT},
      order_op_time = #{orderOpTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      parent_order_id = #{parentOrderId,jdbcType=BIGINT},
      order_source = #{orderSource,jdbcType=TINYINT},
      extra = #{extra,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into rerent_order
    (order_no, order_id, store_id, start_time, end_time, pay_amount, receivable_amount, pay_mode,
      order_status, pay_status, source_order_id, source_inner_order_id, last_ver, op_user_id, order_time, order_op_time,
      create_time, op_time, parent_order_id, order_source, extra)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderNo,jdbcType=VARCHAR},#{item.orderId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.startTime,jdbcType=TIMESTAMP},
        #{item.endTime,jdbcType=TIMESTAMP}, #{item.payAmount,jdbcType=INTEGER}, #{item.receivableAmount,jdbcType=INTEGER}, 
        #{item.payMode,jdbcType=TINYINT}, #{item.orderStatus,jdbcType=TINYINT}, #{item.payStatus,jdbcType=TINYINT}, 
        #{item.sourceOrderId,jdbcType=VARCHAR}, #{item.sourceInnerOrderId,jdbcType=VARCHAR}, #{item.lastVer,jdbcType=INTEGER}, #{item.opUserId,jdbcType=BIGINT},
        #{item.orderTime,jdbcType=BIGINT}, #{item.orderOpTime,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.parentOrderId,jdbcType=BIGINT}, #{item.orderSource,jdbcType=TINYINT},
       #{item.extra,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into rerent_order (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_no'.toString() == column.value">
          #{item.orderNo,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'start_time'.toString() == column.value">
          #{item.startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'end_time'.toString() == column.value">
          #{item.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'pay_amount'.toString() == column.value">
          #{item.payAmount,jdbcType=INTEGER}
        </if>
        <if test="'receivable_amount'.toString() == column.value">
          #{item.receivableAmount,jdbcType=INTEGER}
        </if>
        <if test="'pay_mode'.toString() == column.value">
          #{item.payMode,jdbcType=TINYINT}
        </if>
        <if test="'order_status'.toString() == column.value">
          #{item.orderStatus,jdbcType=TINYINT}
        </if>
        <if test="'pay_status'.toString() == column.value">
          #{item.payStatus,jdbcType=TINYINT}
        </if>
        <if test="'source_order_id'.toString() == column.value">
          #{item.sourceOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'source_inner_order_id'.toString() == column.value">
          #{item.sourceInnerOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'order_time'.toString() == column.value">
          #{item.orderTime,jdbcType=BIGINT}
        </if>
        <if test="'order_op_time'.toString() == column.value">
          #{item.orderOpTime,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'parent_order_id'.toString() == column.value">
          #{item.parentOrderId,jdbcType=BIGINT}
        </if>
        <if test="'order_source'.toString() == column.value">
          #{item.orderSource,jdbcType=TINYINT}
        </if>
        <if test="'extra'.toString() == column.value">
          #{item.extra,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
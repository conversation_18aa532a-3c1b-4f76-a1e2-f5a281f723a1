<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.FinanceDetailMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.FinanceDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="relation_id" jdbcType="BIGINT" property="relationId" />
    <result column="relation_type" jdbcType="INTEGER" property="relationType" />
    <result column="relation_no" jdbcType="VARCHAR" property="relationNo" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="finance_type" jdbcType="INTEGER" property="financeType" />
    <result column="finance_detail_type" jdbcType="INTEGER" property="financeDetailType" />
    <result column="total_amount" jdbcType="BIGINT" property="totalAmount" />
    <result column="settlement_amount" jdbcType="BIGINT" property="settlementAmount" />
    <result column="settled_amount" jdbcType="BIGINT" property="settledAmount" />
    <result column="third_pay_source" jdbcType="INTEGER" property="thirdPaySource" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, store_id, relation_id, relation_type, relation_no, type, finance_type, finance_detail_type, 
    total_amount, settlement_amount, settled_amount, third_pay_source, merchant_id, remark, 
    deleted, op_user_id, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.FinanceDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from finance_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from finance_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.FinanceDetailExample">
    delete from finance_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.FinanceDetail">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into finance_detail (store_id, relation_id, relation_type, 
      relation_no, type, finance_type, 
      finance_detail_type, total_amount, settlement_amount, 
      settled_amount, third_pay_source, merchant_id, 
      remark, deleted, op_user_id, 
      create_time, op_time)
    values (#{storeId,jdbcType=BIGINT}, #{relationId,jdbcType=BIGINT}, #{relationType,jdbcType=INTEGER}, 
      #{relationNo,jdbcType=VARCHAR}, #{type,jdbcType=TINYINT}, #{financeType,jdbcType=INTEGER}, 
      #{financeDetailType,jdbcType=INTEGER}, #{totalAmount,jdbcType=BIGINT}, #{settlementAmount,jdbcType=BIGINT}, 
      #{settledAmount,jdbcType=BIGINT}, #{thirdPaySource,jdbcType=INTEGER}, #{merchantId,jdbcType=BIGINT}, 
      #{remark,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, #{opUserId,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.FinanceDetail">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into finance_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        store_id,
      </if>
      <if test="relationId != null">
        relation_id,
      </if>
      <if test="relationType != null">
        relation_type,
      </if>
      <if test="relationNo != null">
        relation_no,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="financeType != null">
        finance_type,
      </if>
      <if test="financeDetailType != null">
        finance_detail_type,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="settlementAmount != null">
        settlement_amount,
      </if>
      <if test="settledAmount != null">
        settled_amount,
      </if>
      <if test="thirdPaySource != null">
        third_pay_source,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="relationId != null">
        #{relationId,jdbcType=BIGINT},
      </if>
      <if test="relationType != null">
        #{relationType,jdbcType=INTEGER},
      </if>
      <if test="relationNo != null">
        #{relationNo,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="financeType != null">
        #{financeType,jdbcType=INTEGER},
      </if>
      <if test="financeDetailType != null">
        #{financeDetailType,jdbcType=INTEGER},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=BIGINT},
      </if>
      <if test="settlementAmount != null">
        #{settlementAmount,jdbcType=BIGINT},
      </if>
      <if test="settledAmount != null">
        #{settledAmount,jdbcType=BIGINT},
      </if>
      <if test="thirdPaySource != null">
        #{thirdPaySource,jdbcType=INTEGER},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.FinanceDetailExample" resultType="java.lang.Long">
    select count(*) from finance_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update finance_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.relationId != null">
        relation_id = #{record.relationId,jdbcType=BIGINT},
      </if>
      <if test="record.relationType != null">
        relation_type = #{record.relationType,jdbcType=INTEGER},
      </if>
      <if test="record.relationNo != null">
        relation_no = #{record.relationNo,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=TINYINT},
      </if>
      <if test="record.financeType != null">
        finance_type = #{record.financeType,jdbcType=INTEGER},
      </if>
      <if test="record.financeDetailType != null">
        finance_detail_type = #{record.financeDetailType,jdbcType=INTEGER},
      </if>
      <if test="record.totalAmount != null">
        total_amount = #{record.totalAmount,jdbcType=BIGINT},
      </if>
      <if test="record.settlementAmount != null">
        settlement_amount = #{record.settlementAmount,jdbcType=BIGINT},
      </if>
      <if test="record.settledAmount != null">
        settled_amount = #{record.settledAmount,jdbcType=BIGINT},
      </if>
      <if test="record.thirdPaySource != null">
        third_pay_source = #{record.thirdPaySource,jdbcType=INTEGER},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update finance_detail
    set id = #{record.id,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      relation_id = #{record.relationId,jdbcType=BIGINT},
      relation_type = #{record.relationType,jdbcType=INTEGER},
      relation_no = #{record.relationNo,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=TINYINT},
      finance_type = #{record.financeType,jdbcType=INTEGER},
      finance_detail_type = #{record.financeDetailType,jdbcType=INTEGER},
      total_amount = #{record.totalAmount,jdbcType=BIGINT},
      settlement_amount = #{record.settlementAmount,jdbcType=BIGINT},
      settled_amount = #{record.settledAmount,jdbcType=BIGINT},
      third_pay_source = #{record.thirdPaySource,jdbcType=INTEGER},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.FinanceDetail">
    update finance_detail
    <set>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="relationId != null">
        relation_id = #{relationId,jdbcType=BIGINT},
      </if>
      <if test="relationType != null">
        relation_type = #{relationType,jdbcType=INTEGER},
      </if>
      <if test="relationNo != null">
        relation_no = #{relationNo,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="financeType != null">
        finance_type = #{financeType,jdbcType=INTEGER},
      </if>
      <if test="financeDetailType != null">
        finance_detail_type = #{financeDetailType,jdbcType=INTEGER},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=BIGINT},
      </if>
      <if test="settlementAmount != null">
        settlement_amount = #{settlementAmount,jdbcType=BIGINT},
      </if>
      <if test="settledAmount != null">
        settled_amount = #{settledAmount,jdbcType=BIGINT},
      </if>
      <if test="thirdPaySource != null">
        third_pay_source = #{thirdPaySource,jdbcType=INTEGER},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.FinanceDetail">
    update finance_detail
    set store_id = #{storeId,jdbcType=BIGINT},
      relation_id = #{relationId,jdbcType=BIGINT},
      relation_type = #{relationType,jdbcType=INTEGER},
      relation_no = #{relationNo,jdbcType=VARCHAR},
      type = #{type,jdbcType=TINYINT},
      finance_type = #{financeType,jdbcType=INTEGER},
      finance_detail_type = #{financeDetailType,jdbcType=INTEGER},
      total_amount = #{totalAmount,jdbcType=BIGINT},
      settlement_amount = #{settlementAmount,jdbcType=BIGINT},
      settled_amount = #{settledAmount,jdbcType=BIGINT},
      third_pay_source = #{thirdPaySource,jdbcType=INTEGER},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into finance_detail
    (store_id, relation_id, relation_type, relation_no, type, finance_type, finance_detail_type, 
      total_amount, settlement_amount, settled_amount, third_pay_source, merchant_id, 
      remark, deleted, op_user_id, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.storeId,jdbcType=BIGINT}, #{item.relationId,jdbcType=BIGINT}, #{item.relationType,jdbcType=INTEGER}, 
        #{item.relationNo,jdbcType=VARCHAR}, #{item.type,jdbcType=TINYINT}, #{item.financeType,jdbcType=INTEGER}, 
        #{item.financeDetailType,jdbcType=INTEGER}, #{item.totalAmount,jdbcType=BIGINT}, 
        #{item.settlementAmount,jdbcType=BIGINT}, #{item.settledAmount,jdbcType=BIGINT}, 
        #{item.thirdPaySource,jdbcType=INTEGER}, #{item.merchantId,jdbcType=BIGINT}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.deleted,jdbcType=TINYINT}, #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into finance_detail (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'relation_id'.toString() == column.value">
          #{item.relationId,jdbcType=BIGINT}
        </if>
        <if test="'relation_type'.toString() == column.value">
          #{item.relationType,jdbcType=INTEGER}
        </if>
        <if test="'relation_no'.toString() == column.value">
          #{item.relationNo,jdbcType=VARCHAR}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=TINYINT}
        </if>
        <if test="'finance_type'.toString() == column.value">
          #{item.financeType,jdbcType=INTEGER}
        </if>
        <if test="'finance_detail_type'.toString() == column.value">
          #{item.financeDetailType,jdbcType=INTEGER}
        </if>
        <if test="'total_amount'.toString() == column.value">
          #{item.totalAmount,jdbcType=BIGINT}
        </if>
        <if test="'settlement_amount'.toString() == column.value">
          #{item.settlementAmount,jdbcType=BIGINT}
        </if>
        <if test="'settled_amount'.toString() == column.value">
          #{item.settledAmount,jdbcType=BIGINT}
        </if>
        <if test="'third_pay_source'.toString() == column.value">
          #{item.thirdPaySource,jdbcType=INTEGER}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
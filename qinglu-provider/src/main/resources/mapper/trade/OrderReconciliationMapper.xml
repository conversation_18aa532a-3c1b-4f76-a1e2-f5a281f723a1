<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.OrderReconciliationMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.OrderReconciliation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="source_order_id" jdbcType="VARCHAR" property="sourceOrderId" />
    <result column="order_source" jdbcType="TINYINT" property="orderSource" />
    <result column="third_pay_source" jdbcType="INTEGER" property="thirdPaySource" />
    <result column="order_amount" jdbcType="BIGINT" property="orderAmount" />
    <result column="total_settlement_amount" jdbcType="BIGINT" property="totalSettlementAmount" />
    <result column="settled_amount" jdbcType="BIGINT" property="settledAmount" />
    <result column="source_commission_rate" jdbcType="DECIMAL" property="sourceCommissionRate" />
    <result column="source_commission" jdbcType="BIGINT" property="sourceCommission" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="settlement_status" jdbcType="TINYINT" property="settlementStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, order_no, source_order_id, order_source, third_pay_source, order_amount, 
    total_settlement_amount, settled_amount, source_commission_rate, source_commission, 
    merchant_id, settlement_status, remark, deleted, last_ver, create_time, op_time, 
    op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.OrderReconciliationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from order_reconciliation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_reconciliation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_reconciliation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.OrderReconciliationExample">
    delete from order_reconciliation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.OrderReconciliation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_reconciliation (order_id, order_no, source_order_id, 
      order_source, third_pay_source, order_amount, 
      total_settlement_amount, settled_amount, source_commission_rate, 
      source_commission, merchant_id, settlement_status, 
      remark, deleted, last_ver, 
      create_time, op_time, op_user_id
      )
    values (#{orderId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{sourceOrderId,jdbcType=VARCHAR}, 
      #{orderSource,jdbcType=TINYINT}, #{thirdPaySource,jdbcType=INTEGER}, #{orderAmount,jdbcType=BIGINT}, 
      #{totalSettlementAmount,jdbcType=BIGINT}, #{settledAmount,jdbcType=BIGINT}, #{sourceCommissionRate,jdbcType=DECIMAL}, 
      #{sourceCommission,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, #{settlementStatus,jdbcType=TINYINT}, 
      #{remark,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER}, 
      #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.OrderReconciliation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_reconciliation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="sourceOrderId != null">
        source_order_id,
      </if>
      <if test="orderSource != null">
        order_source,
      </if>
      <if test="thirdPaySource != null">
        third_pay_source,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="totalSettlementAmount != null">
        total_settlement_amount,
      </if>
      <if test="settledAmount != null">
        settled_amount,
      </if>
      <if test="sourceCommissionRate != null">
        source_commission_rate,
      </if>
      <if test="sourceCommission != null">
        source_commission,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="settlementStatus != null">
        settlement_status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="sourceOrderId != null">
        #{sourceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderSource != null">
        #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="thirdPaySource != null">
        #{thirdPaySource,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="totalSettlementAmount != null">
        #{totalSettlementAmount,jdbcType=BIGINT},
      </if>
      <if test="settledAmount != null">
        #{settledAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceCommissionRate != null">
        #{sourceCommissionRate,jdbcType=DECIMAL},
      </if>
      <if test="sourceCommission != null">
        #{sourceCommission,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="settlementStatus != null">
        #{settlementStatus,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.OrderReconciliationExample" resultType="java.lang.Long">
    select count(*) from order_reconciliation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_reconciliation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceOrderId != null">
        source_order_id = #{record.sourceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderSource != null">
        order_source = #{record.orderSource,jdbcType=TINYINT},
      </if>
      <if test="record.thirdPaySource != null">
        third_pay_source = #{record.thirdPaySource,jdbcType=INTEGER},
      </if>
      <if test="record.orderAmount != null">
        order_amount = #{record.orderAmount,jdbcType=BIGINT},
      </if>
      <if test="record.totalSettlementAmount != null">
        total_settlement_amount = #{record.totalSettlementAmount,jdbcType=BIGINT},
      </if>
      <if test="record.settledAmount != null">
        settled_amount = #{record.settledAmount,jdbcType=BIGINT},
      </if>
      <if test="record.sourceCommissionRate != null">
        source_commission_rate = #{record.sourceCommissionRate,jdbcType=DECIMAL},
      </if>
      <if test="record.sourceCommission != null">
        source_commission = #{record.sourceCommission,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.settlementStatus != null">
        settlement_status = #{record.settlementStatus,jdbcType=TINYINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_reconciliation
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      source_order_id = #{record.sourceOrderId,jdbcType=VARCHAR},
      order_source = #{record.orderSource,jdbcType=TINYINT},
      third_pay_source = #{record.thirdPaySource,jdbcType=INTEGER},
      order_amount = #{record.orderAmount,jdbcType=BIGINT},
      total_settlement_amount = #{record.totalSettlementAmount,jdbcType=BIGINT},
      settled_amount = #{record.settledAmount,jdbcType=BIGINT},
      source_commission_rate = #{record.sourceCommissionRate,jdbcType=DECIMAL},
      source_commission = #{record.sourceCommission,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      settlement_status = #{record.settlementStatus,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.OrderReconciliation">
    update order_reconciliation
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="sourceOrderId != null">
        source_order_id = #{sourceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderSource != null">
        order_source = #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="thirdPaySource != null">
        third_pay_source = #{thirdPaySource,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="totalSettlementAmount != null">
        total_settlement_amount = #{totalSettlementAmount,jdbcType=BIGINT},
      </if>
      <if test="settledAmount != null">
        settled_amount = #{settledAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceCommissionRate != null">
        source_commission_rate = #{sourceCommissionRate,jdbcType=DECIMAL},
      </if>
      <if test="sourceCommission != null">
        source_commission = #{sourceCommission,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="settlementStatus != null">
        settlement_status = #{settlementStatus,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.OrderReconciliation">
    update order_reconciliation
    set order_id = #{orderId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      source_order_id = #{sourceOrderId,jdbcType=VARCHAR},
      order_source = #{orderSource,jdbcType=TINYINT},
      third_pay_source = #{thirdPaySource,jdbcType=INTEGER},
      order_amount = #{orderAmount,jdbcType=BIGINT},
      total_settlement_amount = #{totalSettlementAmount,jdbcType=BIGINT},
      settled_amount = #{settledAmount,jdbcType=BIGINT},
      source_commission_rate = #{sourceCommissionRate,jdbcType=DECIMAL},
      source_commission = #{sourceCommission,jdbcType=BIGINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      settlement_status = #{settlementStatus,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_reconciliation
    (order_id, order_no, source_order_id, order_source, third_pay_source, order_amount, 
      total_settlement_amount, settled_amount, source_commission_rate, source_commission, 
      merchant_id, settlement_status, remark, deleted, last_ver, create_time, op_time, 
      op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=BIGINT}, #{item.orderNo,jdbcType=VARCHAR}, #{item.sourceOrderId,jdbcType=VARCHAR}, 
        #{item.orderSource,jdbcType=TINYINT}, #{item.thirdPaySource,jdbcType=INTEGER}, 
        #{item.orderAmount,jdbcType=BIGINT}, #{item.totalSettlementAmount,jdbcType=BIGINT}, 
        #{item.settledAmount,jdbcType=BIGINT}, #{item.sourceCommissionRate,jdbcType=DECIMAL}, 
        #{item.sourceCommission,jdbcType=BIGINT}, #{item.merchantId,jdbcType=BIGINT}, #{item.settlementStatus,jdbcType=TINYINT}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.deleted,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_reconciliation (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'order_no'.toString() == column.value">
          #{item.orderNo,jdbcType=VARCHAR}
        </if>
        <if test="'source_order_id'.toString() == column.value">
          #{item.sourceOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'order_source'.toString() == column.value">
          #{item.orderSource,jdbcType=TINYINT}
        </if>
        <if test="'third_pay_source'.toString() == column.value">
          #{item.thirdPaySource,jdbcType=INTEGER}
        </if>
        <if test="'order_amount'.toString() == column.value">
          #{item.orderAmount,jdbcType=BIGINT}
        </if>
        <if test="'total_settlement_amount'.toString() == column.value">
          #{item.totalSettlementAmount,jdbcType=BIGINT}
        </if>
        <if test="'settled_amount'.toString() == column.value">
          #{item.settledAmount,jdbcType=BIGINT}
        </if>
        <if test="'source_commission_rate'.toString() == column.value">
          #{item.sourceCommissionRate,jdbcType=DECIMAL}
        </if>
        <if test="'source_commission'.toString() == column.value">
          #{item.sourceCommission,jdbcType=BIGINT}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'settlement_status'.toString() == column.value">
          #{item.settlementStatus,jdbcType=TINYINT}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
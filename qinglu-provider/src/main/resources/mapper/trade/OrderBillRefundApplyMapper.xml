<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.OrderBillRefundApplyMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.OrderBillRefundApply">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_bill_id" jdbcType="BIGINT" property="orderBillId" />
    <result column="actual_refund_amount" jdbcType="BIGINT" property="actualRefundAmount" />
    <result column="refund_reason" jdbcType="VARCHAR" property="refundReason" />
    <result column="audit_status" jdbcType="TINYINT" property="auditStatus" />
    <result column="reject_reason" jdbcType="VARCHAR" property="rejectReason" />
    <result column="apply_no" jdbcType="INTEGER" property="applyNo" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_bill_id, actual_refund_amount, refund_reason, audit_status, reject_reason, 
    apply_no, merchant_id, last_ver, deleted, create_time, op_time, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.OrderBillRefundApplyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from order_bill_refund_apply
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_bill_refund_apply
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_bill_refund_apply
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.OrderBillRefundApplyExample">
    delete from order_bill_refund_apply
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.OrderBillRefundApply">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_bill_refund_apply (order_bill_id, actual_refund_amount, refund_reason, 
      audit_status, reject_reason, apply_no, 
      merchant_id, last_ver, deleted, 
      create_time, op_time, op_user_id
      )
    values (#{orderBillId,jdbcType=BIGINT}, #{actualRefundAmount,jdbcType=BIGINT}, #{refundReason,jdbcType=VARCHAR}, 
      #{auditStatus,jdbcType=TINYINT}, #{rejectReason,jdbcType=VARCHAR}, #{applyNo,jdbcType=INTEGER}, 
      #{merchantId,jdbcType=BIGINT}, #{lastVer,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT}, 
      #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.OrderBillRefundApply">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_bill_refund_apply
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderBillId != null">
        order_bill_id,
      </if>
      <if test="actualRefundAmount != null">
        actual_refund_amount,
      </if>
      <if test="refundReason != null">
        refund_reason,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="rejectReason != null">
        reject_reason,
      </if>
      <if test="applyNo != null">
        apply_no,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderBillId != null">
        #{orderBillId,jdbcType=BIGINT},
      </if>
      <if test="actualRefundAmount != null">
        #{actualRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="refundReason != null">
        #{refundReason,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="rejectReason != null">
        #{rejectReason,jdbcType=VARCHAR},
      </if>
      <if test="applyNo != null">
        #{applyNo,jdbcType=INTEGER},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.OrderBillRefundApplyExample" resultType="java.lang.Long">
    select count(*) from order_bill_refund_apply
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_bill_refund_apply
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderBillId != null">
        order_bill_id = #{record.orderBillId,jdbcType=BIGINT},
      </if>
      <if test="record.actualRefundAmount != null">
        actual_refund_amount = #{record.actualRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="record.refundReason != null">
        refund_reason = #{record.refundReason,jdbcType=VARCHAR},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=TINYINT},
      </if>
      <if test="record.rejectReason != null">
        reject_reason = #{record.rejectReason,jdbcType=VARCHAR},
      </if>
      <if test="record.applyNo != null">
        apply_no = #{record.applyNo,jdbcType=INTEGER},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_bill_refund_apply
    set id = #{record.id,jdbcType=BIGINT},
      order_bill_id = #{record.orderBillId,jdbcType=BIGINT},
      actual_refund_amount = #{record.actualRefundAmount,jdbcType=BIGINT},
      refund_reason = #{record.refundReason,jdbcType=VARCHAR},
      audit_status = #{record.auditStatus,jdbcType=TINYINT},
      reject_reason = #{record.rejectReason,jdbcType=VARCHAR},
      apply_no = #{record.applyNo,jdbcType=INTEGER},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.OrderBillRefundApply">
    update order_bill_refund_apply
    <set>
      <if test="orderBillId != null">
        order_bill_id = #{orderBillId,jdbcType=BIGINT},
      </if>
      <if test="actualRefundAmount != null">
        actual_refund_amount = #{actualRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="refundReason != null">
        refund_reason = #{refundReason,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="rejectReason != null">
        reject_reason = #{rejectReason,jdbcType=VARCHAR},
      </if>
      <if test="applyNo != null">
        apply_no = #{applyNo,jdbcType=INTEGER},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.OrderBillRefundApply">
    update order_bill_refund_apply
    set order_bill_id = #{orderBillId,jdbcType=BIGINT},
      actual_refund_amount = #{actualRefundAmount,jdbcType=BIGINT},
      refund_reason = #{refundReason,jdbcType=VARCHAR},
      audit_status = #{auditStatus,jdbcType=TINYINT},
      reject_reason = #{rejectReason,jdbcType=VARCHAR},
      apply_no = #{applyNo,jdbcType=INTEGER},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_bill_refund_apply
    (order_bill_id, actual_refund_amount, refund_reason, audit_status, reject_reason, 
      apply_no, merchant_id, last_ver, deleted, create_time, op_time, op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderBillId,jdbcType=BIGINT}, #{item.actualRefundAmount,jdbcType=BIGINT}, 
        #{item.refundReason,jdbcType=VARCHAR}, #{item.auditStatus,jdbcType=TINYINT}, #{item.rejectReason,jdbcType=VARCHAR}, 
        #{item.applyNo,jdbcType=INTEGER}, #{item.merchantId,jdbcType=BIGINT}, #{item.lastVer,jdbcType=INTEGER}, 
        #{item.deleted,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, 
        #{item.opUserId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_bill_refund_apply (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_bill_id'.toString() == column.value">
          #{item.orderBillId,jdbcType=BIGINT}
        </if>
        <if test="'actual_refund_amount'.toString() == column.value">
          #{item.actualRefundAmount,jdbcType=BIGINT}
        </if>
        <if test="'refund_reason'.toString() == column.value">
          #{item.refundReason,jdbcType=VARCHAR}
        </if>
        <if test="'audit_status'.toString() == column.value">
          #{item.auditStatus,jdbcType=TINYINT}
        </if>
        <if test="'reject_reason'.toString() == column.value">
          #{item.rejectReason,jdbcType=VARCHAR}
        </if>
        <if test="'apply_no'.toString() == column.value">
          #{item.applyNo,jdbcType=INTEGER}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
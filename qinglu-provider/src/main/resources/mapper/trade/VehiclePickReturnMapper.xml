<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.VehiclePickReturnMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.VehiclePickReturn">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="mileage" jdbcType="INTEGER" property="mileage" />
    <result column="oil_liter" jdbcType="INTEGER" property="oilLiter" />
    <result column="max_oil_liter" jdbcType="INTEGER" property="maxOilLiter" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="pr_time" jdbcType="TIMESTAMP" property="prTime" />
    <result column="pr_type" jdbcType="TINYINT" property="prType" />
    <result column="deduction_pay_type" jdbcType="TINYINT" property="deductionPayType" />
    <result column="refund_pay_type" jdbcType="TINYINT" property="refundPayType" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="partner_create_time" jdbcType="BIGINT" property="partnerCreateTime" />
    <result column="partner_op_time" jdbcType="BIGINT" property="partnerOpTime" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, vehicle_id, mileage, oil_liter, max_oil_liter, last_ver, op_user_id, 
    pr_time, pr_type, deduction_pay_type, refund_pay_type, merchant_id, deleted, partner_create_time, 
    partner_op_time, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.VehiclePickReturnExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_pick_return
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_pick_return
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_pick_return
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.VehiclePickReturn">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_pick_return (order_id, vehicle_id, mileage, 
      oil_liter, max_oil_liter, last_ver, 
      op_user_id, pr_time, pr_type, 
      deduction_pay_type, refund_pay_type, merchant_id, 
      deleted, partner_create_time, partner_op_time, 
      create_time, op_time)
    values (#{orderId,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, #{mileage,jdbcType=INTEGER}, 
      #{oilLiter,jdbcType=INTEGER}, #{maxOilLiter,jdbcType=INTEGER}, #{lastVer,jdbcType=INTEGER}, 
      #{opUserId,jdbcType=BIGINT}, #{prTime,jdbcType=TIMESTAMP}, #{prType,jdbcType=TINYINT}, 
      #{deductionPayType,jdbcType=TINYINT}, #{refundPayType,jdbcType=TINYINT}, #{merchantId,jdbcType=BIGINT}, 
      #{deleted,jdbcType=TINYINT}, #{partnerCreateTime,jdbcType=BIGINT}, #{partnerOpTime,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.VehiclePickReturn">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_pick_return
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="mileage != null">
        mileage,
      </if>
      <if test="oilLiter != null">
        oil_liter,
      </if>
      <if test="maxOilLiter != null">
        max_oil_liter,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="prTime != null">
        pr_time,
      </if>
      <if test="prType != null">
        pr_type,
      </if>
      <if test="deductionPayType != null">
        deduction_pay_type,
      </if>
      <if test="refundPayType != null">
        refund_pay_type,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="partnerCreateTime != null">
        partner_create_time,
      </if>
      <if test="partnerOpTime != null">
        partner_op_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="mileage != null">
        #{mileage,jdbcType=INTEGER},
      </if>
      <if test="oilLiter != null">
        #{oilLiter,jdbcType=INTEGER},
      </if>
      <if test="maxOilLiter != null">
        #{maxOilLiter,jdbcType=INTEGER},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="prTime != null">
        #{prTime,jdbcType=TIMESTAMP},
      </if>
      <if test="prType != null">
        #{prType,jdbcType=TINYINT},
      </if>
      <if test="deductionPayType != null">
        #{deductionPayType,jdbcType=TINYINT},
      </if>
      <if test="refundPayType != null">
        #{refundPayType,jdbcType=TINYINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="partnerCreateTime != null">
        #{partnerCreateTime,jdbcType=BIGINT},
      </if>
      <if test="partnerOpTime != null">
        #{partnerOpTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.VehiclePickReturnExample" resultType="java.lang.Long">
    select count(*) from vehicle_pick_return
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_pick_return
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="record.mileage != null">
        mileage = #{record.mileage,jdbcType=INTEGER},
      </if>
      <if test="record.oilLiter != null">
        oil_liter = #{record.oilLiter,jdbcType=INTEGER},
      </if>
      <if test="record.maxOilLiter != null">
        max_oil_liter = #{record.maxOilLiter,jdbcType=INTEGER},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.prTime != null">
        pr_time = #{record.prTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.prType != null">
        pr_type = #{record.prType,jdbcType=TINYINT},
      </if>
      <if test="record.deductionPayType != null">
        deduction_pay_type = #{record.deductionPayType,jdbcType=TINYINT},
      </if>
      <if test="record.refundPayType != null">
        refund_pay_type = #{record.refundPayType,jdbcType=TINYINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.partnerCreateTime != null">
        partner_create_time = #{record.partnerCreateTime,jdbcType=BIGINT},
      </if>
      <if test="record.partnerOpTime != null">
        partner_op_time = #{record.partnerOpTime,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_pick_return
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      mileage = #{record.mileage,jdbcType=INTEGER},
      oil_liter = #{record.oilLiter,jdbcType=INTEGER},
      max_oil_liter = #{record.maxOilLiter,jdbcType=INTEGER},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      pr_time = #{record.prTime,jdbcType=TIMESTAMP},
      pr_type = #{record.prType,jdbcType=TINYINT},
      deduction_pay_type = #{record.deductionPayType,jdbcType=TINYINT},
      refund_pay_type = #{record.refundPayType,jdbcType=TINYINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      deleted = #{record.deleted,jdbcType=TINYINT},
      partner_create_time = #{record.partnerCreateTime,jdbcType=BIGINT},
      partner_op_time = #{record.partnerOpTime,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.VehiclePickReturn">
    update vehicle_pick_return
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="mileage != null">
        mileage = #{mileage,jdbcType=INTEGER},
      </if>
      <if test="oilLiter != null">
        oil_liter = #{oilLiter,jdbcType=INTEGER},
      </if>
      <if test="maxOilLiter != null">
        max_oil_liter = #{maxOilLiter,jdbcType=INTEGER},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="prTime != null">
        pr_time = #{prTime,jdbcType=TIMESTAMP},
      </if>
      <if test="prType != null">
        pr_type = #{prType,jdbcType=TINYINT},
      </if>
      <if test="deductionPayType != null">
        deduction_pay_type = #{deductionPayType,jdbcType=TINYINT},
      </if>
      <if test="refundPayType != null">
        refund_pay_type = #{refundPayType,jdbcType=TINYINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="partnerCreateTime != null">
        partner_create_time = #{partnerCreateTime,jdbcType=BIGINT},
      </if>
      <if test="partnerOpTime != null">
        partner_op_time = #{partnerOpTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.VehiclePickReturn">
    update vehicle_pick_return
    set order_id = #{orderId,jdbcType=BIGINT},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      mileage = #{mileage,jdbcType=INTEGER},
      oil_liter = #{oilLiter,jdbcType=INTEGER},
      max_oil_liter = #{maxOilLiter,jdbcType=INTEGER},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      pr_time = #{prTime,jdbcType=TIMESTAMP},
      pr_type = #{prType,jdbcType=TINYINT},
      deduction_pay_type = #{deductionPayType,jdbcType=TINYINT},
      refund_pay_type = #{refundPayType,jdbcType=TINYINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=TINYINT},
      partner_create_time = #{partnerCreateTime,jdbcType=BIGINT},
      partner_op_time = #{partnerOpTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_pick_return
    (order_id, vehicle_id, mileage, oil_liter, max_oil_liter, last_ver, op_user_id, pr_time, 
      pr_type, deduction_pay_type, refund_pay_type, merchant_id, deleted, partner_create_time, 
      partner_op_time, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=BIGINT}, #{item.vehicleId,jdbcType=BIGINT}, #{item.mileage,jdbcType=INTEGER}, 
        #{item.oilLiter,jdbcType=INTEGER}, #{item.maxOilLiter,jdbcType=INTEGER}, #{item.lastVer,jdbcType=INTEGER}, 
        #{item.opUserId,jdbcType=BIGINT}, #{item.prTime,jdbcType=TIMESTAMP}, #{item.prType,jdbcType=TINYINT}, 
        #{item.deductionPayType,jdbcType=TINYINT}, #{item.refundPayType,jdbcType=TINYINT}, 
        #{item.merchantId,jdbcType=BIGINT}, #{item.deleted,jdbcType=TINYINT}, #{item.partnerCreateTime,jdbcType=BIGINT}, 
        #{item.partnerOpTime,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_pick_return (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_id'.toString() == column.value">
          #{item.vehicleId,jdbcType=BIGINT}
        </if>
        <if test="'mileage'.toString() == column.value">
          #{item.mileage,jdbcType=INTEGER}
        </if>
        <if test="'oil_liter'.toString() == column.value">
          #{item.oilLiter,jdbcType=INTEGER}
        </if>
        <if test="'max_oil_liter'.toString() == column.value">
          #{item.maxOilLiter,jdbcType=INTEGER}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'pr_time'.toString() == column.value">
          #{item.prTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'pr_type'.toString() == column.value">
          #{item.prType,jdbcType=TINYINT}
        </if>
        <if test="'deduction_pay_type'.toString() == column.value">
          #{item.deductionPayType,jdbcType=TINYINT}
        </if>
        <if test="'refund_pay_type'.toString() == column.value">
          #{item.refundPayType,jdbcType=TINYINT}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'partner_create_time'.toString() == column.value">
          #{item.partnerCreateTime,jdbcType=BIGINT}
        </if>
        <if test="'partner_op_time'.toString() == column.value">
          #{item.partnerOpTime,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
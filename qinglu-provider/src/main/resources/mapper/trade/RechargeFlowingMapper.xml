<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.RechargeFlowingMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.RechargeFlowing">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="no_id" jdbcType="VARCHAR" property="noId" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="recharge_id" jdbcType="BIGINT" property="rechargeId" />
    <result column="operation" jdbcType="INTEGER" property="operation" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="price" jdbcType="INTEGER" property="price" />
    <result column="out_id" jdbcType="VARCHAR" property="outId" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="payment_channel_id" jdbcType="BIGINT" property="paymentChannelId" />
    <result column="discount" jdbcType="INTEGER" property="discount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, no_id, merchant_id, recharge_id, operation, status, price, out_id, memo, deleted, 
    last_ver, op_user_id, create_time, op_time, payment_channel_id, discount
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.RechargeFlowingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from recharge_flowing
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from recharge_flowing
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.RechargeFlowing">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into recharge_flowing (no_id, merchant_id, recharge_id, 
      operation, status, price, 
      out_id, memo, deleted, 
      last_ver, op_user_id, create_time, 
      op_time, payment_channel_id, discount
      )
    values (#{noId,jdbcType=VARCHAR}, #{merchantId,jdbcType=BIGINT}, #{rechargeId,jdbcType=BIGINT}, 
      #{operation,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{price,jdbcType=INTEGER}, 
      #{outId,jdbcType=VARCHAR}, #{memo,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, 
      #{lastVer,jdbcType=INTEGER}, #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{paymentChannelId,jdbcType=BIGINT}, #{discount,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.RechargeFlowing">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into recharge_flowing
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="noId != null">
        no_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="rechargeId != null">
        recharge_id,
      </if>
      <if test="operation != null">
        operation,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="outId != null">
        out_id,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="paymentChannelId != null">
        payment_channel_id,
      </if>
      <if test="discount != null">
        discount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="noId != null">
        #{noId,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="rechargeId != null">
        #{rechargeId,jdbcType=BIGINT},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=INTEGER},
      </if>
      <if test="outId != null">
        #{outId,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="paymentChannelId != null">
        #{paymentChannelId,jdbcType=BIGINT},
      </if>
      <if test="discount != null">
        #{discount,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.RechargeFlowingExample" resultType="java.lang.Long">
    select count(*) from recharge_flowing
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update recharge_flowing
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.noId != null">
        no_id = #{record.noId,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.rechargeId != null">
        recharge_id = #{record.rechargeId,jdbcType=BIGINT},
      </if>
      <if test="record.operation != null">
        operation = #{record.operation,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=INTEGER},
      </if>
      <if test="record.outId != null">
        out_id = #{record.outId,jdbcType=VARCHAR},
      </if>
      <if test="record.memo != null">
        memo = #{record.memo,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.paymentChannelId != null">
        payment_channel_id = #{record.paymentChannelId,jdbcType=BIGINT},
      </if>
      <if test="record.discount != null">
        discount = #{record.discount,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update recharge_flowing
    set id = #{record.id,jdbcType=BIGINT},
      no_id = #{record.noId,jdbcType=VARCHAR},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      recharge_id = #{record.rechargeId,jdbcType=BIGINT},
      operation = #{record.operation,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      price = #{record.price,jdbcType=INTEGER},
      out_id = #{record.outId,jdbcType=VARCHAR},
      memo = #{record.memo,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      payment_channel_id = #{record.paymentChannelId,jdbcType=BIGINT},
      discount = #{record.discount,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.RechargeFlowing">
    update recharge_flowing
    <set>
      <if test="noId != null">
        no_id = #{noId,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="rechargeId != null">
        recharge_id = #{rechargeId,jdbcType=BIGINT},
      </if>
      <if test="operation != null">
        operation = #{operation,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=INTEGER},
      </if>
      <if test="outId != null">
        out_id = #{outId,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="paymentChannelId != null">
        payment_channel_id = #{paymentChannelId,jdbcType=BIGINT},
      </if>
      <if test="discount != null">
        discount = #{discount,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.RechargeFlowing">
    update recharge_flowing
    set no_id = #{noId,jdbcType=VARCHAR},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      recharge_id = #{rechargeId,jdbcType=BIGINT},
      operation = #{operation,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      price = #{price,jdbcType=INTEGER},
      out_id = #{outId,jdbcType=VARCHAR},
      memo = #{memo,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      payment_channel_id = #{paymentChannelId,jdbcType=BIGINT},
      discount = #{discount,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into recharge_flowing
    (no_id, merchant_id, recharge_id, operation, status, price, out_id, memo, deleted, 
      last_ver, op_user_id, create_time, op_time, payment_channel_id, discount)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.noId,jdbcType=VARCHAR}, #{item.merchantId,jdbcType=BIGINT}, #{item.rechargeId,jdbcType=BIGINT}, 
        #{item.operation,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER}, #{item.price,jdbcType=INTEGER}, 
        #{item.outId,jdbcType=VARCHAR}, #{item.memo,jdbcType=VARCHAR}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.lastVer,jdbcType=INTEGER}, #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.paymentChannelId,jdbcType=BIGINT}, #{item.discount,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into recharge_flowing (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'no_id'.toString() == column.value">
          #{item.noId,jdbcType=VARCHAR}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'recharge_id'.toString() == column.value">
          #{item.rechargeId,jdbcType=BIGINT}
        </if>
        <if test="'operation'.toString() == column.value">
          #{item.operation,jdbcType=INTEGER}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'price'.toString() == column.value">
          #{item.price,jdbcType=INTEGER}
        </if>
        <if test="'out_id'.toString() == column.value">
          #{item.outId,jdbcType=VARCHAR}
        </if>
        <if test="'memo'.toString() == column.value">
          #{item.memo,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'payment_channel_id'.toString() == column.value">
          #{item.paymentChannelId,jdbcType=BIGINT}
        </if>
        <if test="'discount'.toString() == column.value">
          #{item.discount,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
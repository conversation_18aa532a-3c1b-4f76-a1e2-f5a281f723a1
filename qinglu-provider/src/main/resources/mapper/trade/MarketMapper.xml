<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.MarketMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.Market">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="market_name" jdbcType="VARCHAR" property="marketName" />
    <result column="market_code" jdbcType="VARCHAR" property="marketCode" />
    <result column="market_type" jdbcType="INTEGER" property="marketType" />
    <result column="dimension" jdbcType="INTEGER" property="dimension" />
    <result column="discount_mode" jdbcType="INTEGER" property="discountMode" />
    <result column="discount_sub_mode" jdbcType="INTEGER" property="discountSubMode" />
    <result column="satisfy" jdbcType="INTEGER" property="satisfy" />
    <result column="discount" jdbcType="INTEGER" property="discount" />
    <result column="max_money" jdbcType="INTEGER" property="maxMoney" />
    <result column="max_lease_term" jdbcType="INTEGER" property="maxLeaseTerm" />
    <result column="min_lease_term" jdbcType="INTEGER" property="minLeaseTerm" />
    <result column="place_order_start" jdbcType="BIGINT" property="placeOrderStart" />
    <result column="place_order_end" jdbcType="BIGINT" property="placeOrderEnd" />
    <result column="pick_start" jdbcType="BIGINT" property="pickStart" />
    <result column="pick_end" jdbcType="BIGINT" property="pickEnd" />
    <result column="return_start" jdbcType="BIGINT" property="returnStart" />
    <result column="return_end" jdbcType="BIGINT" property="returnEnd" />
    <result column="cost_party" jdbcType="INTEGER" property="costParty" />
    <result column="cost_party_type" jdbcType="INTEGER" property="costPartyType" />
    <result column="merchant_cost_party_value" jdbcType="INTEGER" property="merchantCostPartyValue" />
    <result column="platform_cost_party_value" jdbcType="INTEGER" property="platformCostPartyValue" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, channel_id, status, market_name, market_code, market_type, dimension, 
    discount_mode, discount_sub_mode, satisfy, discount, max_money, max_lease_term, min_lease_term, 
    place_order_start, place_order_end, pick_start, pick_end, return_start, return_end, 
    cost_party, cost_party_type, merchant_cost_party_value, platform_cost_party_value, 
    deleted, last_ver, op_user_id, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.MarketExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from market
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from market
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from market
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.MarketExample">
    delete from market
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.Market">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into market (merchant_id, channel_id, status, 
      market_name, market_code, market_type, 
      dimension, discount_mode, discount_sub_mode, 
      satisfy, discount, max_money, 
      max_lease_term, min_lease_term, place_order_start, 
      place_order_end, pick_start, pick_end, 
      return_start, return_end, cost_party, 
      cost_party_type, merchant_cost_party_value, 
      platform_cost_party_value, deleted, last_ver, 
      op_user_id, create_time, op_time
      )
    values (#{merchantId,jdbcType=BIGINT}, #{channelId,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, 
      #{marketName,jdbcType=VARCHAR}, #{marketCode,jdbcType=VARCHAR}, #{marketType,jdbcType=INTEGER}, 
      #{dimension,jdbcType=INTEGER}, #{discountMode,jdbcType=INTEGER}, #{discountSubMode,jdbcType=INTEGER}, 
      #{satisfy,jdbcType=INTEGER}, #{discount,jdbcType=INTEGER}, #{maxMoney,jdbcType=INTEGER}, 
      #{maxLeaseTerm,jdbcType=INTEGER}, #{minLeaseTerm,jdbcType=INTEGER}, #{placeOrderStart,jdbcType=BIGINT}, 
      #{placeOrderEnd,jdbcType=BIGINT}, #{pickStart,jdbcType=BIGINT}, #{pickEnd,jdbcType=BIGINT}, 
      #{returnStart,jdbcType=BIGINT}, #{returnEnd,jdbcType=BIGINT}, #{costParty,jdbcType=INTEGER}, 
      #{costPartyType,jdbcType=INTEGER}, #{merchantCostPartyValue,jdbcType=INTEGER}, 
      #{platformCostPartyValue,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER}, 
      #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.Market">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into market
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="marketName != null">
        market_name,
      </if>
      <if test="marketCode != null">
        market_code,
      </if>
      <if test="marketType != null">
        market_type,
      </if>
      <if test="dimension != null">
        dimension,
      </if>
      <if test="discountMode != null">
        discount_mode,
      </if>
      <if test="discountSubMode != null">
        discount_sub_mode,
      </if>
      <if test="satisfy != null">
        satisfy,
      </if>
      <if test="discount != null">
        discount,
      </if>
      <if test="maxMoney != null">
        max_money,
      </if>
      <if test="maxLeaseTerm != null">
        max_lease_term,
      </if>
      <if test="minLeaseTerm != null">
        min_lease_term,
      </if>
      <if test="placeOrderStart != null">
        place_order_start,
      </if>
      <if test="placeOrderEnd != null">
        place_order_end,
      </if>
      <if test="pickStart != null">
        pick_start,
      </if>
      <if test="pickEnd != null">
        pick_end,
      </if>
      <if test="returnStart != null">
        return_start,
      </if>
      <if test="returnEnd != null">
        return_end,
      </if>
      <if test="costParty != null">
        cost_party,
      </if>
      <if test="costPartyType != null">
        cost_party_type,
      </if>
      <if test="merchantCostPartyValue != null">
        merchant_cost_party_value,
      </if>
      <if test="platformCostPartyValue != null">
        platform_cost_party_value,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="marketName != null">
        #{marketName,jdbcType=VARCHAR},
      </if>
      <if test="marketCode != null">
        #{marketCode,jdbcType=VARCHAR},
      </if>
      <if test="marketType != null">
        #{marketType,jdbcType=INTEGER},
      </if>
      <if test="dimension != null">
        #{dimension,jdbcType=INTEGER},
      </if>
      <if test="discountMode != null">
        #{discountMode,jdbcType=INTEGER},
      </if>
      <if test="discountSubMode != null">
        #{discountSubMode,jdbcType=INTEGER},
      </if>
      <if test="satisfy != null">
        #{satisfy,jdbcType=INTEGER},
      </if>
      <if test="discount != null">
        #{discount,jdbcType=INTEGER},
      </if>
      <if test="maxMoney != null">
        #{maxMoney,jdbcType=INTEGER},
      </if>
      <if test="maxLeaseTerm != null">
        #{maxLeaseTerm,jdbcType=INTEGER},
      </if>
      <if test="minLeaseTerm != null">
        #{minLeaseTerm,jdbcType=INTEGER},
      </if>
      <if test="placeOrderStart != null">
        #{placeOrderStart,jdbcType=BIGINT},
      </if>
      <if test="placeOrderEnd != null">
        #{placeOrderEnd,jdbcType=BIGINT},
      </if>
      <if test="pickStart != null">
        #{pickStart,jdbcType=BIGINT},
      </if>
      <if test="pickEnd != null">
        #{pickEnd,jdbcType=BIGINT},
      </if>
      <if test="returnStart != null">
        #{returnStart,jdbcType=BIGINT},
      </if>
      <if test="returnEnd != null">
        #{returnEnd,jdbcType=BIGINT},
      </if>
      <if test="costParty != null">
        #{costParty,jdbcType=INTEGER},
      </if>
      <if test="costPartyType != null">
        #{costPartyType,jdbcType=INTEGER},
      </if>
      <if test="merchantCostPartyValue != null">
        #{merchantCostPartyValue,jdbcType=INTEGER},
      </if>
      <if test="platformCostPartyValue != null">
        #{platformCostPartyValue,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.MarketExample" resultType="java.lang.Long">
    select count(*) from market
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update market
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.marketName != null">
        market_name = #{record.marketName,jdbcType=VARCHAR},
      </if>
      <if test="record.marketCode != null">
        market_code = #{record.marketCode,jdbcType=VARCHAR},
      </if>
      <if test="record.marketType != null">
        market_type = #{record.marketType,jdbcType=INTEGER},
      </if>
      <if test="record.dimension != null">
        dimension = #{record.dimension,jdbcType=INTEGER},
      </if>
      <if test="record.discountMode != null">
        discount_mode = #{record.discountMode,jdbcType=INTEGER},
      </if>
      <if test="record.discountSubMode != null">
        discount_sub_mode = #{record.discountSubMode,jdbcType=INTEGER},
      </if>
      <if test="record.satisfy != null">
        satisfy = #{record.satisfy,jdbcType=INTEGER},
      </if>
      <if test="record.discount != null">
        discount = #{record.discount,jdbcType=INTEGER},
      </if>
      <if test="record.maxMoney != null">
        max_money = #{record.maxMoney,jdbcType=INTEGER},
      </if>
      <if test="record.maxLeaseTerm != null">
        max_lease_term = #{record.maxLeaseTerm,jdbcType=INTEGER},
      </if>
      <if test="record.minLeaseTerm != null">
        min_lease_term = #{record.minLeaseTerm,jdbcType=INTEGER},
      </if>
      <if test="record.placeOrderStart != null">
        place_order_start = #{record.placeOrderStart,jdbcType=BIGINT},
      </if>
      <if test="record.placeOrderEnd != null">
        place_order_end = #{record.placeOrderEnd,jdbcType=BIGINT},
      </if>
      <if test="record.pickStart != null">
        pick_start = #{record.pickStart,jdbcType=BIGINT},
      </if>
      <if test="record.pickEnd != null">
        pick_end = #{record.pickEnd,jdbcType=BIGINT},
      </if>
      <if test="record.returnStart != null">
        return_start = #{record.returnStart,jdbcType=BIGINT},
      </if>
      <if test="record.returnEnd != null">
        return_end = #{record.returnEnd,jdbcType=BIGINT},
      </if>
      <if test="record.costParty != null">
        cost_party = #{record.costParty,jdbcType=INTEGER},
      </if>
      <if test="record.costPartyType != null">
        cost_party_type = #{record.costPartyType,jdbcType=INTEGER},
      </if>
      <if test="record.merchantCostPartyValue != null">
        merchant_cost_party_value = #{record.merchantCostPartyValue,jdbcType=INTEGER},
      </if>
      <if test="record.platformCostPartyValue != null">
        platform_cost_party_value = #{record.platformCostPartyValue,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update market
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      channel_id = #{record.channelId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      market_name = #{record.marketName,jdbcType=VARCHAR},
      market_code = #{record.marketCode,jdbcType=VARCHAR},
      market_type = #{record.marketType,jdbcType=INTEGER},
      dimension = #{record.dimension,jdbcType=INTEGER},
      discount_mode = #{record.discountMode,jdbcType=INTEGER},
      discount_sub_mode = #{record.discountSubMode,jdbcType=INTEGER},
      satisfy = #{record.satisfy,jdbcType=INTEGER},
      discount = #{record.discount,jdbcType=INTEGER},
      max_money = #{record.maxMoney,jdbcType=INTEGER},
      max_lease_term = #{record.maxLeaseTerm,jdbcType=INTEGER},
      min_lease_term = #{record.minLeaseTerm,jdbcType=INTEGER},
      place_order_start = #{record.placeOrderStart,jdbcType=BIGINT},
      place_order_end = #{record.placeOrderEnd,jdbcType=BIGINT},
      pick_start = #{record.pickStart,jdbcType=BIGINT},
      pick_end = #{record.pickEnd,jdbcType=BIGINT},
      return_start = #{record.returnStart,jdbcType=BIGINT},
      return_end = #{record.returnEnd,jdbcType=BIGINT},
      cost_party = #{record.costParty,jdbcType=INTEGER},
      cost_party_type = #{record.costPartyType,jdbcType=INTEGER},
      merchant_cost_party_value = #{record.merchantCostPartyValue,jdbcType=INTEGER},
      platform_cost_party_value = #{record.platformCostPartyValue,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.Market">
    update market
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="marketName != null">
        market_name = #{marketName,jdbcType=VARCHAR},
      </if>
      <if test="marketCode != null">
        market_code = #{marketCode,jdbcType=VARCHAR},
      </if>
      <if test="marketType != null">
        market_type = #{marketType,jdbcType=INTEGER},
      </if>
      <if test="dimension != null">
        dimension = #{dimension,jdbcType=INTEGER},
      </if>
      <if test="discountMode != null">
        discount_mode = #{discountMode,jdbcType=INTEGER},
      </if>
      <if test="discountSubMode != null">
        discount_sub_mode = #{discountSubMode,jdbcType=INTEGER},
      </if>
      <if test="satisfy != null">
        satisfy = #{satisfy,jdbcType=INTEGER},
      </if>
      <if test="discount != null">
        discount = #{discount,jdbcType=INTEGER},
      </if>
      <if test="maxMoney != null">
        max_money = #{maxMoney,jdbcType=INTEGER},
      </if>
      <if test="maxLeaseTerm != null">
        max_lease_term = #{maxLeaseTerm,jdbcType=INTEGER},
      </if>
      <if test="minLeaseTerm != null">
        min_lease_term = #{minLeaseTerm,jdbcType=INTEGER},
      </if>
      <if test="placeOrderStart != null">
        place_order_start = #{placeOrderStart,jdbcType=BIGINT},
      </if>
      <if test="placeOrderEnd != null">
        place_order_end = #{placeOrderEnd,jdbcType=BIGINT},
      </if>
      <if test="pickStart != null">
        pick_start = #{pickStart,jdbcType=BIGINT},
      </if>
      <if test="pickEnd != null">
        pick_end = #{pickEnd,jdbcType=BIGINT},
      </if>
      <if test="returnStart != null">
        return_start = #{returnStart,jdbcType=BIGINT},
      </if>
      <if test="returnEnd != null">
        return_end = #{returnEnd,jdbcType=BIGINT},
      </if>
      <if test="costParty != null">
        cost_party = #{costParty,jdbcType=INTEGER},
      </if>
      <if test="costPartyType != null">
        cost_party_type = #{costPartyType,jdbcType=INTEGER},
      </if>
      <if test="merchantCostPartyValue != null">
        merchant_cost_party_value = #{merchantCostPartyValue,jdbcType=INTEGER},
      </if>
      <if test="platformCostPartyValue != null">
        platform_cost_party_value = #{platformCostPartyValue,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.Market">
    update market
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      channel_id = #{channelId,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      market_name = #{marketName,jdbcType=VARCHAR},
      market_code = #{marketCode,jdbcType=VARCHAR},
      market_type = #{marketType,jdbcType=INTEGER},
      dimension = #{dimension,jdbcType=INTEGER},
      discount_mode = #{discountMode,jdbcType=INTEGER},
      discount_sub_mode = #{discountSubMode,jdbcType=INTEGER},
      satisfy = #{satisfy,jdbcType=INTEGER},
      discount = #{discount,jdbcType=INTEGER},
      max_money = #{maxMoney,jdbcType=INTEGER},
      max_lease_term = #{maxLeaseTerm,jdbcType=INTEGER},
      min_lease_term = #{minLeaseTerm,jdbcType=INTEGER},
      place_order_start = #{placeOrderStart,jdbcType=BIGINT},
      place_order_end = #{placeOrderEnd,jdbcType=BIGINT},
      pick_start = #{pickStart,jdbcType=BIGINT},
      pick_end = #{pickEnd,jdbcType=BIGINT},
      return_start = #{returnStart,jdbcType=BIGINT},
      return_end = #{returnEnd,jdbcType=BIGINT},
      cost_party = #{costParty,jdbcType=INTEGER},
      cost_party_type = #{costPartyType,jdbcType=INTEGER},
      merchant_cost_party_value = #{merchantCostPartyValue,jdbcType=INTEGER},
      platform_cost_party_value = #{platformCostPartyValue,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into market
    (merchant_id, channel_id, status, market_name, market_code, market_type, dimension, 
      discount_mode, discount_sub_mode, satisfy, discount, max_money, max_lease_term, 
      min_lease_term, place_order_start, place_order_end, pick_start, pick_end, return_start, 
      return_end, cost_party, cost_party_type, merchant_cost_party_value, platform_cost_party_value, 
      deleted, last_ver, op_user_id, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.channelId,jdbcType=BIGINT}, #{item.status,jdbcType=INTEGER}, 
        #{item.marketName,jdbcType=VARCHAR}, #{item.marketCode,jdbcType=VARCHAR}, #{item.marketType,jdbcType=INTEGER}, 
        #{item.dimension,jdbcType=INTEGER}, #{item.discountMode,jdbcType=INTEGER}, #{item.discountSubMode,jdbcType=INTEGER}, 
        #{item.satisfy,jdbcType=INTEGER}, #{item.discount,jdbcType=INTEGER}, #{item.maxMoney,jdbcType=INTEGER}, 
        #{item.maxLeaseTerm,jdbcType=INTEGER}, #{item.minLeaseTerm,jdbcType=INTEGER}, #{item.placeOrderStart,jdbcType=BIGINT}, 
        #{item.placeOrderEnd,jdbcType=BIGINT}, #{item.pickStart,jdbcType=BIGINT}, #{item.pickEnd,jdbcType=BIGINT}, 
        #{item.returnStart,jdbcType=BIGINT}, #{item.returnEnd,jdbcType=BIGINT}, #{item.costParty,jdbcType=INTEGER}, 
        #{item.costPartyType,jdbcType=INTEGER}, #{item.merchantCostPartyValue,jdbcType=INTEGER}, 
        #{item.platformCostPartyValue,jdbcType=INTEGER}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.lastVer,jdbcType=INTEGER}, #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into market (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'channel_id'.toString() == column.value">
          #{item.channelId,jdbcType=BIGINT}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'market_name'.toString() == column.value">
          #{item.marketName,jdbcType=VARCHAR}
        </if>
        <if test="'market_code'.toString() == column.value">
          #{item.marketCode,jdbcType=VARCHAR}
        </if>
        <if test="'market_type'.toString() == column.value">
          #{item.marketType,jdbcType=INTEGER}
        </if>
        <if test="'dimension'.toString() == column.value">
          #{item.dimension,jdbcType=INTEGER}
        </if>
        <if test="'discount_mode'.toString() == column.value">
          #{item.discountMode,jdbcType=INTEGER}
        </if>
        <if test="'discount_sub_mode'.toString() == column.value">
          #{item.discountSubMode,jdbcType=INTEGER}
        </if>
        <if test="'satisfy'.toString() == column.value">
          #{item.satisfy,jdbcType=INTEGER}
        </if>
        <if test="'discount'.toString() == column.value">
          #{item.discount,jdbcType=INTEGER}
        </if>
        <if test="'max_money'.toString() == column.value">
          #{item.maxMoney,jdbcType=INTEGER}
        </if>
        <if test="'max_lease_term'.toString() == column.value">
          #{item.maxLeaseTerm,jdbcType=INTEGER}
        </if>
        <if test="'min_lease_term'.toString() == column.value">
          #{item.minLeaseTerm,jdbcType=INTEGER}
        </if>
        <if test="'place_order_start'.toString() == column.value">
          #{item.placeOrderStart,jdbcType=BIGINT}
        </if>
        <if test="'place_order_end'.toString() == column.value">
          #{item.placeOrderEnd,jdbcType=BIGINT}
        </if>
        <if test="'pick_start'.toString() == column.value">
          #{item.pickStart,jdbcType=BIGINT}
        </if>
        <if test="'pick_end'.toString() == column.value">
          #{item.pickEnd,jdbcType=BIGINT}
        </if>
        <if test="'return_start'.toString() == column.value">
          #{item.returnStart,jdbcType=BIGINT}
        </if>
        <if test="'return_end'.toString() == column.value">
          #{item.returnEnd,jdbcType=BIGINT}
        </if>
        <if test="'cost_party'.toString() == column.value">
          #{item.costParty,jdbcType=INTEGER}
        </if>
        <if test="'cost_party_type'.toString() == column.value">
          #{item.costPartyType,jdbcType=INTEGER}
        </if>
        <if test="'merchant_cost_party_value'.toString() == column.value">
          #{item.merchantCostPartyValue,jdbcType=INTEGER}
        </if>
        <if test="'platform_cost_party_value'.toString() == column.value">
          #{item.platformCostPartyValue,jdbcType=INTEGER}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.VehiclePickReturnAttMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.VehiclePickReturnAtt">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="pr_type" jdbcType="TINYINT" property="prType" />
    <result column="vehicle_pick_return_id" jdbcType="BIGINT" property="vehiclePickReturnId" />
    <result column="att_url" jdbcType="VARCHAR" property="attUrl" />
    <result column="att_type" jdbcType="TINYINT" property="attType" />
    <result column="busi_type" jdbcType="TINYINT" property="busiType" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, pr_type, vehicle_pick_return_id, att_url, att_type, busi_type, source, 
    create_time, op_time, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.VehiclePickReturnAttExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_pick_return_att
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_pick_return_att
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_pick_return_att
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.VehiclePickReturnAttExample">
    delete from vehicle_pick_return_att
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.VehiclePickReturnAtt">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_pick_return_att (order_id, pr_type, vehicle_pick_return_id, 
      att_url, att_type, busi_type, 
      source, create_time, op_time, 
      op_user_id)
    values (#{orderId,jdbcType=BIGINT}, #{prType,jdbcType=TINYINT}, #{vehiclePickReturnId,jdbcType=BIGINT}, 
      #{attUrl,jdbcType=VARCHAR}, #{attType,jdbcType=TINYINT}, #{busiType,jdbcType=TINYINT}, 
      #{source,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, 
      #{opUserId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.VehiclePickReturnAtt">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_pick_return_att
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="prType != null">
        pr_type,
      </if>
      <if test="vehiclePickReturnId != null">
        vehicle_pick_return_id,
      </if>
      <if test="attUrl != null">
        att_url,
      </if>
      <if test="attType != null">
        att_type,
      </if>
      <if test="busiType != null">
        busi_type,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="prType != null">
        #{prType,jdbcType=TINYINT},
      </if>
      <if test="vehiclePickReturnId != null">
        #{vehiclePickReturnId,jdbcType=BIGINT},
      </if>
      <if test="attUrl != null">
        #{attUrl,jdbcType=VARCHAR},
      </if>
      <if test="attType != null">
        #{attType,jdbcType=TINYINT},
      </if>
      <if test="busiType != null">
        #{busiType,jdbcType=TINYINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.VehiclePickReturnAttExample" resultType="java.lang.Long">
    select count(*) from vehicle_pick_return_att
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_pick_return_att
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.prType != null">
        pr_type = #{record.prType,jdbcType=TINYINT},
      </if>
      <if test="record.vehiclePickReturnId != null">
        vehicle_pick_return_id = #{record.vehiclePickReturnId,jdbcType=BIGINT},
      </if>
      <if test="record.attUrl != null">
        att_url = #{record.attUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.attType != null">
        att_type = #{record.attType,jdbcType=TINYINT},
      </if>
      <if test="record.busiType != null">
        busi_type = #{record.busiType,jdbcType=TINYINT},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_pick_return_att
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      pr_type = #{record.prType,jdbcType=TINYINT},
      vehicle_pick_return_id = #{record.vehiclePickReturnId,jdbcType=BIGINT},
      att_url = #{record.attUrl,jdbcType=VARCHAR},
      att_type = #{record.attType,jdbcType=TINYINT},
      busi_type = #{record.busiType,jdbcType=TINYINT},
      source = #{record.source,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.VehiclePickReturnAtt">
    update vehicle_pick_return_att
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="prType != null">
        pr_type = #{prType,jdbcType=TINYINT},
      </if>
      <if test="vehiclePickReturnId != null">
        vehicle_pick_return_id = #{vehiclePickReturnId,jdbcType=BIGINT},
      </if>
      <if test="attUrl != null">
        att_url = #{attUrl,jdbcType=VARCHAR},
      </if>
      <if test="attType != null">
        att_type = #{attType,jdbcType=TINYINT},
      </if>
      <if test="busiType != null">
        busi_type = #{busiType,jdbcType=TINYINT},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.VehiclePickReturnAtt">
    update vehicle_pick_return_att
    set order_id = #{orderId,jdbcType=BIGINT},
      pr_type = #{prType,jdbcType=TINYINT},
      vehicle_pick_return_id = #{vehiclePickReturnId,jdbcType=BIGINT},
      att_url = #{attUrl,jdbcType=VARCHAR},
      att_type = #{attType,jdbcType=TINYINT},
      busi_type = #{busiType,jdbcType=TINYINT},
      source = #{source,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_pick_return_att
    (order_id, pr_type, vehicle_pick_return_id, att_url, att_type, busi_type, source, 
      create_time, op_time, op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=BIGINT}, #{item.prType,jdbcType=TINYINT}, #{item.vehiclePickReturnId,jdbcType=BIGINT}, 
        #{item.attUrl,jdbcType=VARCHAR}, #{item.attType,jdbcType=TINYINT}, #{item.busiType,jdbcType=TINYINT}, 
        #{item.source,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, 
        #{item.opUserId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_pick_return_att (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'pr_type'.toString() == column.value">
          #{item.prType,jdbcType=TINYINT}
        </if>
        <if test="'vehicle_pick_return_id'.toString() == column.value">
          #{item.vehiclePickReturnId,jdbcType=BIGINT}
        </if>
        <if test="'att_url'.toString() == column.value">
          #{item.attUrl,jdbcType=VARCHAR}
        </if>
        <if test="'att_type'.toString() == column.value">
          #{item.attType,jdbcType=TINYINT}
        </if>
        <if test="'busi_type'.toString() == column.value">
          #{item.busiType,jdbcType=TINYINT}
        </if>
        <if test="'source'.toString() == column.value">
          #{item.source,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
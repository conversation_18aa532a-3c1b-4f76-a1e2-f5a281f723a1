<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.OrderDepositMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.OrderDeposit">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="vehicle_info_id" jdbcType="BIGINT" property="vehicleInfoId" />
    <result column="vehicle_deposit_amount" jdbcType="INTEGER" property="vehicleDepositAmount" />
    <result column="illegal_order_deposit_amount" jdbcType="INTEGER" property="illegalOrderDepositAmount" />
    <result column="vehicle_deposit_pay_amount" jdbcType="INTEGER" property="vehicleDepositPayAmount" />
    <result column="illegal_order_deposit_pay_amount" jdbcType="INTEGER" property="illegalOrderDepositPayAmount" />
    <result column="pay_type" jdbcType="TINYINT" property="payType" />
    <result column="pay_status" jdbcType="TINYINT" property="payStatus" />
    <result column="order_source" jdbcType="TINYINT" property="orderSource" />
    <result column="charge_proof_url" jdbcType="VARCHAR" property="chargeProofUrl" />
    <result column="charge_proof_url2" jdbcType="VARCHAR" property="chargeProofUrl2" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="remark_desc" jdbcType="VARCHAR" property="remarkDesc" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, vehicle_info_id, vehicle_deposit_amount, illegal_order_deposit_amount,  vehicle_deposit_pay_amount, illegal_order_deposit_pay_amount,
    pay_type, pay_status, order_source, charge_proof_url, charge_proof_url2,
    remark, remark_desc, deleted, last_ver, create_user_id, op_time, op_user_id, extra
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.OrderDepositExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from order_deposit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_deposit
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_deposit
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.OrderDepositExample">
    delete from order_deposit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.OrderDeposit">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_deposit (order_id, vehicle_info_id, vehicle_deposit_amount, 
      illegal_order_deposit_amount, vehicle_deposit_pay_amount,
      illegal_order_deposit_pay_amount, pay_type,
      pay_status, order_source, charge_proof_url, 
      charge_proof_url2, remark, remark_desc, 
      deleted, last_ver, create_user_id, 
      op_time, op_user_id, extra)
    values (#{orderId,jdbcType=BIGINT}, #{vehicleInfoId,jdbcType=BIGINT}, #{vehicleDepositAmount,jdbcType=INTEGER},
      #{illegalOrderDepositAmount,jdbcType=INTEGER}, #{vehicleDepositPayAmount,jdbcType=INTEGER},
      #{illegalOrderDepositPayAmount,jdbcType=INTEGER}, #{payType,jdbcType=TINYINT},
      #{payStatus,jdbcType=TINYINT}, #{orderSource,jdbcType=TINYINT}, #{chargeProofUrl,jdbcType=VARCHAR}, 
      #{chargeProofUrl2,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{remarkDesc,jdbcType=VARCHAR}, 
      #{deleted,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER}, #{createUserId,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}, #{extra,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.OrderDeposit">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_deposit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="vehicleInfoId != null">
        vehicle_info_id,
      </if>
      <if test="vehicleDepositAmount != null">
        vehicle_deposit_amount,
      </if>
      <if test="illegalOrderDepositAmount != null">
        illegal_order_deposit_amount,
      </if>
      <if test="vehicleDepositPayAmount != null">
        vehicle_deposit_pay_amount,
      </if>
      <if test="illegalOrderDepositPayAmount != null">
        illegal_order_deposit_pay_amount,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="payStatus != null">
        pay_status,
      </if>
      <if test="orderSource != null">
        order_source,
      </if>
      <if test="chargeProofUrl != null">
        charge_proof_url,
      </if>
      <if test="chargeProofUrl2 != null">
        charge_proof_url2,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="remarkDesc != null">
        remark_desc,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleInfoId != null">
        #{vehicleInfoId,jdbcType=BIGINT},
      </if>
      <if test="vehicleDepositAmount != null">
        #{vehicleDepositAmount,jdbcType=INTEGER},
      </if>
      <if test="illegalOrderDepositAmount != null">
        #{illegalOrderDepositAmount,jdbcType=INTEGER},
      </if>
      <if test="vehicleDepositPayAmount != null">
        #{vehicleDepositPayAmount,jdbcType=INTEGER},
      </if>
      <if test="illegalOrderDepositPayAmount != null">
        #{illegalOrderDepositPayAmount,jdbcType=INTEGER},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=TINYINT},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=TINYINT},
      </if>
      <if test="orderSource != null">
        #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="chargeProofUrl != null">
        #{chargeProofUrl,jdbcType=VARCHAR},
      </if>
      <if test="chargeProofUrl2 != null">
        #{chargeProofUrl2,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="remarkDesc != null">
        #{remarkDesc,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.OrderDepositExample" resultType="java.lang.Long">
    select count(*) from order_deposit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_deposit
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleInfoId != null">
        vehicle_info_id = #{record.vehicleInfoId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleDepositAmount != null">
        vehicle_deposit_amount = #{record.vehicleDepositAmount,jdbcType=INTEGER},
      </if>
      <if test="record.illegalOrderDepositAmount != null">
        illegal_order_deposit_amount = #{record.illegalOrderDepositAmount,jdbcType=INTEGER},
      </if>
      <if test="record.vehicleDepositPayAmount != null">
        vehicle_deposit_pay_amount = #{record.vehicleDepositPayAmount,jdbcType=INTEGER},
      </if>
      <if test="record.illegalOrderDepositPayAmount != null">
        illegal_order_deposit_pay_amount = #{record.illegalOrderDepositPayAmount,jdbcType=INTEGER},
      </if>
      <if test="record.payType != null">
        pay_type = #{record.payType,jdbcType=TINYINT},
      </if>
      <if test="record.payStatus != null">
        pay_status = #{record.payStatus,jdbcType=TINYINT},
      </if>
      <if test="record.orderSource != null">
        order_source = #{record.orderSource,jdbcType=TINYINT},
      </if>
      <if test="record.chargeProofUrl != null">
        charge_proof_url = #{record.chargeProofUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeProofUrl2 != null">
        charge_proof_url2 = #{record.chargeProofUrl2,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.remarkDesc != null">
        remark_desc = #{record.remarkDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_deposit
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      vehicle_info_id = #{record.vehicleInfoId,jdbcType=BIGINT},
      vehicle_deposit_amount = #{record.vehicleDepositAmount,jdbcType=INTEGER},
      illegal_order_deposit_amount = #{record.illegalOrderDepositAmount,jdbcType=INTEGER},
      vehicle_deposit_pay_amount = #{record.vehicleDepositPayAmount,jdbcType=INTEGER},
      illegal_order_deposit_pay_amount = #{record.illegalOrderDepositPayAmount,jdbcType=INTEGER},
      pay_type = #{record.payType,jdbcType=TINYINT},
      pay_status = #{record.payStatus,jdbcType=TINYINT},
      order_source = #{record.orderSource,jdbcType=TINYINT},
      charge_proof_url = #{record.chargeProofUrl,jdbcType=VARCHAR},
      charge_proof_url2 = #{record.chargeProofUrl2,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      remark_desc = #{record.remarkDesc,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      create_user_id = #{record.createUserId,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      extra = #{record.extra,jdbcType=VARCHAR},
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.OrderDeposit">
    update order_deposit
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleInfoId != null">
        vehicle_info_id = #{vehicleInfoId,jdbcType=BIGINT},
      </if>
      <if test="vehicleDepositAmount != null">
        vehicle_deposit_amount = #{vehicleDepositAmount,jdbcType=INTEGER},
      </if>
      <if test="illegalOrderDepositAmount != null">
        illegal_order_deposit_amount = #{illegalOrderDepositAmount,jdbcType=INTEGER},
      </if>
      <if test="vehicleDepositPayAmount != null">
        vehicle_deposit_pay_amount = #{vehicleDepositPayAmount,jdbcType=INTEGER},
      </if>
      <if test="illegalOrderDepositPayAmount != null">
        illegal_order_deposit_pay_amount = #{illegalOrderDepositPayAmount,jdbcType=INTEGER},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=TINYINT},
      </if>
      <if test="payStatus != null">
        pay_status = #{payStatus,jdbcType=TINYINT},
      </if>
      <if test="orderSource != null">
        order_source = #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="chargeProofUrl != null">
        charge_proof_url = #{chargeProofUrl,jdbcType=VARCHAR},
      </if>
      <if test="chargeProofUrl2 != null">
        charge_proof_url2 = #{chargeProofUrl2,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="remarkDesc != null">
        remark_desc = #{remarkDesc,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.OrderDeposit">
    update order_deposit
    set order_id = #{orderId,jdbcType=BIGINT},
      vehicle_info_id = #{vehicleInfoId,jdbcType=BIGINT},
      vehicle_deposit_amount = #{vehicleDepositAmount,jdbcType=INTEGER},
      illegal_order_deposit_amount = #{illegalOrderDepositAmount,jdbcType=INTEGER},
       vehicle_deposit_pay_amount = #{vehicleDepositPayAmount,jdbcType=INTEGER},
      illegal_order_deposit_pay_amount = #{illegalOrderDepositPayAmount,jdbcType=INTEGER},
      pay_type = #{payType,jdbcType=TINYINT},
      pay_status = #{payStatus,jdbcType=TINYINT},
      order_source = #{orderSource,jdbcType=TINYINT},
      charge_proof_url = #{chargeProofUrl,jdbcType=VARCHAR},
      charge_proof_url2 = #{chargeProofUrl2,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      remark_desc = #{remarkDesc,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      create_user_id = #{createUserId,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      extra = #{extra,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_deposit
    (order_id, vehicle_info_id, vehicle_deposit_amount, illegal_order_deposit_amount,
      vehicle_deposit_pay_amount, illegal_order_deposit_pay_amount, pay_type, pay_status, order_source, charge_proof_url, charge_proof_url2,
      remark, remark_desc, deleted, last_ver, create_user_id, op_time, op_user_id, extra)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=BIGINT}, #{item.vehicleInfoId,jdbcType=BIGINT}, #{item.vehicleDepositAmount,jdbcType=INTEGER},
        #{item.illegalOrderDepositAmount,jdbcType=INTEGER}, #{item.vehicleDepositPayAmount,jdbcType=INTEGER}, #{item.illegalOrderDepositPayAmount,jdbcType=INTEGER},
        #{item.payType,jdbcType=TINYINT}, #{item.payStatus,jdbcType=TINYINT}, #{item.orderSource,jdbcType=TINYINT},
        #{item.chargeProofUrl,jdbcType=VARCHAR}, #{item.chargeProofUrl2,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.remarkDesc,jdbcType=VARCHAR}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.lastVer,jdbcType=INTEGER}, #{item.createUserId,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, 
        #{item.opUserId,jdbcType=BIGINT}, #{item.extra,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_deposit (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_info_id'.toString() == column.value">
          #{item.vehicleInfoId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_deposit_amount'.toString() == column.value">
          #{item.vehicleDepositAmount,jdbcType=INTEGER}
        </if>
        <if test="'illegal_order_deposit_amount'.toString() == column.value">
          #{item.illegalOrderDepositAmount,jdbcType=INTEGER}
        </if>
        <if test="'vehicle_deposit_pay_amount'.toString() == column.value">
          #{item.vehicleDepositPayAmount,jdbcType=INTEGER}
        </if>
        <if test="'illegal_order_deposit_pay_amount'.toString() == column.value">
          #{item.illegalOrderDepositPayAmount,jdbcType=INTEGER}
        </if>
        <if test="'pay_type'.toString() == column.value">
          #{item.payType,jdbcType=TINYINT}
        </if>
        <if test="'pay_status'.toString() == column.value">
          #{item.payStatus,jdbcType=TINYINT}
        </if>
        <if test="'order_source'.toString() == column.value">
          #{item.orderSource,jdbcType=TINYINT}
        </if>
        <if test="'charge_proof_url'.toString() == column.value">
          #{item.chargeProofUrl,jdbcType=VARCHAR}
        </if>
        <if test="'charge_proof_url2'.toString() == column.value">
          #{item.chargeProofUrl2,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'remark_desc'.toString() == column.value">
          #{item.remarkDesc,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'create_user_id'.toString() == column.value">
          #{item.createUserId,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'extra'.toString() == column.value">
          #{item.extra,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
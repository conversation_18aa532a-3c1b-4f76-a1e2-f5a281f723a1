<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.PackageSettingMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.PackageSetting">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="package_code" jdbcType="VARCHAR" property="packageCode" />
    <result column="package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="rent_type_code" jdbcType="VARCHAR" property="rentTypeCode" />
    <result column="rent_type" jdbcType="VARCHAR" property="rentType" />
    <result column="package_price" jdbcType="BIGINT" property="packagePrice" />
    <result column="fee_spec" jdbcType="VARCHAR" property="feeSpec" />
    <result column="fee_spec_code" jdbcType="VARCHAR" property="feeSpecCode" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="last_ver" jdbcType="BIGINT" property="lastVer" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, package_code, package_name, rent_type_code, rent_type, package_price, 
    fee_spec, fee_spec_code, create_time, op_time, last_ver, deleted, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.PackageSettingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from package_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from package_setting
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.PackageSetting">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into package_setting (merchant_id, package_code, package_name, 
      rent_type_code, rent_type, package_price, 
      fee_spec, fee_spec_code, create_time, 
      op_time, last_ver, deleted, 
      op_user_id)
    values (#{merchantId,jdbcType=BIGINT}, #{packageCode,jdbcType=VARCHAR}, #{packageName,jdbcType=VARCHAR}, 
      #{rentTypeCode,jdbcType=VARCHAR}, #{rentType,jdbcType=VARCHAR}, #{packagePrice,jdbcType=BIGINT}, 
      #{feeSpec,jdbcType=VARCHAR}, #{feeSpecCode,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{lastVer,jdbcType=BIGINT}, #{deleted,jdbcType=TINYINT}, 
      #{opUserId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.PackageSetting">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into package_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="packageCode != null">
        package_code,
      </if>
      <if test="packageName != null">
        package_name,
      </if>
      <if test="rentTypeCode != null">
        rent_type_code,
      </if>
      <if test="rentType != null">
        rent_type,
      </if>
      <if test="packagePrice != null">
        package_price,
      </if>
      <if test="feeSpec != null">
        fee_spec,
      </if>
      <if test="feeSpecCode != null">
        fee_spec_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="packageCode != null">
        #{packageCode,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null">
        #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="rentTypeCode != null">
        #{rentTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="rentType != null">
        #{rentType,jdbcType=VARCHAR},
      </if>
      <if test="packagePrice != null">
        #{packagePrice,jdbcType=BIGINT},
      </if>
      <if test="feeSpec != null">
        #{feeSpec,jdbcType=VARCHAR},
      </if>
      <if test="feeSpecCode != null">
        #{feeSpecCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.PackageSettingExample" resultType="java.lang.Long">
    select count(*) from package_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update package_setting
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.packageCode != null">
        package_code = #{record.packageCode,jdbcType=VARCHAR},
      </if>
      <if test="record.packageName != null">
        package_name = #{record.packageName,jdbcType=VARCHAR},
      </if>
      <if test="record.rentTypeCode != null">
        rent_type_code = #{record.rentTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.rentType != null">
        rent_type = #{record.rentType,jdbcType=VARCHAR},
      </if>
      <if test="record.packagePrice != null">
        package_price = #{record.packagePrice,jdbcType=BIGINT},
      </if>
      <if test="record.feeSpec != null">
        fee_spec = #{record.feeSpec,jdbcType=VARCHAR},
      </if>
      <if test="record.feeSpecCode != null">
        fee_spec_code = #{record.feeSpecCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=BIGINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update package_setting
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      package_code = #{record.packageCode,jdbcType=VARCHAR},
      package_name = #{record.packageName,jdbcType=VARCHAR},
      rent_type_code = #{record.rentTypeCode,jdbcType=VARCHAR},
      rent_type = #{record.rentType,jdbcType=VARCHAR},
      package_price = #{record.packagePrice,jdbcType=BIGINT},
      fee_spec = #{record.feeSpec,jdbcType=VARCHAR},
      fee_spec_code = #{record.feeSpecCode,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      last_ver = #{record.lastVer,jdbcType=BIGINT},
      deleted = #{record.deleted,jdbcType=TINYINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.PackageSetting">
    update package_setting
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="packageCode != null">
        package_code = #{packageCode,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null">
        package_name = #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="rentTypeCode != null">
        rent_type_code = #{rentTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="rentType != null">
        rent_type = #{rentType,jdbcType=VARCHAR},
      </if>
      <if test="packagePrice != null">
        package_price = #{packagePrice,jdbcType=BIGINT},
      </if>
      <if test="feeSpec != null">
        fee_spec = #{feeSpec,jdbcType=VARCHAR},
      </if>
      <if test="feeSpecCode != null">
        fee_spec_code = #{feeSpecCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.PackageSetting">
    update package_setting
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      package_code = #{packageCode,jdbcType=VARCHAR},
      package_name = #{packageName,jdbcType=VARCHAR},
      rent_type_code = #{rentTypeCode,jdbcType=VARCHAR},
      rent_type = #{rentType,jdbcType=VARCHAR},
      package_price = #{packagePrice,jdbcType=BIGINT},
      fee_spec = #{feeSpec,jdbcType=VARCHAR},
      fee_spec_code = #{feeSpecCode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      last_ver = #{lastVer,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=TINYINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into package_setting
    (merchant_id, package_code, package_name, rent_type_code, rent_type, package_price, 
      fee_spec, fee_spec_code, create_time, op_time, last_ver, deleted, op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.packageCode,jdbcType=VARCHAR}, #{item.packageName,jdbcType=VARCHAR}, 
        #{item.rentTypeCode,jdbcType=VARCHAR}, #{item.rentType,jdbcType=VARCHAR}, #{item.packagePrice,jdbcType=BIGINT}, 
        #{item.feeSpec,jdbcType=VARCHAR}, #{item.feeSpecCode,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.lastVer,jdbcType=BIGINT}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.opUserId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into package_setting (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'package_code'.toString() == column.value">
          #{item.packageCode,jdbcType=VARCHAR}
        </if>
        <if test="'package_name'.toString() == column.value">
          #{item.packageName,jdbcType=VARCHAR}
        </if>
        <if test="'rent_type_code'.toString() == column.value">
          #{item.rentTypeCode,jdbcType=VARCHAR}
        </if>
        <if test="'rent_type'.toString() == column.value">
          #{item.rentType,jdbcType=VARCHAR}
        </if>
        <if test="'package_price'.toString() == column.value">
          #{item.packagePrice,jdbcType=BIGINT}
        </if>
        <if test="'fee_spec'.toString() == column.value">
          #{item.feeSpec,jdbcType=VARCHAR}
        </if>
        <if test="'fee_spec_code'.toString() == column.value">
          #{item.feeSpecCode,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=BIGINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
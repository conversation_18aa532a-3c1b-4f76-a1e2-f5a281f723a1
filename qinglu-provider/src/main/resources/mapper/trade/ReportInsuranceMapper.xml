<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.ReportInsuranceMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.ReportInsurance">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="city_id" jdbcType="BIGINT" property="cityId" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="type_id" jdbcType="BIGINT" property="typeId" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="insurance_gmv" jdbcType="BIGINT" property="insuranceGmv" />
    <result column="insurance_rate" jdbcType="DOUBLE" property="insuranceRate" />
    <result column="ymd" jdbcType="INTEGER" property="ymd" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, city_id, channel_id, store_id, vehicle_model_id, type_id, type_name, 
    insurance_gmv, insurance_rate, ymd, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.ReportInsuranceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from report_insurance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from report_insurance
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from report_insurance
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.ReportInsuranceExample">
    delete from report_insurance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.ReportInsurance">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into report_insurance (merchant_id, city_id, channel_id, 
      store_id, vehicle_model_id, type_id, 
      type_name, insurance_gmv, insurance_rate, 
      ymd, create_time, op_time
      )
    values (#{merchantId,jdbcType=BIGINT}, #{cityId,jdbcType=BIGINT}, #{channelId,jdbcType=BIGINT}, 
      #{storeId,jdbcType=BIGINT}, #{vehicleModelId,jdbcType=BIGINT}, #{typeId,jdbcType=BIGINT}, 
      #{typeName,jdbcType=VARCHAR}, #{insuranceGmv,jdbcType=BIGINT}, #{insuranceRate,jdbcType=DOUBLE},
      #{ymd,jdbcType=INTEGER}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.ReportInsurance">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into report_insurance
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="typeId != null">
        type_id,
      </if>
      <if test="typeName != null">
        type_name,
      </if>
      <if test="insuranceGmv != null">
        insurance_gmv,
      </if>
      <if test="insuranceRate != null">
        insurance_rate,
      </if>
      <if test="ymd != null">
        ymd,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=BIGINT},
      </if>
      <if test="typeName != null">
        #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="insuranceGmv != null">
        #{insuranceGmv,jdbcType=BIGINT},
      </if>
      <if test="insuranceRate != null">
        #{insuranceRate,jdbcType=DOUBLE},
      </if>
      <if test="ymd != null">
        #{ymd,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.ReportInsuranceExample" resultType="java.lang.Long">
    select count(*) from report_insurance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update report_insurance
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.cityId != null">
        city_id = #{record.cityId,jdbcType=BIGINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.typeId != null">
        type_id = #{record.typeId,jdbcType=BIGINT},
      </if>
      <if test="record.typeName != null">
        type_name = #{record.typeName,jdbcType=VARCHAR},
      </if>
      <if test="record.insuranceGmv != null">
        insurance_gmv = #{record.insuranceGmv,jdbcType=BIGINT},
      </if>
      <if test="record.insuranceRate != null">
        insurance_rate = #{record.insuranceRate,jdbcType=DOUBLE},
      </if>
      <if test="record.ymd != null">
        ymd = #{record.ymd,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update report_insurance
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      city_id = #{record.cityId,jdbcType=BIGINT},
      channel_id = #{record.channelId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      type_id = #{record.typeId,jdbcType=BIGINT},
      type_name = #{record.typeName,jdbcType=VARCHAR},
      insurance_gmv = #{record.insuranceGmv,jdbcType=BIGINT},
      insurance_rate = #{record.insuranceRate,jdbcType=DOUBLE},
      ymd = #{record.ymd,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.ReportInsurance">
    update report_insurance
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        type_id = #{typeId,jdbcType=BIGINT},
      </if>
      <if test="typeName != null">
        type_name = #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="insuranceGmv != null">
        insurance_gmv = #{insuranceGmv,jdbcType=BIGINT},
      </if>
      <if test="insuranceRate != null">
        insurance_rate = #{insuranceRate,jdbcType=DOUBLE},
      </if>
      <if test="ymd != null">
        ymd = #{ymd,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.ReportInsurance">
    update report_insurance
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      city_id = #{cityId,jdbcType=BIGINT},
      channel_id = #{channelId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      type_id = #{typeId,jdbcType=BIGINT},
      type_name = #{typeName,jdbcType=VARCHAR},
      insurance_gmv = #{insuranceGmv,jdbcType=BIGINT},
      insurance_rate = #{insuranceRate,jdbcType=DOUBLE},
      ymd = #{ymd,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into report_insurance
    (merchant_id, city_id, channel_id, store_id, vehicle_model_id, type_id, type_name, 
      insurance_gmv, insurance_rate, ymd, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.cityId,jdbcType=BIGINT}, #{item.channelId,jdbcType=BIGINT}, 
        #{item.storeId,jdbcType=BIGINT}, #{item.vehicleModelId,jdbcType=BIGINT}, #{item.typeId,jdbcType=BIGINT}, 
        #{item.typeName,jdbcType=VARCHAR}, #{item.insuranceGmv,jdbcType=BIGINT}, #{item.insuranceRate,jdbcType=DOUBLE},
        #{item.ymd,jdbcType=INTEGER}, now(), now()
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into report_insurance (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'city_id'.toString() == column.value">
          #{item.cityId,jdbcType=BIGINT}
        </if>
        <if test="'channel_id'.toString() == column.value">
          #{item.channelId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'type_id'.toString() == column.value">
          #{item.typeId,jdbcType=BIGINT}
        </if>
        <if test="'type_name'.toString() == column.value">
          #{item.typeName,jdbcType=VARCHAR}
        </if>
        <if test="'insurance_gmv'.toString() == column.value">
          #{item.insuranceGmv,jdbcType=BIGINT}
        </if>
        <if test="'insurance_rate'.toString() == column.value">
          #{item.insuranceRate,jdbcType=DOUBLE}
        </if>
        <if test="'ymd'.toString() == column.value">
          #{item.ymd,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
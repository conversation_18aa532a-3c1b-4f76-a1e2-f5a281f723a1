<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.EtcOrderTollFeeMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.EtcOrderTollFee">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="etc_order" jdbcType="VARCHAR" property="etcOrder" />
    <result column="trip_id" jdbcType="VARCHAR" property="tripId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="start_station_name" jdbcType="VARCHAR" property="startStationName" />
    <result column="end_station_name" jdbcType="VARCHAR" property="endStationName" />
    <result column="sub_type" jdbcType="TINYINT" property="subType" />
    <result column="sub_scene" jdbcType="TINYINT" property="subScene" />
    <result column="amt" jdbcType="BIGINT" property="amt" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, etc_order, trip_id, start_time, end_time, start_station_name, end_station_name, 
    sub_type, sub_scene, amt, op_user_id, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.EtcOrderTollFeeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from etc_order_toll_fee
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from etc_order_toll_fee
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from etc_order_toll_fee
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.EtcOrderTollFeeExample">
    delete from etc_order_toll_fee
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.EtcOrderTollFee">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into etc_order_toll_fee (etc_order, trip_id, start_time, 
      end_time, start_station_name, end_station_name, 
      sub_type, sub_scene, amt, 
      op_user_id, create_time, op_time
      )
    values (#{etcOrder,jdbcType=VARCHAR}, #{tripId,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{startStationName,jdbcType=VARCHAR}, #{endStationName,jdbcType=VARCHAR}, 
      #{subType,jdbcType=TINYINT}, #{subScene,jdbcType=TINYINT}, #{amt,jdbcType=BIGINT}, 
      #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.EtcOrderTollFee">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into etc_order_toll_fee
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="etcOrder != null">
        etc_order,
      </if>
      <if test="tripId != null">
        trip_id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="startStationName != null">
        start_station_name,
      </if>
      <if test="endStationName != null">
        end_station_name,
      </if>
      <if test="subType != null">
        sub_type,
      </if>
      <if test="subScene != null">
        sub_scene,
      </if>
      <if test="amt != null">
        amt,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="etcOrder != null">
        #{etcOrder,jdbcType=VARCHAR},
      </if>
      <if test="tripId != null">
        #{tripId,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startStationName != null">
        #{startStationName,jdbcType=VARCHAR},
      </if>
      <if test="endStationName != null">
        #{endStationName,jdbcType=VARCHAR},
      </if>
      <if test="subType != null">
        #{subType,jdbcType=TINYINT},
      </if>
      <if test="subScene != null">
        #{subScene,jdbcType=TINYINT},
      </if>
      <if test="amt != null">
        #{amt,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.EtcOrderTollFeeExample" resultType="java.lang.Long">
    select count(*) from etc_order_toll_fee
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update etc_order_toll_fee
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.etcOrder != null">
        etc_order = #{record.etcOrder,jdbcType=VARCHAR},
      </if>
      <if test="record.tripId != null">
        trip_id = #{record.tripId,jdbcType=VARCHAR},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.startStationName != null">
        start_station_name = #{record.startStationName,jdbcType=VARCHAR},
      </if>
      <if test="record.endStationName != null">
        end_station_name = #{record.endStationName,jdbcType=VARCHAR},
      </if>
      <if test="record.subType != null">
        sub_type = #{record.subType,jdbcType=TINYINT},
      </if>
      <if test="record.subScene != null">
        sub_scene = #{record.subScene,jdbcType=TINYINT},
      </if>
      <if test="record.amt != null">
        amt = #{record.amt,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update etc_order_toll_fee
    set id = #{record.id,jdbcType=BIGINT},
      etc_order = #{record.etcOrder,jdbcType=VARCHAR},
      trip_id = #{record.tripId,jdbcType=VARCHAR},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      start_station_name = #{record.startStationName,jdbcType=VARCHAR},
      end_station_name = #{record.endStationName,jdbcType=VARCHAR},
      sub_type = #{record.subType,jdbcType=TINYINT},
      sub_scene = #{record.subScene,jdbcType=TINYINT},
      amt = #{record.amt,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.EtcOrderTollFee">
    update etc_order_toll_fee
    <set>
      <if test="etcOrder != null">
        etc_order = #{etcOrder,jdbcType=VARCHAR},
      </if>
      <if test="tripId != null">
        trip_id = #{tripId,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startStationName != null">
        start_station_name = #{startStationName,jdbcType=VARCHAR},
      </if>
      <if test="endStationName != null">
        end_station_name = #{endStationName,jdbcType=VARCHAR},
      </if>
      <if test="subType != null">
        sub_type = #{subType,jdbcType=TINYINT},
      </if>
      <if test="subScene != null">
        sub_scene = #{subScene,jdbcType=TINYINT},
      </if>
      <if test="amt != null">
        amt = #{amt,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.EtcOrderTollFee">
    update etc_order_toll_fee
    set etc_order = #{etcOrder,jdbcType=VARCHAR},
      trip_id = #{tripId,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      start_station_name = #{startStationName,jdbcType=VARCHAR},
      end_station_name = #{endStationName,jdbcType=VARCHAR},
      sub_type = #{subType,jdbcType=TINYINT},
      sub_scene = #{subScene,jdbcType=TINYINT},
      amt = #{amt,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into etc_order_toll_fee
    (etc_order, trip_id, start_time, end_time, start_station_name, end_station_name, 
      sub_type, sub_scene, amt, op_user_id, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.etcOrder,jdbcType=VARCHAR}, #{item.tripId,jdbcType=VARCHAR}, #{item.startTime,jdbcType=TIMESTAMP}, 
        #{item.endTime,jdbcType=TIMESTAMP}, #{item.startStationName,jdbcType=VARCHAR}, 
        #{item.endStationName,jdbcType=VARCHAR}, #{item.subType,jdbcType=TINYINT}, #{item.subScene,jdbcType=TINYINT}, 
        #{item.amt,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into etc_order_toll_fee (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'etc_order'.toString() == column.value">
          #{item.etcOrder,jdbcType=VARCHAR}
        </if>
        <if test="'trip_id'.toString() == column.value">
          #{item.tripId,jdbcType=VARCHAR}
        </if>
        <if test="'start_time'.toString() == column.value">
          #{item.startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'end_time'.toString() == column.value">
          #{item.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'start_station_name'.toString() == column.value">
          #{item.startStationName,jdbcType=VARCHAR}
        </if>
        <if test="'end_station_name'.toString() == column.value">
          #{item.endStationName,jdbcType=VARCHAR}
        </if>
        <if test="'sub_type'.toString() == column.value">
          #{item.subType,jdbcType=TINYINT}
        </if>
        <if test="'sub_scene'.toString() == column.value">
          #{item.subScene,jdbcType=TINYINT}
        </if>
        <if test="'amt'.toString() == column.value">
          #{item.amt,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
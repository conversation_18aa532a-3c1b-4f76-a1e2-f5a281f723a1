<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.RechargeMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.Recharge">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="no_id" jdbcType="VARCHAR" property="noId" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="recharge_price" jdbcType="INTEGER" property="rechargePrice" />
    <result column="available_price" jdbcType="INTEGER" property="availablePrice" />
    <result column="deduct_price" jdbcType="INTEGER" property="deductPrice" />
    <result column="withdrawal_price" jdbcType="INTEGER" property="withdrawalPrice" />
    <result column="invoicing_price" jdbcType="INTEGER" property="invoicingPrice" />
    <result column="invoicing_status" jdbcType="INTEGER" property="invoicingStatus" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="payment_channel_id" jdbcType="BIGINT" property="paymentChannelId" />
    <result column="discount" jdbcType="INTEGER" property="discount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, no_id, merchant_id, status, source, recharge_price, available_price, deduct_price, 
    withdrawal_price, invoicing_price, invoicing_status, memo, deleted, last_ver, op_user_id, 
    create_time, op_time, payment_channel_id, discount
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.RechargeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from recharge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from recharge
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.Recharge">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into recharge (no_id, merchant_id, status, 
      source, recharge_price, available_price, 
      deduct_price, withdrawal_price, invoicing_price, 
      invoicing_status, memo, deleted, 
      last_ver, op_user_id, create_time, 
      op_time, payment_channel_id, discount
      )
    values (#{noId,jdbcType=VARCHAR}, #{merchantId,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, 
      #{source,jdbcType=INTEGER}, #{rechargePrice,jdbcType=INTEGER}, #{availablePrice,jdbcType=INTEGER}, 
      #{deductPrice,jdbcType=INTEGER}, #{withdrawalPrice,jdbcType=INTEGER}, #{invoicingPrice,jdbcType=INTEGER}, 
      #{invoicingStatus,jdbcType=INTEGER}, #{memo,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, 
      #{lastVer,jdbcType=INTEGER}, #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{paymentChannelId,jdbcType=BIGINT}, #{discount,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.Recharge">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into recharge
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="noId != null">
        no_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="rechargePrice != null">
        recharge_price,
      </if>
      <if test="availablePrice != null">
        available_price,
      </if>
      <if test="deductPrice != null">
        deduct_price,
      </if>
      <if test="withdrawalPrice != null">
        withdrawal_price,
      </if>
      <if test="invoicingPrice != null">
        invoicing_price,
      </if>
      <if test="invoicingStatus != null">
        invoicing_status,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="paymentChannelId != null">
        payment_channel_id,
      </if>
      <if test="discount != null">
        discount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="noId != null">
        #{noId,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="rechargePrice != null">
        #{rechargePrice,jdbcType=INTEGER},
      </if>
      <if test="availablePrice != null">
        #{availablePrice,jdbcType=INTEGER},
      </if>
      <if test="deductPrice != null">
        #{deductPrice,jdbcType=INTEGER},
      </if>
      <if test="withdrawalPrice != null">
        #{withdrawalPrice,jdbcType=INTEGER},
      </if>
      <if test="invoicingPrice != null">
        #{invoicingPrice,jdbcType=INTEGER},
      </if>
      <if test="invoicingStatus != null">
        #{invoicingStatus,jdbcType=INTEGER},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="paymentChannelId != null">
        #{paymentChannelId,jdbcType=BIGINT},
      </if>
      <if test="discount != null">
        #{discount,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.RechargeExample" resultType="java.lang.Long">
    select count(*) from recharge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update recharge
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.noId != null">
        no_id = #{record.noId,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=INTEGER},
      </if>
      <if test="record.rechargePrice != null">
        recharge_price = #{record.rechargePrice,jdbcType=INTEGER},
      </if>
      <if test="record.availablePrice != null">
        available_price = #{record.availablePrice,jdbcType=INTEGER},
      </if>
      <if test="record.deductPrice != null">
        deduct_price = #{record.deductPrice,jdbcType=INTEGER},
      </if>
      <if test="record.withdrawalPrice != null">
        withdrawal_price = #{record.withdrawalPrice,jdbcType=INTEGER},
      </if>
      <if test="record.invoicingPrice != null">
        invoicing_price = #{record.invoicingPrice,jdbcType=INTEGER},
      </if>
      <if test="record.invoicingStatus != null">
        invoicing_status = #{record.invoicingStatus,jdbcType=INTEGER},
      </if>
      <if test="record.memo != null">
        memo = #{record.memo,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.paymentChannelId != null">
        payment_channel_id = #{record.paymentChannelId,jdbcType=BIGINT},
      </if>
      <if test="record.discount != null">
        discount = #{record.discount,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update recharge
    set id = #{record.id,jdbcType=BIGINT},
      no_id = #{record.noId,jdbcType=VARCHAR},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      source = #{record.source,jdbcType=INTEGER},
      recharge_price = #{record.rechargePrice,jdbcType=INTEGER},
      available_price = #{record.availablePrice,jdbcType=INTEGER},
      deduct_price = #{record.deductPrice,jdbcType=INTEGER},
      withdrawal_price = #{record.withdrawalPrice,jdbcType=INTEGER},
      invoicing_price = #{record.invoicingPrice,jdbcType=INTEGER},
      invoicing_status = #{record.invoicingStatus,jdbcType=INTEGER},
      memo = #{record.memo,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      payment_channel_id = #{record.paymentChannelId,jdbcType=BIGINT},
      discount = #{record.discount,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.Recharge">
    update recharge
    <set>
      <if test="noId != null">
        no_id = #{noId,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="rechargePrice != null">
        recharge_price = #{rechargePrice,jdbcType=INTEGER},
      </if>
      <if test="availablePrice != null">
        available_price = #{availablePrice,jdbcType=INTEGER},
      </if>
      <if test="deductPrice != null">
        deduct_price = #{deductPrice,jdbcType=INTEGER},
      </if>
      <if test="withdrawalPrice != null">
        withdrawal_price = #{withdrawalPrice,jdbcType=INTEGER},
      </if>
      <if test="invoicingPrice != null">
        invoicing_price = #{invoicingPrice,jdbcType=INTEGER},
      </if>
      <if test="invoicingStatus != null">
        invoicing_status = #{invoicingStatus,jdbcType=INTEGER},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="paymentChannelId != null">
        payment_channel_id = #{paymentChannelId,jdbcType=BIGINT},
      </if>
      <if test="discount != null">
        discount = #{discount,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.Recharge">
    update recharge
    set no_id = #{noId,jdbcType=VARCHAR},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      source = #{source,jdbcType=INTEGER},
      recharge_price = #{rechargePrice,jdbcType=INTEGER},
      available_price = #{availablePrice,jdbcType=INTEGER},
      deduct_price = #{deductPrice,jdbcType=INTEGER},
      withdrawal_price = #{withdrawalPrice,jdbcType=INTEGER},
      invoicing_price = #{invoicingPrice,jdbcType=INTEGER},
      invoicing_status = #{invoicingStatus,jdbcType=INTEGER},
      memo = #{memo,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      payment_channel_id = #{paymentChannelId,jdbcType=BIGINT},
      discount = #{discount,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into recharge
    (no_id, merchant_id, status, source, recharge_price, available_price, deduct_price, 
      withdrawal_price, invoicing_price, invoicing_status, memo, deleted, last_ver, op_user_id, 
      create_time, op_time, payment_channel_id, discount)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.noId,jdbcType=VARCHAR}, #{item.merchantId,jdbcType=BIGINT}, #{item.status,jdbcType=INTEGER}, 
        #{item.source,jdbcType=INTEGER}, #{item.rechargePrice,jdbcType=INTEGER}, #{item.availablePrice,jdbcType=INTEGER}, 
        #{item.deductPrice,jdbcType=INTEGER}, #{item.withdrawalPrice,jdbcType=INTEGER}, 
        #{item.invoicingPrice,jdbcType=INTEGER}, #{item.invoicingStatus,jdbcType=INTEGER}, 
        #{item.memo,jdbcType=VARCHAR}, #{item.deleted,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER}, 
        #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, 
        #{item.paymentChannelId,jdbcType=BIGINT}, #{item.discount,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into recharge (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'no_id'.toString() == column.value">
          #{item.noId,jdbcType=VARCHAR}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'source'.toString() == column.value">
          #{item.source,jdbcType=INTEGER}
        </if>
        <if test="'recharge_price'.toString() == column.value">
          #{item.rechargePrice,jdbcType=INTEGER}
        </if>
        <if test="'available_price'.toString() == column.value">
          #{item.availablePrice,jdbcType=INTEGER}
        </if>
        <if test="'deduct_price'.toString() == column.value">
          #{item.deductPrice,jdbcType=INTEGER}
        </if>
        <if test="'withdrawal_price'.toString() == column.value">
          #{item.withdrawalPrice,jdbcType=INTEGER}
        </if>
        <if test="'invoicing_price'.toString() == column.value">
          #{item.invoicingPrice,jdbcType=INTEGER}
        </if>
        <if test="'invoicing_status'.toString() == column.value">
          #{item.invoicingStatus,jdbcType=INTEGER}
        </if>
        <if test="'memo'.toString() == column.value">
          #{item.memo,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'payment_channel_id'.toString() == column.value">
          #{item.paymentChannelId,jdbcType=BIGINT}
        </if>
        <if test="'discount'.toString() == column.value">
          #{item.discount,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>

  <update id="addRechargePrice">
    update recharge
    set
    available_price = available_price + #{availablePrice,jdbcType=INTEGER},
    deduct_price = deduct_price + #{deductPrice,jdbcType=INTEGER},
    withdrawal_price = withdrawal_price + #{withdrawalPrice,jdbcType=INTEGER},
    invoicing_price = invoicing_price + #{invoicingPrice,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
    and merchant_id = #{merchantId,jdbcType=BIGINT}
  </update>

  <update id="invoicingStatus">
    update recharge
    set
    invoicing_status = #{invoicingStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
    and merchant_id = #{merchantId,jdbcType=BIGINT}
  </update>


</mapper>
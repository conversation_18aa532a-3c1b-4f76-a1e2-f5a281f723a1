<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.RefundPayMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.RefundPay">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="sub_order_id" jdbcType="BIGINT" property="subOrderId" />
    <result column="pay_id" jdbcType="BIGINT" property="payId" />
    <result column="refund_pay_source_id" jdbcType="VARCHAR" property="refundPaySourceId" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="actual_amount" jdbcType="INTEGER" property="actualAmount" />
    <result column="pay_source" jdbcType="TINYINT" property="paySource" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="pay_time" jdbcType="BIGINT" property="payTime" />
    <result column="pay_op_time" jdbcType="BIGINT" property="payOpTime" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, sub_order_id, pay_id, refund_pay_source_id, amount, actual_amount, pay_source, status, last_ver,
    op_user_id, pay_time, pay_op_time, create_time, op_time, extra
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.RefundPayExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from refund_pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from refund_pay
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from refund_pay
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.RefundPayExample">
    delete from refund_pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.RefundPay">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into refund_pay (order_id, sub_order_id, pay_id, refund_pay_source_id,
      amount, actual_amount, pay_source, 
      status, last_ver, op_user_id, 
      pay_time, pay_op_time, create_time, 
      op_time, extra)
    values (#{orderId,jdbcType=BIGINT}, #{subOrderId,jdbcType=BIGINT}, #{payId,jdbcType=BIGINT}, 
      #{refundPaySourceId,jdbcType=VARCHAR}, #{amount,jdbcType=INTEGER}, #{actualAmount,jdbcType=INTEGER}, #{paySource,jdbcType=TINYINT},
      #{status,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER}, #{opUserId,jdbcType=BIGINT}, 
      #{payTime,jdbcType=BIGINT}, #{payOpTime,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{extra,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.RefundPay">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into refund_pay
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="subOrderId != null">
        sub_order_id,
      </if>
      <if test="payId != null">
        pay_id,
      </if>
      <if test="refundPaySourceId != null">
        refund_pay_source_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="actualAmount != null">
        actual_amount,
      </if>
      <if test="paySource != null">
        pay_source,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="payOpTime != null">
        pay_op_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="subOrderId != null">
        #{subOrderId,jdbcType=BIGINT},
      </if>
      <if test="payId != null">
        #{payId,jdbcType=BIGINT},
      </if>
      <if test="refundPaySourceId != null">
        #{refundPaySourceId,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="actualAmount != null">
        #{actualAmount,jdbcType=INTEGER},
      </if>
      <if test="paySource != null">
        #{paySource,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=BIGINT},
      </if>
      <if test="payOpTime != null">
        #{payOpTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.RefundPayExample" resultType="java.lang.Long">
    select count(*) from refund_pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update refund_pay
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.subOrderId != null">
        sub_order_id = #{record.subOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.payId != null">
        pay_id = #{record.payId,jdbcType=BIGINT},
      </if>
      <if test="record.refundPaySourceId != null">
        refund_pay_source_id = #{record.refundPaySourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=INTEGER},
      </if>
      <if test="record.actualAmount != null">
        actual_amount = #{record.actualAmount,jdbcType=INTEGER},
      </if>
      <if test="record.paySource != null">
        pay_source = #{record.paySource,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.payTime != null">
        pay_time = #{record.payTime,jdbcType=BIGINT},
      </if>
      <if test="record.payOpTime != null">
        pay_op_time = #{record.payOpTime,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update refund_pay
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      sub_order_id = #{record.subOrderId,jdbcType=BIGINT},
      pay_id = #{record.payId,jdbcType=BIGINT},
      refund_pay_source_id = #{record.refundPaySourceId,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=INTEGER},
      actual_amount = #{record.actualAmount,jdbcType=INTEGER},
      pay_source = #{record.paySource,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      pay_time = #{record.payTime,jdbcType=BIGINT},
      pay_op_time = #{record.payOpTime,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      extra = #{record.extra,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.RefundPay">
    update refund_pay
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="subOrderId != null">
        sub_order_id = #{subOrderId,jdbcType=BIGINT},
      </if>
      <if test="payId != null">
        pay_id = #{payId,jdbcType=BIGINT},
      </if>
      <if test="refundPaySourceId != null">
        refund_pay_source_id = #{refundPaySourceId,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=INTEGER},
      </if>
      <if test="actualAmount != null">
        actual_amount = #{actualAmount,jdbcType=INTEGER},
      </if>
      <if test="paySource != null">
        pay_source = #{paySource,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=BIGINT},
      </if>
      <if test="payOpTime != null">
        pay_op_time = #{payOpTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.RefundPay">
    update refund_pay
    set order_id = #{orderId,jdbcType=BIGINT},
      sub_order_id = #{subOrderId,jdbcType=BIGINT},
      pay_id = #{payId,jdbcType=BIGINT},
      refund_pay_source_id = #{refundPaySourceId,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=INTEGER},
      actual_amount = #{actualAmount,jdbcType=INTEGER},
      pay_source = #{paySource,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      pay_time = #{payTime,jdbcType=BIGINT},
      pay_op_time = #{payOpTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      extra = #{extra,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into refund_pay
    (order_id, sub_order_id, pay_id, refund_pay_source_id, amount, actual_amount, pay_source, status, last_ver,
      op_user_id, pay_time, pay_op_time, create_time, op_time, extra)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=BIGINT}, #{item.subOrderId,jdbcType=BIGINT}, #{item.payId,jdbcType=BIGINT}, 
        #{item.refundPaySourceId,jdbcType=VARCHAR}, #{item.amount,jdbcType=INTEGER}, #{item.actualAmount,jdbcType=INTEGER},
        #{item.paySource,jdbcType=TINYINT}, #{item.status,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER},
        #{item.opUserId,jdbcType=BIGINT},
        #{item.payTime,jdbcType=BIGINT}, #{item.payOpTime,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.extra,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into refund_pay (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'sub_order_id'.toString() == column.value">
          #{item.subOrderId,jdbcType=BIGINT}
        </if>
        <if test="'pay_id'.toString() == column.value">
          #{item.payId,jdbcType=BIGINT}
        </if>
        <if test="'refund_pay_source_id'.toString() == column.value">
          #{item.refundPaySourceId,jdbcType=VARCHAR}
        </if>
        <if test="'amount'.toString() == column.value">
          #{item.amount,jdbcType=INTEGER}
        </if>
        <if test="'actual_amount'.toString() == column.value">
          #{item.actualAmount,jdbcType=INTEGER}
        </if>
        <if test="'pay_source'.toString() == column.value">
          #{item.paySource,jdbcType=TINYINT}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'pay_time'.toString() == column.value">
          #{item.payTime,jdbcType=BIGINT}
        </if>
        <if test="'pay_op_time'.toString() == column.value">
          #{item.payOpTime,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'extra'.toString() == column.value">
          #{item.extra,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
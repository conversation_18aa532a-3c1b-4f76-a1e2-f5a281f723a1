<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.EtcOrderChargeMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.EtcOrderCharge">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="tenancy_fee" jdbcType="BIGINT" property="tenancyFee" />
    <result column="merchant_profit" jdbcType="BIGINT" property="merchantProfit" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="etc_order" jdbcType="VARCHAR" property="etcOrder" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="amt" jdbcType="BIGINT" property="amt" />
    <result column="order_lock" jdbcType="TINYINT" property="orderLock" />
    <result column="withdrawal_id" jdbcType="BIGINT" property="withdrawalId" />
    <result column="order_start_time" jdbcType="TIMESTAMP" property="orderStartTime" />
    <result column="order_end_time" jdbcType="TIMESTAMP" property="orderEndTime" />
    <result column="real_start_time" jdbcType="TIMESTAMP" property="realStartTime" />
    <result column="real_end_time" jdbcType="TIMESTAMP" property="realEndTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, store_id, vehicle_model_id, vehicle_id, tenancy_fee, merchant_profit, 
    order_id, etc_order, source, order_status, last_ver, op_user_id, create_time, op_time, 
    amt, order_lock, withdrawal_id, order_start_time, order_end_time, real_start_time, 
    real_end_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.EtcOrderChargeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from etc_order_charge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from etc_order_charge
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from etc_order_charge
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.EtcOrderChargeExample">
    delete from etc_order_charge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.EtcOrderCharge">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into etc_order_charge (merchant_id, store_id, vehicle_model_id, 
      vehicle_id, tenancy_fee, merchant_profit, 
      order_id, etc_order, source, 
      order_status, last_ver, op_user_id, 
      create_time, op_time, amt, 
      order_lock, withdrawal_id, order_start_time, 
      order_end_time, real_start_time, real_end_time
      )
    values (#{merchantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{vehicleModelId,jdbcType=BIGINT}, 
      #{vehicleId,jdbcType=BIGINT}, #{tenancyFee,jdbcType=BIGINT}, #{merchantProfit,jdbcType=BIGINT}, 
      #{orderId,jdbcType=VARCHAR}, #{etcOrder,jdbcType=VARCHAR}, #{source,jdbcType=TINYINT}, 
      #{orderStatus,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER}, #{opUserId,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, #{amt,jdbcType=BIGINT}, 
      #{orderLock,jdbcType=TINYINT}, #{withdrawalId,jdbcType=BIGINT}, #{orderStartTime,jdbcType=TIMESTAMP}, 
      #{orderEndTime,jdbcType=TIMESTAMP}, #{realStartTime,jdbcType=TIMESTAMP}, #{realEndTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.EtcOrderCharge">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into etc_order_charge
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="tenancyFee != null">
        tenancy_fee,
      </if>
      <if test="merchantProfit != null">
        merchant_profit,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="etcOrder != null">
        etc_order,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="amt != null">
        amt,
      </if>
      <if test="orderLock != null">
        order_lock,
      </if>
      <if test="withdrawalId != null">
        withdrawal_id,
      </if>
      <if test="orderStartTime != null">
        order_start_time,
      </if>
      <if test="orderEndTime != null">
        order_end_time,
      </if>
      <if test="realStartTime != null">
        real_start_time,
      </if>
      <if test="realEndTime != null">
        real_end_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="tenancyFee != null">
        #{tenancyFee,jdbcType=BIGINT},
      </if>
      <if test="merchantProfit != null">
        #{merchantProfit,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="etcOrder != null">
        #{etcOrder,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="amt != null">
        #{amt,jdbcType=BIGINT},
      </if>
      <if test="orderLock != null">
        #{orderLock,jdbcType=TINYINT},
      </if>
      <if test="withdrawalId != null">
        #{withdrawalId,jdbcType=BIGINT},
      </if>
      <if test="orderStartTime != null">
        #{orderStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderEndTime != null">
        #{orderEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realStartTime != null">
        #{realStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realEndTime != null">
        #{realEndTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.EtcOrderChargeExample" resultType="java.lang.Long">
    select count(*) from etc_order_charge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update etc_order_charge
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="record.tenancyFee != null">
        tenancy_fee = #{record.tenancyFee,jdbcType=BIGINT},
      </if>
      <if test="record.merchantProfit != null">
        merchant_profit = #{record.merchantProfit,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.etcOrder != null">
        etc_order = #{record.etcOrder,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=TINYINT},
      </if>
      <if test="record.orderStatus != null">
        order_status = #{record.orderStatus,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.amt != null">
        amt = #{record.amt,jdbcType=BIGINT},
      </if>
      <if test="record.orderLock != null">
        order_lock = #{record.orderLock,jdbcType=TINYINT},
      </if>
      <if test="record.withdrawalId != null">
        withdrawal_id = #{record.withdrawalId,jdbcType=BIGINT},
      </if>
      <if test="record.orderStartTime != null">
        order_start_time = #{record.orderStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderEndTime != null">
        order_end_time = #{record.orderEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.realStartTime != null">
        real_start_time = #{record.realStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.realEndTime != null">
        real_end_time = #{record.realEndTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update etc_order_charge
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      tenancy_fee = #{record.tenancyFee,jdbcType=BIGINT},
      merchant_profit = #{record.merchantProfit,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      etc_order = #{record.etcOrder,jdbcType=VARCHAR},
      source = #{record.source,jdbcType=TINYINT},
      order_status = #{record.orderStatus,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      amt = #{record.amt,jdbcType=BIGINT},
      order_lock = #{record.orderLock,jdbcType=TINYINT},
      withdrawal_id = #{record.withdrawalId,jdbcType=BIGINT},
      order_start_time = #{record.orderStartTime,jdbcType=TIMESTAMP},
      order_end_time = #{record.orderEndTime,jdbcType=TIMESTAMP},
      real_start_time = #{record.realStartTime,jdbcType=TIMESTAMP},
      real_end_time = #{record.realEndTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.EtcOrderCharge">
    update etc_order_charge
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="tenancyFee != null">
        tenancy_fee = #{tenancyFee,jdbcType=BIGINT},
      </if>
      <if test="merchantProfit != null">
        merchant_profit = #{merchantProfit,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="etcOrder != null">
        etc_order = #{etcOrder,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=TINYINT},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="amt != null">
        amt = #{amt,jdbcType=BIGINT},
      </if>
      <if test="orderLock != null">
        order_lock = #{orderLock,jdbcType=TINYINT},
      </if>
      <if test="withdrawalId != null">
        withdrawal_id = #{withdrawalId,jdbcType=BIGINT},
      </if>
      <if test="orderStartTime != null">
        order_start_time = #{orderStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderEndTime != null">
        order_end_time = #{orderEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realStartTime != null">
        real_start_time = #{realStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realEndTime != null">
        real_end_time = #{realEndTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.EtcOrderCharge">
    update etc_order_charge
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      tenancy_fee = #{tenancyFee,jdbcType=BIGINT},
      merchant_profit = #{merchantProfit,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=VARCHAR},
      etc_order = #{etcOrder,jdbcType=VARCHAR},
      source = #{source,jdbcType=TINYINT},
      order_status = #{orderStatus,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      amt = #{amt,jdbcType=BIGINT},
      order_lock = #{orderLock,jdbcType=TINYINT},
      withdrawal_id = #{withdrawalId,jdbcType=BIGINT},
      order_start_time = #{orderStartTime,jdbcType=TIMESTAMP},
      order_end_time = #{orderEndTime,jdbcType=TIMESTAMP},
      real_start_time = #{realStartTime,jdbcType=TIMESTAMP},
      real_end_time = #{realEndTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into etc_order_charge
    (merchant_id, store_id, vehicle_model_id, vehicle_id, tenancy_fee, merchant_profit, 
      order_id, etc_order, source, order_status, last_ver, op_user_id, create_time, op_time, 
      amt, order_lock, withdrawal_id, order_start_time, order_end_time, real_start_time, 
      real_end_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.vehicleModelId,jdbcType=BIGINT}, 
        #{item.vehicleId,jdbcType=BIGINT}, #{item.tenancyFee,jdbcType=BIGINT}, #{item.merchantProfit,jdbcType=BIGINT}, 
        #{item.orderId,jdbcType=VARCHAR}, #{item.etcOrder,jdbcType=VARCHAR}, #{item.source,jdbcType=TINYINT}, 
        #{item.orderStatus,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER}, #{item.opUserId,jdbcType=BIGINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, #{item.amt,jdbcType=BIGINT}, 
        #{item.orderLock,jdbcType=TINYINT}, #{item.withdrawalId,jdbcType=BIGINT}, #{item.orderStartTime,jdbcType=TIMESTAMP}, 
        #{item.orderEndTime,jdbcType=TIMESTAMP}, #{item.realStartTime,jdbcType=TIMESTAMP}, 
        #{item.realEndTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into etc_order_charge (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_id'.toString() == column.value">
          #{item.vehicleId,jdbcType=BIGINT}
        </if>
        <if test="'tenancy_fee'.toString() == column.value">
          #{item.tenancyFee,jdbcType=BIGINT}
        </if>
        <if test="'merchant_profit'.toString() == column.value">
          #{item.merchantProfit,jdbcType=BIGINT}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'etc_order'.toString() == column.value">
          #{item.etcOrder,jdbcType=VARCHAR}
        </if>
        <if test="'source'.toString() == column.value">
          #{item.source,jdbcType=TINYINT}
        </if>
        <if test="'order_status'.toString() == column.value">
          #{item.orderStatus,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'amt'.toString() == column.value">
          #{item.amt,jdbcType=BIGINT}
        </if>
        <if test="'order_lock'.toString() == column.value">
          #{item.orderLock,jdbcType=TINYINT}
        </if>
        <if test="'withdrawal_id'.toString() == column.value">
          #{item.withdrawalId,jdbcType=BIGINT}
        </if>
        <if test="'order_start_time'.toString() == column.value">
          #{item.orderStartTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'order_end_time'.toString() == column.value">
          #{item.orderEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'real_start_time'.toString() == column.value">
          #{item.realStartTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'real_end_time'.toString() == column.value">
          #{item.realEndTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>

  <select id="obtainWithdrawalAmount" resultType="java.lang.Integer">
    select sum(merchant_profit) as withdrawalAmount from etc_order_charge where merchant_id = #{merchantId}
    and order_lock = 0 and order_status in
    <foreach item="item" collection="list" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>

  </select>
</mapper>
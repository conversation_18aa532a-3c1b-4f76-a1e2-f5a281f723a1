<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.OrderMemberMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.OrderMember">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="third_user_id" jdbcType="VARCHAR" property="thirdUserId" />
    <result column="user_source" jdbcType="TINYINT" property="userSource" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="idcard_no" jdbcType="VARCHAR" property="idcardNo" />
    <result column="sex" jdbcType="TINYINT" property="sex" />
    <result column="birthday" jdbcType="VARCHAR" property="birthday" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="notionality" jdbcType="VARCHAR" property="notionality" />
    <result column="idcard_valid_begin" jdbcType="VARCHAR" property="idcardValidBegin" />
    <result column="idcard_valid_end" jdbcType="VARCHAR" property="idcardValidEnd" />
    <result column="idcard_issue_authority" jdbcType="VARCHAR" property="idcardIssueAuthority" />
    <result column="idcard_main_image_url" jdbcType="VARCHAR" property="idcardMainImageUrl" />
    <result column="idcard_sub_image_url" jdbcType="VARCHAR" property="idcardSubImageUrl" />
    <result column="driverlic_no" jdbcType="VARCHAR" property="driverlicNo" />
    <result column="driverlic_class" jdbcType="VARCHAR" property="driverlicClass" />
    <result column="driverlic_issue_date" jdbcType="VARCHAR" property="driverlicIssueDate" />
    <result column="driverlic_valid_begin" jdbcType="VARCHAR" property="driverlicValidBegin" />
    <result column="driverlic_valid_end" jdbcType="VARCHAR" property="driverlicValidEnd" />
    <result column="driverlic_issue_authority" jdbcType="VARCHAR" property="driverlicIssueAuthority" />
    <result column="driverlic_main_image_url" jdbcType="VARCHAR" property="driverlicMainImageUrl" />
    <result column="driverlic_sub_image_url" jdbcType="VARCHAR" property="driverlicSubImageUrl" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, third_user_id, user_source, user_name, mobile, idcard_no, sex, birthday, address, 
    notionality, idcard_valid_begin, idcard_valid_end, idcard_issue_authority, idcard_main_image_url, 
    idcard_sub_image_url, driverlic_no, driverlic_class, driverlic_issue_date, driverlic_valid_begin, 
    driverlic_valid_end, driverlic_issue_authority, driverlic_main_image_url, driverlic_sub_image_url, 
    last_ver, create_time, op_time, extra
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.OrderMemberExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from order_member
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_member
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_member
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.OrderMemberExample">
    delete from order_member
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.OrderMember">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_member (third_user_id, user_source, user_name, 
      mobile, idcard_no, sex, 
      birthday, address, notionality, 
      idcard_valid_begin, idcard_valid_end, idcard_issue_authority, 
      idcard_main_image_url, idcard_sub_image_url, 
      driverlic_no, driverlic_class, driverlic_issue_date, 
      driverlic_valid_begin, driverlic_valid_end, 
      driverlic_issue_authority, driverlic_main_image_url, 
      driverlic_sub_image_url, last_ver, create_time, 
      op_time, extra)
    values (#{thirdUserId,jdbcType=VARCHAR}, #{userSource,jdbcType=TINYINT}, #{userName,jdbcType=VARCHAR}, 
      #{mobile,jdbcType=VARCHAR}, #{idcardNo,jdbcType=VARCHAR}, #{sex,jdbcType=TINYINT}, 
      #{birthday,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{notionality,jdbcType=VARCHAR}, 
      #{idcardValidBegin,jdbcType=VARCHAR}, #{idcardValidEnd,jdbcType=VARCHAR}, #{idcardIssueAuthority,jdbcType=VARCHAR}, 
      #{idcardMainImageUrl,jdbcType=VARCHAR}, #{idcardSubImageUrl,jdbcType=VARCHAR}, 
      #{driverlicNo,jdbcType=VARCHAR}, #{driverlicClass,jdbcType=VARCHAR}, #{driverlicIssueDate,jdbcType=VARCHAR}, 
      #{driverlicValidBegin,jdbcType=VARCHAR}, #{driverlicValidEnd,jdbcType=VARCHAR}, 
      #{driverlicIssueAuthority,jdbcType=VARCHAR}, #{driverlicMainImageUrl,jdbcType=VARCHAR}, 
      #{driverlicSubImageUrl,jdbcType=VARCHAR}, #{lastVer,jdbcType=INTEGER}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{extra,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.OrderMember">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_member
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="thirdUserId != null">
        third_user_id,
      </if>
      <if test="userSource != null">
        user_source,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="idcardNo != null">
        idcard_no,
      </if>
      <if test="sex != null">
        sex,
      </if>
      <if test="birthday != null">
        birthday,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="notionality != null">
        notionality,
      </if>
      <if test="idcardValidBegin != null">
        idcard_valid_begin,
      </if>
      <if test="idcardValidEnd != null">
        idcard_valid_end,
      </if>
      <if test="idcardIssueAuthority != null">
        idcard_issue_authority,
      </if>
      <if test="idcardMainImageUrl != null">
        idcard_main_image_url,
      </if>
      <if test="idcardSubImageUrl != null">
        idcard_sub_image_url,
      </if>
      <if test="driverlicNo != null">
        driverlic_no,
      </if>
      <if test="driverlicClass != null">
        driverlic_class,
      </if>
      <if test="driverlicIssueDate != null">
        driverlic_issue_date,
      </if>
      <if test="driverlicValidBegin != null">
        driverlic_valid_begin,
      </if>
      <if test="driverlicValidEnd != null">
        driverlic_valid_end,
      </if>
      <if test="driverlicIssueAuthority != null">
        driverlic_issue_authority,
      </if>
      <if test="driverlicMainImageUrl != null">
        driverlic_main_image_url,
      </if>
      <if test="driverlicSubImageUrl != null">
        driverlic_sub_image_url,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="thirdUserId != null">
        #{thirdUserId,jdbcType=VARCHAR},
      </if>
      <if test="userSource != null">
        #{userSource,jdbcType=TINYINT},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="idcardNo != null">
        #{idcardNo,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=TINYINT},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="notionality != null">
        #{notionality,jdbcType=VARCHAR},
      </if>
      <if test="idcardValidBegin != null">
        #{idcardValidBegin,jdbcType=VARCHAR},
      </if>
      <if test="idcardValidEnd != null">
        #{idcardValidEnd,jdbcType=VARCHAR},
      </if>
      <if test="idcardIssueAuthority != null">
        #{idcardIssueAuthority,jdbcType=VARCHAR},
      </if>
      <if test="idcardMainImageUrl != null">
        #{idcardMainImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="idcardSubImageUrl != null">
        #{idcardSubImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="driverlicNo != null">
        #{driverlicNo,jdbcType=VARCHAR},
      </if>
      <if test="driverlicClass != null">
        #{driverlicClass,jdbcType=VARCHAR},
      </if>
      <if test="driverlicIssueDate != null">
        #{driverlicIssueDate,jdbcType=VARCHAR},
      </if>
      <if test="driverlicValidBegin != null">
        #{driverlicValidBegin,jdbcType=VARCHAR},
      </if>
      <if test="driverlicValidEnd != null">
        #{driverlicValidEnd,jdbcType=VARCHAR},
      </if>
      <if test="driverlicIssueAuthority != null">
        #{driverlicIssueAuthority,jdbcType=VARCHAR},
      </if>
      <if test="driverlicMainImageUrl != null">
        #{driverlicMainImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="driverlicSubImageUrl != null">
        #{driverlicSubImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.OrderMemberExample" resultType="java.lang.Long">
    select count(*) from order_member
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_member
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.thirdUserId != null">
        third_user_id = #{record.thirdUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.userSource != null">
        user_source = #{record.userSource,jdbcType=TINYINT},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.mobile != null">
        mobile = #{record.mobile,jdbcType=VARCHAR},
      </if>
      <if test="record.idcardNo != null">
        idcard_no = #{record.idcardNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sex != null">
        sex = #{record.sex,jdbcType=TINYINT},
      </if>
      <if test="record.birthday != null">
        birthday = #{record.birthday,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.notionality != null">
        notionality = #{record.notionality,jdbcType=VARCHAR},
      </if>
      <if test="record.idcardValidBegin != null">
        idcard_valid_begin = #{record.idcardValidBegin,jdbcType=VARCHAR},
      </if>
      <if test="record.idcardValidEnd != null">
        idcard_valid_end = #{record.idcardValidEnd,jdbcType=VARCHAR},
      </if>
      <if test="record.idcardIssueAuthority != null">
        idcard_issue_authority = #{record.idcardIssueAuthority,jdbcType=VARCHAR},
      </if>
      <if test="record.idcardMainImageUrl != null">
        idcard_main_image_url = #{record.idcardMainImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.idcardSubImageUrl != null">
        idcard_sub_image_url = #{record.idcardSubImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.driverlicNo != null">
        driverlic_no = #{record.driverlicNo,jdbcType=VARCHAR},
      </if>
      <if test="record.driverlicClass != null">
        driverlic_class = #{record.driverlicClass,jdbcType=VARCHAR},
      </if>
      <if test="record.driverlicIssueDate != null">
        driverlic_issue_date = #{record.driverlicIssueDate,jdbcType=VARCHAR},
      </if>
      <if test="record.driverlicValidBegin != null">
        driverlic_valid_begin = #{record.driverlicValidBegin,jdbcType=VARCHAR},
      </if>
      <if test="record.driverlicValidEnd != null">
        driverlic_valid_end = #{record.driverlicValidEnd,jdbcType=VARCHAR},
      </if>
      <if test="record.driverlicIssueAuthority != null">
        driverlic_issue_authority = #{record.driverlicIssueAuthority,jdbcType=VARCHAR},
      </if>
      <if test="record.driverlicMainImageUrl != null">
        driverlic_main_image_url = #{record.driverlicMainImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.driverlicSubImageUrl != null">
        driverlic_sub_image_url = #{record.driverlicSubImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_member
    set id = #{record.id,jdbcType=BIGINT},
      third_user_id = #{record.thirdUserId,jdbcType=VARCHAR},
      user_source = #{record.userSource,jdbcType=TINYINT},
      user_name = #{record.userName,jdbcType=VARCHAR},
      mobile = #{record.mobile,jdbcType=VARCHAR},
      idcard_no = #{record.idcardNo,jdbcType=VARCHAR},
      sex = #{record.sex,jdbcType=TINYINT},
      birthday = #{record.birthday,jdbcType=VARCHAR},
      address = #{record.address,jdbcType=VARCHAR},
      notionality = #{record.notionality,jdbcType=VARCHAR},
      idcard_valid_begin = #{record.idcardValidBegin,jdbcType=VARCHAR},
      idcard_valid_end = #{record.idcardValidEnd,jdbcType=VARCHAR},
      idcard_issue_authority = #{record.idcardIssueAuthority,jdbcType=VARCHAR},
      idcard_main_image_url = #{record.idcardMainImageUrl,jdbcType=VARCHAR},
      idcard_sub_image_url = #{record.idcardSubImageUrl,jdbcType=VARCHAR},
      driverlic_no = #{record.driverlicNo,jdbcType=VARCHAR},
      driverlic_class = #{record.driverlicClass,jdbcType=VARCHAR},
      driverlic_issue_date = #{record.driverlicIssueDate,jdbcType=VARCHAR},
      driverlic_valid_begin = #{record.driverlicValidBegin,jdbcType=VARCHAR},
      driverlic_valid_end = #{record.driverlicValidEnd,jdbcType=VARCHAR},
      driverlic_issue_authority = #{record.driverlicIssueAuthority,jdbcType=VARCHAR},
      driverlic_main_image_url = #{record.driverlicMainImageUrl,jdbcType=VARCHAR},
      driverlic_sub_image_url = #{record.driverlicSubImageUrl,jdbcType=VARCHAR},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      extra = #{record.extra,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.OrderMember">
    update order_member
    <set>
      <if test="thirdUserId != null">
        third_user_id = #{thirdUserId,jdbcType=VARCHAR},
      </if>
      <if test="userSource != null">
        user_source = #{userSource,jdbcType=TINYINT},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="idcardNo != null">
        idcard_no = #{idcardNo,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        sex = #{sex,jdbcType=TINYINT},
      </if>
      <if test="birthday != null">
        birthday = #{birthday,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="notionality != null">
        notionality = #{notionality,jdbcType=VARCHAR},
      </if>
      <if test="idcardValidBegin != null">
        idcard_valid_begin = #{idcardValidBegin,jdbcType=VARCHAR},
      </if>
      <if test="idcardValidEnd != null">
        idcard_valid_end = #{idcardValidEnd,jdbcType=VARCHAR},
      </if>
      <if test="idcardIssueAuthority != null">
        idcard_issue_authority = #{idcardIssueAuthority,jdbcType=VARCHAR},
      </if>
      <if test="idcardMainImageUrl != null">
        idcard_main_image_url = #{idcardMainImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="idcardSubImageUrl != null">
        idcard_sub_image_url = #{idcardSubImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="driverlicNo != null">
        driverlic_no = #{driverlicNo,jdbcType=VARCHAR},
      </if>
      <if test="driverlicClass != null">
        driverlic_class = #{driverlicClass,jdbcType=VARCHAR},
      </if>
      <if test="driverlicIssueDate != null">
        driverlic_issue_date = #{driverlicIssueDate,jdbcType=VARCHAR},
      </if>
      <if test="driverlicValidBegin != null">
        driverlic_valid_begin = #{driverlicValidBegin,jdbcType=VARCHAR},
      </if>
      <if test="driverlicValidEnd != null">
        driverlic_valid_end = #{driverlicValidEnd,jdbcType=VARCHAR},
      </if>
      <if test="driverlicIssueAuthority != null">
        driverlic_issue_authority = #{driverlicIssueAuthority,jdbcType=VARCHAR},
      </if>
      <if test="driverlicMainImageUrl != null">
        driverlic_main_image_url = #{driverlicMainImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="driverlicSubImageUrl != null">
        driverlic_sub_image_url = #{driverlicSubImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.OrderMember">
    update order_member
    set third_user_id = #{thirdUserId,jdbcType=VARCHAR},
      user_source = #{userSource,jdbcType=TINYINT},
      user_name = #{userName,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      idcard_no = #{idcardNo,jdbcType=VARCHAR},
      sex = #{sex,jdbcType=TINYINT},
      birthday = #{birthday,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      notionality = #{notionality,jdbcType=VARCHAR},
      idcard_valid_begin = #{idcardValidBegin,jdbcType=VARCHAR},
      idcard_valid_end = #{idcardValidEnd,jdbcType=VARCHAR},
      idcard_issue_authority = #{idcardIssueAuthority,jdbcType=VARCHAR},
      idcard_main_image_url = #{idcardMainImageUrl,jdbcType=VARCHAR},
      idcard_sub_image_url = #{idcardSubImageUrl,jdbcType=VARCHAR},
      driverlic_no = #{driverlicNo,jdbcType=VARCHAR},
      driverlic_class = #{driverlicClass,jdbcType=VARCHAR},
      driverlic_issue_date = #{driverlicIssueDate,jdbcType=VARCHAR},
      driverlic_valid_begin = #{driverlicValidBegin,jdbcType=VARCHAR},
      driverlic_valid_end = #{driverlicValidEnd,jdbcType=VARCHAR},
      driverlic_issue_authority = #{driverlicIssueAuthority,jdbcType=VARCHAR},
      driverlic_main_image_url = #{driverlicMainImageUrl,jdbcType=VARCHAR},
      driverlic_sub_image_url = #{driverlicSubImageUrl,jdbcType=VARCHAR},
      last_ver = #{lastVer,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      extra = #{extra,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_member
    (third_user_id, user_source, user_name, mobile, idcard_no, sex, birthday, address, 
      notionality, idcard_valid_begin, idcard_valid_end, idcard_issue_authority, idcard_main_image_url, 
      idcard_sub_image_url, driverlic_no, driverlic_class, driverlic_issue_date, driverlic_valid_begin, 
      driverlic_valid_end, driverlic_issue_authority, driverlic_main_image_url, driverlic_sub_image_url, 
      last_ver, create_time, op_time, extra)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.thirdUserId,jdbcType=VARCHAR}, #{item.userSource,jdbcType=TINYINT}, #{item.userName,jdbcType=VARCHAR}, 
        #{item.mobile,jdbcType=VARCHAR}, #{item.idcardNo,jdbcType=VARCHAR}, #{item.sex,jdbcType=TINYINT}, 
        #{item.birthday,jdbcType=VARCHAR}, #{item.address,jdbcType=VARCHAR}, #{item.notionality,jdbcType=VARCHAR}, 
        #{item.idcardValidBegin,jdbcType=VARCHAR}, #{item.idcardValidEnd,jdbcType=VARCHAR}, 
        #{item.idcardIssueAuthority,jdbcType=VARCHAR}, #{item.idcardMainImageUrl,jdbcType=VARCHAR}, 
        #{item.idcardSubImageUrl,jdbcType=VARCHAR}, #{item.driverlicNo,jdbcType=VARCHAR}, 
        #{item.driverlicClass,jdbcType=VARCHAR}, #{item.driverlicIssueDate,jdbcType=VARCHAR}, 
        #{item.driverlicValidBegin,jdbcType=VARCHAR}, #{item.driverlicValidEnd,jdbcType=VARCHAR}, 
        #{item.driverlicIssueAuthority,jdbcType=VARCHAR}, #{item.driverlicMainImageUrl,jdbcType=VARCHAR}, 
        #{item.driverlicSubImageUrl,jdbcType=VARCHAR}, #{item.lastVer,jdbcType=INTEGER}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, #{item.extra,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_member (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'third_user_id'.toString() == column.value">
          #{item.thirdUserId,jdbcType=VARCHAR}
        </if>
        <if test="'user_source'.toString() == column.value">
          #{item.userSource,jdbcType=TINYINT}
        </if>
        <if test="'user_name'.toString() == column.value">
          #{item.userName,jdbcType=VARCHAR}
        </if>
        <if test="'mobile'.toString() == column.value">
          #{item.mobile,jdbcType=VARCHAR}
        </if>
        <if test="'idcard_no'.toString() == column.value">
          #{item.idcardNo,jdbcType=VARCHAR}
        </if>
        <if test="'sex'.toString() == column.value">
          #{item.sex,jdbcType=TINYINT}
        </if>
        <if test="'birthday'.toString() == column.value">
          #{item.birthday,jdbcType=VARCHAR}
        </if>
        <if test="'address'.toString() == column.value">
          #{item.address,jdbcType=VARCHAR}
        </if>
        <if test="'notionality'.toString() == column.value">
          #{item.notionality,jdbcType=VARCHAR}
        </if>
        <if test="'idcard_valid_begin'.toString() == column.value">
          #{item.idcardValidBegin,jdbcType=VARCHAR}
        </if>
        <if test="'idcard_valid_end'.toString() == column.value">
          #{item.idcardValidEnd,jdbcType=VARCHAR}
        </if>
        <if test="'idcard_issue_authority'.toString() == column.value">
          #{item.idcardIssueAuthority,jdbcType=VARCHAR}
        </if>
        <if test="'idcard_main_image_url'.toString() == column.value">
          #{item.idcardMainImageUrl,jdbcType=VARCHAR}
        </if>
        <if test="'idcard_sub_image_url'.toString() == column.value">
          #{item.idcardSubImageUrl,jdbcType=VARCHAR}
        </if>
        <if test="'driverlic_no'.toString() == column.value">
          #{item.driverlicNo,jdbcType=VARCHAR}
        </if>
        <if test="'driverlic_class'.toString() == column.value">
          #{item.driverlicClass,jdbcType=VARCHAR}
        </if>
        <if test="'driverlic_issue_date'.toString() == column.value">
          #{item.driverlicIssueDate,jdbcType=VARCHAR}
        </if>
        <if test="'driverlic_valid_begin'.toString() == column.value">
          #{item.driverlicValidBegin,jdbcType=VARCHAR}
        </if>
        <if test="'driverlic_valid_end'.toString() == column.value">
          #{item.driverlicValidEnd,jdbcType=VARCHAR}
        </if>
        <if test="'driverlic_issue_authority'.toString() == column.value">
          #{item.driverlicIssueAuthority,jdbcType=VARCHAR}
        </if>
        <if test="'driverlic_main_image_url'.toString() == column.value">
          #{item.driverlicMainImageUrl,jdbcType=VARCHAR}
        </if>
        <if test="'driverlic_sub_image_url'.toString() == column.value">
          #{item.driverlicSubImageUrl,jdbcType=VARCHAR}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'extra'.toString() == column.value">
          #{item.extra,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.VehicleReturnExpenseItemMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.VehicleReturnExpenseItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="vehicle_return_id" jdbcType="BIGINT" property="vehicleReturnId" />
    <result column="expense_item_prop_id" jdbcType="BIGINT" property="expenseItemPropId" />
    <result column="expense_item_name" jdbcType="VARCHAR" property="expenseItemName" />
    <result column="expense_amount" jdbcType="BIGINT" property="expenseAmount" />
    <result column="item_type" jdbcType="TINYINT" property="itemType" />
    <result column="pay_kind" jdbcType="TINYINT" property="payKind" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, vehicle_return_id, expense_item_prop_id, expense_item_name, expense_amount, 
    item_type, pay_kind, last_ver, deleted, create_time, op_time, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.VehicleReturnExpenseItemExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_return_expense_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_return_expense_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_return_expense_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.VehicleReturnExpenseItemExample">
    delete from vehicle_return_expense_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.VehicleReturnExpenseItem">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_return_expense_item (order_id, vehicle_return_id, expense_item_prop_id, 
      expense_item_name, expense_amount, item_type, pay_kind
      last_ver, deleted, create_time, 
      op_time, op_user_id)
    values (#{orderId,jdbcType=BIGINT}, #{vehicleReturnId,jdbcType=BIGINT}, #{expenseItemPropId,jdbcType=BIGINT}, 
      #{expenseItemName,jdbcType=VARCHAR}, #{expenseAmount,jdbcType=BIGINT}, #{itemType,jdbcType=TINYINT}, #{payKind,jdbcType=TINYINT},
    #{lastVer,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT},
      #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.VehicleReturnExpenseItem">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_return_expense_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="vehicleReturnId != null">
        vehicle_return_id,
      </if>
      <if test="expenseItemPropId != null">
        expense_item_prop_id,
      </if>
      <if test="expenseItemName != null">
        expense_item_name,
      </if>
      <if test="expenseAmount != null">
        expense_amount,
      </if>
      <if test="itemType != null">
        item_type,
      </if>
      <if test="payKind!= null">
        pay_kind,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleReturnId != null">
        #{vehicleReturnId,jdbcType=BIGINT},
      </if>
      <if test="expenseItemPropId != null">
        #{expenseItemPropId,jdbcType=BIGINT},
      </if>
      <if test="expenseItemName != null">
        #{expenseItemName,jdbcType=VARCHAR},
      </if>
      <if test="expenseAmount != null">
        #{expenseAmount,jdbcType=BIGINT},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=TINYINT},
      </if>
      <if test="payKind!= null">
        #{payKind,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.VehicleReturnExpenseItemExample" resultType="java.lang.Long">
    select count(*) from vehicle_return_expense_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_return_expense_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleReturnId != null">
        vehicle_return_id = #{record.vehicleReturnId,jdbcType=BIGINT},
      </if>
      <if test="record.expenseItemPropId != null">
        expense_item_prop_id = #{record.expenseItemPropId,jdbcType=BIGINT},
      </if>
      <if test="record.expenseItemName != null">
        expense_item_name = #{record.expenseItemName,jdbcType=VARCHAR},
      </if>
      <if test="record.expenseAmount != null">
        expense_amount = #{record.expenseAmount,jdbcType=BIGINT},
      </if>
      <if test="record.itemType != null">
        item_type = #{record.itemType,jdbcType=TINYINT},
      </if>
        <if test="record.payKind!= null">
        pay_kind = #{record.payKind,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_return_expense_item
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      vehicle_return_id = #{record.vehicleReturnId,jdbcType=BIGINT},
      expense_item_prop_id = #{record.expenseItemPropId,jdbcType=BIGINT},
      expense_item_name = #{record.expenseItemName,jdbcType=VARCHAR},
      expense_amount = #{record.expenseAmount,jdbcType=BIGINT},
      item_type = #{record.itemType,jdbcType=TINYINT},
      pay_kind = #{record.payKind,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.VehicleReturnExpenseItem">
    update vehicle_return_expense_item
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleReturnId != null">
        vehicle_return_id = #{vehicleReturnId,jdbcType=BIGINT},
      </if>
      <if test="expenseItemPropId != null">
        expense_item_prop_id = #{expenseItemPropId,jdbcType=BIGINT},
      </if>
      <if test="expenseItemName != null">
        expense_item_name = #{expenseItemName,jdbcType=VARCHAR},
      </if>
      <if test="expenseAmount != null">
        expense_amount = #{expenseAmount,jdbcType=BIGINT},
      </if>
      <if test="itemType != null">
        item_type = #{itemType,jdbcType=TINYINT},
      </if>
        <if test="payKind!= null">
        pay_kind = #{payKind,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.VehicleReturnExpenseItem">
    update vehicle_return_expense_item
    set order_id = #{orderId,jdbcType=BIGINT},
      vehicle_return_id = #{vehicleReturnId,jdbcType=BIGINT},
      expense_item_prop_id = #{expenseItemPropId,jdbcType=BIGINT},
      expense_item_name = #{expenseItemName,jdbcType=VARCHAR},
      expense_amount = #{expenseAmount,jdbcType=BIGINT},
      item_type = #{itemType,jdbcType=TINYINT},
      pay_kind = #{payKind,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_return_expense_item
    (order_id, vehicle_return_id, expense_item_prop_id, expense_item_name, expense_amount, 
      item_type, pay_kind, last_ver, deleted, create_time, op_time, op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=BIGINT}, #{item.vehicleReturnId,jdbcType=BIGINT}, #{item.expenseItemPropId,jdbcType=BIGINT}, 
        #{item.expenseItemName,jdbcType=VARCHAR}, #{item.expenseAmount,jdbcType=BIGINT}, 
        #{item.itemType,jdbcType=TINYINT}, #{item.payKind,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER}, #{item.deleted,jdbcType=TINYINT},
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_return_expense_item (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_return_id'.toString() == column.value">
          #{item.vehicleReturnId,jdbcType=BIGINT}
        </if>
        <if test="'expense_item_prop_id'.toString() == column.value">
          #{item.expenseItemPropId,jdbcType=BIGINT}
        </if>
        <if test="'expense_item_name'.toString() == column.value">
          #{item.expenseItemName,jdbcType=VARCHAR}
        </if>
        <if test="'expense_amount'.toString() == column.value">
          #{item.expenseAmount,jdbcType=BIGINT}
        </if>
        <if test="'item_type'.toString() == column.value">
          #{item.itemType,jdbcType=TINYINT}
        </if>
        <if test="'pay_kind'.toString() == column.value">
          #{item.payKind,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
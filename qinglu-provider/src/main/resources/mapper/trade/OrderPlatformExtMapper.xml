<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.OrderPlatformExtMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.OrderPlatformExt">
    <id column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
    <result column="order_source" jdbcType="TINYINT" property="orderSource" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="free_deposit_way" jdbcType="VARCHAR" property="freeDepositWay" />
    <result column="partner_user_info" jdbcType="VARCHAR" property="partnerUserInfo" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    order_id, ext, order_source, last_ver, free_deposit_way, partner_user_info, op_user_id, 
    create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.OrderPlatformExtExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from order_platform_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_platform_ext
    where order_id = #{orderId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_platform_ext
    where order_id = #{orderId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.OrderPlatformExtExample">
    delete from order_platform_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.OrderPlatformExt">
    insert into order_platform_ext (order_id, ext, order_source, 
      last_ver, free_deposit_way, partner_user_info, 
      op_user_id, create_time, op_time
      )
    values (#{orderId,jdbcType=BIGINT}, #{ext,jdbcType=VARCHAR}, #{orderSource,jdbcType=TINYINT}, 
      #{lastVer,jdbcType=INTEGER}, #{freeDepositWay,jdbcType=VARCHAR}, #{partnerUserInfo,jdbcType=VARCHAR}, 
      #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.OrderPlatformExt">
    insert into order_platform_ext
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="orderSource != null">
        order_source,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="freeDepositWay != null">
        free_deposit_way,
      </if>
      <if test="partnerUserInfo != null">
        partner_user_info,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="orderSource != null">
        #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="freeDepositWay != null">
        #{freeDepositWay,jdbcType=VARCHAR},
      </if>
      <if test="partnerUserInfo != null">
        #{partnerUserInfo,jdbcType=VARCHAR},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.OrderPlatformExtExample" resultType="java.lang.Long">
    select count(*) from order_platform_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_platform_ext
    <set>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.ext != null">
        ext = #{record.ext,jdbcType=VARCHAR},
      </if>
      <if test="record.orderSource != null">
        order_source = #{record.orderSource,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.freeDepositWay != null">
        free_deposit_way = #{record.freeDepositWay,jdbcType=VARCHAR},
      </if>
      <if test="record.partnerUserInfo != null">
        partner_user_info = #{record.partnerUserInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_platform_ext
    set order_id = #{record.orderId,jdbcType=BIGINT},
      ext = #{record.ext,jdbcType=VARCHAR},
      order_source = #{record.orderSource,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      free_deposit_way = #{record.freeDepositWay,jdbcType=VARCHAR},
      partner_user_info = #{record.partnerUserInfo,jdbcType=VARCHAR},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.OrderPlatformExt">
    update order_platform_ext
    <set>
      <if test="ext != null">
        ext = #{ext,jdbcType=VARCHAR},
      </if>
      <if test="orderSource != null">
        order_source = #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="freeDepositWay != null">
        free_deposit_way = #{freeDepositWay,jdbcType=VARCHAR},
      </if>
      <if test="partnerUserInfo != null">
        partner_user_info = #{partnerUserInfo,jdbcType=VARCHAR},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.OrderPlatformExt">
    update order_platform_ext
    set ext = #{ext,jdbcType=VARCHAR},
      order_source = #{orderSource,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      free_deposit_way = #{freeDepositWay,jdbcType=VARCHAR},
      partner_user_info = #{partnerUserInfo,jdbcType=VARCHAR},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_platform_ext
    (order_id, ext, order_source, last_ver, free_deposit_way, partner_user_info, op_user_id, 
      create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=BIGINT}, #{item.ext,jdbcType=VARCHAR}, #{item.orderSource,jdbcType=TINYINT}, 
        #{item.lastVer,jdbcType=INTEGER}, #{item.freeDepositWay,jdbcType=VARCHAR}, #{item.partnerUserInfo,jdbcType=VARCHAR}, 
        #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_platform_ext (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'ext'.toString() == column.value">
          #{item.ext,jdbcType=VARCHAR}
        </if>
        <if test="'order_source'.toString() == column.value">
          #{item.orderSource,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'free_deposit_way'.toString() == column.value">
          #{item.freeDepositWay,jdbcType=VARCHAR}
        </if>
        <if test="'partner_user_info'.toString() == column.value">
          #{item.partnerUserInfo,jdbcType=VARCHAR}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.FinanceDetailAttMapper">
    
    <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.FinanceDetailAtt">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="finance_detail_id" jdbcType="BIGINT" property="financeDetailId"/>
        <result column="att_url" jdbcType="VARCHAR" property="attUrl"/>
        <result column="sort_no" jdbcType="INTEGER" property="sortNo"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="last_ver" jdbcType="INTEGER" property="lastVer"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="op_time" jdbcType="BIGINT" property="opTime"/>
        <result column="op_user_id" jdbcType="BIGINT" property="opUserId"/>
    </resultMap>
    
    <sql id="Base_Column_List">
        id, finance_detail_id, att_url, sort_no, deleted, last_ver, create_time, op_time, op_user_id
    </sql>
    
    <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.FinanceDetailAtt">
        insert into finance_detail_att
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="financeDetailId != null">
                finance_detail_id,
            </if>
            <if test="attUrl != null">
                att_url,
            </if>
            <if test="sortNo != null">
                sort_no,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="lastVer != null">
                last_ver,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="opTime != null">
                op_time,
            </if>
            <if test="opUserId != null">
                op_user_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="financeDetailId != null">
                #{financeDetailId,jdbcType=BIGINT},
            </if>
            <if test="attUrl != null">
                #{attUrl,jdbcType=VARCHAR},
            </if>
            <if test="sortNo != null">
                #{sortNo,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=TINYINT},
            </if>
            <if test="lastVer != null">
                #{lastVer,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="opTime != null">
                #{opTime,jdbcType=BIGINT},
            </if>
            <if test="opUserId != null">
                #{opUserId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    
    <insert id="batchInsert" parameterType="java.util.List">
        insert into finance_detail_att (finance_detail_id, att_url, sort_no, deleted, last_ver, create_time, op_time, op_user_id)
        values
        <foreach collection="records" item="record" separator=",">
            (#{record.financeDetailId,jdbcType=BIGINT},
             #{record.attUrl,jdbcType=VARCHAR},
             #{record.sortNo,jdbcType=INTEGER},
             #{record.deleted,jdbcType=TINYINT},
             #{record.lastVer,jdbcType=INTEGER},
             #{record.createTime,jdbcType=BIGINT},
             #{record.opTime,jdbcType=BIGINT},
             #{record.opUserId,jdbcType=BIGINT})
        </foreach>
    </insert>
    
    <select id="selectByFinanceDetailId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from finance_detail_att
        where finance_detail_id = #{financeDetailId,jdbcType=BIGINT}
          and deleted = 0
        order by sort_no
    </select>
    
    <update id="deleteByFinanceDetailId">
        update finance_detail_att
        set deleted = 1,
            op_time = #{opTime,jdbcType=BIGINT},
            op_user_id = #{opUserId,jdbcType=BIGINT}
        where finance_detail_id = #{financeDetailId,jdbcType=BIGINT}
          and deleted = 0
    </update>
    
</mapper> 
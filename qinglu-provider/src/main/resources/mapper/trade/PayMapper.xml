<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.PayMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.Pay">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="sub_order_id" jdbcType="BIGINT" property="subOrderId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="quantity" jdbcType="SMALLINT" property="quantity" />
    <result column="pay_amount" jdbcType="INTEGER" property="payAmount" />
    <result column="actual_pay_amount" jdbcType="INTEGER" property="actualPayAmount" />
    <result column="fee_type" jdbcType="TINYINT" property="feeType" />
    <result column="pay_status" jdbcType="TINYINT" property="payStatus" />
    <result column="pay_source" jdbcType="TINYINT" property="paySource" />
    <result column="pay_source_id" jdbcType="VARCHAR" property="paySourceId" />
    <result column="source_failed_reason" jdbcType="VARCHAR" property="sourceFailedReason" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="pay_time" jdbcType="BIGINT" property="payTime" />
    <result column="pay_op_time" jdbcType="BIGINT" property="payOpTime" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, sub_order_id, store_id, quantity, pay_amount, actual_pay_amount, fee_type, 
    pay_status, pay_source, pay_source_id, source_failed_reason, last_ver, op_user_id, 
    pay_time, pay_op_time, create_time, op_time, extra
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.PayExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pay
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pay
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.PayExample">
    delete from pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.Pay">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pay (order_id, sub_order_id, store_id, 
      quantity, pay_amount, actual_pay_amount, 
      fee_type, pay_status, pay_source, 
      pay_source_id, source_failed_reason, last_ver, 
      op_user_id, pay_time, pay_op_time, 
      create_time, op_time, extra)
    values (#{orderId,jdbcType=BIGINT}, #{subOrderId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, 
      #{quantity,jdbcType=SMALLINT}, #{payAmount,jdbcType=INTEGER}, #{actualPayAmount,jdbcType=INTEGER}, 
      #{feeType,jdbcType=TINYINT}, #{payStatus,jdbcType=TINYINT}, #{paySource,jdbcType=TINYINT}, 
      #{paySourceId,jdbcType=VARCHAR}, #{sourceFailedReason,jdbcType=VARCHAR}, #{lastVer,jdbcType=INTEGER}, 
      #{opUserId,jdbcType=BIGINT}, #{payTime,jdbcType=BIGINT}, #{payOpTime,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, #{extra,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.Pay">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pay
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="subOrderId != null">
        sub_order_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="payAmount != null">
        pay_amount,
      </if>
      <if test="actualPayAmount != null">
        actual_pay_amount,
      </if>
      <if test="feeType != null">
        fee_type,
      </if>
      <if test="payStatus != null">
        pay_status,
      </if>
      <if test="paySource != null">
        pay_source,
      </if>
      <if test="paySourceId != null">
        pay_source_id,
      </if>
      <if test="sourceFailedReason != null">
        source_failed_reason,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="payOpTime != null">
        pay_op_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="subOrderId != null">
        #{subOrderId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=SMALLINT},
      </if>
      <if test="payAmount != null">
        #{payAmount,jdbcType=INTEGER},
      </if>
      <if test="actualPayAmount != null">
        #{actualPayAmount,jdbcType=INTEGER},
      </if>
      <if test="feeType != null">
        #{feeType,jdbcType=TINYINT},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=TINYINT},
      </if>
      <if test="paySource != null">
        #{paySource,jdbcType=TINYINT},
      </if>
      <if test="paySourceId != null">
        #{paySourceId,jdbcType=VARCHAR},
      </if>
      <if test="sourceFailedReason != null">
        #{sourceFailedReason,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=BIGINT},
      </if>
      <if test="payOpTime != null">
        #{payOpTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.PayExample" resultType="java.lang.Long">
    select count(*) from pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update pay
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.subOrderId != null">
        sub_order_id = #{record.subOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=SMALLINT},
      </if>
      <if test="record.payAmount != null">
        pay_amount = #{record.payAmount,jdbcType=INTEGER},
      </if>
      <if test="record.actualPayAmount != null">
        actual_pay_amount = #{record.actualPayAmount,jdbcType=INTEGER},
      </if>
      <if test="record.feeType != null">
        fee_type = #{record.feeType,jdbcType=TINYINT},
      </if>
      <if test="record.payStatus != null">
        pay_status = #{record.payStatus,jdbcType=TINYINT},
      </if>
      <if test="record.paySource != null">
        pay_source = #{record.paySource,jdbcType=TINYINT},
      </if>
      <if test="record.paySourceId != null">
        pay_source_id = #{record.paySourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceFailedReason != null">
        source_failed_reason = #{record.sourceFailedReason,jdbcType=VARCHAR},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.payTime != null">
        pay_time = #{record.payTime,jdbcType=BIGINT},
      </if>
      <if test="record.payOpTime != null">
        pay_op_time = #{record.payOpTime,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update pay
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      sub_order_id = #{record.subOrderId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      quantity = #{record.quantity,jdbcType=SMALLINT},
      pay_amount = #{record.payAmount,jdbcType=INTEGER},
      actual_pay_amount = #{record.actualPayAmount,jdbcType=INTEGER},
      fee_type = #{record.feeType,jdbcType=TINYINT},
      pay_status = #{record.payStatus,jdbcType=TINYINT},
      pay_source = #{record.paySource,jdbcType=TINYINT},
      pay_source_id = #{record.paySourceId,jdbcType=VARCHAR},
      source_failed_reason = #{record.sourceFailedReason,jdbcType=VARCHAR},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      pay_time = #{record.payTime,jdbcType=BIGINT},
      pay_op_time = #{record.payOpTime,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      extra = #{record.extra,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.Pay">
    update pay
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="subOrderId != null">
        sub_order_id = #{subOrderId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=SMALLINT},
      </if>
      <if test="payAmount != null">
        pay_amount = #{payAmount,jdbcType=INTEGER},
      </if>
      <if test="actualPayAmount != null">
        actual_pay_amount = #{actualPayAmount,jdbcType=INTEGER},
      </if>
      <if test="feeType != null">
        fee_type = #{feeType,jdbcType=TINYINT},
      </if>
      <if test="payStatus != null">
        pay_status = #{payStatus,jdbcType=TINYINT},
      </if>
      <if test="paySource != null">
        pay_source = #{paySource,jdbcType=TINYINT},
      </if>
      <if test="paySourceId != null">
        pay_source_id = #{paySourceId,jdbcType=VARCHAR},
      </if>
      <if test="sourceFailedReason != null">
        source_failed_reason = #{sourceFailedReason,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=BIGINT},
      </if>
      <if test="payOpTime != null">
        pay_op_time = #{payOpTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.Pay">
    update pay
    set order_id = #{orderId,jdbcType=BIGINT},
      sub_order_id = #{subOrderId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      quantity = #{quantity,jdbcType=SMALLINT},
      pay_amount = #{payAmount,jdbcType=INTEGER},
      actual_pay_amount = #{actualPayAmount,jdbcType=INTEGER},
      fee_type = #{feeType,jdbcType=TINYINT},
      pay_status = #{payStatus,jdbcType=TINYINT},
      pay_source = #{paySource,jdbcType=TINYINT},
      pay_source_id = #{paySourceId,jdbcType=VARCHAR},
      source_failed_reason = #{sourceFailedReason,jdbcType=VARCHAR},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      pay_time = #{payTime,jdbcType=BIGINT},
      pay_op_time = #{payOpTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      extra = #{extra,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into pay
    (order_id, sub_order_id, store_id, quantity, pay_amount, actual_pay_amount, fee_type, 
      pay_status, pay_source, pay_source_id, source_failed_reason, last_ver, op_user_id, 
      pay_time, pay_op_time, create_time, op_time, extra)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=BIGINT}, #{item.subOrderId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, 
        #{item.quantity,jdbcType=SMALLINT}, #{item.payAmount,jdbcType=INTEGER}, #{item.actualPayAmount,jdbcType=INTEGER}, 
        #{item.feeType,jdbcType=TINYINT}, #{item.payStatus,jdbcType=TINYINT}, #{item.paySource,jdbcType=TINYINT}, 
        #{item.paySourceId,jdbcType=VARCHAR}, #{item.sourceFailedReason,jdbcType=VARCHAR}, 
        #{item.lastVer,jdbcType=INTEGER}, #{item.opUserId,jdbcType=BIGINT}, #{item.payTime,jdbcType=BIGINT}, 
        #{item.payOpTime,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, #{item.extra,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into pay (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'sub_order_id'.toString() == column.value">
          #{item.subOrderId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'quantity'.toString() == column.value">
          #{item.quantity,jdbcType=SMALLINT}
        </if>
        <if test="'pay_amount'.toString() == column.value">
          #{item.payAmount,jdbcType=INTEGER}
        </if>
        <if test="'actual_pay_amount'.toString() == column.value">
          #{item.actualPayAmount,jdbcType=INTEGER}
        </if>
        <if test="'fee_type'.toString() == column.value">
          #{item.feeType,jdbcType=TINYINT}
        </if>
        <if test="'pay_status'.toString() == column.value">
          #{item.payStatus,jdbcType=TINYINT}
        </if>
        <if test="'pay_source'.toString() == column.value">
          #{item.paySource,jdbcType=TINYINT}
        </if>
        <if test="'pay_source_id'.toString() == column.value">
          #{item.paySourceId,jdbcType=VARCHAR}
        </if>
        <if test="'source_failed_reason'.toString() == column.value">
          #{item.sourceFailedReason,jdbcType=VARCHAR}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'pay_time'.toString() == column.value">
          #{item.payTime,jdbcType=BIGINT}
        </if>
        <if test="'pay_op_time'.toString() == column.value">
          #{item.payOpTime,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'extra'.toString() == column.value">
          #{item.extra,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.OrderDailyPriceMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.OrderDailyPrice">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="rent_date" jdbcType="VARCHAR" property="rentDate" />
    <result column="price" jdbcType="INTEGER" property="price" />
    <result column="part_daily_price" jdbcType="INTEGER" property="partDailyPrice" />
    <result column="rent_hour" jdbcType="INTEGER" property="rentHour" />
    <result column="hour_pre" jdbcType="DOUBLE" property="hourPre" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, order_type, vehicle_id, rent_date, price, part_daily_price, rent_hour, 
    hour_pre, last_ver, op_user_id, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.OrderDailyPriceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from order_daily_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_daily_price
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_daily_price
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.OrderDailyPriceExample">
    delete from order_daily_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.OrderDailyPrice">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_daily_price (order_id, order_type, vehicle_id, 
      rent_date, price, part_daily_price, 
      rent_hour, hour_pre, last_ver, 
      op_user_id, create_time, op_time
      )
    values (#{orderId,jdbcType=BIGINT}, #{orderType,jdbcType=TINYINT}, #{vehicleId,jdbcType=BIGINT}, 
      #{rentDate,jdbcType=VARCHAR}, #{price,jdbcType=INTEGER}, #{partDailyPrice,jdbcType=INTEGER}, 
      #{rentHour,jdbcType=INTEGER}, #{hourPre,jdbcType=DOUBLE}, #{lastVer,jdbcType=INTEGER}, 
      #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.OrderDailyPrice">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_daily_price
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="rentDate != null">
        rent_date,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="partDailyPrice != null">
        part_daily_price,
      </if>
      <if test="rentHour != null">
        rent_hour,
      </if>
      <if test="hourPre != null">
        hour_pre,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="rentDate != null">
        #{rentDate,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=INTEGER},
      </if>
      <if test="partDailyPrice != null">
        #{partDailyPrice,jdbcType=INTEGER},
      </if>
      <if test="rentHour != null">
        #{rentHour,jdbcType=INTEGER},
      </if>
      <if test="hourPre != null">
        #{hourPre,jdbcType=DOUBLE},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.OrderDailyPriceExample" resultType="java.lang.Long">
    select count(*) from order_daily_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_daily_price
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=TINYINT},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="record.rentDate != null">
        rent_date = #{record.rentDate,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=INTEGER},
      </if>
      <if test="record.partDailyPrice != null">
        part_daily_price = #{record.partDailyPrice,jdbcType=INTEGER},
      </if>
      <if test="record.rentHour != null">
        rent_hour = #{record.rentHour,jdbcType=INTEGER},
      </if>
      <if test="record.hourPre != null">
        hour_pre = #{record.hourPre,jdbcType=DOUBLE},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_daily_price
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      order_type = #{record.orderType,jdbcType=TINYINT},
      vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      rent_date = #{record.rentDate,jdbcType=VARCHAR},
      price = #{record.price,jdbcType=INTEGER},
      part_daily_price = #{record.partDailyPrice,jdbcType=INTEGER},
      rent_hour = #{record.rentHour,jdbcType=INTEGER},
      hour_pre = #{record.hourPre,jdbcType=DOUBLE},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.OrderDailyPrice">
    update order_daily_price
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="rentDate != null">
        rent_date = #{rentDate,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=INTEGER},
      </if>
      <if test="partDailyPrice != null">
        part_daily_price = #{partDailyPrice,jdbcType=INTEGER},
      </if>
      <if test="rentHour != null">
        rent_hour = #{rentHour,jdbcType=INTEGER},
      </if>
      <if test="hourPre != null">
        hour_pre = #{hourPre,jdbcType=DOUBLE},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.OrderDailyPrice">
    update order_daily_price
    set order_id = #{orderId,jdbcType=BIGINT},
      order_type = #{orderType,jdbcType=TINYINT},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      rent_date = #{rentDate,jdbcType=VARCHAR},
      price = #{price,jdbcType=INTEGER},
      part_daily_price = #{partDailyPrice,jdbcType=INTEGER},
      rent_hour = #{rentHour,jdbcType=INTEGER},
      hour_pre = #{hourPre,jdbcType=DOUBLE},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_daily_price
    (order_id, order_type, vehicle_id, rent_date, price, part_daily_price, rent_hour, 
      hour_pre, last_ver, op_user_id, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=BIGINT}, #{item.orderType,jdbcType=TINYINT}, #{item.vehicleId,jdbcType=BIGINT}, 
        #{item.rentDate,jdbcType=VARCHAR}, #{item.price,jdbcType=INTEGER}, #{item.partDailyPrice,jdbcType=INTEGER}, 
        #{item.rentHour,jdbcType=INTEGER}, #{item.hourPre,jdbcType=DOUBLE}, #{item.lastVer,jdbcType=INTEGER}, 
        #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_daily_price (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'order_type'.toString() == column.value">
          #{item.orderType,jdbcType=TINYINT}
        </if>
        <if test="'vehicle_id'.toString() == column.value">
          #{item.vehicleId,jdbcType=BIGINT}
        </if>
        <if test="'rent_date'.toString() == column.value">
          #{item.rentDate,jdbcType=VARCHAR}
        </if>
        <if test="'price'.toString() == column.value">
          #{item.price,jdbcType=INTEGER}
        </if>
        <if test="'part_daily_price'.toString() == column.value">
          #{item.partDailyPrice,jdbcType=INTEGER}
        </if>
        <if test="'rent_hour'.toString() == column.value">
          #{item.rentHour,jdbcType=INTEGER}
        </if>
        <if test="'hour_pre'.toString() == column.value">
          #{item.hourPre,jdbcType=DOUBLE}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
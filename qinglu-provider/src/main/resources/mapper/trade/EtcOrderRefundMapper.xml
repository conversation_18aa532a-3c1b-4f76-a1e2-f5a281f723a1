<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.EtcOrderRefundMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.EtcOrderRefund">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="pay_id" jdbcType="BIGINT" property="payId" />
    <result column="refund_id" jdbcType="VARCHAR" property="refundId" />
    <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime" />
    <result column="refund_amt" jdbcType="BIGINT" property="refundAmt" />
    <result column="actual_refund_amt" jdbcType="BIGINT" property="actualRefundAmt" />
    <result column="refund_status" jdbcType="BIGINT" property="refundStatus" />
    <result column="third_pay_no" jdbcType="VARCHAR" property="thirdPayNo" />
    <result column="third_refund_source" jdbcType="BIGINT" property="thirdRefundSource" />
    <result column="third_fail_reason" jdbcType="VARCHAR" property="thirdFailReason" />
    <result column="deleted" jdbcType="BIGINT" property="deleted" />
    <result column="last_ver" jdbcType="BIGINT" property="lastVer" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, order_id, pay_id, refund_id, refund_time, refund_amt, actual_refund_amt, 
    refund_status, third_pay_no, third_refund_source, third_fail_reason, deleted, last_ver, 
    create_time, op_user_id, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.EtcOrderRefundExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from etc_order_refund
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from etc_order_refund
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.EtcOrderRefund">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into etc_order_refund (merchant_id, order_id, pay_id, 
      refund_id, refund_time, refund_amt, 
      actual_refund_amt, refund_status, third_pay_no, 
      third_refund_source, third_fail_reason, deleted, 
      last_ver, create_time, op_user_id, 
      op_time)
    values (#{merchantId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{payId,jdbcType=BIGINT}, 
      #{refundId,jdbcType=VARCHAR}, #{refundTime,jdbcType=TIMESTAMP}, #{refundAmt,jdbcType=BIGINT}, 
      #{actualRefundAmt,jdbcType=BIGINT}, #{refundStatus,jdbcType=BIGINT}, #{thirdPayNo,jdbcType=VARCHAR}, 
      #{thirdRefundSource,jdbcType=BIGINT}, #{thirdFailReason,jdbcType=VARCHAR}, #{deleted,jdbcType=BIGINT}, 
      #{lastVer,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.EtcOrderRefund">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into etc_order_refund
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="payId != null">
        pay_id,
      </if>
      <if test="refundId != null">
        refund_id,
      </if>
      <if test="refundTime != null">
        refund_time,
      </if>
      <if test="refundAmt != null">
        refund_amt,
      </if>
      <if test="actualRefundAmt != null">
        actual_refund_amt,
      </if>
      <if test="refundStatus != null">
        refund_status,
      </if>
      <if test="thirdPayNo != null">
        third_pay_no,
      </if>
      <if test="thirdRefundSource != null">
        third_refund_source,
      </if>
      <if test="thirdFailReason != null">
        third_fail_reason,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="payId != null">
        #{payId,jdbcType=BIGINT},
      </if>
      <if test="refundId != null">
        #{refundId,jdbcType=VARCHAR},
      </if>
      <if test="refundTime != null">
        #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundAmt != null">
        #{refundAmt,jdbcType=BIGINT},
      </if>
      <if test="actualRefundAmt != null">
        #{actualRefundAmt,jdbcType=BIGINT},
      </if>
      <if test="refundStatus != null">
        #{refundStatus,jdbcType=BIGINT},
      </if>
      <if test="thirdPayNo != null">
        #{thirdPayNo,jdbcType=VARCHAR},
      </if>
      <if test="thirdRefundSource != null">
        #{thirdRefundSource,jdbcType=BIGINT},
      </if>
      <if test="thirdFailReason != null">
        #{thirdFailReason,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.EtcOrderRefundExample" resultType="java.lang.Long">
    select count(*) from etc_order_refund
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update etc_order_refund
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.payId != null">
        pay_id = #{record.payId,jdbcType=BIGINT},
      </if>
      <if test="record.refundId != null">
        refund_id = #{record.refundId,jdbcType=VARCHAR},
      </if>
      <if test="record.refundTime != null">
        refund_time = #{record.refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.refundAmt != null">
        refund_amt = #{record.refundAmt,jdbcType=BIGINT},
      </if>
      <if test="record.actualRefundAmt != null">
        actual_refund_amt = #{record.actualRefundAmt,jdbcType=BIGINT},
      </if>
      <if test="record.refundStatus != null">
        refund_status = #{record.refundStatus,jdbcType=BIGINT},
      </if>
      <if test="record.thirdPayNo != null">
        third_pay_no = #{record.thirdPayNo,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdRefundSource != null">
        third_refund_source = #{record.thirdRefundSource,jdbcType=BIGINT},
      </if>
      <if test="record.thirdFailReason != null">
        third_fail_reason = #{record.thirdFailReason,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIGINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update etc_order_refund
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      pay_id = #{record.payId,jdbcType=BIGINT},
      refund_id = #{record.refundId,jdbcType=VARCHAR},
      refund_time = #{record.refundTime,jdbcType=TIMESTAMP},
      refund_amt = #{record.refundAmt,jdbcType=BIGINT},
      actual_refund_amt = #{record.actualRefundAmt,jdbcType=BIGINT},
      refund_status = #{record.refundStatus,jdbcType=BIGINT},
      third_pay_no = #{record.thirdPayNo,jdbcType=VARCHAR},
      third_refund_source = #{record.thirdRefundSource,jdbcType=BIGINT},
      third_fail_reason = #{record.thirdFailReason,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=BIGINT},
      last_ver = #{record.lastVer,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.EtcOrderRefund">
    update etc_order_refund
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="payId != null">
        pay_id = #{payId,jdbcType=BIGINT},
      </if>
      <if test="refundId != null">
        refund_id = #{refundId,jdbcType=VARCHAR},
      </if>
      <if test="refundTime != null">
        refund_time = #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundAmt != null">
        refund_amt = #{refundAmt,jdbcType=BIGINT},
      </if>
      <if test="actualRefundAmt != null">
        actual_refund_amt = #{actualRefundAmt,jdbcType=BIGINT},
      </if>
      <if test="refundStatus != null">
        refund_status = #{refundStatus,jdbcType=BIGINT},
      </if>
      <if test="thirdPayNo != null">
        third_pay_no = #{thirdPayNo,jdbcType=VARCHAR},
      </if>
      <if test="thirdRefundSource != null">
        third_refund_source = #{thirdRefundSource,jdbcType=BIGINT},
      </if>
      <if test="thirdFailReason != null">
        third_fail_reason = #{thirdFailReason,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.EtcOrderRefund">
    update etc_order_refund
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      pay_id = #{payId,jdbcType=BIGINT},
      refund_id = #{refundId,jdbcType=VARCHAR},
      refund_time = #{refundTime,jdbcType=TIMESTAMP},
      refund_amt = #{refundAmt,jdbcType=BIGINT},
      actual_refund_amt = #{actualRefundAmt,jdbcType=BIGINT},
      refund_status = #{refundStatus,jdbcType=BIGINT},
      third_pay_no = #{thirdPayNo,jdbcType=VARCHAR},
      third_refund_source = #{thirdRefundSource,jdbcType=BIGINT},
      third_fail_reason = #{thirdFailReason,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BIGINT},
      last_ver = #{lastVer,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into etc_order_refund
    (merchant_id, order_id, pay_id, refund_id, refund_time, refund_amt, actual_refund_amt, 
      refund_status, third_pay_no, third_refund_source, third_fail_reason, deleted, last_ver, 
      create_time, op_user_id, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.orderId,jdbcType=BIGINT}, #{item.payId,jdbcType=BIGINT}, 
        #{item.refundId,jdbcType=VARCHAR}, #{item.refundTime,jdbcType=TIMESTAMP}, #{item.refundAmt,jdbcType=BIGINT}, 
        #{item.actualRefundAmt,jdbcType=BIGINT}, #{item.refundStatus,jdbcType=BIGINT}, 
        #{item.thirdPayNo,jdbcType=VARCHAR}, #{item.thirdRefundSource,jdbcType=BIGINT}, 
        #{item.thirdFailReason,jdbcType=VARCHAR}, #{item.deleted,jdbcType=BIGINT}, #{item.lastVer,jdbcType=BIGINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into etc_order_refund (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'pay_id'.toString() == column.value">
          #{item.payId,jdbcType=BIGINT}
        </if>
        <if test="'refund_id'.toString() == column.value">
          #{item.refundId,jdbcType=VARCHAR}
        </if>
        <if test="'refund_time'.toString() == column.value">
          #{item.refundTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'refund_amt'.toString() == column.value">
          #{item.refundAmt,jdbcType=BIGINT}
        </if>
        <if test="'actual_refund_amt'.toString() == column.value">
          #{item.actualRefundAmt,jdbcType=BIGINT}
        </if>
        <if test="'refund_status'.toString() == column.value">
          #{item.refundStatus,jdbcType=BIGINT}
        </if>
        <if test="'third_pay_no'.toString() == column.value">
          #{item.thirdPayNo,jdbcType=VARCHAR}
        </if>
        <if test="'third_refund_source'.toString() == column.value">
          #{item.thirdRefundSource,jdbcType=BIGINT}
        </if>
        <if test="'third_fail_reason'.toString() == column.value">
          #{item.thirdFailReason,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=BIGINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
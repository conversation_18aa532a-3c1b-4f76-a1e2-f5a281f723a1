<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.ThirdAllocationDetailMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.ThirdAllocationDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="biz_type" jdbcType="TINYINT" property="bizType" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="ratio" jdbcType="DECIMAL" property="ratio" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="allocation_time" jdbcType="BIGINT" property="allocationTime" />
    <result column="trans_order_no" jdbcType="VARCHAR" property="transOrderNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
  </resultMap>
  <sql id="Base_Column_List">
    id, task_id, store_id, merchant_id, biz_type, amount, ratio, status, remark, create_time, update_time, allocation_time, trans_order_no, order_no, error_msg
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from third_allocation_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.ThirdAllocationDetail">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into third_allocation_detail (task_id, store_id, merchant_id, 
      biz_type, amount, ratio, status, remark, create_time, update_time, allocation_time, trans_order_no, order_no, error_msg)
    values (#{taskId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, 
      #{bizType,jdbcType=TINYINT}, #{amount,jdbcType=DECIMAL}, #{ratio,jdbcType=DECIMAL}, 
      #{status,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT}, #{allocationTime,jdbcType=BIGINT}, #{transOrderNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{errorMsg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.ThirdAllocationDetail">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into third_allocation_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="ratio != null">
        ratio,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="allocationTime != null">
        allocation_time,
      </if>
      <if test="transOrderNo != null">
        trans_order_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="errorMsg != null">
        error_msg,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=TINYINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="ratio != null">
        #{ratio,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="allocationTime != null">
        #{allocationTime,jdbcType=BIGINT},
      </if>
      <if test="transOrderNo != null">
        #{transOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        #{errorMsg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.ThirdAllocationDetail">
    update third_allocation_detail
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=TINYINT},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="ratio != null">
        ratio = #{ratio,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="allocationTime != null">
        allocation_time = #{allocationTime,jdbcType=BIGINT},
      </if>
      <if test="transOrderNo != null">
        trans_order_no = #{transOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.ThirdAllocationDetail">
    update third_allocation_detail
    set task_id = #{taskId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      biz_type = #{bizType,jdbcType=TINYINT},
      amount = #{amount,jdbcType=DECIMAL},
      ratio = #{ratio,jdbcType=DECIMAL},
      status = #{status,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      allocation_time = #{allocationTime,jdbcType=BIGINT},
      trans_order_no = #{transOrderNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      error_msg = #{errorMsg,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into third_allocation_detail
    (task_id, store_id, merchant_id, biz_type, amount, ratio, status, remark, create_time, update_time, allocation_time, trans_order_no, order_no, error_msg)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.taskId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.merchantId,jdbcType=BIGINT}, 
        #{item.bizType,jdbcType=TINYINT}, #{item.amount,jdbcType=DECIMAL}, #{item.ratio,jdbcType=DECIMAL}, 
        #{item.status,jdbcType=TINYINT}, #{item.remark,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.updateTime,jdbcType=BIGINT}, #{item.allocationTime,jdbcType=BIGINT}, #{item.transOrderNo,jdbcType=VARCHAR}, #{item.orderNo,jdbcType=VARCHAR}, #{item.errorMsg,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <select id="selectByTaskId" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List"/>
    from third_allocation_detail 
    where task_id = #{taskId,jdbcType=BIGINT}
    order by id asc
  </select>
  <select id="getAllByStatus" resultMap="BaseResultMap" parameterType="java.lang.Byte">
    select
    <include refid="Base_Column_List"/>
    from third_allocation_detail 
    where status = #{status,jdbcType=TINYINT}
    order by id desc
  </select>
  <select id="selectByStoreId" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List"/>
    from third_allocation_detail 
    where store_id = #{storeId,jdbcType=BIGINT}
    order by id desc
  </select>
  <select id="selectByMerchantId" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List"/>
    from third_allocation_detail 
    where merchant_id = #{merchantId,jdbcType=BIGINT}
    order by id desc
  </select>
  <select id="selectByTaskIdAndStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from third_allocation_detail 
    where task_id = #{taskId,jdbcType=BIGINT} and status = #{status,jdbcType=TINYINT}
    order by id asc
  </select>
  <select id="selectPage" resultMap="BaseResultMap" parameterType="com.ql.rent.param.trade.ThirdAllocationDetailQueryParam">
        SELECT * FROM third_allocation_detail
        <where>
          <if test="param.storeId != null">
            and store_id = #{param.storeId}
          </if>
        </where>
    order by id desc
    limit #{param.offset}, #{param.pageSize}
    </select>
    <select id="countPage" resultType="long" parameterType="com.ql.rent.param.trade.ThirdAllocationDetailQueryParam">
        SELECT COUNT(1) FROM third_allocation_detail
          <where>
            <if test="param.storeId != null">
              and store_id = #{param.storeId}
            </if>
        </where>
    </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from third_allocation_detail where id = #{id,jdbcType=BIGINT}
  </delete>
</mapper> 
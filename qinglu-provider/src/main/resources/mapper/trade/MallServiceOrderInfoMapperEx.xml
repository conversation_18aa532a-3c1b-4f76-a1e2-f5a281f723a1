<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.MallServiceOrderInfoMapperEx">

    <!-- 扣减订单剩余次数，确保不会扣减到负数 -->
    <update id="decreaseRemainingCount">
        UPDATE mall_service_order_info 
        SET remaining_count = remaining_count - #{count}
        WHERE id = #{orderId} 
        AND remaining_count >= #{count}
        AND deleted = 0
    </update>

    <!-- 增加订单剩余次数（用于补偿） -->
    <update id="increaseRemainingCount">
        UPDATE mall_service_order_info 
        SET remaining_count = remaining_count + #{count}
        WHERE id = #{orderId}
        AND deleted = 0
    </update>

</mapper> 
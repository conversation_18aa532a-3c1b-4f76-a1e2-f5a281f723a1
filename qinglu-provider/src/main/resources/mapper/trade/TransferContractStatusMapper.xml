<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.TransferContractStatusMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.TransferContractStatus">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="contract_id" jdbcType="VARCHAR" property="contractId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="post_code" jdbcType="VARCHAR" property="postCode" />
    <result column="message" jdbcType="VARCHAR" property="message" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="driver_phone" jdbcType="VARCHAR" property="driverPhone" />
    <result column="driver_id" jdbcType="VARCHAR" property="driverId" />
    <result column="account" jdbcType="VARCHAR" property="account" />
    <result column="car_number" jdbcType="VARCHAR" property="carNumber" />
    <result column="car_type" jdbcType="VARCHAR" property="carType" />
    <result column="contract_no" jdbcType="VARCHAR" property="contractNo" />
    <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, vehicle_id, contract_id, status, post_code, message, error_msg, driver_name, 
    driver_phone, driver_id, account, car_number, car_type, contract_no, begin_time, 
    end_time, biz_type, merchant_id, last_ver, op_user_id, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.TransferContractStatusExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from transfer_contract_status
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from transfer_contract_status
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.TransferContractStatus">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into transfer_contract_status (order_id, vehicle_id, contract_id, 
      status, post_code, message, 
      error_msg, driver_name, driver_phone, 
      driver_id, account, car_number, 
      car_type, contract_no, begin_time, 
      end_time, biz_type, merchant_id, 
      last_ver, op_user_id, create_time, 
      op_time)
    values (#{orderId,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, #{contractId,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{postCode,jdbcType=VARCHAR}, #{message,jdbcType=VARCHAR}, 
      #{errorMsg,jdbcType=VARCHAR}, #{driverName,jdbcType=VARCHAR}, #{driverPhone,jdbcType=VARCHAR}, 
      #{driverId,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR}, #{carNumber,jdbcType=VARCHAR}, 
      #{carType,jdbcType=VARCHAR}, #{contractNo,jdbcType=VARCHAR}, #{beginTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{bizType,jdbcType=INTEGER}, #{merchantId,jdbcType=BIGINT}, 
      #{lastVer,jdbcType=INTEGER}, #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.TransferContractStatus">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into transfer_contract_status
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="contractId != null">
        contract_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="postCode != null">
        post_code,
      </if>
      <if test="message != null">
        message,
      </if>
      <if test="errorMsg != null">
        error_msg,
      </if>
      <if test="driverName != null">
        driver_name,
      </if>
      <if test="driverPhone != null">
        driver_phone,
      </if>
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="account != null">
        account,
      </if>
      <if test="carNumber != null">
        car_number,
      </if>
      <if test="carType != null">
        car_type,
      </if>
      <if test="contractNo != null">
        contract_no,
      </if>
      <if test="beginTime != null">
        begin_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="contractId != null">
        #{contractId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="postCode != null">
        #{postCode,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        #{message,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="driverName != null">
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverPhone != null">
        #{driverPhone,jdbcType=VARCHAR},
      </if>
      <if test="driverId != null">
        #{driverId,jdbcType=VARCHAR},
      </if>
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="carNumber != null">
        #{carNumber,jdbcType=VARCHAR},
      </if>
      <if test="carType != null">
        #{carType,jdbcType=VARCHAR},
      </if>
      <if test="contractNo != null">
        #{contractNo,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.TransferContractStatusExample" resultType="java.lang.Long">
    select count(*) from transfer_contract_status
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update transfer_contract_status
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="record.contractId != null">
        contract_id = #{record.contractId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.postCode != null">
        post_code = #{record.postCode,jdbcType=VARCHAR},
      </if>
      <if test="record.message != null">
        message = #{record.message,jdbcType=VARCHAR},
      </if>
      <if test="record.errorMsg != null">
        error_msg = #{record.errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.driverName != null">
        driver_name = #{record.driverName,jdbcType=VARCHAR},
      </if>
      <if test="record.driverPhone != null">
        driver_phone = #{record.driverPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.driverId != null">
        driver_id = #{record.driverId,jdbcType=VARCHAR},
      </if>
      <if test="record.account != null">
        account = #{record.account,jdbcType=VARCHAR},
      </if>
      <if test="record.carNumber != null">
        car_number = #{record.carNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.carType != null">
        car_type = #{record.carType,jdbcType=VARCHAR},
      </if>
      <if test="record.contractNo != null">
        contract_no = #{record.contractNo,jdbcType=VARCHAR},
      </if>
      <if test="record.beginTime != null">
        begin_time = #{record.beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=INTEGER},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update transfer_contract_status
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      contract_id = #{record.contractId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      post_code = #{record.postCode,jdbcType=VARCHAR},
      message = #{record.message,jdbcType=VARCHAR},
      error_msg = #{record.errorMsg,jdbcType=VARCHAR},
      driver_name = #{record.driverName,jdbcType=VARCHAR},
      driver_phone = #{record.driverPhone,jdbcType=VARCHAR},
      driver_id = #{record.driverId,jdbcType=VARCHAR},
      account = #{record.account,jdbcType=VARCHAR},
      car_number = #{record.carNumber,jdbcType=VARCHAR},
      car_type = #{record.carType,jdbcType=VARCHAR},
      contract_no = #{record.contractNo,jdbcType=VARCHAR},
      begin_time = #{record.beginTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      biz_type = #{record.bizType,jdbcType=INTEGER},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.TransferContractStatus">
    update transfer_contract_status
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="contractId != null">
        contract_id = #{contractId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="postCode != null">
        post_code = #{postCode,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        message = #{message,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="driverName != null">
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverPhone != null">
        driver_phone = #{driverPhone,jdbcType=VARCHAR},
      </if>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=VARCHAR},
      </if>
      <if test="account != null">
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="carNumber != null">
        car_number = #{carNumber,jdbcType=VARCHAR},
      </if>
      <if test="carType != null">
        car_type = #{carType,jdbcType=VARCHAR},
      </if>
      <if test="contractNo != null">
        contract_no = #{contractNo,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        begin_time = #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=INTEGER},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.TransferContractStatus">
    update transfer_contract_status
    set order_id = #{orderId,jdbcType=BIGINT},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      contract_id = #{contractId,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      post_code = #{postCode,jdbcType=VARCHAR},
      message = #{message,jdbcType=VARCHAR},
      error_msg = #{errorMsg,jdbcType=VARCHAR},
      driver_name = #{driverName,jdbcType=VARCHAR},
      driver_phone = #{driverPhone,jdbcType=VARCHAR},
      driver_id = #{driverId,jdbcType=VARCHAR},
      account = #{account,jdbcType=VARCHAR},
      car_number = #{carNumber,jdbcType=VARCHAR},
      car_type = #{carType,jdbcType=VARCHAR},
      contract_no = #{contractNo,jdbcType=VARCHAR},
      begin_time = #{beginTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      biz_type = #{bizType,jdbcType=INTEGER},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into transfer_contract_status
    (order_id, vehicle_id, contract_id, status, post_code, message, error_msg, driver_name, 
      driver_phone, driver_id, account, car_number, car_type, contract_no, begin_time, 
      end_time, biz_type, merchant_id, last_ver, op_user_id, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=BIGINT}, #{item.vehicleId,jdbcType=BIGINT}, #{item.contractId,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=TINYINT}, #{item.postCode,jdbcType=VARCHAR}, #{item.message,jdbcType=VARCHAR}, 
        #{item.errorMsg,jdbcType=VARCHAR}, #{item.driverName,jdbcType=VARCHAR}, #{item.driverPhone,jdbcType=VARCHAR}, 
        #{item.driverId,jdbcType=VARCHAR}, #{item.account,jdbcType=VARCHAR}, #{item.carNumber,jdbcType=VARCHAR}, 
        #{item.carType,jdbcType=VARCHAR}, #{item.contractNo,jdbcType=VARCHAR}, #{item.beginTime,jdbcType=TIMESTAMP}, 
        #{item.endTime,jdbcType=TIMESTAMP}, #{item.bizType,jdbcType=INTEGER}, #{item.merchantId,jdbcType=BIGINT}, 
        #{item.lastVer,jdbcType=INTEGER}, #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into transfer_contract_status (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_id'.toString() == column.value">
          #{item.vehicleId,jdbcType=BIGINT}
        </if>
        <if test="'contract_id'.toString() == column.value">
          #{item.contractId,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'post_code'.toString() == column.value">
          #{item.postCode,jdbcType=VARCHAR}
        </if>
        <if test="'message'.toString() == column.value">
          #{item.message,jdbcType=VARCHAR}
        </if>
        <if test="'error_msg'.toString() == column.value">
          #{item.errorMsg,jdbcType=VARCHAR}
        </if>
        <if test="'driver_name'.toString() == column.value">
          #{item.driverName,jdbcType=VARCHAR}
        </if>
        <if test="'driver_phone'.toString() == column.value">
          #{item.driverPhone,jdbcType=VARCHAR}
        </if>
        <if test="'driver_id'.toString() == column.value">
          #{item.driverId,jdbcType=VARCHAR}
        </if>
        <if test="'account'.toString() == column.value">
          #{item.account,jdbcType=VARCHAR}
        </if>
        <if test="'car_number'.toString() == column.value">
          #{item.carNumber,jdbcType=VARCHAR}
        </if>
        <if test="'car_type'.toString() == column.value">
          #{item.carType,jdbcType=VARCHAR}
        </if>
        <if test="'contract_no'.toString() == column.value">
          #{item.contractNo,jdbcType=VARCHAR}
        </if>
        <if test="'begin_time'.toString() == column.value">
          #{item.beginTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'end_time'.toString() == column.value">
          #{item.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'biz_type'.toString() == column.value">
          #{item.bizType,jdbcType=INTEGER}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
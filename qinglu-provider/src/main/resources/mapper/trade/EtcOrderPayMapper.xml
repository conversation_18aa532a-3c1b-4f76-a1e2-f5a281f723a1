<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.EtcOrderPayMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.EtcOrderPay">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="pay_no" jdbcType="VARCHAR" property="payNo" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="pay_amt" jdbcType="BIGINT" property="payAmt" />
    <result column="actual_pay_amt" jdbcType="BIGINT" property="actualPayAmt" />
    <result column="pay_status" jdbcType="BIGINT" property="payStatus" />
    <result column="third_pay_no" jdbcType="VARCHAR" property="thirdPayNo" />
    <result column="third_pay_source" jdbcType="BIGINT" property="thirdPaySource" />
    <result column="third_fail_reason" jdbcType="VARCHAR" property="thirdFailReason" />
    <result column="deleted" jdbcType="BIGINT" property="deleted" />
    <result column="last_ver" jdbcType="BIGINT" property="lastVer" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, order_id, pay_no, pay_time, pay_amt, actual_pay_amt, pay_status, 
    third_pay_no, third_pay_source, third_fail_reason, deleted, last_ver, create_time, 
    op_user_id, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.EtcOrderPayExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from etc_order_pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from etc_order_pay
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.EtcOrderPay">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into etc_order_pay (merchant_id, order_id, pay_no, 
      pay_time, pay_amt, actual_pay_amt, 
      pay_status, third_pay_no, third_pay_source, 
      third_fail_reason, deleted, last_ver, 
      create_time, op_user_id, op_time
      )
    values (#{merchantId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{payNo,jdbcType=VARCHAR}, 
      #{payTime,jdbcType=TIMESTAMP}, #{payAmt,jdbcType=BIGINT}, #{actualPayAmt,jdbcType=BIGINT}, 
      #{payStatus,jdbcType=BIGINT}, #{thirdPayNo,jdbcType=VARCHAR}, #{thirdPaySource,jdbcType=BIGINT}, 
      #{thirdFailReason,jdbcType=VARCHAR}, #{deleted,jdbcType=BIGINT}, #{lastVer,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.EtcOrderPay">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into etc_order_pay
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="payNo != null">
        pay_no,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="payAmt != null">
        pay_amt,
      </if>
      <if test="actualPayAmt != null">
        actual_pay_amt,
      </if>
      <if test="payStatus != null">
        pay_status,
      </if>
      <if test="thirdPayNo != null">
        third_pay_no,
      </if>
      <if test="thirdPaySource != null">
        third_pay_source,
      </if>
      <if test="thirdFailReason != null">
        third_fail_reason,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="payNo != null">
        #{payNo,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payAmt != null">
        #{payAmt,jdbcType=BIGINT},
      </if>
      <if test="actualPayAmt != null">
        #{actualPayAmt,jdbcType=BIGINT},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=BIGINT},
      </if>
      <if test="thirdPayNo != null">
        #{thirdPayNo,jdbcType=VARCHAR},
      </if>
      <if test="thirdPaySource != null">
        #{thirdPaySource,jdbcType=BIGINT},
      </if>
      <if test="thirdFailReason != null">
        #{thirdFailReason,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.EtcOrderPayExample" resultType="java.lang.Long">
    select count(*) from etc_order_pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update etc_order_pay
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.payNo != null">
        pay_no = #{record.payNo,jdbcType=VARCHAR},
      </if>
      <if test="record.payTime != null">
        pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.payAmt != null">
        pay_amt = #{record.payAmt,jdbcType=BIGINT},
      </if>
      <if test="record.actualPayAmt != null">
        actual_pay_amt = #{record.actualPayAmt,jdbcType=BIGINT},
      </if>
      <if test="record.payStatus != null">
        pay_status = #{record.payStatus,jdbcType=BIGINT},
      </if>
      <if test="record.thirdPayNo != null">
        third_pay_no = #{record.thirdPayNo,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdPaySource != null">
        third_pay_source = #{record.thirdPaySource,jdbcType=BIGINT},
      </if>
      <if test="record.thirdFailReason != null">
        third_fail_reason = #{record.thirdFailReason,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIGINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update etc_order_pay
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      pay_no = #{record.payNo,jdbcType=VARCHAR},
      pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      pay_amt = #{record.payAmt,jdbcType=BIGINT},
      actual_pay_amt = #{record.actualPayAmt,jdbcType=BIGINT},
      pay_status = #{record.payStatus,jdbcType=BIGINT},
      third_pay_no = #{record.thirdPayNo,jdbcType=VARCHAR},
      third_pay_source = #{record.thirdPaySource,jdbcType=BIGINT},
      third_fail_reason = #{record.thirdFailReason,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=BIGINT},
      last_ver = #{record.lastVer,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.EtcOrderPay">
    update etc_order_pay
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="payNo != null">
        pay_no = #{payNo,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payAmt != null">
        pay_amt = #{payAmt,jdbcType=BIGINT},
      </if>
      <if test="actualPayAmt != null">
        actual_pay_amt = #{actualPayAmt,jdbcType=BIGINT},
      </if>
      <if test="payStatus != null">
        pay_status = #{payStatus,jdbcType=BIGINT},
      </if>
      <if test="thirdPayNo != null">
        third_pay_no = #{thirdPayNo,jdbcType=VARCHAR},
      </if>
      <if test="thirdPaySource != null">
        third_pay_source = #{thirdPaySource,jdbcType=BIGINT},
      </if>
      <if test="thirdFailReason != null">
        third_fail_reason = #{thirdFailReason,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.EtcOrderPay">
    update etc_order_pay
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      pay_no = #{payNo,jdbcType=VARCHAR},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      pay_amt = #{payAmt,jdbcType=BIGINT},
      actual_pay_amt = #{actualPayAmt,jdbcType=BIGINT},
      pay_status = #{payStatus,jdbcType=BIGINT},
      third_pay_no = #{thirdPayNo,jdbcType=VARCHAR},
      third_pay_source = #{thirdPaySource,jdbcType=BIGINT},
      third_fail_reason = #{thirdFailReason,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BIGINT},
      last_ver = #{lastVer,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into etc_order_pay
    (merchant_id, order_id, pay_no, pay_time, pay_amt, actual_pay_amt, pay_status, third_pay_no, 
      third_pay_source, third_fail_reason, deleted, last_ver, create_time, op_user_id, 
      op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.orderId,jdbcType=BIGINT}, #{item.payNo,jdbcType=VARCHAR}, 
        #{item.payTime,jdbcType=TIMESTAMP}, #{item.payAmt,jdbcType=BIGINT}, #{item.actualPayAmt,jdbcType=BIGINT}, 
        #{item.payStatus,jdbcType=BIGINT}, #{item.thirdPayNo,jdbcType=VARCHAR}, #{item.thirdPaySource,jdbcType=BIGINT}, 
        #{item.thirdFailReason,jdbcType=VARCHAR}, #{item.deleted,jdbcType=BIGINT}, #{item.lastVer,jdbcType=BIGINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into etc_order_pay (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'pay_no'.toString() == column.value">
          #{item.payNo,jdbcType=VARCHAR}
        </if>
        <if test="'pay_time'.toString() == column.value">
          #{item.payTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'pay_amt'.toString() == column.value">
          #{item.payAmt,jdbcType=BIGINT}
        </if>
        <if test="'actual_pay_amt'.toString() == column.value">
          #{item.actualPayAmt,jdbcType=BIGINT}
        </if>
        <if test="'pay_status'.toString() == column.value">
          #{item.payStatus,jdbcType=BIGINT}
        </if>
        <if test="'third_pay_no'.toString() == column.value">
          #{item.thirdPayNo,jdbcType=VARCHAR}
        </if>
        <if test="'third_pay_source'.toString() == column.value">
          #{item.thirdPaySource,jdbcType=BIGINT}
        </if>
        <if test="'third_fail_reason'.toString() == column.value">
          #{item.thirdFailReason,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=BIGINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
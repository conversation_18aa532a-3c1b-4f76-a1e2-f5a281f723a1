<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.EtcOrderTripMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.EtcOrderTrip">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="trip_id" jdbcType="VARCHAR" property="tripId" />
    <result column="trip_time" jdbcType="TIMESTAMP" property="tripTime" />
    <result column="trip_start_time" jdbcType="TIMESTAMP" property="tripStartTime" />
    <result column="trip_end_time" jdbcType="TIMESTAMP" property="tripEndTime" />
    <result column="start_station_name" jdbcType="VARCHAR" property="startStationName" />
    <result column="end_station_name" jdbcType="VARCHAR" property="endStationName" />
    <result column="sub_type" jdbcType="VARCHAR" property="subType" />
    <result column="sub_scene" jdbcType="VARCHAR" property="subScene" />
    <result column="trip_amt" jdbcType="BIGINT" property="tripAmt" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
    <result column="out_order_id" jdbcType="VARCHAR" property="outOrderId" />
    <result column="deleted" jdbcType="BIGINT" property="deleted" />
    <result column="last_ver" jdbcType="BIGINT" property="lastVer" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, order_id, trip_id, trip_time, trip_start_time, trip_end_time, start_station_name, 
    end_station_name, sub_type, sub_scene, trip_amt, trade_no, out_order_id, deleted, 
    last_ver, create_time, op_user_id, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.EtcOrderTripExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from etc_order_trip
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from etc_order_trip
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.EtcOrderTrip">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into etc_order_trip (merchant_id, order_id, trip_id, 
      trip_time, trip_start_time, trip_end_time, 
      start_station_name, end_station_name, sub_type, 
      sub_scene, trip_amt, trade_no, 
      out_order_id, deleted, last_ver, 
      create_time, op_user_id, op_time
      )
    values (#{merchantId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{tripId,jdbcType=VARCHAR}, 
      #{tripTime,jdbcType=TIMESTAMP}, #{tripStartTime,jdbcType=TIMESTAMP}, #{tripEndTime,jdbcType=TIMESTAMP}, 
      #{startStationName,jdbcType=VARCHAR}, #{endStationName,jdbcType=VARCHAR}, #{subType,jdbcType=VARCHAR}, 
      #{subScene,jdbcType=VARCHAR}, #{tripAmt,jdbcType=BIGINT}, #{tradeNo,jdbcType=VARCHAR}, 
      #{outOrderId,jdbcType=VARCHAR}, #{deleted,jdbcType=BIGINT}, #{lastVer,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.EtcOrderTrip">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into etc_order_trip
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="tripId != null">
        trip_id,
      </if>
      <if test="tripTime != null">
        trip_time,
      </if>
      <if test="tripStartTime != null">
        trip_start_time,
      </if>
      <if test="tripEndTime != null">
        trip_end_time,
      </if>
      <if test="startStationName != null">
        start_station_name,
      </if>
      <if test="endStationName != null">
        end_station_name,
      </if>
      <if test="subType != null">
        sub_type,
      </if>
      <if test="subScene != null">
        sub_scene,
      </if>
      <if test="tripAmt != null">
        trip_amt,
      </if>
      <if test="tradeNo != null">
        trade_no,
      </if>
      <if test="outOrderId != null">
        out_order_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="tripId != null">
        #{tripId,jdbcType=VARCHAR},
      </if>
      <if test="tripTime != null">
        #{tripTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tripStartTime != null">
        #{tripStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tripEndTime != null">
        #{tripEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startStationName != null">
        #{startStationName,jdbcType=VARCHAR},
      </if>
      <if test="endStationName != null">
        #{endStationName,jdbcType=VARCHAR},
      </if>
      <if test="subType != null">
        #{subType,jdbcType=VARCHAR},
      </if>
      <if test="subScene != null">
        #{subScene,jdbcType=VARCHAR},
      </if>
      <if test="tripAmt != null">
        #{tripAmt,jdbcType=BIGINT},
      </if>
      <if test="tradeNo != null">
        #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="outOrderId != null">
        #{outOrderId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.EtcOrderTripExample" resultType="java.lang.Long">
    select count(*) from etc_order_trip
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update etc_order_trip
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.tripId != null">
        trip_id = #{record.tripId,jdbcType=VARCHAR},
      </if>
      <if test="record.tripTime != null">
        trip_time = #{record.tripTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tripStartTime != null">
        trip_start_time = #{record.tripStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tripEndTime != null">
        trip_end_time = #{record.tripEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.startStationName != null">
        start_station_name = #{record.startStationName,jdbcType=VARCHAR},
      </if>
      <if test="record.endStationName != null">
        end_station_name = #{record.endStationName,jdbcType=VARCHAR},
      </if>
      <if test="record.subType != null">
        sub_type = #{record.subType,jdbcType=VARCHAR},
      </if>
      <if test="record.subScene != null">
        sub_scene = #{record.subScene,jdbcType=VARCHAR},
      </if>
      <if test="record.tripAmt != null">
        trip_amt = #{record.tripAmt,jdbcType=BIGINT},
      </if>
      <if test="record.tradeNo != null">
        trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="record.outOrderId != null">
        out_order_id = #{record.outOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIGINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update etc_order_trip
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      trip_id = #{record.tripId,jdbcType=VARCHAR},
      trip_time = #{record.tripTime,jdbcType=TIMESTAMP},
      trip_start_time = #{record.tripStartTime,jdbcType=TIMESTAMP},
      trip_end_time = #{record.tripEndTime,jdbcType=TIMESTAMP},
      start_station_name = #{record.startStationName,jdbcType=VARCHAR},
      end_station_name = #{record.endStationName,jdbcType=VARCHAR},
      sub_type = #{record.subType,jdbcType=VARCHAR},
      sub_scene = #{record.subScene,jdbcType=VARCHAR},
      trip_amt = #{record.tripAmt,jdbcType=BIGINT},
      trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      out_order_id = #{record.outOrderId,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=BIGINT},
      last_ver = #{record.lastVer,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.EtcOrderTrip">
    update etc_order_trip
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="tripId != null">
        trip_id = #{tripId,jdbcType=VARCHAR},
      </if>
      <if test="tripTime != null">
        trip_time = #{tripTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tripStartTime != null">
        trip_start_time = #{tripStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tripEndTime != null">
        trip_end_time = #{tripEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startStationName != null">
        start_station_name = #{startStationName,jdbcType=VARCHAR},
      </if>
      <if test="endStationName != null">
        end_station_name = #{endStationName,jdbcType=VARCHAR},
      </if>
      <if test="subType != null">
        sub_type = #{subType,jdbcType=VARCHAR},
      </if>
      <if test="subScene != null">
        sub_scene = #{subScene,jdbcType=VARCHAR},
      </if>
      <if test="tripAmt != null">
        trip_amt = #{tripAmt,jdbcType=BIGINT},
      </if>
      <if test="tradeNo != null">
        trade_no = #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="outOrderId != null">
        out_order_id = #{outOrderId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.EtcOrderTrip">
    update etc_order_trip
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      trip_id = #{tripId,jdbcType=VARCHAR},
      trip_time = #{tripTime,jdbcType=TIMESTAMP},
      trip_start_time = #{tripStartTime,jdbcType=TIMESTAMP},
      trip_end_time = #{tripEndTime,jdbcType=TIMESTAMP},
      start_station_name = #{startStationName,jdbcType=VARCHAR},
      end_station_name = #{endStationName,jdbcType=VARCHAR},
      sub_type = #{subType,jdbcType=VARCHAR},
      sub_scene = #{subScene,jdbcType=VARCHAR},
      trip_amt = #{tripAmt,jdbcType=BIGINT},
      trade_no = #{tradeNo,jdbcType=VARCHAR},
      out_order_id = #{outOrderId,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BIGINT},
      last_ver = #{lastVer,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into etc_order_trip
    (merchant_id, order_id, trip_id, trip_time, trip_start_time, trip_end_time, start_station_name, 
      end_station_name, sub_type, sub_scene, trip_amt, trade_no, out_order_id, deleted, 
      last_ver, create_time, op_user_id, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.orderId,jdbcType=BIGINT}, #{item.tripId,jdbcType=VARCHAR}, 
        #{item.tripTime,jdbcType=TIMESTAMP}, #{item.tripStartTime,jdbcType=TIMESTAMP}, 
        #{item.tripEndTime,jdbcType=TIMESTAMP}, #{item.startStationName,jdbcType=VARCHAR}, 
        #{item.endStationName,jdbcType=VARCHAR}, #{item.subType,jdbcType=VARCHAR}, #{item.subScene,jdbcType=VARCHAR}, 
        #{item.tripAmt,jdbcType=BIGINT}, #{item.tradeNo,jdbcType=VARCHAR}, #{item.outOrderId,jdbcType=VARCHAR}, 
        #{item.deleted,jdbcType=BIGINT}, #{item.lastVer,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opUserId,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into etc_order_trip (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'trip_id'.toString() == column.value">
          #{item.tripId,jdbcType=VARCHAR}
        </if>
        <if test="'trip_time'.toString() == column.value">
          #{item.tripTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'trip_start_time'.toString() == column.value">
          #{item.tripStartTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'trip_end_time'.toString() == column.value">
          #{item.tripEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'start_station_name'.toString() == column.value">
          #{item.startStationName,jdbcType=VARCHAR}
        </if>
        <if test="'end_station_name'.toString() == column.value">
          #{item.endStationName,jdbcType=VARCHAR}
        </if>
        <if test="'sub_type'.toString() == column.value">
          #{item.subType,jdbcType=VARCHAR}
        </if>
        <if test="'sub_scene'.toString() == column.value">
          #{item.subScene,jdbcType=VARCHAR}
        </if>
        <if test="'trip_amt'.toString() == column.value">
          #{item.tripAmt,jdbcType=BIGINT}
        </if>
        <if test="'trade_no'.toString() == column.value">
          #{item.tradeNo,jdbcType=VARCHAR}
        </if>
        <if test="'out_order_id'.toString() == column.value">
          #{item.outOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=BIGINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
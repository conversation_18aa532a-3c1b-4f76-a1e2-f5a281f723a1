<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.MallServiceItemMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.MallServiceItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="item_type" jdbcType="TINYINT" property="itemType" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="item_price" jdbcType="BIGINT" property="itemPrice" />
    <result column="item_count" jdbcType="INTEGER" property="itemCount" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="msg_content" jdbcType="VARCHAR" property="msgContent" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="deposit" jdbcType="INTEGER" property="deposit" />
    <result column="item_sub_package" jdbcType="VARCHAR" property="itemSubPackage" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, item_type, item_name, item_code, item_price, item_count, duration, msg_content, 
    deleted, create_time, op_time, op_user_id, deposit, item_sub_package
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.MallServiceItemExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from mall_service_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mall_service_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mall_service_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.MallServiceItemExample">
    delete from mall_service_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.MallServiceItem">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mall_service_item (item_type, item_name, item_code, 
      item_price, item_count, duration, 
      msg_content, deleted, create_time, 
      op_time, op_user_id, deposit, 
      item_sub_package)
    values (#{itemType,jdbcType=TINYINT}, #{itemName,jdbcType=VARCHAR}, #{itemCode,jdbcType=VARCHAR}, 
      #{itemPrice,jdbcType=BIGINT}, #{itemCount,jdbcType=INTEGER}, #{duration,jdbcType=INTEGER}, 
      #{msgContent,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}, #{deposit,jdbcType=INTEGER}, 
      #{itemSubPackage,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.MallServiceItem">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mall_service_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="itemType != null">
        item_type,
      </if>
      <if test="itemName != null">
        item_name,
      </if>
      <if test="itemCode != null">
        item_code,
      </if>
      <if test="itemPrice != null">
        item_price,
      </if>
      <if test="itemCount != null">
        item_count,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="msgContent != null">
        msg_content,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="deposit != null">
        deposit,
      </if>
      <if test="itemSubPackage != null">
        item_sub_package,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="itemType != null">
        #{itemType,jdbcType=TINYINT},
      </if>
      <if test="itemName != null">
        #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="itemPrice != null">
        #{itemPrice,jdbcType=BIGINT},
      </if>
      <if test="itemCount != null">
        #{itemCount,jdbcType=INTEGER},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="msgContent != null">
        #{msgContent,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="deposit != null">
        #{deposit,jdbcType=INTEGER},
      </if>
      <if test="itemSubPackage != null">
        #{itemSubPackage,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.MallServiceItemExample" resultType="java.lang.Long">
    select count(*) from mall_service_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mall_service_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.itemType != null">
        item_type = #{record.itemType,jdbcType=TINYINT},
      </if>
      <if test="record.itemName != null">
        item_name = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.itemCode != null">
        item_code = #{record.itemCode,jdbcType=VARCHAR},
      </if>
      <if test="record.itemPrice != null">
        item_price = #{record.itemPrice,jdbcType=BIGINT},
      </if>
      <if test="record.itemCount != null">
        item_count = #{record.itemCount,jdbcType=INTEGER},
      </if>
      <if test="record.duration != null">
        duration = #{record.duration,jdbcType=INTEGER},
      </if>
      <if test="record.msgContent != null">
        msg_content = #{record.msgContent,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.deposit != null">
        deposit = #{record.deposit,jdbcType=INTEGER},
      </if>
      <if test="record.itemSubPackage != null">
        item_sub_package = #{record.itemSubPackage,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mall_service_item
    set id = #{record.id,jdbcType=BIGINT},
      item_type = #{record.itemType,jdbcType=TINYINT},
      item_name = #{record.itemName,jdbcType=VARCHAR},
      item_code = #{record.itemCode,jdbcType=VARCHAR},
      item_price = #{record.itemPrice,jdbcType=BIGINT},
      item_count = #{record.itemCount,jdbcType=INTEGER},
      duration = #{record.duration,jdbcType=INTEGER},
      msg_content = #{record.msgContent,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      deposit = #{record.deposit,jdbcType=INTEGER},
      item_sub_package = #{record.itemSubPackage,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.MallServiceItem">
    update mall_service_item
    <set>
      <if test="itemType != null">
        item_type = #{itemType,jdbcType=TINYINT},
      </if>
      <if test="itemName != null">
        item_name = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        item_code = #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="itemPrice != null">
        item_price = #{itemPrice,jdbcType=BIGINT},
      </if>
      <if test="itemCount != null">
        item_count = #{itemCount,jdbcType=INTEGER},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=INTEGER},
      </if>
      <if test="msgContent != null">
        msg_content = #{msgContent,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="deposit != null">
        deposit = #{deposit,jdbcType=INTEGER},
      </if>
      <if test="itemSubPackage != null">
        item_sub_package = #{itemSubPackage,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.MallServiceItem">
    update mall_service_item
    set item_type = #{itemType,jdbcType=TINYINT},
      item_name = #{itemName,jdbcType=VARCHAR},
      item_code = #{itemCode,jdbcType=VARCHAR},
      item_price = #{itemPrice,jdbcType=BIGINT},
      item_count = #{itemCount,jdbcType=INTEGER},
      duration = #{duration,jdbcType=INTEGER},
      msg_content = #{msgContent,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      deposit = #{deposit,jdbcType=INTEGER},
      item_sub_package = #{itemSubPackage,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into mall_service_item
    (item_type, item_name, item_code, item_price, item_count, duration, msg_content, 
      deleted, create_time, op_time, op_user_id, deposit, item_sub_package)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.itemType,jdbcType=TINYINT}, #{item.itemName,jdbcType=VARCHAR}, #{item.itemCode,jdbcType=VARCHAR}, 
        #{item.itemPrice,jdbcType=BIGINT}, #{item.itemCount,jdbcType=INTEGER}, #{item.duration,jdbcType=INTEGER}, 
        #{item.msgContent,jdbcType=VARCHAR}, #{item.deleted,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT}, #{item.deposit,jdbcType=INTEGER}, 
        #{item.itemSubPackage,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into mall_service_item (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'item_type'.toString() == column.value">
          #{item.itemType,jdbcType=TINYINT}
        </if>
        <if test="'item_name'.toString() == column.value">
          #{item.itemName,jdbcType=VARCHAR}
        </if>
        <if test="'item_code'.toString() == column.value">
          #{item.itemCode,jdbcType=VARCHAR}
        </if>
        <if test="'item_price'.toString() == column.value">
          #{item.itemPrice,jdbcType=BIGINT}
        </if>
        <if test="'item_count'.toString() == column.value">
          #{item.itemCount,jdbcType=INTEGER}
        </if>
        <if test="'duration'.toString() == column.value">
          #{item.duration,jdbcType=INTEGER}
        </if>
        <if test="'msg_content'.toString() == column.value">
          #{item.msgContent,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'deposit'.toString() == column.value">
          #{item.deposit,jdbcType=INTEGER}
        </if>
        <if test="'item_sub_package'.toString() == column.value">
          #{item.itemSubPackage,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.VehicleOrderStaticInfoMapper">
    <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.VehicleOrderStaticInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="merchant_id" jdbcType="BIGINT" property="merchantId"/>
        <result column="record_date" jdbcType="TIMESTAMP" property="recordDate"/>
        <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId"/>
        <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="purchase_way" jdbcType="TINYINT" property="purchaseWay"/>
        <result column="return_date" jdbcType="TIMESTAMP" property="returnDate"/>
        <result column="last_return_date" jdbcType="TIMESTAMP" property="lastReturnDate"/>
        <result column="pay_amount" jdbcType="INTEGER" property="payAmount"/>
        <result column="receivable_amount" jdbcType="INTEGER" property="receivableAmount"/>
        <result column="order_source" jdbcType="TINYINT" property="orderSource"/>
        <result column="vehicle_source" jdbcType="TINYINT" property="vehicleSource"/>
        <result column="order_status" jdbcType="TINYINT" property="orderStatus"/>
        <result column="last_ver" jdbcType="INTEGER" property="lastVer"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="op_user_id" jdbcType="BIGINT" property="opUserId"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="op_time" jdbcType="BIGINT" property="opTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, merchant_id, record_date, vehicle_id, vehicle_no, order_no,order_id, store_id,purchase_way, return_date,
    last_return_date, pay_amount, receivable_amount, order_source, vehicle_source, order_status, 
    last_ver, deleted, op_user_id, create_time, op_time
    </sql>
    <insert id="insert" parameterType="com.ql.rent.entity.trade.VehicleOrderStaticInfo">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into vehicle_order_static_info (merchant_id, record_date, vehicle_id,
        vehicle_no, order_no,order_id, store_id,purchase_way,
        return_date, last_return_date, pay_amount,
        receivable_amount, order_source, vehicle_source,
        order_status, last_ver, deleted,
        op_user_id, create_time, op_time
        )
        values (#{merchantId,jdbcType=BIGINT}, #{recordDate,jdbcType=TIMESTAMP}, #{vehicleId,jdbcType=BIGINT},
        #{vehicleNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR},#{orderId,jdbcType=BIGINT},
        #{storeId,jdbcType=BIGINT},
        #{purchase_way},
        #{returnDate,jdbcType=TIMESTAMP}, #{lastReturnDate,jdbcType=TIMESTAMP}, #{payAmount,jdbcType=INTEGER},
        #{receivableAmount,jdbcType=INTEGER}, #{orderSource,jdbcType=TINYINT}, #{vehicleSource,jdbcType=TINYINT},
        #{orderStatus,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT},
        #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}
        )
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.VehicleOrderStaticInfo">
        update vehicle_order_static_info
        <set>
            <if test="merchantId != null">
                merchant_id = #{merchantId,jdbcType=BIGINT},
            </if>
            <if test="recordDate != null">
                record_date = #{recordDate,jdbcType=TIMESTAMP},
            </if>
            <if test="vehicleId != null">
                vehicle_id = #{vehicleId,jdbcType=BIGINT},
            </if>
            <if test="vehicleNo != null">
                vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="storeId != null">
                store_id = #{storeId,jdbcType=BIGINT},
            </if>
            <if test="returnDate != null">
                return_date = #{returnDate,jdbcType=TIMESTAMP},
            </if>
            <if test="lastReturnDate != null">
                last_return_date = #{lastReturnDate,jdbcType=TIMESTAMP},
            </if>
            <if test="payAmount != null">
                pay_amount = #{payAmount,jdbcType=INTEGER},
            </if>
            <if test="receivableAmount != null">
                receivable_amount = #{receivableAmount,jdbcType=INTEGER},
            </if>
            <if test="orderSource != null">
                order_source = #{orderSource,jdbcType=TINYINT},
            </if>
            <if test="vehicleSource != null">
                vehicle_source = #{vehicleSource,jdbcType=TINYINT},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=TINYINT},
            </if>
            <if test="lastVer != null">
                last_ver = #{lastVer,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="opUserId != null">
                op_user_id = #{opUserId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="opTime != null">
                op_time = #{opTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.VehicleOrderStaticInfo">
        update vehicle_order_static_info
        set merchant_id       = #{merchantId,jdbcType=BIGINT},
            record_date       = #{recordDate,jdbcType=TIMESTAMP},
            vehicle_id        = #{vehicleId,jdbcType=BIGINT},
            vehicle_no        = #{vehicleNo,jdbcType=VARCHAR},
            order_no          = #{orderNo,jdbcType=VARCHAR},
            store_id          = #{storeId,jdbcType=BIGINT},
            order_id          = #{orderId,jdbcType=BIGINT},
            return_date       = #{returnDate,jdbcType=TIMESTAMP},
            last_return_date  = #{lastReturnDate,jdbcType=TIMESTAMP},
            pay_amount        = #{payAmount,jdbcType=INTEGER},
            receivable_amount = #{receivableAmount,jdbcType=INTEGER},
            order_source      = #{orderSource,jdbcType=TINYINT},
            vehicle_source    = #{vehicleSource,jdbcType=TINYINT},
            order_status      = #{orderStatus,jdbcType=TINYINT},
            last_ver          = #{lastVer,jdbcType=INTEGER},
            deleted           = #{deleted,jdbcType=TINYINT},
            op_user_id        = #{opUserId,jdbcType=BIGINT},
            create_time       = #{createTime,jdbcType=BIGINT},
            op_time           = #{opTime,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into vehicle_order_static_info
        (merchant_id, record_date, vehicle_id, vehicle_no, order_no,order_id, store_id,purchase_way, return_date,
        last_return_date, pay_amount, receivable_amount, order_source, vehicle_source,
        order_status, last_ver, deleted, op_user_id, create_time, op_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.merchantId,jdbcType=BIGINT}, #{item.recordDate,jdbcType=TIMESTAMP},
            #{item.vehicleId,jdbcType=BIGINT},
            #{item.vehicleNo,jdbcType=VARCHAR}, #{item.orderNo,jdbcType=VARCHAR},
            #{item.orderId,jdbcType=BIGINT},#{item.storeId,jdbcType=BIGINT},#{item.purchaseWay,jdbcType=TINYINT},
            #{item.returnDate,jdbcType=TIMESTAMP}, #{item.lastReturnDate,jdbcType=TIMESTAMP},
            #{item.payAmount,jdbcType=INTEGER}, #{item.receivableAmount,jdbcType=INTEGER},
            #{item.orderSource,jdbcType=TINYINT}, #{item.vehicleSource,jdbcType=TINYINT},
            #{item.orderStatus,jdbcType=TINYINT},
            #{item.lastVer,jdbcType=INTEGER}, #{item.deleted,jdbcType=TINYINT}, #{item.opUserId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT})
        </foreach>
    </insert>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from vehicle_order_static_info
        where id = #{id,jdbcType=BIGINT}
    </select>


    <select id="queryVehicleReport" resultType="com.ql.rent.vo.trade.VehicleOrderSummaryReportVO">
        SELECT
        COUNT(*) AS orderCount,
        ROUND(SUM(CAST(pay_amount AS DECIMAL(18,2)) / 100), 2) AS totalPayAmount,
        ROUND(SUM(CAST(receivable_amount AS DECIMAL(18,2)) / 100), 2) AS totalReceivableAmount
        FROM vehicle_order_static_info
        WHERE
        merchant_id = #{query.merchantId} and deleted = 0
        <if test="query.vehicleNo != null">
            and vehicle_no = #{query.vehicleNo}
        </if>
        <if test="query.vehicleSourceList!=null and query.vehicleSourceList.size() > 0 ">
            and vehicle_source in
            <foreach item="item" collection="query.vehicleSourceList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.purchaseWayList!=null and query.purchaseWayList.size() > 0 ">
            and purchase_way in
            <foreach item="item" collection="query.purchaseWayList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.startDate != null">
            AND record_date >= #{query.startDate}
        </if>
        <if test="query.endDate != null">
            <![CDATA[
            AND record_date <= #{query.endDate}
           ]]>
        </if>
    </select>

    <select id="queryByOrderNo" resultMap="BaseResultMap">
        select *
        from vehicle_order_static_info
        where order_no = #{orderNo}
          and deleted = 0
    </select>

    <update id="batchUpdate" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <foreach collection="list" item="item" separator=";">
            update vehicle_order_static_info
            <set>
                <if test="item.merchantId != null">
                    merchant_id = #{item.merchantId,jdbcType=BIGINT},
                </if>
                <if test="item.recordDate != null">
                    record_date = #{item.recordDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.storeId != null">
                    store_id = #{item.storeId,jdbcType=TIMESTAMP},
                </if>
                <if test="item.orderId != null">
                    order_id = #{item.orderId,jdbcType=TIMESTAMP},
                </if>
                <if test="item.vehicleId != null">
                    vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleNo != null">
                    vehicle_no = #{item.vehicleNo,jdbcType=VARCHAR},
                </if>
                <if test="item.orderNo != null">
                    order_no = #{item.orderNo,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseWay != null">
                    purchase_way = #{item.purchaseWay,jdbcType=TINYINT},
                </if>
                <if test="item.returnDate != null">
                    return_date = #{item.returnDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastReturnDate != null">
                    last_return_date = #{item.lastReturnDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.payAmount != null">
                    pay_amount = #{item.payAmount,jdbcType=INTEGER},
                </if>
                <if test="item.receivableAmount != null">
                    receivable_amount = #{item.receivableAmount,jdbcType=INTEGER},
                </if>
                <if test="item.orderSource != null">
                    order_source = #{item.orderSource,jdbcType=TINYINT},
                </if>
                <if test="item.vehicleSource != null">
                    vehicle_source = #{item.vehicleSource,jdbcType=TINYINT},
                </if>
                <if test="item.orderStatus != null">
                    order_status = #{item.orderStatus,jdbcType=TINYINT},
                </if>
                <if test="item.lastVer != null">
                    last_ver = #{item.lastVer,jdbcType=INTEGER},
                </if>
                <if test="item.opUserId != null">
                    op_user_id = #{item.opUserId,jdbcType=BIGINT},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=BIGINT},
                </if>
                <if test="item.opTime != null">
                    op_time = #{item.opTime,jdbcType=BIGINT},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="queryByCondition" resultType="com.ql.rent.vo.trade.VehicleOrderBillDetailVO">
        SELECT vehicle_no AS vehicleNo,
<!--        vehicle_source AS vehicleSource,-->
        COUNT(*) AS orderCount,
        SUM(pay_amount ) as totalPayAmount,
        SUM(receivable_amount ) AS totalReceivableAmount
        FROM vehicle_order_static_info
        WHERE
        deleted = 0 and merchant_id = #{query.merchantId}
        <if test="query.vehicleNo != null">
            and vehicle_no = #{query.vehicleNo}
        </if>
        <if test="query.startDate != null">
            and record_date &gt;= #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and record_date <![CDATA[<= #{query.endDate}
        ]]>
        </if>
        <if test="query.vehicleSourceList!=null and query.vehicleSourceList.size() > 0 ">
            and vehicle_source in
            <foreach item="item" collection="query.vehicleSourceList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.purchaseWayList!=null and query.purchaseWayList.size() > 0 ">
            and purchase_way in
            <foreach item="item" collection="query.purchaseWayList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        GROUP BY vehicle_no
        <if test="query.pageIndex!=null and query.pageSize!=null ">
            limit #{query.startPos},#{query.pageSize}
        </if>
    </select>

    <select id="countByCondition" resultType="java.lang.Long">
        SELECT count(distinct (vehicle_no))
        FROM vehicle_order_static_info
        WHERE
        deleted = 0 and merchant_id = #{query.merchantId}
        <if test="query.vehicleNo != null">
            and vehicle_no = #{query.vehicleNo}
        </if>
        <if test="query.startDate != null">
            and record_date &gt;= #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and record_date <![CDATA[<= #{query.endDate}
        ]]>
        </if>
        <if test="query.vehicleSourceList!=null and query.vehicleSourceList.size() > 0 ">
            and vehicle_source in
            <foreach item="item" collection="query.vehicleSourceList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.purchaseWayList!=null and query.purchaseWayList.size() > 0 ">
            and purchase_way in
            <foreach item="item" collection="query.purchaseWayList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryByVehicleNo" resultMap="BaseResultMap">
        select *
        from vehicle_order_static_info
        where
        deleted = 0 and merchant_id = #{query.merchantId}
        <if test="query.vehicleNo != null">
            and vehicle_no = #{query.vehicleNo}
        </if>
        <if test="query.startDate != null">
            and record_date &gt;= #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and record_date <![CDATA[<= #{query.endDate}
        ]]>
        </if>
        <if test="query.vehicleSourceList!=null and query.vehicleSourceList.size() > 0 ">
            and vehicle_source in
            <foreach item="item" collection="query.vehicleSourceList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.purchaseWayList!=null and query.purchaseWayList.size() > 0 ">
            and purchase_way in
            <foreach item="item" collection="query.purchaseWayList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="query.pageIndex!=null and query.pageSize!=null ">
            limit #{query.startPos},#{query.pageSize}
        </if>
    </select>

    <select id="countByVehicleNo" resultType="java.lang.Integer">
        select count(*)
        from vehicle_order_static_info
        where
        deleted = 0 and merchant_id = #{query.merchantId}
        <if test="query.vehicleNo != null">
            and vehicle_no = #{query.vehicleNo}
        </if>
        <if test="query.startDate != null">
            and record_date &gt;= #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and record_date <![CDATA[<= #{query.endDate}
        ]]>
        </if>
    </select>

    <select id="pullDataByDate" resultMap="BaseResultMap">
        select *
        from vehicle_order_static_info
        where
        deleted = 0 and merchant_id = #{query.merchantId}
        <if test="query.startDate != null">
            and record_date &gt;= #{query.startDate}
        </if>
        <if test="query.endDate != null">
            and record_date <![CDATA[<= #{query.endDate}
        ]]>
        </if>
    </select>
</mapper>
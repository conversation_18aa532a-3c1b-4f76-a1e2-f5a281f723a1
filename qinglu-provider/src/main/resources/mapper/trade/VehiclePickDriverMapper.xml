<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.VehiclePickDriverMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.VehiclePickDriver">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="driver_type" jdbcType="TINYINT" property="driverType" />
    <result column="execpt_pr_time" jdbcType="TIMESTAMP" property="execptPrTime" />
    <result column="task_time" jdbcType="TIMESTAMP" property="taskTime" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="driver_user_id" jdbcType="BIGINT" property="driverUserId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="task_type_id" jdbcType="BIGINT" property="taskTypeId" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, driver_type, execpt_pr_time, task_time, store_id, driver_user_id, driver_name, 
    task_type_id, last_ver, deleted, op_user_id, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.VehiclePickDriverExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_pick_driver
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_pick_driver
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.VehiclePickDriver">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_pick_driver
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="driverType != null">
        driver_type,
      </if>
      <if test="execptPrTime != null">
        execpt_pr_time,
      </if>
      <if test="taskTime != null">
        task_time,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="driverUserId != null">
        driver_user_id,
      </if>
      <if test="driverName != null">
        driver_name,
      </if>
      <if test="taskTypeId != null">
        task_type_id,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="driverType != null">
        #{driverType,jdbcType=TINYINT},
      </if>
      <if test="execptPrTime != null">
        #{execptPrTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskTime != null">
        #{taskTime,jdbcType=TIMESTAMP},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="driverUserId != null">
        #{driverUserId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null">
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="taskTypeId != null">
        #{taskTypeId,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.VehiclePickDriverExample" resultType="java.lang.Long">
    select count(*) from vehicle_pick_driver
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_pick_driver
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.driverType != null">
        driver_type = #{record.driverType,jdbcType=TINYINT},
      </if>
      <if test="record.execptPrTime != null">
        execpt_pr_time = #{record.execptPrTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taskTime != null">
        task_time = #{record.taskTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.driverUserId != null">
        driver_user_id = #{record.driverUserId,jdbcType=BIGINT},
      </if>
      <if test="record.driverName != null">
        driver_name = #{record.driverName,jdbcType=VARCHAR},
      </if>
      <if test="record.taskTypeId != null">
        task_type_id = #{record.taskTypeId,jdbcType=BIGINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_pick_driver
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      driver_type = #{record.driverType,jdbcType=TINYINT},
      execpt_pr_time = #{record.execptPrTime,jdbcType=TIMESTAMP},
      task_time = #{record.taskTime,jdbcType=TIMESTAMP},
      store_id = #{record.storeId,jdbcType=BIGINT},
      driver_user_id = #{record.driverUserId,jdbcType=BIGINT},
      driver_name = #{record.driverName,jdbcType=VARCHAR},
      task_type_id = #{record.taskTypeId,jdbcType=BIGINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=TINYINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.VehiclePickDriver">
    update vehicle_pick_driver
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="driverType != null">
        driver_type = #{driverType,jdbcType=TINYINT},
      </if>
      <if test="execptPrTime != null">
        execpt_pr_time = #{execptPrTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskTime != null">
        task_time = #{taskTime,jdbcType=TIMESTAMP},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="driverUserId != null">
        driver_user_id = #{driverUserId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null">
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="taskTypeId != null">
        task_type_id = #{taskTypeId,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.VehiclePickDriver">
    update vehicle_pick_driver
    set order_id = #{orderId,jdbcType=BIGINT},
      driver_type = #{driverType,jdbcType=TINYINT},
      execpt_pr_time = #{execptPrTime,jdbcType=TIMESTAMP},
      task_time = #{taskTime,jdbcType=TIMESTAMP},
      store_id = #{storeId,jdbcType=BIGINT},
      driver_user_id = #{driverUserId,jdbcType=BIGINT},
      driver_name = #{driverName,jdbcType=VARCHAR},
      task_type_id = #{taskTypeId,jdbcType=BIGINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
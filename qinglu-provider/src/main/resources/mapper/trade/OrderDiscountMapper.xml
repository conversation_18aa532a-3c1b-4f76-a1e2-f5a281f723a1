<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.OrderDiscountMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.OrderDiscount">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="parent_order_id" jdbcType="BIGINT" property="parentOrderId" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="discount_id" jdbcType="BIGINT" property="discountId" />
    <result column="discount_type" jdbcType="TINYINT" property="discountType" />
    <result column="discount_kind" jdbcType="TINYINT" property="discountKind" />
    <result column="discount_amount" jdbcType="INTEGER" property="discountAmount" />
    <result column="merchant_assume_amount" jdbcType="INTEGER" property="merchantAssumeAmount" />
    <result column="third_assume_amount" jdbcType="INTEGER" property="thirdAssumeAmount" />
    <result column="third_code" jdbcType="VARCHAR" property="thirdCode" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, order_id, parent_order_id, order_type, discount_id, discount_type, 
    discount_kind, discount_amount, merchant_assume_amount, third_assume_amount, third_code, 
    last_ver, op_user_id, create_time, op_time, extra
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.OrderDiscountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from order_discount
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_discount
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_discount
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.OrderDiscountExample">
    delete from order_discount
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.OrderDiscount">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_discount (merchant_id, order_id, parent_order_id, 
      order_type, discount_id, discount_type, 
      discount_kind, discount_amount, merchant_assume_amount, 
      third_assume_amount, third_code, last_ver, 
      op_user_id, create_time, op_time, 
      extra)
    values (#{merchantId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{parentOrderId,jdbcType=BIGINT}, 
      #{orderType,jdbcType=TINYINT}, #{discountId,jdbcType=BIGINT}, #{discountType,jdbcType=TINYINT}, 
      #{discountKind,jdbcType=TINYINT}, #{discountAmount,jdbcType=INTEGER}, #{merchantAssumeAmount,jdbcType=INTEGER}, 
      #{thirdAssumeAmount,jdbcType=INTEGER}, #{thirdCode,jdbcType=VARCHAR}, #{lastVer,jdbcType=INTEGER}, 
      #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, 
      #{extra,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.OrderDiscount">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_discount
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="parentOrderId != null">
        parent_order_id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="discountId != null">
        discount_id,
      </if>
      <if test="discountType != null">
        discount_type,
      </if>
      <if test="discountKind != null">
        discount_kind,
      </if>
      <if test="discountAmount != null">
        discount_amount,
      </if>
      <if test="merchantAssumeAmount != null">
        merchant_assume_amount,
      </if>
      <if test="thirdAssumeAmount != null">
        third_assume_amount,
      </if>
      <if test="thirdCode != null">
        third_code,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="parentOrderId != null">
        #{parentOrderId,jdbcType=BIGINT},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="discountId != null">
        #{discountId,jdbcType=BIGINT},
      </if>
      <if test="discountType != null">
        #{discountType,jdbcType=TINYINT},
      </if>
      <if test="discountKind != null">
        #{discountKind,jdbcType=TINYINT},
      </if>
      <if test="discountAmount != null">
        #{discountAmount,jdbcType=INTEGER},
      </if>
      <if test="merchantAssumeAmount != null">
        #{merchantAssumeAmount,jdbcType=INTEGER},
      </if>
      <if test="thirdAssumeAmount != null">
        #{thirdAssumeAmount,jdbcType=INTEGER},
      </if>
      <if test="thirdCode != null">
        #{thirdCode,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.OrderDiscountExample" resultType="java.lang.Long">
    select count(*) from order_discount
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_discount
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.parentOrderId != null">
        parent_order_id = #{record.parentOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=TINYINT},
      </if>
      <if test="record.discountId != null">
        discount_id = #{record.discountId,jdbcType=BIGINT},
      </if>
      <if test="record.discountType != null">
        discount_type = #{record.discountType,jdbcType=TINYINT},
      </if>
      <if test="record.discountKind != null">
        discount_kind = #{record.discountKind,jdbcType=TINYINT},
      </if>
      <if test="record.discountAmount != null">
        discount_amount = #{record.discountAmount,jdbcType=INTEGER},
      </if>
      <if test="record.merchantAssumeAmount != null">
        merchant_assume_amount = #{record.merchantAssumeAmount,jdbcType=INTEGER},
      </if>
      <if test="record.thirdAssumeAmount != null">
        third_assume_amount = #{record.thirdAssumeAmount,jdbcType=INTEGER},
      </if>
      <if test="record.thirdCode != null">
        third_code = #{record.thirdCode,jdbcType=VARCHAR},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_discount
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      parent_order_id = #{record.parentOrderId,jdbcType=BIGINT},
      order_type = #{record.orderType,jdbcType=TINYINT},
      discount_id = #{record.discountId,jdbcType=BIGINT},
      discount_type = #{record.discountType,jdbcType=TINYINT},
      discount_kind = #{record.discountKind,jdbcType=TINYINT},
      discount_amount = #{record.discountAmount,jdbcType=INTEGER},
      merchant_assume_amount = #{record.merchantAssumeAmount,jdbcType=INTEGER},
      third_assume_amount = #{record.thirdAssumeAmount,jdbcType=INTEGER},
      third_code = #{record.thirdCode,jdbcType=VARCHAR},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      extra = #{record.extra,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.OrderDiscount">
    update order_discount
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="parentOrderId != null">
        parent_order_id = #{parentOrderId,jdbcType=BIGINT},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="discountId != null">
        discount_id = #{discountId,jdbcType=BIGINT},
      </if>
      <if test="discountType != null">
        discount_type = #{discountType,jdbcType=TINYINT},
      </if>
      <if test="discountKind != null">
        discount_kind = #{discountKind,jdbcType=TINYINT},
      </if>
      <if test="discountAmount != null">
        discount_amount = #{discountAmount,jdbcType=INTEGER},
      </if>
      <if test="merchantAssumeAmount != null">
        merchant_assume_amount = #{merchantAssumeAmount,jdbcType=INTEGER},
      </if>
      <if test="thirdAssumeAmount != null">
        third_assume_amount = #{thirdAssumeAmount,jdbcType=INTEGER},
      </if>
      <if test="thirdCode != null">
        third_code = #{thirdCode,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.OrderDiscount">
    update order_discount
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      parent_order_id = #{parentOrderId,jdbcType=BIGINT},
      order_type = #{orderType,jdbcType=TINYINT},
      discount_id = #{discountId,jdbcType=BIGINT},
      discount_type = #{discountType,jdbcType=TINYINT},
      discount_kind = #{discountKind,jdbcType=TINYINT},
      discount_amount = #{discountAmount,jdbcType=INTEGER},
      merchant_assume_amount = #{merchantAssumeAmount,jdbcType=INTEGER},
      third_assume_amount = #{thirdAssumeAmount,jdbcType=INTEGER},
      third_code = #{thirdCode,jdbcType=VARCHAR},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      extra = #{extra,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_discount
    (merchant_id, order_id, parent_order_id, order_type, discount_id, discount_type, 
      discount_kind, discount_amount, merchant_assume_amount, third_assume_amount, third_code, 
      last_ver, op_user_id, create_time, op_time, extra)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.orderId,jdbcType=BIGINT}, #{item.parentOrderId,jdbcType=BIGINT}, 
        #{item.orderType,jdbcType=TINYINT}, #{item.discountId,jdbcType=BIGINT}, #{item.discountType,jdbcType=TINYINT}, 
        #{item.discountKind,jdbcType=TINYINT}, #{item.discountAmount,jdbcType=INTEGER}, 
        #{item.merchantAssumeAmount,jdbcType=INTEGER}, #{item.thirdAssumeAmount,jdbcType=INTEGER}, 
        #{item.thirdCode,jdbcType=VARCHAR}, #{item.lastVer,jdbcType=INTEGER}, #{item.opUserId,jdbcType=BIGINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, #{item.extra,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_discount (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'parent_order_id'.toString() == column.value">
          #{item.parentOrderId,jdbcType=BIGINT}
        </if>
        <if test="'order_type'.toString() == column.value">
          #{item.orderType,jdbcType=TINYINT}
        </if>
        <if test="'discount_id'.toString() == column.value">
          #{item.discountId,jdbcType=BIGINT}
        </if>
        <if test="'discount_type'.toString() == column.value">
          #{item.discountType,jdbcType=TINYINT}
        </if>
        <if test="'discount_kind'.toString() == column.value">
          #{item.discountKind,jdbcType=TINYINT}
        </if>
        <if test="'discount_amount'.toString() == column.value">
          #{item.discountAmount,jdbcType=INTEGER}
        </if>
        <if test="'merchant_assume_amount'.toString() == column.value">
          #{item.merchantAssumeAmount,jdbcType=INTEGER}
        </if>
        <if test="'third_assume_amount'.toString() == column.value">
          #{item.thirdAssumeAmount,jdbcType=INTEGER}
        </if>
        <if test="'third_code'.toString() == column.value">
          #{item.thirdCode,jdbcType=VARCHAR}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'extra'.toString() == column.value">
          #{item.extra,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
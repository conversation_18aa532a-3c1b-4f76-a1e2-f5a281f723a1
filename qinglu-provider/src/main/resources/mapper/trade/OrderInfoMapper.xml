<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.OrderInfoMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.OrderInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_name" jdbcType="VARCHAR" property="vehicleName" />
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo" />
    <result column="pickup_date" jdbcType="TIMESTAMP" property="pickupDate" />
    <result column="return_date" jdbcType="TIMESTAMP" property="returnDate" />
    <result column="last_return_date" jdbcType="TIMESTAMP" property="lastReturnDate" />
    <result column="pickup_store_id" jdbcType="BIGINT" property="pickupStoreId" />
    <result column="return_store_id" jdbcType="BIGINT" property="returnStoreId" />
    <result column="pickup_city_id" jdbcType="BIGINT" property="pickupCityId" />
    <result column="return_city_id" jdbcType="BIGINT" property="returnCityId" />
    <result column="pickup_addr" jdbcType="VARCHAR" property="pickupAddr" />
    <result column="return_addr" jdbcType="VARCHAR" property="returnAddr" />
    <result column="pickup_addr_type" jdbcType="TINYINT" property="pickupAddrType" />
    <result column="return_addr_type" jdbcType="TINYINT" property="returnAddrType" />
    <result column="pay_amount" jdbcType="INTEGER" property="payAmount" />
    <result column="receivable_amount" jdbcType="INTEGER" property="receivableAmount" />
    <result column="free_deposit_degree" jdbcType="INTEGER" property="freeDepositDegree" />
    <result column="order_member_id" jdbcType="BIGINT" property="orderMemberId" />
    <result column="order_user_id" jdbcType="BIGINT" property="orderUserId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="source_order_id" jdbcType="VARCHAR" property="sourceOrderId" />
    <result column="source_inner_order_id" jdbcType="VARCHAR" property="sourceInnerOrderId" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="order_source" jdbcType="TINYINT" property="orderSource" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="self_pr_status" jdbcType="TINYINT" property="selfPrStatus" />
    <result column="self_pr_order" jdbcType="TINYINT" property="selfPrOrder" />
    <result column="pay_status" jdbcType="TINYINT" property="payStatus" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="order_time" jdbcType="BIGINT" property="orderTime" />
    <result column="order_op_time" jdbcType="BIGINT" property="orderOpTime" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="pickup_gis_addr" jdbcType="VARCHAR" property="pickupGisAddr" />
    <result column="return_gis_addr" jdbcType="VARCHAR" property="returnGisAddr" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
    <result column="cancel_reason_type" jdbcType="INTEGER" property="cancelReasonType" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.ql.rent.entity.trade.OrderInfo">
    <result column="pickup_addr_gis" jdbcType="BINARY" property="pickupAddrGis" />
    <result column="return_addr_gis" jdbcType="BINARY" property="returnAddrGis" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, merchant_id, vehicle_model_id, vehicle_id, vehicle_name, vehicle_no, pickup_date, return_date,
    last_return_date, pickup_store_id,return_store_id, pickup_city_id, return_city_id, pickup_addr, return_addr,
    pickup_addr_type, return_addr_type, pay_amount, receivable_amount, free_deposit_degree, order_member_id, order_user_id,
    user_name, mobile, source_order_id, source_inner_order_id, order_type, order_source, order_status, self_pr_status, pay_status, self_pr_order,
    last_ver, op_user_id, order_time, order_op_time, create_time, op_time, pickup_gis_addr, return_gis_addr, extra, cancel_reason_type
  </sql>
  <sql id="Blob_Column_List">
    pickup_addr_gis, return_addr_gis
  </sql>

  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.OrderInfoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from order_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.OrderInfoExample">
    delete from order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.OrderInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_info (order_no, merchant_id, vehicle_model_id, vehicle_id, vehicle_name, vehicle_no,
    pickup_date, return_date, last_return_date, pickup_store_id,
    return_store_id, pickup_city_id, return_city_id,
    pickup_addr, return_addr, pickup_addr_type,
    return_addr_type, pay_amount, receivable_amount,
    free_deposit_degree, order_member_id, user_name,
    mobile, source_order_id, source_inner_order_id, order_type, order_source, order_status, self_pr_status, pay_status, self_pr_order
    last_ver, op_user_id, order_time,
    order_op_time, create_time, op_time,
    pickup_addr_gis, return_addr_gis, pickup_gis_addr, return_gis_addr, extra, cancel_reason_type)
    values (#{orderNo,jdbcType=VARCHAR}, #{merchantId,jdbcType=BIGINT}, #{vehicleModelId,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, #{vehicleName,jdbcType=VARCHAR}, #{vehicleNo,jdbcType=VARCHAR},
    #{pickupDate,jdbcType=TIMESTAMP}, #{returnDate,jdbcType=TIMESTAMP}, #{lastReturnDate,jdbcType=TIMESTAMP}, #{pickupStoreId,jdbcType=BIGINT},
    #{returnStoreId,jdbcType=BIGINT}, #{pickupCityId,jdbcType=BIGINT}, #{returnCityId,jdbcType=BIGINT},
    #{pickupAddr,jdbcType=VARCHAR}, #{returnAddr,jdbcType=VARCHAR}, #{pickupAddrType,jdbcType=TINYINT},
    #{returnAddrType,jdbcType=TINYINT}, #{payAmount,jdbcType=INTEGER}, #{receivableAmount,jdbcType=INTEGER},
    #{freeDepositDegree,jdbcType=INTEGER}, #{orderMemberId,jdbcType=BIGINT}, #{userName,jdbcType=VARCHAR},
    #{mobile,jdbcType=VARCHAR}, #{sourceOrderId,jdbcType=VARCHAR}, #{sourceInnerOrderId,jdbcType=VARCHAR}, #{orderType,jdbcType=TINYINT}, #{orderSource,jdbcType=TINYINT},
    #{orderStatus,jdbcType=TINYINT}, #{selfPrStatus,jdbcType=TINYINT}, #{payStatus,jdbcType=TINYINT}, #{selfPrOrder,jdbcType=TINYINT},
    #{lastVer,jdbcType=INTEGER}, #{opUserId,jdbcType=BIGINT}, #{orderTime,jdbcType=BIGINT},
    #{orderOpTime,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT},
    #{pickupAddrGis,jdbcType=BINARY}, #{returnAddrGis,jdbcType=BINARY}, #{pickupGisAddr,jdbcType=VARCHAR}, #{returnGisAddr,jdbcType=VARCHAR}, #{extra,jdbcType=VARCHAR}, #{cancelReasonType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.OrderInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleName != null">
        vehicle_name,
      </if>
      <if test="vehicleNo != null">
        vehicle_no,
      </if>
      <if test="pickupDate != null">
        pickup_date,
      </if>
      <if test="returnDate != null">
        return_date,
      </if>
      <if test="lastReturnDate != null">
        last_return_date,
      </if>
      <if test="pickupStoreId != null">
        pickup_store_id,
      </if>
      <if test="returnStoreId != null">
        return_store_id,
      </if>
      <if test="pickupCityId != null">
        pickup_city_id,
      </if>
      <if test="returnCityId != null">
        return_city_id,
      </if>
      <if test="pickupAddr != null">
        pickup_addr,
      </if>
      <if test="returnAddr != null">
        return_addr,
      </if>
      <if test="pickupAddrType != null">
        pickup_addr_type,
      </if>
      <if test="returnAddrType != null">
        return_addr_type,
      </if>
      <if test="payAmount != null">
        pay_amount,
      </if>
      <if test="receivableAmount != null">
        receivable_amount,
      </if>
      <if test="freeDepositDegree != null">
        free_deposit_degree,
      </if>
      <if test="orderMemberId != null">
        order_member_id,
      </if>
      <if test="orderUserId != null">
        order_user_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="sourceOrderId != null">
        source_order_id,
      </if>
      <if test="sourceInnerOrderId != null">
        source_inner_order_id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderSource != null">
        order_source,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="selfPrStatus != null">
        self_pr_status,
      </if>
      <if test="payStatus != null">
        pay_status,
      </if>
      <if test="selfPrOrder != null">
        self_pr_order,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="orderTime != null">
        order_time,
      </if>
      <if test="orderOpTime != null">
        order_op_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="pickupAddrGis != null">
        pickup_addr_gis,
      </if>
      <if test="returnAddrGis != null">
        return_addr_gis,
      </if>
      <if test="pickupGisAddr != null">
        pickup_gis_addr,
      </if>
      <if test="returnGisAddr != null">
        return_gis_addr,
      </if>
      <if test="extra != null">
        extra,
      </if>
        <if test="cancelReasonType != null">
        cancel_reason_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleName != null">
        #{vehicleName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleNo != null">
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="pickupDate != null">
        #{pickupDate,jdbcType=BIGINT},
      </if>
      <if test="returnDate != null">
        #{returnDate,jdbcType=BIGINT},
      </if>
      <if test="lastReturnDate != null">
        #{lastReturnDate,jdbcType=BIGINT},
      </if>
      <if test="pickupStoreId != null">
        #{pickupStoreId,jdbcType=BIGINT},
      </if>
      <if test="returnStoreId != null">
        #{returnStoreId,jdbcType=BIGINT},
      </if>
      <if test="pickupCityId != null">
        #{pickupCityId,jdbcType=BIGINT},
      </if>
      <if test="returnCityId != null">
        #{returnCityId,jdbcType=BIGINT},
      </if>
      <if test="pickupAddr != null">
        #{pickupAddr,jdbcType=VARCHAR},
      </if>
      <if test="returnAddr != null">
        #{returnAddr,jdbcType=VARCHAR},
      </if>
      <if test="pickupAddrType != null">
        #{pickupAddrType,jdbcType=TINYINT},
      </if>
      <if test="returnAddrType != null">
        #{returnAddrType,jdbcType=TINYINT},
      </if>
      <if test="payAmount != null">
        #{payAmount,jdbcType=INTEGER},
      </if>
      <if test="receivableAmount != null">
        #{receivableAmount,jdbcType=INTEGER},
      </if>
      <if test="freeDepositDegree != null">
        #{freeDepositDegree,jdbcType=INTEGER},
      </if>
      <if test="orderMemberId != null">
        #{orderMemberId,jdbcType=BIGINT},
      </if>
      <if test="orderUserId != null">
        #{orderUserId,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="sourceOrderId != null">
        #{sourceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="sourceInnerOrderId != null">
        #{sourceInnerOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="orderSource != null">
        #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="selfPrStatus != null">
        #{selfPrStatus,jdbcType=TINYINT},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=TINYINT},
      </if>
      <if test="selfPrOrder != null">
        #{selfPrOrder,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="orderTime != null">
        #{orderTime,jdbcType=BIGINT},
      </if>
      <if test="orderOpTime != null">
        #{orderOpTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="pickupAddrGis != null">
        #{pickupAddrGis,jdbcType=BINARY},
      </if>
      <if test="returnAddrGis != null">
        #{returnAddrGis,jdbcType=BINARY},
      </if>
      <if test="pickupGisAddr != null">
        #{pickupGisAddr,jdbcType=VARCHAR},
      </if>
      <if test="returnGisAddr != null">
        #{returnGisAddr,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="cancelReasonType != null">
        #{cancelReasonType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.OrderInfoExample" resultType="java.lang.Long">
    select count(*) from order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleName != null">
        vehicle_name = #{record.vehicleName,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleNo != null">
        vehicle_no = #{record.vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="record.pickupDate != null">
        pickup_date = #{record.pickupDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.returnDate != null">
        return_date = #{record.returnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastReturnDate != null">
        last_return_date = #{record.lastReturnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.pickupStoreId != null">
        pickup_store_id = #{record.pickupStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.returnStoreId != null">
        return_store_id = #{record.returnStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.pickupCityId != null">
        pickup_city_id = #{record.pickupCityId,jdbcType=BIGINT},
      </if>
      <if test="record.returnCityId != null">
        return_city_id = #{record.returnCityId,jdbcType=BIGINT},
      </if>
      <if test="record.pickupAddr != null">
        pickup_addr = #{record.pickupAddr,jdbcType=VARCHAR},
      </if>
      <if test="record.returnAddr != null">
        return_addr = #{record.returnAddr,jdbcType=VARCHAR},
      </if>
      <if test="record.pickupAddrType != null">
        pickup_addr_type = #{record.pickupAddrType,jdbcType=TINYINT},
      </if>
      <if test="record.returnAddrType != null">
        return_addr_type = #{record.returnAddrType,jdbcType=TINYINT},
      </if>
      <if test="record.payAmount != null">
        pay_amount = #{record.payAmount,jdbcType=INTEGER},
      </if>
      <if test="record.receivableAmount != null">
        receivable_amount = #{record.receivableAmount,jdbcType=INTEGER},
      </if>
      <if test="record.freeDepositDegree != null">
        free_deposit_degree = #{record.freeDepositDegree,jdbcType=INTEGER},
      </if>
      <if test="record.orderMemberId != null">
        order_member_id = #{record.orderMemberId,jdbcType=BIGINT},
      </if>
      <if test="record.orderUserId != null">
        order_user_id = #{record.orderUserId,jdbcType=BIGINT},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.mobile != null">
        mobile = #{record.mobile,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceOrderId != null">
        source_order_id = #{record.sourceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceInnerOrderId != null">
        source_inner_order_id = #{record.sourceInnerOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=TINYINT},
      </if>
      <if test="record.orderSource != null">
        order_source = #{record.orderSource,jdbcType=TINYINT},
      </if>
      <if test="record.orderStatus != null">
        order_status = #{record.orderStatus,jdbcType=TINYINT},
      </if>
      <if test="record.selfPrStatus != null">
        self_pr_status = #{record.selfPrStatus,jdbcType=TINYINT},
      </if>
      <if test="record.payStatus != null">
        pay_status = #{record.payStatus,jdbcType=TINYINT},
      </if>
      <if test="record.selfPrOrder != null">
        self_pr_order = #{record.selfPrOrder,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.orderTime != null">
        order_time = #{record.orderTime,jdbcType=BIGINT},
      </if>
      <if test="record.orderOpTime != null">
        order_op_time = #{record.orderOpTime,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.pickupAddrGis != null">
        pickup_addr_gis = #{record.pickupAddrGis,jdbcType=BINARY},
      </if>
      <if test="record.returnAddrGis != null">
        return_addr_gis = #{record.returnAddrGis,jdbcType=BINARY},
      </if>
      <if test="record.pickupGisAddr != null">
        pickup_gis_addr = #{record.pickupGisAddr,jdbcType=VARCHAR},
      </if>
      <if test="record.returnGisAddr != null">
        return_gis_addr = #{record.returnGisAddr,jdbcType=VARCHAR},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
      <if test="record.cancelReasonType != null">
        cancel_reason_type = #{record.cancelReasonType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_info
    set id = #{record.id,jdbcType=BIGINT},
    order_no = #{record.orderNo,jdbcType=VARCHAR},
    merchant_id = #{record.merchantId,jdbcType=BIGINT},
    vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
    vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
    vehicle_name = #{record.vehicleName,jdbcType=VARCHAR},
    vehicle_no = #{record.vehicleNo,jdbcType=VARCHAR},
    pickup_date = #{record.pickupDate,jdbcType=TIMESTAMP},
    return_date = #{record.returnDate,jdbcType=TIMESTAMP},
    last_return_date = #{record.lastReturnDate,jdbcType=TIMESTAMP},
    pickup_store_id = #{record.pickupStoreId,jdbcType=BIGINT},
    return_store_id = #{record.returnStoreId,jdbcType=BIGINT},
    pickup_city_id = #{record.pickupCityId,jdbcType=BIGINT},
    return_city_id = #{record.returnCityId,jdbcType=BIGINT},
    pickup_addr = #{record.pickupAddr,jdbcType=VARCHAR},
    return_addr = #{record.returnAddr,jdbcType=VARCHAR},
    pickup_addr_type = #{record.pickupAddrType,jdbcType=TINYINT},
    return_addr_type = #{record.returnAddrType,jdbcType=TINYINT},
    pay_amount = #{record.payAmount,jdbcType=INTEGER},
    receivable_amount = #{record.receivableAmount,jdbcType=INTEGER},
    free_deposit_degree = #{record.freeDepositDegree,jdbcType=INTEGER},
    order_member_id = #{record.orderMemberId,jdbcType=BIGINT},
    user_name = #{record.userName,jdbcType=VARCHAR},
    mobile = #{record.mobile,jdbcType=VARCHAR},
    source_order_id = #{record.sourceOrderId,jdbcType=VARCHAR},
    source_inner_order_id = #{record.sourceInnerOrderId,jdbcType=VARCHAR},
    order_type = #{record.orderType,jdbcType=TINYINT},
    order_source = #{record.orderSource,jdbcType=TINYINT},
    order_status = #{record.orderStatus,jdbcType=TINYINT},
    self_pr_status = #{record.selfPrStatus,jdbcType=TINYINT},
    pay_status = #{record.payStatus,jdbcType=TINYINT},
    self_pr_order = #{record.selfPrOrder,jdbcType=TINYINT},
    last_ver = #{record.lastVer,jdbcType=INTEGER},
    op_user_id = #{record.opUserId,jdbcType=BIGINT},
    order_time = #{record.orderTime,jdbcType=BIGINT},
    order_op_time = #{record.orderOpTime,jdbcType=BIGINT},
    create_time = #{record.createTime,jdbcType=BIGINT},
    op_time = #{record.opTime,jdbcType=BIGINT},
    pickup_addr_gis = #{record.pickupAddrGis,jdbcType=BINARY},
    return_addr_gis = #{record.returnAddrGis,jdbcType=BINARY},
    pickup_gis_addr = #{record.pickupGisAddr,jdbcType=VARCHAR},
    return_gis_addr = #{record.returnGisAddr,jdbcType=VARCHAR},
    extra = #{record.extra ,jdbcType=VARCHAR},
    cancel_reason_type = #{record.cancelReasonType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.OrderInfo">
    update order_info
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleName != null">
        vehicle_name = #{vehicleName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleNo != null">
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="pickupDate != null">
        pickup_date = #{pickupDate,jdbcType=TIMESTAMP},
      </if>
      <if test="returnDate != null">
        return_date = #{returnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastReturnDate != null">
        last_return_date = #{lastReturnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="pickupStoreId != null">
        pickup_store_id = #{pickupStoreId,jdbcType=BIGINT},
      </if>
      <if test="returnStoreId != null">
        return_store_id = #{returnStoreId,jdbcType=BIGINT},
      </if>
      <if test="pickupCityId != null">
        pickup_city_id = #{pickupCityId,jdbcType=BIGINT},
      </if>
      <if test="returnCityId != null">
        return_city_id = #{returnCityId,jdbcType=BIGINT},
      </if>
      <if test="pickupAddr != null">
        pickup_addr = #{pickupAddr,jdbcType=VARCHAR},
      </if>
      <if test="returnAddr != null">
        return_addr = #{returnAddr,jdbcType=VARCHAR},
      </if>
      <if test="pickupAddrType != null">
        pickup_addr_type = #{pickupAddrType,jdbcType=TINYINT},
      </if>
      <if test="returnAddrType != null">
        return_addr_type = #{returnAddrType,jdbcType=TINYINT},
      </if>
      <if test="payAmount != null">
        pay_amount = #{payAmount,jdbcType=INTEGER},
      </if>
      <if test="receivableAmount != null">
        receivable_amount = #{receivableAmount,jdbcType=INTEGER},
      </if>
      <if test="freeDepositDegree != null">
        free_deposit_degree = #{freeDepositDegree,jdbcType=INTEGER},
      </if>
      <if test="orderMemberId != null">
        order_member_id = #{orderMemberId,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="sourceOrderId != null">
        source_order_id = #{sourceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="sourceInnerOrderId != null">
        source_inner_order_id = #{sourceInnerOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="orderSource != null">
        order_source = #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=TINYINT},
      </if>
        <if test="selfPrStatus != null">
        self_pr_status = #{selfPrStatus,jdbcType=TINYINT},
      </if>
      <if test="payStatus != null">
        pay_status = #{payStatus,jdbcType=TINYINT},
      </if>
        <if test="selfPrOrder != null">
        self_pr_order = #{selfPrOrder,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="orderTime != null">
        order_time = #{orderTime,jdbcType=BIGINT},
      </if>
      <if test="orderOpTime != null">
        order_op_time = #{orderOpTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <!--      <if test="pickupAddrGis != null">-->
      <!--        pickup_addr_gis = #{pickupAddrGis,jdbcType=BINARY},-->
      <!--      </if>-->
      <!--      <if test="returnAddrGis != null">-->
      <!--        return_addr_gis = #{returnAddrGis,jdbcType=BINARY},-->
      <!--      </if>-->
      <if test="pickupAddrGisStr != null">
        pickup_addr_gis = ST_GeomFromText(#{pickupAddrGisStr}),
      </if>
      <if test="returnAddrGisStr != null">
        return_addr_gis = ST_GeomFromText(#{returnAddrGisStr}),
      </if>
      <if test="pickupGisAddr != null">
        pickup_gis_addr = #{pickupGisAddr,jdbcType=VARCHAR},
      </if>
      <if test="returnGisAddr != null">
        return_gis_addr = #{returnGisAddr,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="cancelReasonType != null">
        cancel_reason_type = #{cancelReasonType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.OrderInfo">
    update order_info
    set order_no = #{orderNo,jdbcType=VARCHAR},
        merchant_id = #{merchantId,jdbcType=BIGINT},
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
        vehicle_name = #{vehicleName,jdbcType=VARCHAR},
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
        pickup_date = #{pickupDate,jdbcType=TIMESTAMP},
        return_date = #{returnDate,jdbcType=TIMESTAMP},
        last_return_date = #{lastReturnDate,jdbcType=TIMESTAMP},
        pickup_store_id = #{pickupStoreId,jdbcType=BIGINT},
        return_store_id = #{returnStoreId,jdbcType=BIGINT},
        pickup_city_id = #{pickupCityId,jdbcType=BIGINT},
        return_city_id = #{returnCityId,jdbcType=BIGINT},
        pickup_addr = #{pickupAddr,jdbcType=VARCHAR},
        return_addr = #{returnAddr,jdbcType=VARCHAR},
        pickup_addr_type = #{pickupAddrType,jdbcType=TINYINT},
        return_addr_type = #{returnAddrType,jdbcType=TINYINT},
        pay_amount = #{payAmount,jdbcType=INTEGER},
        receivable_amount = #{receivableAmount,jdbcType=INTEGER},
        free_deposit_degree = #{freeDepositDegree,jdbcType=INTEGER},
        order_member_id = #{orderMemberId,jdbcType=BIGINT},
        user_name = #{userName,jdbcType=VARCHAR},
        mobile = #{mobile,jdbcType=VARCHAR},
        source_order_id = #{sourceOrderId,jdbcType=VARCHAR},
        source_inner_order_id = #{sourceInnerOrderId,jdbcType=VARCHAR},
        order_type = #{orderType,jdbcType=TINYINT},
        order_source = #{orderSource,jdbcType=TINYINT},
        order_status = #{orderStatus,jdbcType=TINYINT},
        self_pr_status = #{selfPrStatus,jdbcType=TINYINT},
        pay_status = #{payStatus,jdbcType=TINYINT},
        self_pr_order = #{selfPrOrder,jdbcType=TINYINT},
        last_ver = #{lastVer,jdbcType=INTEGER},
        op_user_id = #{opUserId,jdbcType=BIGINT},
        order_time = #{orderTime,jdbcType=BIGINT},
        order_op_time = #{orderOpTime,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=BIGINT},
        op_time = #{opTime,jdbcType=BIGINT},
        pickup_addr_gis = #{pickupAddrGis,jdbcType=BINARY},
        return_addr_gis = #{returnAddrGis,jdbcType=BINARY},
        pickup_gis_addr = #{pickupGisAddr,jdbcType=VARCHAR},
        return_gis_addr = #{returnGisAddr,jdbcType=VARCHAR},
        extra = #{extra,jdbcType=VARCHAR},
        cancel_reason_type = #{cancelReasonType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_info
    (order_no, merchant_id, vehicle_model_id, vehicle_id, vehicle_name, vehicle_no, pickup_date, return_date, last_return_date, pickup_store_id,
    return_store_id, pickup_city_id, return_city_id, pickup_addr, return_addr, pickup_addr_type,
    return_addr_type, pay_amount, receivable_amount, free_deposit_degree, order_member_id,
    user_name, mobile, source_order_id, source_inner_order_id, order_type, order_source, order_status,self_pr_status,
    pay_status, self_pr_order, last_ver, op_user_id, order_time, order_op_time, create_time, op_time,
    pickup_addr_gis, return_addr_gis, pickup_gis_addr, return_gis_addr, extra, cancel_reason_type)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderNo,jdbcType=VARCHAR},#{item.merchantId,jdbcType=BIGINT},#{item.vehicleModelId,jdbcType=BIGINT},#{item.vehicleId,jdbcType=BIGINT}, #{item.vehicleName,jdbcType=VARCHAR}, #{item.vehicleNo,jdbcType=VARCHAR},
      #{item.pickupDate,jdbcType=TIMESTAMP}, #{item.returnDate,jdbcType=TIMESTAMP},#{lastReturnDate,jdbcType=TIMESTAMP}, #{item.pickupStoreId,jdbcType=BIGINT},
      #{item.returnStoreId,jdbcType=BIGINT}, #{item.pickupCityId,jdbcType=BIGINT}, #{item.returnCityId,jdbcType=BIGINT},
      #{item.pickupAddr,jdbcType=VARCHAR}, #{item.returnAddr,jdbcType=VARCHAR}, #{item.pickupAddrType,jdbcType=TINYINT},
      #{item.returnAddrType,jdbcType=TINYINT}, #{item.payAmount,jdbcType=INTEGER}, #{item.receivableAmount,jdbcType=INTEGER},
      #{item.freeDepositDegree,jdbcType=INTEGER}, #{item.orderMemberId,jdbcType=BIGINT},
      #{item.userName,jdbcType=VARCHAR}, #{item.mobile,jdbcType=VARCHAR}, #{item.sourceOrderId,jdbcType=VARCHAR}, #{item.sourceInnerOrderId,jdbcType=VARCHAR}, #{item.orderType,jdbcType=TINYINT},
      #{item.orderSource,jdbcType=TINYINT}, #{item.orderStatus,jdbcType=TINYINT}, #{item.selfPrStatus,jdbcType=TINYINT},
      #{item.payStatus,jdbcType=TINYINT},#{item.selfPrOrder,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER}, #{item.opUserId,jdbcType=BIGINT},
      #{item.orderTime,jdbcType=BIGINT}, #{item.orderOpTime,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT},
      #{item.opTime,jdbcType=BIGINT}, #{item.pickupAddrGis,jdbcType=BINARY}, #{item.returnAddrGis,jdbcType=BINARY}, #{item.pickupGisAddr,jdbcType=VARCHAR}, #{item.returnGisAddr,jdbcType=VARCHAR}, #{item.extra,jdbcType=VARCHAR},
       #{item.cancelReasonType,jdbcType=INTEGER})
      )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_no'.toString() == column.value">
          #{item.orderNo,jdbcType=VARCHAR}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_id'.toString() == column.value">
          #{item.vehicleId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_name'.toString() == column.value">
          #{item.vehicleName,jdbcType=VARCHAR}
        </if>
        <if test="'vehicle_no'.toString() == column.value">
          #{item.vehicleNo,jdbcType=VARCHAR}
        </if>
        <if test="'pickup_date'.toString() == column.value">
          #{item.pickupDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'return_date'.toString() == column.value">
          #{item.returnDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'last_return_date'.toString() == column.value">
          #{item.lastReturnDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'pickup_store_id'.toString() == column.value">
          #{item.pickupStoreId,jdbcType=BIGINT}
        </if>
        <if test="'return_store_id'.toString() == column.value">
          #{item.returnStoreId,jdbcType=BIGINT}
        </if>
        <if test="'pickup_city_id'.toString() == column.value">
          #{item.pickupCityId,jdbcType=BIGINT}
        </if>
        <if test="'return_city_id'.toString() == column.value">
          #{item.returnCityId,jdbcType=BIGINT}
        </if>
        <if test="'pickup_addr'.toString() == column.value">
          #{item.pickupAddr,jdbcType=VARCHAR}
        </if>
        <if test="'return_addr'.toString() == column.value">
          #{item.returnAddr,jdbcType=VARCHAR}
        </if>
        <if test="'pickup_addr_type'.toString() == column.value">
          #{item.pickupAddrType,jdbcType=TINYINT}
        </if>
        <if test="'return_addr_type'.toString() == column.value">
          #{item.returnAddrType,jdbcType=TINYINT}
        </if>
        <if test="'pay_amount'.toString() == column.value">
          #{item.payAmount,jdbcType=INTEGER}
        </if>
        <if test="'receivable_amount'.toString() == column.value">
          #{item.receivableAmount,jdbcType=INTEGER}
        </if>
        <if test="'free_deposit_degree'.toString() == column.value">
          #{item.freeDepositDegree,jdbcType=INTEGER}
        </if>
        <if test="'order_member_id'.toString() == column.value">
          #{item.orderMemberId,jdbcType=BIGINT}
        </if>
        <if test="'user_name'.toString() == column.value">
          #{item.userName,jdbcType=VARCHAR}
        </if>
        <if test="'mobile'.toString() == column.value">
          #{item.mobile,jdbcType=VARCHAR}
        </if>
        <if test="'source_order_id'.toString() == column.value">
          #{item.sourceOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'source_inner_order_id'.toString() == column.value">
          #{item.sourceInnerOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'order_type'.toString() == column.value">
          #{item.orderType,jdbcType=TINYINT}
        </if>
        <if test="'order_source'.toString() == column.value">
          #{item.orderSource,jdbcType=TINYINT}
        </if>
        <if test="'order_status'.toString() == column.value">
          #{item.orderStatus,jdbcType=TINYINT}
        </if>
        <if test="'self_pr_status'.toString() == column.value">
          #{item.selfPrStatus,jdbcType=TINYINT}
        </if>
        <if test="'pay_status'.toString() == column.value">
          #{item.payStatus,jdbcType=TINYINT}
        </if>
        <if test="'self_pr_order'.toString() == column.value">
          #{item.selfPrOrder,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'order_time'.toString() == column.value">
          #{item.orderTime,jdbcType=BIGINT}
        </if>
        <if test="'order_op_time'.toString() == column.value">
          #{item.orderOpTime,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'pickup_addr_gis'.toString() == column.value">
          #{item.pickupAddrGis,jdbcType=BINARY}
        </if>
        <if test="'return_addr_gis'.toString() == column.value">
          #{item.returnAddrGis,jdbcType=BINARY}
        </if>
        <if test="'pickup_gis_addr'.toString() == column.value">
          #{item.pickupGisAddr,jdbcType=VARCHAR}
        </if>
        <if test="'return_gis_addr'.toString() == column.value">
          #{item.returnGisAddr,jdbcType=VARCHAR}
        </if>
        <if test="'extra'.toString() == column.value">
          #{item.extra,jdbcType=VARCHAR}
        </if>
        <if test="'cancel_reason_type'.toString() == column.value">
          #{item.cancelReasonType,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>

  <update id="updateGisById" parameterType="map">
    update order_info
    <set>
      <if test="pickupGis != null">
        pickup_addr_gis = ST_GeomFromText(#{pickupGis}),
      </if>
      <if test="returnGis != null">
        return_addr_gis = ST_GeomFromText(#{returnGis})
      </if>
    </set>
    where id = #{id}
  </update>

  <select id="selectByQuery" resultMap="ResultMapWithBLOBs" parameterType="com.ql.rent.param.trade.OrderInfoParam">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List" />
    from order_info
    where 1 = 1
    <if test="mobile!=null">
      and mobile=#{mobile}
    </if>
    <if test="name!=null">
      and user_name LIKE CONCAT(#{name},'%')
    </if>
    <if test="orderNo!=null">
      and order_no=#{orderNo}
    </if>
    <if test="merchantId!=null">
      and merchant_id=#{merchantId}
    </if>
    <if test="id!=null">
      and id=#{id}
    </if>
    <if test="idList!=null">
      and id in
      <foreach item="item" collection="idList" separator="," open="(" close=")" index="">
        #{item, jdbcType=BIGINT}
      </foreach>
    </if>
    <if test="sourceOrderId!=null">
      and source_order_id=#{sourceOrderId}
    </if>
    <if test="vehicleNo!=null">
      and vehicle_no=#{vehicleNo}
    </if>
    <if test="orderType!=null">
      and order_type = #{orderType}
    </if>
    <if test="orderSourceList!=null">
      and  order_source in
      <foreach item="item" collection="orderSourceList" separator="," open="(" close=")" index="">
        #{item}
      </foreach>
    </if>
    <if test="orderStatusList!=null and !orderStatusList.isEmpty()">
      and  order_status in
      <foreach item="item" collection="orderStatusList" separator="," open="(" close=")" index="">
        #{item, jdbcType=TINYINT}
      </foreach>
    </if>
    <if test="payStatusList!=null and !payStatusList.isEmpty()">
      and  pay_status in
      <foreach item="item" collection="payStatusList" separator="," open="(" close=")" index="">
        #{item, jdbcType=TINYINT}
      </foreach>
    </if>
    <if test="vehicleModelIdList!=null  and vehicleModelIdList.size > 0">
      and  vehicle_model_id in
      <foreach item="item" collection="vehicleModelIdList" separator="," open="(" close=")" index="">
        #{item, jdbcType=BIGINT}
      </foreach>
    </if>
    <if test="vehicleIdList!=null and vehicleIdList.size > 0">
      and  vehicle_id in
      <foreach item="item" collection="vehicleIdList" separator="," open="(" close=")" index="">
        #{item, jdbcType=BIGINT}
      </foreach>
    </if>
    <if test="storeIdList!=null and storeIdList.size > 0 ">
      and (pickup_store_id in
      <foreach item="item" collection="storeIdList" separator="," open="(" close=")" index="">
        #{item, jdbcType=BIGINT}
      </foreach>
      OR return_store_id in
      <foreach item="item" collection="storeIdList" separator="," open="(" close=")" index="">
        #{item, jdbcType=BIGINT}
      </foreach>
      )
    </if>
    <if test="pickupCityId!=null">
      and pickup_city_id = #{pickupCityId}
    </if>
    <if test="vehicleId!=null">
      and vehicle_id = #{vehicleId}
    </if>
    <if test="returnCityId!=null">
      and return_city_id = #{returnCityId}
    </if>
    <if test="startCreateTime!=null and endCreateTime!=null">
      and create_time between #{startCreateTime} and #{endCreateTime}
    </if>
    <if test="startPickupDate!=null and endPickupDate!=null">
      and pickup_date between #{startPickupDate} and #{endPickupDate}
    </if>
    <if test="startReturnDate!=null and endReturnDate!=null">
      and last_return_date between #{startReturnDate} and #{endReturnDate}
    </if>
    <if test="selfPrStatusList!=null and !selfPrStatusList.isEmpty()">
      and  self_pr_status in
      <foreach item="item" collection="selfPrStatusList" separator="," open="(" close=")" index="">
        #{item, jdbcType=TINYINT}
      </foreach>
    </if>
    <if test="selfPrOrder!=null">
      and self_pr_order = #{selfPrOrder}
    </if>

    <!--    <if test="sortField==null">-->
    <!--     order by ABS(UNIX_TIMESTAMP(CAST(SYSDATE()AS DATE)) - UNIX_TIMESTAMP (pickup_date)) ${sortType}-->
    <!--    </if>-->
    <if test="sortField!=null">
      order by ${sortField} ${sortType}, id DESC
    </if>
    <if test="pageIndex!=null and pageSize!=null ">
      limit #{startPos},#{pageSize}
    </if>
  </select>

  <select id="selectCountByQuery" resultType="java.lang.Integer" parameterType="com.ql.rent.param.trade.OrderInfoParam">
    select
    count(id)
    from order_info
    where 1 = 1
    <if test="mobile!=null">
      and mobile=#{mobile}
    </if>
    <if test="name!=null">
      and user_name LIKE CONCAT(#{name},'%')
    </if>
    <if test="orderNo!=null">
      and order_no=#{orderNo}
    </if>
    <if test="merchantId!=null">
      and merchant_id=#{merchantId}
    </if>
    <if test="id!=null">
      and id=#{id}
    </if>
    <if test="idList!=null">
      and id in
      <foreach item="item" collection="idList" separator="," open="(" close=")" index="">
        #{item, jdbcType=BIGINT}
      </foreach>
    </if>
    <if test="sourceOrderId!=null">
      and source_order_id=#{sourceOrderId}
    </if>
    <if test="vehicleNo!=null">
      and vehicle_no=#{vehicleNo}
    </if>
    <if test="orderType!=null">
      and order_type = #{orderType}
    </if>
    <if test="orderSourceList!=null">
      and  order_source in
      <foreach item="item" collection="orderSourceList" separator="," open="(" close=")" index="">
        #{item}
      </foreach>
    </if>
    <if test="orderStatusList!=null and !orderStatusList.isEmpty()">
      and  order_status in
      <foreach item="item" collection="orderStatusList" separator="," open="(" close=")" index="">
        #{item, jdbcType=TINYINT}
      </foreach>
    </if>
    <if test="payStatusList!=null and !payStatusList.isEmpty()">
      and  pay_status in
      <foreach item="item" collection="payStatusList" separator="," open="(" close=")" index="">
        #{item, jdbcType=TINYINT}
      </foreach>
    </if>
    <if test="vehicleModelIdList!=null  and vehicleModelIdList.size > 0">
      and  vehicle_model_id in
      <foreach item="item" collection="vehicleModelIdList" separator="," open="(" close=")" index="">
        #{item, jdbcType=BIGINT}
      </foreach>
    </if>
    <if test="vehicleIdList!=null and vehicleIdList.size > 0">
      and  vehicle_id in
      <foreach item="item" collection="vehicleIdList" separator="," open="(" close=")" index="">
        #{item, jdbcType=BIGINT}
      </foreach>
    </if>
    <if test="storeIdList!=null and storeIdList.size > 0 ">
      and (pickup_store_id in
      <foreach item="item" collection="storeIdList" separator="," open="(" close=")" index="">
        #{item, jdbcType=BIGINT}
      </foreach>
      OR return_store_id in
      <foreach item="item" collection="storeIdList" separator="," open="(" close=")" index="">
        #{item, jdbcType=BIGINT}
      </foreach>
      )
    </if>
    <if test="pickupCityId!=null">
      and pickup_city_id = #{pickupCityId}
    </if>
    <if test="returnCityId!=null">
      and return_city_id = #{returnCityId}
    </if>
    <if test="startCreateTime!=null and endCreateTime!=null">
      and create_time between #{startCreateTime} and #{endCreateTime}
    </if>
    <if test="startPickupDate!=null and endPickupDate!=null">
      and pickup_date between #{startPickupDate} and #{endPickupDate}
    </if>
    <if test="startReturnDate!=null and endReturnDate!=null">
      and last_return_date between #{startReturnDate} and #{endReturnDate}
    </if>
    <if test="selfPrStatusList!=null and !selfPrStatusList.isEmpty()">
      and  self_pr_status in
      <foreach item="item" collection="selfPrStatusList" separator="," open="(" close=")" index="">
        #{item, jdbcType=TINYINT}
      </foreach>
    </if>
    <if test="selfPrOrder!=null">
      and self_pr_order = #{selfPrOrder}
    </if>
  </select>

  <!--  <select id="selectCountByQuery" resultType="java.lang.Integer" parameterType="com.ql.rent.param.trade.OrderInfoParam">-->
  <!--    select SUM(counts) AS total_counts-->
  <!--    from (-->
  <!--    select-->
  <!--    count(id) AS counts-->
  <!--    from order_info-->
  <!--    where merchant_id=#{merchantId}-->
  <!--    <if test="orderType!=null">-->
  <!--      and order_type = #{orderType}-->
  <!--    </if>-->
  <!--    <if test="mobile!=null">-->
  <!--      and mobile=#{mobile}-->
  <!--    </if>-->
  <!--    <if test="name!=null">-->
  <!--      and user_name LIKE CONCAT(CONCAT('%',#{name},'%'))-->
  <!--    </if>-->
  <!--    <if test="orderNo!=null">-->
  <!--      and order_no=#{orderNo}-->
  <!--    </if>-->
  <!--    <if test="id!=null">-->
  <!--      and id=#{id}-->
  <!--    </if>-->
  <!--    <if test="idList!=null">-->
  <!--      and id in-->
  <!--      <foreach item="item" collection="idList" separator="," open="(" close=")" index="">-->
  <!--        #{item, jdbcType=BIGINT}-->
  <!--      </foreach>-->
  <!--    </if>-->
  <!--    <if test="sourceOrderId!=null">-->
  <!--      and source_order_id=#{sourceOrderId}-->
  <!--    </if>-->
  <!--    <if test="vehicleNo!=null">-->
  <!--      and vehicle_no LIKE CONCAT(CONCAT('%',#{vehicleNo},'%'))-->
  <!--    </if>-->
  <!--    <if test="orderSourceList!=null">-->
  <!--      and  order_source in-->
  <!--      <foreach item="item" collection="orderSourceList" separator="," open="(" close=")" index="">-->
  <!--        #{item}-->
  <!--      </foreach>-->
  <!--    </if>-->
  <!--    <if test="orderStatusList!=null and !orderStatusList.isEmpty()">-->
  <!--      and  order_status in-->
  <!--      <foreach item="item" collection="orderStatusList" separator="," open="(" close=")" index="">-->
  <!--        #{item, jdbcType=TINYINT}-->
  <!--      </foreach>-->
  <!--    </if>-->
  <!--    <if test="vehicleModelIdList!=null  and vehicleModelIdList.size > 0">-->
  <!--      and  vehicle_model_id in-->
  <!--      <foreach item="item" collection="vehicleModelIdList" separator="," open="(" close=")" index="">-->
  <!--        #{item, jdbcType=BIGINT}-->
  <!--      </foreach>-->
  <!--    </if>-->
  <!--    <if test="vehicleIdList!=null and vehicleIdList.size > 0">-->
  <!--      and  vehicle_id in-->
  <!--      <foreach item="item" collection="vehicleIdList" separator="," open="(" close=")" index="">-->
  <!--        #{item, jdbcType=BIGINT}-->
  <!--      </foreach>-->
  <!--    </if>-->
  <!--    <if test="storeIdList!=null and storeIdList.size > 0 ">-->
  <!--      and pickup_store_id in-->
  <!--      <foreach item="item" collection="storeIdList" separator="," open="(" close=")" index="">-->
  <!--        #{item, jdbcType=BIGINT}-->
  <!--      </foreach>-->
  <!--    </if>-->
  <!--    <if test="pickupCityId!=null">-->
  <!--      and pickup_city_id = #{pickupCityId}-->
  <!--    </if>-->
  <!--    <if test="returnCityId!=null">-->
  <!--      and return_city_id = #{returnCityId}-->
  <!--    </if>-->
  <!--    <if test="startCreateTime!=null and endCreateTime!=null">-->
  <!--      and create_time between #{startCreateTime} and #{endCreateTime}-->
  <!--    </if>-->
  <!--    <if test="startPickupDate!=null and endPickupDate!=null">-->
  <!--      and pickup_date between #{startPickupDate} and #{endPickupDate}-->
  <!--    </if>-->
  <!--    <if test="startReturnDate!=null and endReturnDate!=null">-->
  <!--      and last_return_date between #{startReturnDate} and #{endReturnDate}-->
  <!--    </if>-->

  <!--    UNION-->

  <!--    select-->
  <!--    count(id) AS counts-->
  <!--    from order_info-->
  <!--    where merchant_id=#{merchantId}-->
  <!--    <if test="orderType!=null">-->
  <!--      and order_type = #{orderType}-->
  <!--    </if>-->
  <!--    <if test="mobile!=null">-->
  <!--      and mobile=#{mobile}-->
  <!--    </if>-->
  <!--    <if test="name!=null">-->
  <!--      and user_name LIKE CONCAT(CONCAT('%',#{name},'%'))-->
  <!--    </if>-->
  <!--    <if test="orderNo!=null">-->
  <!--      and order_no=#{orderNo}-->
  <!--    </if>-->
  <!--    <if test="id!=null">-->
  <!--      and id=#{id}-->
  <!--    </if>-->
  <!--    <if test="idList!=null">-->
  <!--      and id in-->
  <!--      <foreach item="item" collection="idList" separator="," open="(" close=")" index="">-->
  <!--        #{item, jdbcType=BIGINT}-->
  <!--      </foreach>-->
  <!--    </if>-->
  <!--    <if test="sourceOrderId!=null">-->
  <!--      and source_order_id=#{sourceOrderId}-->
  <!--    </if>-->
  <!--    <if test="vehicleNo!=null">-->
  <!--      and vehicle_no LIKE CONCAT(CONCAT('%',#{vehicleNo},'%'))-->
  <!--    </if>-->
  <!--    <if test="orderSourceList!=null">-->
  <!--      and  order_source in-->
  <!--      <foreach item="item" collection="orderSourceList" separator="," open="(" close=")" index="">-->
  <!--        #{item}-->
  <!--      </foreach>-->
  <!--    </if>-->
  <!--    <if test="orderStatusList!=null and !orderStatusList.isEmpty()">-->
  <!--      and  order_status in-->
  <!--      <foreach item="item" collection="orderStatusList" separator="," open="(" close=")" index="">-->
  <!--        #{item, jdbcType=TINYINT}-->
  <!--      </foreach>-->
  <!--    </if>-->
  <!--    <if test="vehicleModelIdList!=null  and vehicleModelIdList.size > 0">-->
  <!--      and  vehicle_model_id in-->
  <!--      <foreach item="item" collection="vehicleModelIdList" separator="," open="(" close=")" index="">-->
  <!--        #{item, jdbcType=BIGINT}-->
  <!--      </foreach>-->
  <!--    </if>-->
  <!--    <if test="vehicleIdList!=null and vehicleIdList.size > 0">-->
  <!--      and  vehicle_id in-->
  <!--      <foreach item="item" collection="vehicleIdList" separator="," open="(" close=")" index="">-->
  <!--        #{item, jdbcType=BIGINT}-->
  <!--      </foreach>-->
  <!--    </if>-->
  <!--    <if test="storeIdList!=null and storeIdList.size > 0 ">-->
  <!--      and return_store_id in-->
  <!--      <foreach item="item" collection="storeIdList" separator="," open="(" close=")" index="">-->
  <!--        #{item, jdbcType=BIGINT}-->
  <!--      </foreach>-->
  <!--    </if>-->
  <!--    <if test="pickupCityId!=null">-->
  <!--      and pickup_city_id = #{pickupCityId}-->
  <!--    </if>-->
  <!--    <if test="returnCityId!=null">-->
  <!--      and return_city_id = #{returnCityId}-->
  <!--    </if>-->
  <!--    <if test="startCreateTime!=null and endCreateTime!=null">-->
  <!--      and create_time between #{startCreateTime} and #{endCreateTime}-->
  <!--    </if>-->
  <!--    <if test="startPickupDate!=null and endPickupDate!=null">-->
  <!--      and pickup_date between #{startPickupDate} and #{endPickupDate}-->
  <!--    </if>-->
  <!--    <if test="startReturnDate!=null and endReturnDate!=null">-->
  <!--      and last_return_date between #{startReturnDate} and #{endReturnDate}-->
  <!--    </if>-->
  <!--    ) as total_table-->
  <!--  </select>-->

  <select id="findDiffNameOrder" resultType="java.lang.Long">
    select table1.orderid from
      (select t1.user_name as orderuser, t1.id as orderid, t2.user_name as memberuser, t2.id as memberid from order_info t1 inner join  order_member t2 on t1.order_member_id = t2.id) as table1
    where table1.orderuser != table1.memberuser
  </select>

  <select id="sumAmount" parameterType="com.ql.rent.entity.trade.OrderInfoExample" resultType="com.ql.rent.entity.trade.ex.OrderInfoAmountStatisticDO">
    select
    sum(pay_amount) AS payAmount,
    sum(receivable_amount) AS receivableAmount
    from order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <select id="sumGroupVehicleModelId" parameterType="com.ql.rent.entity.trade.OrderInfoExample" resultMap="BaseResultMap">
    select
    vehicle_model_id AS vehicleModelId,
    sum(pay_amount) AS payAmount,
    count(id) AS totalCount
    from order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    GROUP BY vehicle_model_id
  </select>
  <select id="selectPage" resultMap="BaseResultMap">
    SELECT id, order_status, merchant_id, `extra`, source_order_id, create_time, last_return_date
    FROM order_info
    WHERE id &gt;= #{startId}
    ORDER BY id ASC LIMIT ${pageSize}
  </select>
</mapper>
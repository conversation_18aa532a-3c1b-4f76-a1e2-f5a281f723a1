<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.OrderBillMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.OrderBill">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_amount" jdbcType="BIGINT" property="orderAmount" />
    <result column="fee_rate" jdbcType="BIGINT" property="feeRate" />
    <result column="fee_amount" jdbcType="BIGINT" property="feeAmount" />
    <result column="actual_fee_amount" jdbcType="BIGINT" property="actualFeeAmount" />
    <result column="bill_status" jdbcType="TINYINT" property="billStatus" />
    <result column="actual_return_date" jdbcType="BIGINT" property="actualReturnDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="refunded_amount" jdbcType="BIGINT" property="refundedAmount" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, store_id, order_id, order_no, order_amount, fee_rate, fee_amount, 
    actual_fee_amount, bill_status, actual_return_date, remark, last_ver, deleted, refunded_amount, 
    create_time, op_time, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.OrderBillExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from order_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_bill
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_bill
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.OrderBillExample">
    delete from order_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.OrderBill">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_bill (merchant_id, store_id, order_id, 
      order_no, order_amount, fee_rate, 
      fee_amount, actual_fee_amount, bill_status, 
      actual_return_date, remark, last_ver, 
      deleted, refunded_amount, create_time, 
      op_time, op_user_id)
    values (#{merchantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, 
      #{orderNo,jdbcType=VARCHAR}, #{orderAmount,jdbcType=BIGINT}, #{feeRate,jdbcType=BIGINT}, 
      #{feeAmount,jdbcType=BIGINT}, #{actualFeeAmount,jdbcType=BIGINT}, #{billStatus,jdbcType=TINYINT}, 
      #{actualReturnDate,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR}, #{lastVer,jdbcType=INTEGER}, 
      #{deleted,jdbcType=TINYINT}, #{refundedAmount,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.OrderBill">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="feeRate != null">
        fee_rate,
      </if>
      <if test="feeAmount != null">
        fee_amount,
      </if>
      <if test="actualFeeAmount != null">
        actual_fee_amount,
      </if>
      <if test="billStatus != null">
        bill_status,
      </if>
      <if test="actualReturnDate != null">
        actual_return_date,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="refundedAmount != null">
        refunded_amount,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="feeRate != null">
        #{feeRate,jdbcType=BIGINT},
      </if>
      <if test="feeAmount != null">
        #{feeAmount,jdbcType=BIGINT},
      </if>
      <if test="actualFeeAmount != null">
        #{actualFeeAmount,jdbcType=BIGINT},
      </if>
      <if test="billStatus != null">
        #{billStatus,jdbcType=TINYINT},
      </if>
      <if test="actualReturnDate != null">
        #{actualReturnDate,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="refundedAmount != null">
        #{refundedAmount,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.OrderBillExample" resultType="java.lang.Long">
    select count(*) from order_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_bill
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderAmount != null">
        order_amount = #{record.orderAmount,jdbcType=BIGINT},
      </if>
      <if test="record.feeRate != null">
        fee_rate = #{record.feeRate,jdbcType=BIGINT},
      </if>
      <if test="record.feeAmount != null">
        fee_amount = #{record.feeAmount,jdbcType=BIGINT},
      </if>
      <if test="record.actualFeeAmount != null">
        actual_fee_amount = #{record.actualFeeAmount,jdbcType=BIGINT},
      </if>
      <if test="record.billStatus != null">
        bill_status = #{record.billStatus,jdbcType=TINYINT},
      </if>
      <if test="record.actualReturnDate != null">
        actual_return_date = #{record.actualReturnDate,jdbcType=BIGINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.refundedAmount != null">
        refunded_amount = #{record.refundedAmount,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_bill
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      order_amount = #{record.orderAmount,jdbcType=BIGINT},
      fee_rate = #{record.feeRate,jdbcType=BIGINT},
      fee_amount = #{record.feeAmount,jdbcType=BIGINT},
      actual_fee_amount = #{record.actualFeeAmount,jdbcType=BIGINT},
      bill_status = #{record.billStatus,jdbcType=TINYINT},
      actual_return_date = #{record.actualReturnDate,jdbcType=BIGINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=TINYINT},
      refunded_amount = #{record.refundedAmount,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.OrderBill">
    update order_bill
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="feeRate != null">
        fee_rate = #{feeRate,jdbcType=BIGINT},
      </if>
      <if test="feeAmount != null">
        fee_amount = #{feeAmount,jdbcType=BIGINT},
      </if>
      <if test="actualFeeAmount != null">
        actual_fee_amount = #{actualFeeAmount,jdbcType=BIGINT},
      </if>
      <if test="billStatus != null">
        bill_status = #{billStatus,jdbcType=TINYINT},
      </if>
      <if test="actualReturnDate != null">
        actual_return_date = #{actualReturnDate,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="refundedAmount != null">
        refunded_amount = #{refundedAmount,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.OrderBill">
    update order_bill
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_amount = #{orderAmount,jdbcType=BIGINT},
      fee_rate = #{feeRate,jdbcType=BIGINT},
      fee_amount = #{feeAmount,jdbcType=BIGINT},
      actual_fee_amount = #{actualFeeAmount,jdbcType=BIGINT},
      bill_status = #{billStatus,jdbcType=TINYINT},
      actual_return_date = #{actualReturnDate,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      last_ver = #{lastVer,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      refunded_amount = #{refundedAmount,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_bill
    (merchant_id, store_id, order_id, order_no, order_amount, fee_rate, fee_amount, actual_fee_amount, 
      bill_status, actual_return_date, remark, last_ver, deleted, refunded_amount, create_time, 
      op_time, op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.orderId,jdbcType=BIGINT}, 
        #{item.orderNo,jdbcType=VARCHAR}, #{item.orderAmount,jdbcType=BIGINT}, #{item.feeRate,jdbcType=BIGINT}, 
        #{item.feeAmount,jdbcType=BIGINT}, #{item.actualFeeAmount,jdbcType=BIGINT}, #{item.billStatus,jdbcType=TINYINT}, 
        #{item.actualReturnDate,jdbcType=BIGINT}, #{item.remark,jdbcType=VARCHAR}, #{item.lastVer,jdbcType=INTEGER}, 
        #{item.deleted,jdbcType=TINYINT}, #{item.refundedAmount,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into order_bill (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'order_no'.toString() == column.value">
          #{item.orderNo,jdbcType=VARCHAR}
        </if>
        <if test="'order_amount'.toString() == column.value">
          #{item.orderAmount,jdbcType=BIGINT}
        </if>
        <if test="'fee_rate'.toString() == column.value">
          #{item.feeRate,jdbcType=BIGINT}
        </if>
        <if test="'fee_amount'.toString() == column.value">
          #{item.feeAmount,jdbcType=BIGINT}
        </if>
        <if test="'actual_fee_amount'.toString() == column.value">
          #{item.actualFeeAmount,jdbcType=BIGINT}
        </if>
        <if test="'bill_status'.toString() == column.value">
          #{item.billStatus,jdbcType=TINYINT}
        </if>
        <if test="'actual_return_date'.toString() == column.value">
          #{item.actualReturnDate,jdbcType=BIGINT}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'refunded_amount'.toString() == column.value">
          #{item.refundedAmount,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
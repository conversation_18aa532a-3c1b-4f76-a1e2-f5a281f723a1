<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.ReportQuotaMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.ReportQuota">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="city_id" jdbcType="BIGINT" property="cityId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="ymd" jdbcType="INTEGER" property="ymd" />
    <result column="quota_type" jdbcType="TINYINT" property="quotaType" />
    <result column="sub_type" jdbcType="BIGINT" property="subType" />
    <result column="quota_value" jdbcType="BIGINT" property="quotaValue" />
    <result column="sub_index" jdbcType="TINYINT" property="subIndex" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, city_id, store_id, channel_id, ymd, quota_type, sub_type, quota_value, sub_index, create_time,
    op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.ReportQuotaExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from report_quota
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from report_quota
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from report_quota
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.ReportQuotaExample">
    delete from report_quota
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.ReportQuota">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into report_quota (merchant_id, city_id, store_id, 
      channel_id, ymd, quota_type, sub_type,
      quota_value, sub_index, create_time, op_time
      )
    values (#{merchantId,jdbcType=BIGINT}, #{cityId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, 
      #{channelId,jdbcType=BIGINT}, #{ymd,jdbcType=INTEGER}, #{quotaType,jdbcType=TINYINT}, #{subType,jdbcType=BIGINT},
    #{quotaValue,jdbcType=BIGINT}, #{subIndex,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.ReportQuota">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into report_quota
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="ymd != null">
        ymd,
      </if>
      <if test="quotaType != null">
        quota_type,
      </if>
      <if test="subType != null">
        sub_type,
      </if>
      <if test="quotaValue != null">
        quota_value,
      </if>
      <if test="subIndex != null">
        sub_index,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="ymd != null">
        #{ymd,jdbcType=INTEGER},
      </if>
      <if test="quotaType != null">
        #{quotaType,jdbcType=TINYINT},
      </if>
      <if test="subType != null">
        #{subType,jdbcType=BIGINT},
      </if>
      <if test="quotaValue != null">
        #{quotaValue,jdbcType=BIGINT},
      </if>
      <if test="subIndex != null">
        #{subIndex,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.ReportQuotaExample" resultType="java.lang.Long">
    select count(*) from report_quota
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update report_quota
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.cityId != null">
        city_id = #{record.cityId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=BIGINT},
      </if>
      <if test="record.ymd != null">
        ymd = #{record.ymd,jdbcType=INTEGER},
      </if>
      <if test="record.quotaType != null">
        quota_type = #{record.quotaType,jdbcType=TINYINT},
      </if>
      <if test="record.subType != null">
        sub_type = #{record.subType,jdbcType=BIGINT},
      </if>
      <if test="record.quotaValue != null">
        quota_value = #{record.quotaValue,jdbcType=BIGINT},
      </if>
      <if test="record.subIndex != null">
        sub_index = #{record.subIndex,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update report_quota
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      city_id = #{record.cityId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      channel_id = #{record.channelId,jdbcType=BIGINT},
      ymd = #{record.ymd,jdbcType=INTEGER},
      quota_type = #{record.quotaType,jdbcType=TINYINT},
      sub_type = #{record.subType,jdbcType=BIGINT},
      quota_value = #{record.quotaValue,jdbcType=BIGINT},
      sub_index = #{record.subIndex,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.ReportQuota">
    update report_quota
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="ymd != null">
        ymd = #{ymd,jdbcType=INTEGER},
      </if>
      <if test="quotaType != null">
        quota_type = #{quotaType,jdbcType=TINYINT},
      </if>
      <if test="subType != null">
        sub_type = #{subType,jdbcType=BIGINT},
      </if>
      <if test="quotaValue != null">
        quota_value = #{quotaValue,jdbcType=BIGINT},
      </if>
      <if test="subIndex != null">
        sub_index = #{subIndex,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.ReportQuota">
    update report_quota
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      city_id = #{cityId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      channel_id = #{channelId,jdbcType=BIGINT},
      ymd = #{ymd,jdbcType=INTEGER},
      quota_type = #{quotaType,jdbcType=TINYINT},
      sub_type = #{subType,jdbcType=BIGINT},
      quota_value = #{quotaValue,jdbcType=BIGINT},
      sub_index = #{subIndex,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into report_quota
    (merchant_id, city_id, store_id, channel_id, ymd, quota_type, sub_type, quota_value, create_time,
      op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.cityId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, 
        #{item.channelId,jdbcType=BIGINT}, #{item.ymd,jdbcType=INTEGER}, #{item.quotaType,jdbcType=TINYINT}, #{item.subType,jdbcType=BIGINT},
      #{item.quotaValue,jdbcType=BIGINT}, #{item.subIndex,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into report_quota (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'city_id'.toString() == column.value">
          #{item.cityId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'channel_id'.toString() == column.value">
          #{item.channelId,jdbcType=BIGINT}
        </if>
        <if test="'ymd'.toString() == column.value">
          #{item.ymd,jdbcType=INTEGER}
        </if>
        <if test="'quota_type'.toString() == column.value">
          #{item.quotaType,jdbcType=TINYINT}
        </if>
        <if test="'sub_type'.toString() == column.value">
          #{item.subType,jdbcType=BIGINT}
        </if>
        <if test="'quota_value'.toString() == column.value">
          #{item.quotaValue,jdbcType=BIGINT}
        </if>
        <if test="'sub_index'.toString() == column.value">
          #{item.subIndex,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
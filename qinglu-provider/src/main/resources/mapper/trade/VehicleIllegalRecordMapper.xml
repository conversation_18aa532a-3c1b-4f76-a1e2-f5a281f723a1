<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.VehicleIllegalRecordMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.VehicleIllegalRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="search_count" jdbcType="INTEGER" property="searchCount" />
    <result column="success_count" jdbcType="INTEGER" property="successCount" />
    <result column="fail_count" jdbcType="INTEGER" property="failCount" />
    <result column="remaining_count" jdbcType="INTEGER" property="remainingCount" />
    <result column="service_order_id" jdbcType="BIGINT" property="serviceOrderId" />
    <result column="service_order_no" jdbcType="VARCHAR" property="serviceOrderNo" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, search_count, success_count, fail_count, remaining_count, service_order_id, service_order_no, 
    merchant_id, deleted, create_time, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.VehicleIllegalRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_illegal_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_illegal_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_illegal_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.VehicleIllegalRecordExample">
    delete from vehicle_illegal_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.VehicleIllegalRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_illegal_record (search_count, success_count, fail_count, 
      remaining_count, service_order_id, service_order_no, 
      merchant_id, deleted, create_time, 
      op_user_id)
    values (#{searchCount,jdbcType=INTEGER}, #{successCount,jdbcType=INTEGER}, #{failCount,jdbcType=INTEGER}, 
      #{remainingCount,jdbcType=INTEGER}, #{serviceOrderId,jdbcType=BIGINT}, #{serviceOrderNo,jdbcType=VARCHAR}, 
      #{merchantId,jdbcType=BIGINT}, #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, 
      #{opUserId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.VehicleIllegalRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_illegal_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="searchCount != null">
        search_count,
      </if>
      <if test="successCount != null">
        success_count,
      </if>
      <if test="failCount != null">
        fail_count,
      </if>
      <if test="remainingCount != null">
        remaining_count,
      </if>
      <if test="serviceOrderId != null">
        service_order_id,
      </if>
      <if test="serviceOrderNo != null">
        service_order_no,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="searchCount != null">
        #{searchCount,jdbcType=INTEGER},
      </if>
      <if test="successCount != null">
        #{successCount,jdbcType=INTEGER},
      </if>
      <if test="failCount != null">
        #{failCount,jdbcType=INTEGER},
      </if>
      <if test="remainingCount != null">
        #{remainingCount,jdbcType=INTEGER},
      </if>
      <if test="serviceOrderId != null">
        #{serviceOrderId,jdbcType=BIGINT},
      </if>
      <if test="serviceOrderNo != null">
        #{serviceOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.VehicleIllegalRecordExample" resultType="java.lang.Long">
    select count(*) from vehicle_illegal_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_illegal_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.searchCount != null">
        search_count = #{record.searchCount,jdbcType=INTEGER},
      </if>
      <if test="record.successCount != null">
        success_count = #{record.successCount,jdbcType=INTEGER},
      </if>
      <if test="record.failCount != null">
        fail_count = #{record.failCount,jdbcType=INTEGER},
      </if>
      <if test="record.remainingCount != null">
        remaining_count = #{record.remainingCount,jdbcType=INTEGER},
      </if>
      <if test="record.serviceOrderId != null">
        service_order_id = #{record.serviceOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.serviceOrderNo != null">
        service_order_no = #{record.serviceOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_illegal_record
    set id = #{record.id,jdbcType=BIGINT},
      search_count = #{record.searchCount,jdbcType=INTEGER},
      success_count = #{record.successCount,jdbcType=INTEGER},
      fail_count = #{record.failCount,jdbcType=INTEGER},
      remaining_count = #{record.remainingCount,jdbcType=INTEGER},
      service_order_id = #{record.serviceOrderId,jdbcType=BIGINT},
      service_order_no = #{record.serviceOrderNo,jdbcType=VARCHAR},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.VehicleIllegalRecord">
    update vehicle_illegal_record
    <set>
      <if test="searchCount != null">
        search_count = #{searchCount,jdbcType=INTEGER},
      </if>
      <if test="successCount != null">
        success_count = #{successCount,jdbcType=INTEGER},
      </if>
      <if test="failCount != null">
        fail_count = #{failCount,jdbcType=INTEGER},
      </if>
      <if test="remainingCount != null">
        remaining_count = #{remainingCount,jdbcType=INTEGER},
      </if>
      <if test="serviceOrderId != null">
        service_order_id = #{serviceOrderId,jdbcType=BIGINT},
      </if>
      <if test="serviceOrderNo != null">
        service_order_no = #{serviceOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.VehicleIllegalRecord">
    update vehicle_illegal_record
    set search_count = #{searchCount,jdbcType=INTEGER},
      success_count = #{successCount,jdbcType=INTEGER},
      fail_count = #{failCount,jdbcType=INTEGER},
      remaining_count = #{remainingCount,jdbcType=INTEGER},
      service_order_id = #{serviceOrderId,jdbcType=BIGINT},
      service_order_no = #{serviceOrderNo,jdbcType=VARCHAR},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_illegal_record
    (search_count, success_count, fail_count, remaining_count, service_order_id, service_order_no, 
      merchant_id, deleted, create_time, op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.searchCount,jdbcType=INTEGER}, #{item.successCount,jdbcType=INTEGER}, #{item.failCount,jdbcType=INTEGER}, 
        #{item.remainingCount,jdbcType=INTEGER}, #{item.serviceOrderId,jdbcType=BIGINT}, 
        #{item.serviceOrderNo,jdbcType=VARCHAR}, #{item.merchantId,jdbcType=BIGINT}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_illegal_record (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'search_count'.toString() == column.value">
          #{item.searchCount,jdbcType=INTEGER}
        </if>
        <if test="'success_count'.toString() == column.value">
          #{item.successCount,jdbcType=INTEGER}
        </if>
        <if test="'fail_count'.toString() == column.value">
          #{item.failCount,jdbcType=INTEGER}
        </if>
        <if test="'remaining_count'.toString() == column.value">
          #{item.remainingCount,jdbcType=INTEGER}
        </if>
        <if test="'service_order_id'.toString() == column.value">
          #{item.serviceOrderId,jdbcType=BIGINT}
        </if>
        <if test="'service_order_no'.toString() == column.value">
          #{item.serviceOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
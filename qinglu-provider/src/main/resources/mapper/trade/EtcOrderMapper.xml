<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.EtcOrderMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.EtcOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="rent_order_no" jdbcType="VARCHAR" property="rentOrderNo" />
    <result column="third_order_no" jdbcType="VARCHAR" property="thirdOrderNo" />
    <result column="order_status" jdbcType="BIGINT" property="orderStatus" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="etc_no" jdbcType="VARCHAR" property="etcNo" />
    <result column="license" jdbcType="VARCHAR" property="license" />
    <result column="package_code" jdbcType="VARCHAR" property="packageCode" />
    <result column="order_days" jdbcType="BIGINT" property="orderDays" />
    <result column="package_price" jdbcType="BIGINT" property="packagePrice" />
    <result column="source" jdbcType="BIGINT" property="source" />
    <result column="start_address" jdbcType="VARCHAR" property="startAddress" />
    <result column="end_address" jdbcType="VARCHAR" property="endAddress" />
    <result column="sign_time" jdbcType="TIMESTAMP" property="signTime" />
    <result column="actual_end_time" jdbcType="TIMESTAMP" property="actualEndTime" />
    <result column="actual_days" jdbcType="BIGINT" property="actualDays" />
    <result column="cancel_reason" jdbcType="VARCHAR" property="cancelReason" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="end_type" jdbcType="BIGINT" property="endType" />
    <result column="cancel_type" jdbcType="TINYINT" property="cancelType" />
    <result column="master_order_id" jdbcType="BIGINT" property="masterOrderId" />
    <result column="re_rent_flag" jdbcType="TINYINT" property="reRentFlag" />
    <result column="deleted" jdbcType="BIGINT" property="deleted" />
    <result column="last_ver" jdbcType="BIGINT" property="lastVer" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>

      <if test="publisher != null">
        and exists(select 1 from vehicle.etc_device ed where ed.license=etc_order.license and ed.publisher=#{publisher})
      </if>

      <if test="etcSource != null">
        and exists(select 1 from vehicle.etc_device ed where ed.license=etc_order.license and ed.etc_source=#{etcSource})
      </if>

    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, order_no, rent_order_no, third_order_no, order_status, start_time, 
    end_time, etc_no, license, package_code, order_days, package_price, source, start_address, 
    end_address, sign_time, actual_end_time, actual_days, cancel_reason, user_id, end_type, 
    cancel_type, master_order_id, re_rent_flag, deleted, last_ver, create_time, op_user_id, 
    op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.EtcOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from etc_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from etc_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.EtcOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into etc_order (merchant_id, order_no, rent_order_no, 
      third_order_no, order_status, start_time, 
      end_time, etc_no, license, 
      package_code, order_days, package_price, 
      source, start_address, end_address, 
      sign_time, actual_end_time, actual_days, 
      cancel_reason, user_id, end_type, 
      cancel_type, master_order_id, re_rent_flag, 
      deleted, last_ver, create_time, 
      op_user_id, op_time)
    values (#{merchantId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{rentOrderNo,jdbcType=VARCHAR}, 
      #{thirdOrderNo,jdbcType=VARCHAR}, #{orderStatus,jdbcType=BIGINT}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{etcNo,jdbcType=VARCHAR}, #{license,jdbcType=VARCHAR}, 
      #{packageCode,jdbcType=VARCHAR}, #{orderDays,jdbcType=BIGINT}, #{packagePrice,jdbcType=BIGINT}, 
      #{source,jdbcType=BIGINT}, #{startAddress,jdbcType=VARCHAR}, #{endAddress,jdbcType=VARCHAR}, 
      #{signTime,jdbcType=TIMESTAMP}, #{actualEndTime,jdbcType=TIMESTAMP}, #{actualDays,jdbcType=BIGINT}, 
      #{cancelReason,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{endType,jdbcType=BIGINT}, 
      #{cancelType,jdbcType=TINYINT}, #{masterOrderId,jdbcType=BIGINT}, #{reRentFlag,jdbcType=TINYINT}, 
      #{deleted,jdbcType=BIGINT}, #{lastVer,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{opUserId,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.EtcOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into etc_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="rentOrderNo != null">
        rent_order_no,
      </if>
      <if test="thirdOrderNo != null">
        third_order_no,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="etcNo != null">
        etc_no,
      </if>
      <if test="license != null">
        license,
      </if>
      <if test="packageCode != null">
        package_code,
      </if>
      <if test="orderDays != null">
        order_days,
      </if>
      <if test="packagePrice != null">
        package_price,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="startAddress != null">
        start_address,
      </if>
      <if test="endAddress != null">
        end_address,
      </if>
      <if test="signTime != null">
        sign_time,
      </if>
      <if test="actualEndTime != null">
        actual_end_time,
      </if>
      <if test="actualDays != null">
        actual_days,
      </if>
      <if test="cancelReason != null">
        cancel_reason,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="endType != null">
        end_type,
      </if>
      <if test="cancelType != null">
        cancel_type,
      </if>
      <if test="masterOrderId != null">
        master_order_id,
      </if>
      <if test="reRentFlag != null">
        re_rent_flag,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="rentOrderNo != null">
        #{rentOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="thirdOrderNo != null">
        #{thirdOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="etcNo != null">
        #{etcNo,jdbcType=VARCHAR},
      </if>
      <if test="license != null">
        #{license,jdbcType=VARCHAR},
      </if>
      <if test="packageCode != null">
        #{packageCode,jdbcType=VARCHAR},
      </if>
      <if test="orderDays != null">
        #{orderDays,jdbcType=BIGINT},
      </if>
      <if test="packagePrice != null">
        #{packagePrice,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=BIGINT},
      </if>
      <if test="startAddress != null">
        #{startAddress,jdbcType=VARCHAR},
      </if>
      <if test="endAddress != null">
        #{endAddress,jdbcType=VARCHAR},
      </if>
      <if test="signTime != null">
        #{signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualEndTime != null">
        #{actualEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualDays != null">
        #{actualDays,jdbcType=BIGINT},
      </if>
      <if test="cancelReason != null">
        #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="endType != null">
        #{endType,jdbcType=BIGINT},
      </if>
      <if test="cancelType != null">
        #{cancelType,jdbcType=TINYINT},
      </if>
      <if test="masterOrderId != null">
        #{masterOrderId,jdbcType=BIGINT},
      </if>
      <if test="reRentFlag != null">
        #{reRentFlag,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.EtcOrderExample" resultType="java.lang.Long">
    select count(*) from etc_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update etc_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.rentOrderNo != null">
        rent_order_no = #{record.rentOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdOrderNo != null">
        third_order_no = #{record.thirdOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderStatus != null">
        order_status = #{record.orderStatus,jdbcType=BIGINT},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.etcNo != null">
        etc_no = #{record.etcNo,jdbcType=VARCHAR},
      </if>
      <if test="record.license != null">
        license = #{record.license,jdbcType=VARCHAR},
      </if>
      <if test="record.packageCode != null">
        package_code = #{record.packageCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orderDays != null">
        order_days = #{record.orderDays,jdbcType=BIGINT},
      </if>
      <if test="record.packagePrice != null">
        package_price = #{record.packagePrice,jdbcType=BIGINT},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=BIGINT},
      </if>
      <if test="record.startAddress != null">
        start_address = #{record.startAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.endAddress != null">
        end_address = #{record.endAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.signTime != null">
        sign_time = #{record.signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.actualEndTime != null">
        actual_end_time = #{record.actualEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.actualDays != null">
        actual_days = #{record.actualDays,jdbcType=BIGINT},
      </if>
      <if test="record.cancelReason != null">
        cancel_reason = #{record.cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.endType != null">
        end_type = #{record.endType,jdbcType=BIGINT},
      </if>
      <if test="record.cancelType != null">
        cancel_type = #{record.cancelType,jdbcType=TINYINT},
      </if>
      <if test="record.masterOrderId != null">
        master_order_id = #{record.masterOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.reRentFlag != null">
        re_rent_flag = #{record.reRentFlag,jdbcType=TINYINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIGINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update etc_order
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      rent_order_no = #{record.rentOrderNo,jdbcType=VARCHAR},
      third_order_no = #{record.thirdOrderNo,jdbcType=VARCHAR},
      order_status = #{record.orderStatus,jdbcType=BIGINT},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      etc_no = #{record.etcNo,jdbcType=VARCHAR},
      license = #{record.license,jdbcType=VARCHAR},
      package_code = #{record.packageCode,jdbcType=VARCHAR},
      order_days = #{record.orderDays,jdbcType=BIGINT},
      package_price = #{record.packagePrice,jdbcType=BIGINT},
      source = #{record.source,jdbcType=BIGINT},
      start_address = #{record.startAddress,jdbcType=VARCHAR},
      end_address = #{record.endAddress,jdbcType=VARCHAR},
      sign_time = #{record.signTime,jdbcType=TIMESTAMP},
      actual_end_time = #{record.actualEndTime,jdbcType=TIMESTAMP},
      actual_days = #{record.actualDays,jdbcType=BIGINT},
      cancel_reason = #{record.cancelReason,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      end_type = #{record.endType,jdbcType=BIGINT},
      cancel_type = #{record.cancelType,jdbcType=TINYINT},
      master_order_id = #{record.masterOrderId,jdbcType=BIGINT},
      re_rent_flag = #{record.reRentFlag,jdbcType=TINYINT},
      deleted = #{record.deleted,jdbcType=BIGINT},
      last_ver = #{record.lastVer,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.EtcOrder">
    update etc_order
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="rentOrderNo != null">
        rent_order_no = #{rentOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="thirdOrderNo != null">
        third_order_no = #{thirdOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="etcNo != null">
        etc_no = #{etcNo,jdbcType=VARCHAR},
      </if>
      <if test="license != null">
        license = #{license,jdbcType=VARCHAR},
      </if>
      <if test="packageCode != null">
        package_code = #{packageCode,jdbcType=VARCHAR},
      </if>
      <if test="orderDays != null">
        order_days = #{orderDays,jdbcType=BIGINT},
      </if>
      <if test="packagePrice != null">
        package_price = #{packagePrice,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=BIGINT},
      </if>
      <if test="startAddress != null">
        start_address = #{startAddress,jdbcType=VARCHAR},
      </if>
      <if test="endAddress != null">
        end_address = #{endAddress,jdbcType=VARCHAR},
      </if>
      <if test="signTime != null">
        sign_time = #{signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualEndTime != null">
        actual_end_time = #{actualEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualDays != null">
        actual_days = #{actualDays,jdbcType=BIGINT},
      </if>
      <if test="cancelReason != null">
        cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="endType != null">
        end_type = #{endType,jdbcType=BIGINT},
      </if>
      <if test="cancelType != null">
        cancel_type = #{cancelType,jdbcType=TINYINT},
      </if>
      <if test="masterOrderId != null">
        master_order_id = #{masterOrderId,jdbcType=BIGINT},
      </if>
      <if test="reRentFlag != null">
        re_rent_flag = #{reRentFlag,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.EtcOrder">
    update etc_order
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      rent_order_no = #{rentOrderNo,jdbcType=VARCHAR},
      third_order_no = #{thirdOrderNo,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      etc_no = #{etcNo,jdbcType=VARCHAR},
      license = #{license,jdbcType=VARCHAR},
      package_code = #{packageCode,jdbcType=VARCHAR},
      order_days = #{orderDays,jdbcType=BIGINT},
      package_price = #{packagePrice,jdbcType=BIGINT},
      source = #{source,jdbcType=BIGINT},
      start_address = #{startAddress,jdbcType=VARCHAR},
      end_address = #{endAddress,jdbcType=VARCHAR},
      sign_time = #{signTime,jdbcType=TIMESTAMP},
      actual_end_time = #{actualEndTime,jdbcType=TIMESTAMP},
      actual_days = #{actualDays,jdbcType=BIGINT},
      cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      end_type = #{endType,jdbcType=BIGINT},
      cancel_type = #{cancelType,jdbcType=TINYINT},
      master_order_id = #{masterOrderId,jdbcType=BIGINT},
      re_rent_flag = #{reRentFlag,jdbcType=TINYINT},
      deleted = #{deleted,jdbcType=BIGINT},
      last_ver = #{lastVer,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into etc_order
    (merchant_id, order_no, rent_order_no, third_order_no, order_status, start_time, 
      end_time, etc_no, license, package_code, order_days, package_price, source, start_address, 
      end_address, sign_time, actual_end_time, actual_days, cancel_reason, user_id, end_type, 
      cancel_type, master_order_id, re_rent_flag, deleted, last_ver, create_time, op_user_id, 
      op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.orderNo,jdbcType=VARCHAR}, #{item.rentOrderNo,jdbcType=VARCHAR}, 
        #{item.thirdOrderNo,jdbcType=VARCHAR}, #{item.orderStatus,jdbcType=BIGINT}, #{item.startTime,jdbcType=TIMESTAMP}, 
        #{item.endTime,jdbcType=TIMESTAMP}, #{item.etcNo,jdbcType=VARCHAR}, #{item.license,jdbcType=VARCHAR}, 
        #{item.packageCode,jdbcType=VARCHAR}, #{item.orderDays,jdbcType=BIGINT}, #{item.packagePrice,jdbcType=BIGINT}, 
        #{item.source,jdbcType=BIGINT}, #{item.startAddress,jdbcType=VARCHAR}, #{item.endAddress,jdbcType=VARCHAR}, 
        #{item.signTime,jdbcType=TIMESTAMP}, #{item.actualEndTime,jdbcType=TIMESTAMP}, 
        #{item.actualDays,jdbcType=BIGINT}, #{item.cancelReason,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, 
        #{item.endType,jdbcType=BIGINT}, #{item.cancelType,jdbcType=TINYINT}, #{item.masterOrderId,jdbcType=BIGINT}, 
        #{item.reRentFlag,jdbcType=TINYINT}, #{item.deleted,jdbcType=BIGINT}, #{item.lastVer,jdbcType=BIGINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into etc_order (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'order_no'.toString() == column.value">
          #{item.orderNo,jdbcType=VARCHAR}
        </if>
        <if test="'rent_order_no'.toString() == column.value">
          #{item.rentOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="'third_order_no'.toString() == column.value">
          #{item.thirdOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="'order_status'.toString() == column.value">
          #{item.orderStatus,jdbcType=BIGINT}
        </if>
        <if test="'start_time'.toString() == column.value">
          #{item.startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'end_time'.toString() == column.value">
          #{item.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'etc_no'.toString() == column.value">
          #{item.etcNo,jdbcType=VARCHAR}
        </if>
        <if test="'license'.toString() == column.value">
          #{item.license,jdbcType=VARCHAR}
        </if>
        <if test="'package_code'.toString() == column.value">
          #{item.packageCode,jdbcType=VARCHAR}
        </if>
        <if test="'order_days'.toString() == column.value">
          #{item.orderDays,jdbcType=BIGINT}
        </if>
        <if test="'package_price'.toString() == column.value">
          #{item.packagePrice,jdbcType=BIGINT}
        </if>
        <if test="'source'.toString() == column.value">
          #{item.source,jdbcType=BIGINT}
        </if>
        <if test="'start_address'.toString() == column.value">
          #{item.startAddress,jdbcType=VARCHAR}
        </if>
        <if test="'end_address'.toString() == column.value">
          #{item.endAddress,jdbcType=VARCHAR}
        </if>
        <if test="'sign_time'.toString() == column.value">
          #{item.signTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'actual_end_time'.toString() == column.value">
          #{item.actualEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'actual_days'.toString() == column.value">
          #{item.actualDays,jdbcType=BIGINT}
        </if>
        <if test="'cancel_reason'.toString() == column.value">
          #{item.cancelReason,jdbcType=VARCHAR}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
        <if test="'end_type'.toString() == column.value">
          #{item.endType,jdbcType=BIGINT}
        </if>
        <if test="'cancel_type'.toString() == column.value">
          #{item.cancelType,jdbcType=TINYINT}
        </if>
        <if test="'master_order_id'.toString() == column.value">
          #{item.masterOrderId,jdbcType=BIGINT}
        </if>
        <if test="'re_rent_flag'.toString() == column.value">
          #{item.reRentFlag,jdbcType=TINYINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=BIGINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
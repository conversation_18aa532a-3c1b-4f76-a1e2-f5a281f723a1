<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.ex.VehicleIllegalOrderMapperEx">
    <resultMap id="IllegalOrderMap" type="com.ql.rent.entity.trade.ex.VehicleIllegalOrderDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="order_id" jdbcType="BIGINT" property="orderId" />
        <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
        <result column="license_no" jdbcType="VARCHAR" property="licenseNo" />
        <result column="handle_store_id" jdbcType="BIGINT" property="handleStoreId" />
        <result column="vehicle_model_name" jdbcType="VARCHAR" property="vehicleModelName" />
        <result column="illegal_time" jdbcType="TIMESTAMP" property="illegalTime" />
        <result column="illegal_city_id" jdbcType="BIGINT" property="illegalCityId" />
        <result column="illegal_addr" jdbcType="VARCHAR" property="illegalAddr" />
        <result column="illegal_action" jdbcType="VARCHAR" property="illegalAction" />
        <result column="fraction" jdbcType="INTEGER" property="fraction" />
        <result column="penalty_amount" jdbcType="BIGINT" property="penaltyAmount" />
        <result column="handle_status" property="handleStatus"/>
        <result column="contract_damage_amount" jdbcType="BIGINT" property="contractDamageAmount" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="op_time" jdbcType="VARCHAR" property="opTime" />
        <result column="create_time" jdbcType="BIGINT" property="createTime" />
        <result column="from_source" jdbcType="TINYINT" property="fromSource" />
        <result column="real_handle_status" jdbcType="TINYINT" property="realHandleStatus" />
        <result column="transfer_status" jdbcType="TINYINT" property="transferStatus" />
        <result column="transfer_id" jdbcType="BIGINT" property="transferId" />
        <result column="extra" jdbcType="VARCHAR" property="extra" />
    </resultMap>

    <select id="countIllegalOrder" resultType="long">
        SELECT COUNT(1)
        <include refid="where_Clause"/>
    </select>

    <select id="selectIllegalOrder" resultMap="IllegalOrderMap">
        SELECT vio.id, vio.vehicle_id, vio.license_no, vio.handle_store_id, vio.vehicle_model_name,
               vio.illegal_time, vio.illegal_city_id, vio.illegal_addr, vio.illegal_action, vio.fraction,
               vio.penalty_amount, vio.contract_damage_amount, vio.handle_status, vio.op_time, vio.order_id, vio.create_time, vio.from_source,
               oi.id AS order_no, vio.real_handle_status, vio.transfer_status, vio.transfer_id, vio.extra
        <include refid="where_Clause"/>
        ORDER BY ${queryParam.orderBy} ${queryParam.sortType}
        LIMIT #{queryParam.startPos},#{queryParam.pageSize}
    </select>

    <sql id="where_Clause">
        FROM vehicle_illegal_order vio
        LEFT JOIN order_info oi ON vio.order_id = oi.id AND oi.merchant_id = #{merchantId}
            <if test="queryParam.orderId != null"> AND oi.id = #{queryParam.orderId}</if>
            <if test="queryParam.orderNo != null and queryParam.orderNo != ''">AND oi.order_no LIKE CONCAT('%', #{queryParam.orderNo}, '%') </if>
        LEFT JOIN vehicle_illegal_order_result vior ON vio.result_id = vior.id
            <if test="queryParam.paymentStatusList != null and queryParam.paymentStatusList.size() > 0">
               AND vior.penalty_amount IN
                <foreach collection="queryParam.paymentStatusList" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        WHERE vio.deleted = 0
            AND vio.merchant_id = #{merchantId}
        <if test="queryParam.vehicleIdList != null and queryParam.vehicleIdList.size() > 0">
            AND vio.vehicle_id IN
            <foreach collection="queryParam.vehicleIdList" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryParam.orderId != null">AND vio.order_id = #{queryParam.orderId}</if>
        <if test="queryParam.storeId != null">AND vio.handle_store_id = #{queryParam.storeId}</if>
        <if test="queryParam.orderSource != null"> AND vio.source = #{queryParam.orderSource} </if>
        <if test="queryParam.licenseNoList != null and queryParam.licenseNoList.size() > 0">
            AND vio.license_no IN
            <foreach collection="queryParam.licenseNoList" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryParam.handleStatusList != null and queryParam.handleStatusList.size() > 0">
            AND vio.handle_status IN
            <foreach collection="queryParam.handleStatusList" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryParam.realHandleStatusList != null and queryParam.realHandleStatusList.size() > 0">
            AND vio.real_handle_status IN
            <foreach collection="queryParam.realHandleStatusList" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryParam.transferStatusList != null and queryParam.transferStatusList.size() > 0">
            AND vio.transfer_status IN
            <foreach collection="queryParam.transferStatusList" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryParam.pickUpStartDate != null">
            AND oi.pickup_date &gt;= #{queryParam.pickUpStartDate}
        </if>
        <if test="queryParam.pickUpEndDate != null">
            AND oi.pickup_date &lt;= #{queryParam.pickUpEndDate}
        </if>
        <if test="queryParam.returnStartDate != null">
            AND oi.last_return_date &gt;= #{queryParam.returnStartDate}
        </if>
        <if test="queryParam.returnEndDate != null">
            AND oi.last_return_date &lt;= #{queryParam.returnEndDate}
        </if>
        <if test="queryParam.illegalStartTime != null">
            AND vio.illegal_time &gt;= #{queryParam.illegalStartTime}
        </if>
        <if test="queryParam.illegalEndTime != null">
            AND vio.illegal_time &lt;= #{queryParam.illegalEndTime}
        </if>
        <if test="queryParam.createStartTime != null">
            AND vio.create_time &gt;= #{queryParam.createStartTime}
        </if>
        <if test="queryParam.createEndTime != null">
            AND vio.create_time &lt;= #{queryParam.createEndTime}
        </if>
        <if test="queryParam.opStartTime != null">
            AND vio.op_time &gt;= #{queryParam.opStartTime}
        </if>
        <if test="queryParam.opEndTime != null">
            AND vio.op_time &lt;= #{queryParam.opEndTime}
        </if>
    </sql>

    <select id="countVehicleIllegalNum" resultType="com.ql.rent.vo.trade.UnHandlerIllegalOrderNumVO">
        SELECT count(1) AS appendingNum, vehicle_id AS vehicleId
        FROM vehicle_illegal_order
        WHERE deleted = 0
          <if test="vehicleIds != null and vehicleIds.size() > 0">
              AND vehicle_id IN <foreach collection="vehicleIds" open="(" item="item" separator="," close=")"> #{item}</foreach>
          </if>
        AND merchant_id = #{merchantId} AND handle_status = #{unHandleStatus}
        GROUP BY vehicle_id
    </select>
</mapper>
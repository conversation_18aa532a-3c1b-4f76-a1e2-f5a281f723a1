<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.MerchantAccountFollowingMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.MerchantAccountFollowing">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_account_id" jdbcType="BIGINT" property="merchantAccountId" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="following_type" jdbcType="TINYINT" property="followingType" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="business_type" jdbcType="TINYINT" property="businessType" />
    <result column="following_amount" jdbcType="BIGINT" property="followingAmount" />
    <result column="before_amount" jdbcType="BIGINT" property="beforeAmount" />
    <result column="after_amount" jdbcType="BIGINT" property="afterAmount" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_account_id, merchant_id, following_type, business_id, business_type, 
    following_amount, before_amount, after_amount, create_time, create_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.MerchantAccountFollowingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from merchant_account_following
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_account_following
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.MerchantAccountFollowing">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into merchant_account_following (merchant_account_id, merchant_id, following_type, 
      business_id, business_type, following_amount, 
      before_amount, after_amount, create_time, 
      create_user_id)
    values (#{merchantAccountId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, #{followingType,jdbcType=TINYINT}, 
      #{businessId,jdbcType=BIGINT}, #{businessType,jdbcType=TINYINT}, #{followingAmount,jdbcType=BIGINT}, 
      #{beforeAmount,jdbcType=BIGINT}, #{afterAmount,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{createUserId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.MerchantAccountFollowing">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into merchant_account_following
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantAccountId != null">
        merchant_account_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="followingType != null">
        following_type,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="followingAmount != null">
        following_amount,
      </if>
      <if test="beforeAmount != null">
        before_amount,
      </if>
      <if test="afterAmount != null">
        after_amount,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantAccountId != null">
        #{merchantAccountId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="followingType != null">
        #{followingType,jdbcType=TINYINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=TINYINT},
      </if>
      <if test="followingAmount != null">
        #{followingAmount,jdbcType=BIGINT},
      </if>
      <if test="beforeAmount != null">
        #{beforeAmount,jdbcType=BIGINT},
      </if>
      <if test="afterAmount != null">
        #{afterAmount,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.MerchantAccountFollowingExample" resultType="java.lang.Long">
    select count(*) from merchant_account_following
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update merchant_account_following
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantAccountId != null">
        merchant_account_id = #{record.merchantAccountId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.followingType != null">
        following_type = #{record.followingType,jdbcType=TINYINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=TINYINT},
      </if>
      <if test="record.followingAmount != null">
        following_amount = #{record.followingAmount,jdbcType=BIGINT},
      </if>
      <if test="record.beforeAmount != null">
        before_amount = #{record.beforeAmount,jdbcType=BIGINT},
      </if>
      <if test="record.afterAmount != null">
        after_amount = #{record.afterAmount,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update merchant_account_following
    set id = #{record.id,jdbcType=BIGINT},
      merchant_account_id = #{record.merchantAccountId,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      following_type = #{record.followingType,jdbcType=TINYINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      business_type = #{record.businessType,jdbcType=TINYINT},
      following_amount = #{record.followingAmount,jdbcType=BIGINT},
      before_amount = #{record.beforeAmount,jdbcType=BIGINT},
      after_amount = #{record.afterAmount,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      create_user_id = #{record.createUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.MerchantAccountFollowing">
    update merchant_account_following
    <set>
      <if test="merchantAccountId != null">
        merchant_account_id = #{merchantAccountId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="followingType != null">
        following_type = #{followingType,jdbcType=TINYINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=TINYINT},
      </if>
      <if test="followingAmount != null">
        following_amount = #{followingAmount,jdbcType=BIGINT},
      </if>
      <if test="beforeAmount != null">
        before_amount = #{beforeAmount,jdbcType=BIGINT},
      </if>
      <if test="afterAmount != null">
        after_amount = #{afterAmount,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.MerchantAccountFollowing">
    update merchant_account_following
    set merchant_account_id = #{merchantAccountId,jdbcType=BIGINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      following_type = #{followingType,jdbcType=TINYINT},
      business_id = #{businessId,jdbcType=BIGINT},
      business_type = #{businessType,jdbcType=TINYINT},
      following_amount = #{followingAmount,jdbcType=BIGINT},
      before_amount = #{beforeAmount,jdbcType=BIGINT},
      after_amount = #{afterAmount,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      create_user_id = #{createUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into merchant_account_following
    (merchant_account_id, merchant_id, following_type, business_id, business_type, following_amount, 
      before_amount, after_amount, create_time, create_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantAccountId,jdbcType=BIGINT}, #{item.merchantId,jdbcType=BIGINT}, #{item.followingType,jdbcType=TINYINT}, 
        #{item.businessId,jdbcType=BIGINT}, #{item.businessType,jdbcType=TINYINT}, #{item.followingAmount,jdbcType=BIGINT}, 
        #{item.beforeAmount,jdbcType=BIGINT}, #{item.afterAmount,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.createUserId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into merchant_account_following (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_account_id'.toString() == column.value">
          #{item.merchantAccountId,jdbcType=BIGINT}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'following_type'.toString() == column.value">
          #{item.followingType,jdbcType=TINYINT}
        </if>
        <if test="'business_id'.toString() == column.value">
          #{item.businessId,jdbcType=BIGINT}
        </if>
        <if test="'business_type'.toString() == column.value">
          #{item.businessType,jdbcType=TINYINT}
        </if>
        <if test="'following_amount'.toString() == column.value">
          #{item.followingAmount,jdbcType=BIGINT}
        </if>
        <if test="'before_amount'.toString() == column.value">
          #{item.beforeAmount,jdbcType=BIGINT}
        </if>
        <if test="'after_amount'.toString() == column.value">
          #{item.afterAmount,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'create_user_id'.toString() == column.value">
          #{item.createUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.LeaseTermConfigExMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.LeaseTermConfigEx">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="main_id" jdbcType="BIGINT" property="mainId" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="correl_id" jdbcType="BIGINT" property="correlId" />
    <result column="min_rent_term" jdbcType="SMALLINT" property="minRentTerm" />
    <result column="max_rent_term" jdbcType="SMALLINT" property="maxRentTerm" />
    <result column="channel_id" jdbcType="TINYINT" property="channelId" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="vehicle_all" jdbcType="TINYINT" property="vehicleAll" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, main_id, parent_id, type, correl_id, min_rent_term, max_rent_term, channel_id, 
    deleted, vehicle_all
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.LeaseTermConfigExExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from lease_term_config_ex
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lease_term_config_ex
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lease_term_config_ex
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.LeaseTermConfigExExample">
    delete from lease_term_config_ex
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.LeaseTermConfigEx">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lease_term_config_ex (main_id, parent_id, type, 
      correl_id, min_rent_term, max_rent_term, 
      channel_id, deleted, vehicle_all
      )
    values (#{mainId,jdbcType=BIGINT}, #{parentId,jdbcType=BIGINT}, #{type,jdbcType=TINYINT}, 
      #{correlId,jdbcType=BIGINT}, #{minRentTerm,jdbcType=SMALLINT}, #{maxRentTerm,jdbcType=SMALLINT}, 
      #{channelId,jdbcType=TINYINT}, #{deleted,jdbcType=TINYINT}, #{vehicleAll,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.LeaseTermConfigEx">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lease_term_config_ex
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mainId != null">
        main_id,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="correlId != null">
        correl_id,
      </if>
      <if test="minRentTerm != null">
        min_rent_term,
      </if>
      <if test="maxRentTerm != null">
        max_rent_term,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="vehicleAll != null">
        vehicle_all,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mainId != null">
        #{mainId,jdbcType=BIGINT},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="correlId != null">
        #{correlId,jdbcType=BIGINT},
      </if>
      <if test="minRentTerm != null">
        #{minRentTerm,jdbcType=SMALLINT},
      </if>
      <if test="maxRentTerm != null">
        #{maxRentTerm,jdbcType=SMALLINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="vehicleAll != null">
        #{vehicleAll,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.LeaseTermConfigExExample" resultType="java.lang.Long">
    select count(*) from lease_term_config_ex
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lease_term_config_ex
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.mainId != null">
        main_id = #{record.mainId,jdbcType=BIGINT},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=TINYINT},
      </if>
      <if test="record.correlId != null">
        correl_id = #{record.correlId,jdbcType=BIGINT},
      </if>
      <if test="record.minRentTerm != null">
        min_rent_term = #{record.minRentTerm,jdbcType=SMALLINT},
      </if>
      <if test="record.maxRentTerm != null">
        max_rent_term = #{record.maxRentTerm,jdbcType=SMALLINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=TINYINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.vehicleAll != null">
        vehicle_all = #{record.vehicleAll,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lease_term_config_ex
    set id = #{record.id,jdbcType=BIGINT},
      main_id = #{record.mainId,jdbcType=BIGINT},
      parent_id = #{record.parentId,jdbcType=BIGINT},
      type = #{record.type,jdbcType=TINYINT},
      correl_id = #{record.correlId,jdbcType=BIGINT},
      min_rent_term = #{record.minRentTerm,jdbcType=SMALLINT},
      max_rent_term = #{record.maxRentTerm,jdbcType=SMALLINT},
      channel_id = #{record.channelId,jdbcType=TINYINT},
      deleted = #{record.deleted,jdbcType=TINYINT},
      vehicle_all = #{record.vehicleAll,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.LeaseTermConfigEx">
    update lease_term_config_ex
    <set>
      <if test="mainId != null">
        main_id = #{mainId,jdbcType=BIGINT},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="correlId != null">
        correl_id = #{correlId,jdbcType=BIGINT},
      </if>
      <if test="minRentTerm != null">
        min_rent_term = #{minRentTerm,jdbcType=SMALLINT},
      </if>
      <if test="maxRentTerm != null">
        max_rent_term = #{maxRentTerm,jdbcType=SMALLINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="vehicleAll != null">
        vehicle_all = #{vehicleAll,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.LeaseTermConfigEx">
    update lease_term_config_ex
    set main_id = #{mainId,jdbcType=BIGINT},
      parent_id = #{parentId,jdbcType=BIGINT},
      type = #{type,jdbcType=TINYINT},
      correl_id = #{correlId,jdbcType=BIGINT},
      min_rent_term = #{minRentTerm,jdbcType=SMALLINT},
      max_rent_term = #{maxRentTerm,jdbcType=SMALLINT},
      channel_id = #{channelId,jdbcType=TINYINT},
      deleted = #{deleted,jdbcType=TINYINT},
      vehicle_all = #{vehicleAll,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into lease_term_config_ex
    (main_id, parent_id, type, correl_id, min_rent_term, max_rent_term, channel_id, deleted, 
      vehicle_all)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.mainId,jdbcType=BIGINT}, #{item.parentId,jdbcType=BIGINT}, #{item.type,jdbcType=TINYINT}, 
        #{item.correlId,jdbcType=BIGINT}, #{item.minRentTerm,jdbcType=SMALLINT}, #{item.maxRentTerm,jdbcType=SMALLINT}, 
        #{item.channelId,jdbcType=TINYINT}, #{item.deleted,jdbcType=TINYINT}, #{item.vehicleAll,jdbcType=TINYINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into lease_term_config_ex (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'main_id'.toString() == column.value">
          #{item.mainId,jdbcType=BIGINT}
        </if>
        <if test="'parent_id'.toString() == column.value">
          #{item.parentId,jdbcType=BIGINT}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=TINYINT}
        </if>
        <if test="'correl_id'.toString() == column.value">
          #{item.correlId,jdbcType=BIGINT}
        </if>
        <if test="'min_rent_term'.toString() == column.value">
          #{item.minRentTerm,jdbcType=SMALLINT}
        </if>
        <if test="'max_rent_term'.toString() == column.value">
          #{item.maxRentTerm,jdbcType=SMALLINT}
        </if>
        <if test="'channel_id'.toString() == column.value">
          #{item.channelId,jdbcType=TINYINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'vehicle_all'.toString() == column.value">
          #{item.vehicleAll,jdbcType=TINYINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.WkCarTypeInfoMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.WkCarTypeInfo">
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="STATUS" jdbcType="TINYINT" property="status" />
    <result column="LAST_VER" jdbcType="BIGINT" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="car_type_id" jdbcType="VARCHAR" property="carTypeId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="brand_id" jdbcType="VARCHAR" property="brandId" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="series_id" jdbcType="VARCHAR" property="seriesId" />
    <result column="series_name" jdbcType="VARCHAR" property="seriesName" />
    <result column="base_config" jdbcType="VARCHAR" property="baseConfig" />
    <result column="transmission" jdbcType="VARCHAR" property="transmission" />
    <result column="transmission_name" jdbcType="VARCHAR" property="transmissionName" />
    <result column="displacement" jdbcType="VARCHAR" property="displacement" />
    <result column="displacement_name" jdbcType="VARCHAR" property="displacementName" />
    <result column="mileage" jdbcType="INTEGER" property="mileage" />
    <result column="year" jdbcType="VARCHAR" property="year" />
    <result column="carriage" jdbcType="VARCHAR" property="carriage" />
    <result column="car_type_url" jdbcType="VARCHAR" property="carTypeUrl" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="level_name" jdbcType="VARCHAR" property="levelName" />
    <result column="is_local" jdbcType="INTEGER" property="isLocal" />
    <result column="capacity" jdbcType="INTEGER" property="capacity" />
    <result column="door_num" jdbcType="INTEGER" property="doorNum" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    ID, STATUS, LAST_VER, op_user_id, create_time, op_time, car_type_id, name, brand_id, 
    brand_name, series_id, series_name, base_config, transmission, transmission_name, 
    displacement, displacement_name, mileage, year, carriage, car_type_url, level, level_name, 
    is_local, capacity, door_num
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.WkCarTypeInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from wk_car_type_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wk_car_type_info
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wk_car_type_info
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.WkCarTypeInfoExample">
    delete from wk_car_type_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.WkCarTypeInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into wk_car_type_info (STATUS, LAST_VER, op_user_id, 
      create_time, op_time, car_type_id, 
      name, brand_id, brand_name, 
      series_id, series_name, base_config, 
      transmission, transmission_name, displacement, 
      displacement_name, mileage, year, 
      carriage, car_type_url, level, 
      level_name, is_local, capacity, 
      door_num)
    values (#{status,jdbcType=TINYINT}, #{lastVer,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, #{carTypeId,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{brandId,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR}, 
      #{seriesId,jdbcType=VARCHAR}, #{seriesName,jdbcType=VARCHAR}, #{baseConfig,jdbcType=VARCHAR}, 
      #{transmission,jdbcType=VARCHAR}, #{transmissionName,jdbcType=VARCHAR}, #{displacement,jdbcType=VARCHAR}, 
      #{displacementName,jdbcType=VARCHAR}, #{mileage,jdbcType=INTEGER}, #{year,jdbcType=VARCHAR}, 
      #{carriage,jdbcType=VARCHAR}, #{carTypeUrl,jdbcType=VARCHAR}, #{level,jdbcType=VARCHAR}, 
      #{levelName,jdbcType=VARCHAR}, #{isLocal,jdbcType=INTEGER}, #{capacity,jdbcType=INTEGER}, 
      #{doorNum,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.WkCarTypeInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into wk_car_type_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="status != null">
        STATUS,
      </if>
      <if test="lastVer != null">
        LAST_VER,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="carTypeId != null">
        car_type_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="brandId != null">
        brand_id,
      </if>
      <if test="brandName != null">
        brand_name,
      </if>
      <if test="seriesId != null">
        series_id,
      </if>
      <if test="seriesName != null">
        series_name,
      </if>
      <if test="baseConfig != null">
        base_config,
      </if>
      <if test="transmission != null">
        transmission,
      </if>
      <if test="transmissionName != null">
        transmission_name,
      </if>
      <if test="displacement != null">
        displacement,
      </if>
      <if test="displacementName != null">
        displacement_name,
      </if>
      <if test="mileage != null">
        mileage,
      </if>
      <if test="year != null">
        year,
      </if>
      <if test="carriage != null">
        carriage,
      </if>
      <if test="carTypeUrl != null">
        car_type_url,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="levelName != null">
        level_name,
      </if>
      <if test="isLocal != null">
        is_local,
      </if>
      <if test="capacity != null">
        capacity,
      </if>
      <if test="doorNum != null">
        door_num,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="carTypeId != null">
        #{carTypeId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="seriesId != null">
        #{seriesId,jdbcType=VARCHAR},
      </if>
      <if test="seriesName != null">
        #{seriesName,jdbcType=VARCHAR},
      </if>
      <if test="baseConfig != null">
        #{baseConfig,jdbcType=VARCHAR},
      </if>
      <if test="transmission != null">
        #{transmission,jdbcType=VARCHAR},
      </if>
      <if test="transmissionName != null">
        #{transmissionName,jdbcType=VARCHAR},
      </if>
      <if test="displacement != null">
        #{displacement,jdbcType=VARCHAR},
      </if>
      <if test="displacementName != null">
        #{displacementName,jdbcType=VARCHAR},
      </if>
      <if test="mileage != null">
        #{mileage,jdbcType=INTEGER},
      </if>
      <if test="year != null">
        #{year,jdbcType=VARCHAR},
      </if>
      <if test="carriage != null">
        #{carriage,jdbcType=VARCHAR},
      </if>
      <if test="carTypeUrl != null">
        #{carTypeUrl,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=VARCHAR},
      </if>
      <if test="levelName != null">
        #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="isLocal != null">
        #{isLocal,jdbcType=INTEGER},
      </if>
      <if test="capacity != null">
        #{capacity,jdbcType=INTEGER},
      </if>
      <if test="doorNum != null">
        #{doorNum,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.WkCarTypeInfoExample" resultType="java.lang.Long">
    select count(*) from wk_car_type_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wk_car_type_info
    <set>
      <if test="record.id != null">
        ID = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        STATUS = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        LAST_VER = #{record.lastVer,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.carTypeId != null">
        car_type_id = #{record.carTypeId,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.brandId != null">
        brand_id = #{record.brandId,jdbcType=VARCHAR},
      </if>
      <if test="record.brandName != null">
        brand_name = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.seriesId != null">
        series_id = #{record.seriesId,jdbcType=VARCHAR},
      </if>
      <if test="record.seriesName != null">
        series_name = #{record.seriesName,jdbcType=VARCHAR},
      </if>
      <if test="record.baseConfig != null">
        base_config = #{record.baseConfig,jdbcType=VARCHAR},
      </if>
      <if test="record.transmission != null">
        transmission = #{record.transmission,jdbcType=VARCHAR},
      </if>
      <if test="record.transmissionName != null">
        transmission_name = #{record.transmissionName,jdbcType=VARCHAR},
      </if>
      <if test="record.displacement != null">
        displacement = #{record.displacement,jdbcType=VARCHAR},
      </if>
      <if test="record.displacementName != null">
        displacement_name = #{record.displacementName,jdbcType=VARCHAR},
      </if>
      <if test="record.mileage != null">
        mileage = #{record.mileage,jdbcType=INTEGER},
      </if>
      <if test="record.year != null">
        year = #{record.year,jdbcType=VARCHAR},
      </if>
      <if test="record.carriage != null">
        carriage = #{record.carriage,jdbcType=VARCHAR},
      </if>
      <if test="record.carTypeUrl != null">
        car_type_url = #{record.carTypeUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.level != null">
        level = #{record.level,jdbcType=VARCHAR},
      </if>
      <if test="record.levelName != null">
        level_name = #{record.levelName,jdbcType=VARCHAR},
      </if>
      <if test="record.isLocal != null">
        is_local = #{record.isLocal,jdbcType=INTEGER},
      </if>
      <if test="record.capacity != null">
        capacity = #{record.capacity,jdbcType=INTEGER},
      </if>
      <if test="record.doorNum != null">
        door_num = #{record.doorNum,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wk_car_type_info
    set ID = #{record.id,jdbcType=BIGINT},
      STATUS = #{record.status,jdbcType=TINYINT},
      LAST_VER = #{record.lastVer,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      car_type_id = #{record.carTypeId,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      brand_id = #{record.brandId,jdbcType=VARCHAR},
      brand_name = #{record.brandName,jdbcType=VARCHAR},
      series_id = #{record.seriesId,jdbcType=VARCHAR},
      series_name = #{record.seriesName,jdbcType=VARCHAR},
      base_config = #{record.baseConfig,jdbcType=VARCHAR},
      transmission = #{record.transmission,jdbcType=VARCHAR},
      transmission_name = #{record.transmissionName,jdbcType=VARCHAR},
      displacement = #{record.displacement,jdbcType=VARCHAR},
      displacement_name = #{record.displacementName,jdbcType=VARCHAR},
      mileage = #{record.mileage,jdbcType=INTEGER},
      year = #{record.year,jdbcType=VARCHAR},
      carriage = #{record.carriage,jdbcType=VARCHAR},
      car_type_url = #{record.carTypeUrl,jdbcType=VARCHAR},
      level = #{record.level,jdbcType=VARCHAR},
      level_name = #{record.levelName,jdbcType=VARCHAR},
      is_local = #{record.isLocal,jdbcType=INTEGER},
      capacity = #{record.capacity,jdbcType=INTEGER},
      door_num = #{record.doorNum,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.WkCarTypeInfo">
    update wk_car_type_info
    <set>
      <if test="status != null">
        STATUS = #{status,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        LAST_VER = #{lastVer,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="carTypeId != null">
        car_type_id = #{carTypeId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="brandId != null">
        brand_id = #{brandId,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        brand_name = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="seriesId != null">
        series_id = #{seriesId,jdbcType=VARCHAR},
      </if>
      <if test="seriesName != null">
        series_name = #{seriesName,jdbcType=VARCHAR},
      </if>
      <if test="baseConfig != null">
        base_config = #{baseConfig,jdbcType=VARCHAR},
      </if>
      <if test="transmission != null">
        transmission = #{transmission,jdbcType=VARCHAR},
      </if>
      <if test="transmissionName != null">
        transmission_name = #{transmissionName,jdbcType=VARCHAR},
      </if>
      <if test="displacement != null">
        displacement = #{displacement,jdbcType=VARCHAR},
      </if>
      <if test="displacementName != null">
        displacement_name = #{displacementName,jdbcType=VARCHAR},
      </if>
      <if test="mileage != null">
        mileage = #{mileage,jdbcType=INTEGER},
      </if>
      <if test="year != null">
        year = #{year,jdbcType=VARCHAR},
      </if>
      <if test="carriage != null">
        carriage = #{carriage,jdbcType=VARCHAR},
      </if>
      <if test="carTypeUrl != null">
        car_type_url = #{carTypeUrl,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=VARCHAR},
      </if>
      <if test="levelName != null">
        level_name = #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="isLocal != null">
        is_local = #{isLocal,jdbcType=INTEGER},
      </if>
      <if test="capacity != null">
        capacity = #{capacity,jdbcType=INTEGER},
      </if>
      <if test="doorNum != null">
        door_num = #{doorNum,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.WkCarTypeInfo">
    update wk_car_type_info
    set STATUS = #{status,jdbcType=TINYINT},
      LAST_VER = #{lastVer,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      car_type_id = #{carTypeId,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      brand_id = #{brandId,jdbcType=VARCHAR},
      brand_name = #{brandName,jdbcType=VARCHAR},
      series_id = #{seriesId,jdbcType=VARCHAR},
      series_name = #{seriesName,jdbcType=VARCHAR},
      base_config = #{baseConfig,jdbcType=VARCHAR},
      transmission = #{transmission,jdbcType=VARCHAR},
      transmission_name = #{transmissionName,jdbcType=VARCHAR},
      displacement = #{displacement,jdbcType=VARCHAR},
      displacement_name = #{displacementName,jdbcType=VARCHAR},
      mileage = #{mileage,jdbcType=INTEGER},
      year = #{year,jdbcType=VARCHAR},
      carriage = #{carriage,jdbcType=VARCHAR},
      car_type_url = #{carTypeUrl,jdbcType=VARCHAR},
      level = #{level,jdbcType=VARCHAR},
      level_name = #{levelName,jdbcType=VARCHAR},
      is_local = #{isLocal,jdbcType=INTEGER},
      capacity = #{capacity,jdbcType=INTEGER},
      door_num = #{doorNum,jdbcType=INTEGER}
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into wk_car_type_info
    (STATUS, LAST_VER, op_user_id, create_time, op_time, car_type_id, name, brand_id, 
      brand_name, series_id, series_name, base_config, transmission, transmission_name, 
      displacement, displacement_name, mileage, year, carriage, car_type_url, level, 
      level_name, is_local, capacity, door_num)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.status,jdbcType=TINYINT}, #{item.lastVer,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, #{item.carTypeId,jdbcType=VARCHAR}, 
        #{item.name,jdbcType=VARCHAR}, #{item.brandId,jdbcType=VARCHAR}, #{item.brandName,jdbcType=VARCHAR}, 
        #{item.seriesId,jdbcType=VARCHAR}, #{item.seriesName,jdbcType=VARCHAR}, #{item.baseConfig,jdbcType=VARCHAR}, 
        #{item.transmission,jdbcType=VARCHAR}, #{item.transmissionName,jdbcType=VARCHAR}, 
        #{item.displacement,jdbcType=VARCHAR}, #{item.displacementName,jdbcType=VARCHAR}, 
        #{item.mileage,jdbcType=INTEGER}, #{item.year,jdbcType=VARCHAR}, #{item.carriage,jdbcType=VARCHAR}, 
        #{item.carTypeUrl,jdbcType=VARCHAR}, #{item.level,jdbcType=VARCHAR}, #{item.levelName,jdbcType=VARCHAR}, 
        #{item.isLocal,jdbcType=INTEGER}, #{item.capacity,jdbcType=INTEGER}, #{item.doorNum,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into wk_car_type_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'STATUS'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'LAST_VER'.toString() == column.value">
          #{item.lastVer,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'car_type_id'.toString() == column.value">
          #{item.carTypeId,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'brand_id'.toString() == column.value">
          #{item.brandId,jdbcType=VARCHAR}
        </if>
        <if test="'brand_name'.toString() == column.value">
          #{item.brandName,jdbcType=VARCHAR}
        </if>
        <if test="'series_id'.toString() == column.value">
          #{item.seriesId,jdbcType=VARCHAR}
        </if>
        <if test="'series_name'.toString() == column.value">
          #{item.seriesName,jdbcType=VARCHAR}
        </if>
        <if test="'base_config'.toString() == column.value">
          #{item.baseConfig,jdbcType=VARCHAR}
        </if>
        <if test="'transmission'.toString() == column.value">
          #{item.transmission,jdbcType=VARCHAR}
        </if>
        <if test="'transmission_name'.toString() == column.value">
          #{item.transmissionName,jdbcType=VARCHAR}
        </if>
        <if test="'displacement'.toString() == column.value">
          #{item.displacement,jdbcType=VARCHAR}
        </if>
        <if test="'displacement_name'.toString() == column.value">
          #{item.displacementName,jdbcType=VARCHAR}
        </if>
        <if test="'mileage'.toString() == column.value">
          #{item.mileage,jdbcType=INTEGER}
        </if>
        <if test="'year'.toString() == column.value">
          #{item.year,jdbcType=VARCHAR}
        </if>
        <if test="'carriage'.toString() == column.value">
          #{item.carriage,jdbcType=VARCHAR}
        </if>
        <if test="'car_type_url'.toString() == column.value">
          #{item.carTypeUrl,jdbcType=VARCHAR}
        </if>
        <if test="'level'.toString() == column.value">
          #{item.level,jdbcType=VARCHAR}
        </if>
        <if test="'level_name'.toString() == column.value">
          #{item.levelName,jdbcType=VARCHAR}
        </if>
        <if test="'is_local'.toString() == column.value">
          #{item.isLocal,jdbcType=INTEGER}
        </if>
        <if test="'capacity'.toString() == column.value">
          #{item.capacity,jdbcType=INTEGER}
        </if>
        <if test="'door_num'.toString() == column.value">
          #{item.doorNum,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
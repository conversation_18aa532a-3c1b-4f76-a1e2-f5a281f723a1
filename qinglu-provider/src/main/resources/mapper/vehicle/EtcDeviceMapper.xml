<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.EtcDeviceMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.EtcDevice">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="third_vehicle_id" jdbcType="VARCHAR" property="thirdVehicleId" />
    <result column="etc_no" jdbcType="VARCHAR" property="etcNo" />
    <result column="ic_no" jdbcType="VARCHAR" property="icNo" />
    <result column="online" jdbcType="TINYINT" property="online" />
    <result column="hard_nink_status" jdbcType="TINYINT" property="hardNinkStatus" />
    <result column="work_status" jdbcType="TINYINT" property="workStatus" />
    <result column="service" jdbcType="TINYINT" property="service" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="plate_color" jdbcType="TINYINT" property="plateColor" />
    <result column="axles" jdbcType="VARCHAR" property="axles" />
    <result column="length" jdbcType="VARCHAR" property="length" />
    <result column="width" jdbcType="VARCHAR" property="width" />
    <result column="height" jdbcType="VARCHAR" property="height" />
    <result column="total_weight" jdbcType="VARCHAR" property="totalWeight" />
    <result column="gross_wass" jdbcType="VARCHAR" property="grossWass" />
    <result column="register_date" jdbcType="TIMESTAMP" property="registerDate" />
    <result column="grant_date" jdbcType="TIMESTAMP" property="grantDate" />
    <result column="owner_name" jdbcType="VARCHAR" property="ownerName" />
    <result column="activate_status" jdbcType="TINYINT" property="activateStatus" />
    <result column="availability_status" jdbcType="TINYINT" property="availabilityStatus" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="sample_is" jdbcType="TINYINT" property="sampleIs" />
    <result column="license" jdbcType="VARCHAR" property="license" />
    <result column="etc_apply_order_id" jdbcType="VARCHAR" property="etcApplyOrderId" />
    <result column="biz_agreement_no" jdbcType="VARCHAR" property="bizAgreementNo" />
    <result column="device_status_detail" jdbcType="VARCHAR" property="deviceStatusDetail" />
    <result column="etc_source" jdbcType="TINYINT" property="etcSource" />
    <result column="publisher" jdbcType="TINYINT" property="publisher" />
    <result column="blacklist_status" jdbcType="TINYINT" property="blacklistStatus" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.ql.rent.entity.vehicle.EtcDevice">
    <result column="gis" jdbcType="BINARY" property="gis" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>

      <if test="vehicleStatus != null">
        and exists(select 1 from vehicle_info where vehicle_info.id=etc_device.vehicle_id and vehicle_info.vehicle_status=#{vehicleStatus})
      </if>

      <if test="frameNum != null and frameNum != ''">
        and exists(select 1 from vehicle_info where vehicle_info.id=etc_device.vehicle_id and vehicle_info.frame_num like #{frameNum})
      </if>

      <if test="vehicleModelIdList != null">
        and exists(select 1 from vehicle_info
        where vehicle_info.id=etc_device.vehicle_id
        and vehicle_info.vehicle_model_id in
        <foreach item="item" index="index" collection="vehicleModelIdList" open="(" separator="," close=")">
          #{item,jdbcType=BIGINT}
        </foreach>
        )
      </if>

    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, store_id, vehicle_model_id, vehicle_id, third_vehicle_id, etc_no, 
    ic_no, online, hard_nink_status, work_status, service, remark, plate_color, axles, 
    length, width, height, total_weight, gross_wass, register_date, grant_date, owner_name, 
    activate_status, availability_status, create_time, sample_is, license, etc_apply_order_id, 
    biz_agreement_no, device_status_detail, etc_source, publisher, blacklist_status, 
    op_time, last_ver, deleted, op_user_id
  </sql>
  <sql id="Blob_Column_List">
    gis
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.ql.rent.entity.vehicle.EtcDeviceExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from etc_device
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.EtcDeviceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from etc_device
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from etc_device
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from etc_device
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.EtcDeviceExample">
    delete from etc_device
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.EtcDevice">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into etc_device (merchant_id, store_id, vehicle_model_id, 
      vehicle_id, third_vehicle_id, etc_no, 
      ic_no, online, hard_nink_status, 
      work_status, service, remark, 
      plate_color, axles, length, 
      width, height, total_weight, 
      gross_wass, register_date, grant_date, 
      owner_name, activate_status, availability_status, 
      create_time, sample_is, license, 
      etc_apply_order_id, biz_agreement_no, device_status_detail, 
      etc_source, publisher, blacklist_status, 
      op_time, last_ver, deleted, 
      op_user_id, gis)
    values (#{merchantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{vehicleModelId,jdbcType=BIGINT}, 
      #{vehicleId,jdbcType=BIGINT}, #{thirdVehicleId,jdbcType=VARCHAR}, #{etcNo,jdbcType=VARCHAR}, 
      #{icNo,jdbcType=VARCHAR}, #{online,jdbcType=TINYINT}, #{hardNinkStatus,jdbcType=TINYINT}, 
      #{workStatus,jdbcType=TINYINT}, #{service,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, 
      #{plateColor,jdbcType=TINYINT}, #{axles,jdbcType=VARCHAR}, #{length,jdbcType=VARCHAR}, 
      #{width,jdbcType=VARCHAR}, #{height,jdbcType=VARCHAR}, #{totalWeight,jdbcType=VARCHAR}, 
      #{grossWass,jdbcType=VARCHAR}, #{registerDate,jdbcType=TIMESTAMP}, #{grantDate,jdbcType=TIMESTAMP}, 
      #{ownerName,jdbcType=VARCHAR}, #{activateStatus,jdbcType=TINYINT}, #{availabilityStatus,jdbcType=TINYINT}, 
      #{createTime,jdbcType=BIGINT}, #{sampleIs,jdbcType=TINYINT}, #{license,jdbcType=VARCHAR}, 
      #{etcApplyOrderId,jdbcType=VARCHAR}, #{bizAgreementNo,jdbcType=VARCHAR}, #{deviceStatusDetail,jdbcType=VARCHAR}, 
      #{etcSource,jdbcType=TINYINT}, #{publisher,jdbcType=TINYINT}, #{blacklistStatus,jdbcType=TINYINT}, 
      #{opTime,jdbcType=BIGINT}, #{lastVer,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT}, 
      #{opUserId,jdbcType=BIGINT}, #{gis,jdbcType=BINARY})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.EtcDevice">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into etc_device
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="thirdVehicleId != null">
        third_vehicle_id,
      </if>
      <if test="etcNo != null">
        etc_no,
      </if>
      <if test="icNo != null">
        ic_no,
      </if>
      <if test="online != null">
        online,
      </if>
      <if test="hardNinkStatus != null">
        hard_nink_status,
      </if>
      <if test="workStatus != null">
        work_status,
      </if>
      <if test="service != null">
        service,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="plateColor != null">
        plate_color,
      </if>
      <if test="axles != null">
        axles,
      </if>
      <if test="length != null">
        length,
      </if>
      <if test="width != null">
        width,
      </if>
      <if test="height != null">
        height,
      </if>
      <if test="totalWeight != null">
        total_weight,
      </if>
      <if test="grossWass != null">
        gross_wass,
      </if>
      <if test="registerDate != null">
        register_date,
      </if>
      <if test="grantDate != null">
        grant_date,
      </if>
      <if test="ownerName != null">
        owner_name,
      </if>
      <if test="activateStatus != null">
        activate_status,
      </if>
      <if test="availabilityStatus != null">
        availability_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="sampleIs != null">
        sample_is,
      </if>
      <if test="license != null">
        license,
      </if>
      <if test="etcApplyOrderId != null">
        etc_apply_order_id,
      </if>
      <if test="bizAgreementNo != null">
        biz_agreement_no,
      </if>
      <if test="deviceStatusDetail != null">
        device_status_detail,
      </if>
      <if test="etcSource != null">
        etc_source,
      </if>
      <if test="publisher != null">
        publisher,
      </if>
      <if test="blacklistStatus != null">
        blacklist_status,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="gis != null">
        gis,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="thirdVehicleId != null">
        #{thirdVehicleId,jdbcType=VARCHAR},
      </if>
      <if test="etcNo != null">
        #{etcNo,jdbcType=VARCHAR},
      </if>
      <if test="icNo != null">
        #{icNo,jdbcType=VARCHAR},
      </if>
      <if test="online != null">
        #{online,jdbcType=TINYINT},
      </if>
      <if test="hardNinkStatus != null">
        #{hardNinkStatus,jdbcType=TINYINT},
      </if>
      <if test="workStatus != null">
        #{workStatus,jdbcType=TINYINT},
      </if>
      <if test="service != null">
        #{service,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="plateColor != null">
        #{plateColor,jdbcType=TINYINT},
      </if>
      <if test="axles != null">
        #{axles,jdbcType=VARCHAR},
      </if>
      <if test="length != null">
        #{length,jdbcType=VARCHAR},
      </if>
      <if test="width != null">
        #{width,jdbcType=VARCHAR},
      </if>
      <if test="height != null">
        #{height,jdbcType=VARCHAR},
      </if>
      <if test="totalWeight != null">
        #{totalWeight,jdbcType=VARCHAR},
      </if>
      <if test="grossWass != null">
        #{grossWass,jdbcType=VARCHAR},
      </if>
      <if test="registerDate != null">
        #{registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="grantDate != null">
        #{grantDate,jdbcType=TIMESTAMP},
      </if>
      <if test="ownerName != null">
        #{ownerName,jdbcType=VARCHAR},
      </if>
      <if test="activateStatus != null">
        #{activateStatus,jdbcType=TINYINT},
      </if>
      <if test="availabilityStatus != null">
        #{availabilityStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="sampleIs != null">
        #{sampleIs,jdbcType=TINYINT},
      </if>
      <if test="license != null">
        #{license,jdbcType=VARCHAR},
      </if>
      <if test="etcApplyOrderId != null">
        #{etcApplyOrderId,jdbcType=VARCHAR},
      </if>
      <if test="bizAgreementNo != null">
        #{bizAgreementNo,jdbcType=VARCHAR},
      </if>
      <if test="deviceStatusDetail != null">
        #{deviceStatusDetail,jdbcType=VARCHAR},
      </if>
      <if test="etcSource != null">
        #{etcSource,jdbcType=TINYINT},
      </if>
      <if test="publisher != null">
        #{publisher,jdbcType=TINYINT},
      </if>
      <if test="blacklistStatus != null">
        #{blacklistStatus,jdbcType=TINYINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="gis != null">
        #{gis,jdbcType=BINARY},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.EtcDeviceExample" resultType="java.lang.Long">
    select count(*) from etc_device
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update etc_device
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="record.thirdVehicleId != null">
        third_vehicle_id = #{record.thirdVehicleId,jdbcType=VARCHAR},
      </if>
      <if test="record.etcNo != null">
        etc_no = #{record.etcNo,jdbcType=VARCHAR},
      </if>
      <if test="record.icNo != null">
        ic_no = #{record.icNo,jdbcType=VARCHAR},
      </if>
      <if test="record.online != null">
        online = #{record.online,jdbcType=TINYINT},
      </if>
      <if test="record.hardNinkStatus != null">
        hard_nink_status = #{record.hardNinkStatus,jdbcType=TINYINT},
      </if>
      <if test="record.workStatus != null">
        work_status = #{record.workStatus,jdbcType=TINYINT},
      </if>
      <if test="record.service != null">
        service = #{record.service,jdbcType=TINYINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.plateColor != null">
        plate_color = #{record.plateColor,jdbcType=TINYINT},
      </if>
      <if test="record.axles != null">
        axles = #{record.axles,jdbcType=VARCHAR},
      </if>
      <if test="record.length != null">
        length = #{record.length,jdbcType=VARCHAR},
      </if>
      <if test="record.width != null">
        width = #{record.width,jdbcType=VARCHAR},
      </if>
      <if test="record.height != null">
        height = #{record.height,jdbcType=VARCHAR},
      </if>
      <if test="record.totalWeight != null">
        total_weight = #{record.totalWeight,jdbcType=VARCHAR},
      </if>
      <if test="record.grossWass != null">
        gross_wass = #{record.grossWass,jdbcType=VARCHAR},
      </if>
      <if test="record.registerDate != null">
        register_date = #{record.registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.grantDate != null">
        grant_date = #{record.grantDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ownerName != null">
        owner_name = #{record.ownerName,jdbcType=VARCHAR},
      </if>
      <if test="record.activateStatus != null">
        activate_status = #{record.activateStatus,jdbcType=TINYINT},
      </if>
      <if test="record.availabilityStatus != null">
        availability_status = #{record.availabilityStatus,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.sampleIs != null">
        sample_is = #{record.sampleIs,jdbcType=TINYINT},
      </if>
      <if test="record.license != null">
        license = #{record.license,jdbcType=VARCHAR},
      </if>
      <if test="record.etcApplyOrderId != null">
        etc_apply_order_id = #{record.etcApplyOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.bizAgreementNo != null">
        biz_agreement_no = #{record.bizAgreementNo,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceStatusDetail != null">
        device_status_detail = #{record.deviceStatusDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.etcSource != null">
        etc_source = #{record.etcSource,jdbcType=TINYINT},
      </if>
      <if test="record.publisher != null">
        publisher = #{record.publisher,jdbcType=TINYINT},
      </if>
      <if test="record.blacklistStatus != null">
        blacklist_status = #{record.blacklistStatus,jdbcType=TINYINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.gis != null">
        gis = #{record.gis,jdbcType=BINARY},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update etc_device
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      third_vehicle_id = #{record.thirdVehicleId,jdbcType=VARCHAR},
      etc_no = #{record.etcNo,jdbcType=VARCHAR},
      ic_no = #{record.icNo,jdbcType=VARCHAR},
      online = #{record.online,jdbcType=TINYINT},
      hard_nink_status = #{record.hardNinkStatus,jdbcType=TINYINT},
      work_status = #{record.workStatus,jdbcType=TINYINT},
      service = #{record.service,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      plate_color = #{record.plateColor,jdbcType=TINYINT},
      axles = #{record.axles,jdbcType=VARCHAR},
      length = #{record.length,jdbcType=VARCHAR},
      width = #{record.width,jdbcType=VARCHAR},
      height = #{record.height,jdbcType=VARCHAR},
      total_weight = #{record.totalWeight,jdbcType=VARCHAR},
      gross_wass = #{record.grossWass,jdbcType=VARCHAR},
      register_date = #{record.registerDate,jdbcType=TIMESTAMP},
      grant_date = #{record.grantDate,jdbcType=TIMESTAMP},
      owner_name = #{record.ownerName,jdbcType=VARCHAR},
      activate_status = #{record.activateStatus,jdbcType=TINYINT},
      availability_status = #{record.availabilityStatus,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      sample_is = #{record.sampleIs,jdbcType=TINYINT},
      license = #{record.license,jdbcType=VARCHAR},
      etc_apply_order_id = #{record.etcApplyOrderId,jdbcType=VARCHAR},
      biz_agreement_no = #{record.bizAgreementNo,jdbcType=VARCHAR},
      device_status_detail = #{record.deviceStatusDetail,jdbcType=VARCHAR},
      etc_source = #{record.etcSource,jdbcType=TINYINT},
      publisher = #{record.publisher,jdbcType=TINYINT},
      blacklist_status = #{record.blacklistStatus,jdbcType=TINYINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=TINYINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      gis = #{record.gis,jdbcType=BINARY}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update etc_device
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      third_vehicle_id = #{record.thirdVehicleId,jdbcType=VARCHAR},
      etc_no = #{record.etcNo,jdbcType=VARCHAR},
      ic_no = #{record.icNo,jdbcType=VARCHAR},
      online = #{record.online,jdbcType=TINYINT},
      hard_nink_status = #{record.hardNinkStatus,jdbcType=TINYINT},
      work_status = #{record.workStatus,jdbcType=TINYINT},
      service = #{record.service,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      plate_color = #{record.plateColor,jdbcType=TINYINT},
      axles = #{record.axles,jdbcType=VARCHAR},
      length = #{record.length,jdbcType=VARCHAR},
      width = #{record.width,jdbcType=VARCHAR},
      height = #{record.height,jdbcType=VARCHAR},
      total_weight = #{record.totalWeight,jdbcType=VARCHAR},
      gross_wass = #{record.grossWass,jdbcType=VARCHAR},
      register_date = #{record.registerDate,jdbcType=TIMESTAMP},
      grant_date = #{record.grantDate,jdbcType=TIMESTAMP},
      owner_name = #{record.ownerName,jdbcType=VARCHAR},
      activate_status = #{record.activateStatus,jdbcType=TINYINT},
      availability_status = #{record.availabilityStatus,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      sample_is = #{record.sampleIs,jdbcType=TINYINT},
      license = #{record.license,jdbcType=VARCHAR},
      etc_apply_order_id = #{record.etcApplyOrderId,jdbcType=VARCHAR},
      biz_agreement_no = #{record.bizAgreementNo,jdbcType=VARCHAR},
      device_status_detail = #{record.deviceStatusDetail,jdbcType=VARCHAR},
      etc_source = #{record.etcSource,jdbcType=TINYINT},
      publisher = #{record.publisher,jdbcType=TINYINT},
      blacklist_status = #{record.blacklistStatus,jdbcType=TINYINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=TINYINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.EtcDevice">
    update etc_device
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="thirdVehicleId != null">
        third_vehicle_id = #{thirdVehicleId,jdbcType=VARCHAR},
      </if>
      <if test="etcNo != null">
        etc_no = #{etcNo,jdbcType=VARCHAR},
      </if>
      <if test="icNo != null">
        ic_no = #{icNo,jdbcType=VARCHAR},
      </if>
      <if test="online != null">
        online = #{online,jdbcType=TINYINT},
      </if>
      <if test="hardNinkStatus != null">
        hard_nink_status = #{hardNinkStatus,jdbcType=TINYINT},
      </if>
      <if test="workStatus != null">
        work_status = #{workStatus,jdbcType=TINYINT},
      </if>
      <if test="service != null">
        service = #{service,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="plateColor != null">
        plate_color = #{plateColor,jdbcType=TINYINT},
      </if>
      <if test="axles != null">
        axles = #{axles,jdbcType=VARCHAR},
      </if>
      <if test="length != null">
        length = #{length,jdbcType=VARCHAR},
      </if>
      <if test="width != null">
        width = #{width,jdbcType=VARCHAR},
      </if>
      <if test="height != null">
        height = #{height,jdbcType=VARCHAR},
      </if>
      <if test="totalWeight != null">
        total_weight = #{totalWeight,jdbcType=VARCHAR},
      </if>
      <if test="grossWass != null">
        gross_wass = #{grossWass,jdbcType=VARCHAR},
      </if>
      <if test="registerDate != null">
        register_date = #{registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="grantDate != null">
        grant_date = #{grantDate,jdbcType=TIMESTAMP},
      </if>
      <if test="ownerName != null">
        owner_name = #{ownerName,jdbcType=VARCHAR},
      </if>
      <if test="activateStatus != null">
        activate_status = #{activateStatus,jdbcType=TINYINT},
      </if>
      <if test="availabilityStatus != null">
        availability_status = #{availabilityStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="sampleIs != null">
        sample_is = #{sampleIs,jdbcType=TINYINT},
      </if>
      <if test="license != null">
        license = #{license,jdbcType=VARCHAR},
      </if>
      <if test="etcApplyOrderId != null">
        etc_apply_order_id = #{etcApplyOrderId,jdbcType=VARCHAR},
      </if>
      <if test="bizAgreementNo != null">
        biz_agreement_no = #{bizAgreementNo,jdbcType=VARCHAR},
      </if>
      <if test="deviceStatusDetail != null">
        device_status_detail = #{deviceStatusDetail,jdbcType=VARCHAR},
      </if>
      <if test="etcSource != null">
        etc_source = #{etcSource,jdbcType=TINYINT},
      </if>
      <if test="publisher != null">
        publisher = #{publisher,jdbcType=TINYINT},
      </if>
      <if test="blacklistStatus != null">
        blacklist_status = #{blacklistStatus,jdbcType=TINYINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="gis != null">
        gis = #{gis,jdbcType=BINARY},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.ql.rent.entity.vehicle.EtcDevice">
    update etc_device
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      third_vehicle_id = #{thirdVehicleId,jdbcType=VARCHAR},
      etc_no = #{etcNo,jdbcType=VARCHAR},
      ic_no = #{icNo,jdbcType=VARCHAR},
      online = #{online,jdbcType=TINYINT},
      hard_nink_status = #{hardNinkStatus,jdbcType=TINYINT},
      work_status = #{workStatus,jdbcType=TINYINT},
      service = #{service,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      plate_color = #{plateColor,jdbcType=TINYINT},
      axles = #{axles,jdbcType=VARCHAR},
      length = #{length,jdbcType=VARCHAR},
      width = #{width,jdbcType=VARCHAR},
      height = #{height,jdbcType=VARCHAR},
      total_weight = #{totalWeight,jdbcType=VARCHAR},
      gross_wass = #{grossWass,jdbcType=VARCHAR},
      register_date = #{registerDate,jdbcType=TIMESTAMP},
      grant_date = #{grantDate,jdbcType=TIMESTAMP},
      owner_name = #{ownerName,jdbcType=VARCHAR},
      activate_status = #{activateStatus,jdbcType=TINYINT},
      availability_status = #{availabilityStatus,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      sample_is = #{sampleIs,jdbcType=TINYINT},
      license = #{license,jdbcType=VARCHAR},
      etc_apply_order_id = #{etcApplyOrderId,jdbcType=VARCHAR},
      biz_agreement_no = #{bizAgreementNo,jdbcType=VARCHAR},
      device_status_detail = #{deviceStatusDetail,jdbcType=VARCHAR},
      etc_source = #{etcSource,jdbcType=TINYINT},
      publisher = #{publisher,jdbcType=TINYINT},
      blacklist_status = #{blacklistStatus,jdbcType=TINYINT},
      op_time = #{opTime,jdbcType=BIGINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      gis = #{gis,jdbcType=BINARY}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.EtcDevice">
    update etc_device
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      third_vehicle_id = #{thirdVehicleId,jdbcType=VARCHAR},
      etc_no = #{etcNo,jdbcType=VARCHAR},
      ic_no = #{icNo,jdbcType=VARCHAR},
      online = #{online,jdbcType=TINYINT},
      hard_nink_status = #{hardNinkStatus,jdbcType=TINYINT},
      work_status = #{workStatus,jdbcType=TINYINT},
      service = #{service,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      plate_color = #{plateColor,jdbcType=TINYINT},
      axles = #{axles,jdbcType=VARCHAR},
      length = #{length,jdbcType=VARCHAR},
      width = #{width,jdbcType=VARCHAR},
      height = #{height,jdbcType=VARCHAR},
      total_weight = #{totalWeight,jdbcType=VARCHAR},
      gross_wass = #{grossWass,jdbcType=VARCHAR},
      register_date = #{registerDate,jdbcType=TIMESTAMP},
      grant_date = #{grantDate,jdbcType=TIMESTAMP},
      owner_name = #{ownerName,jdbcType=VARCHAR},
      activate_status = #{activateStatus,jdbcType=TINYINT},
      availability_status = #{availabilityStatus,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      sample_is = #{sampleIs,jdbcType=TINYINT},
      license = #{license,jdbcType=VARCHAR},
      etc_apply_order_id = #{etcApplyOrderId,jdbcType=VARCHAR},
      biz_agreement_no = #{bizAgreementNo,jdbcType=VARCHAR},
      device_status_detail = #{deviceStatusDetail,jdbcType=VARCHAR},
      etc_source = #{etcSource,jdbcType=TINYINT},
      publisher = #{publisher,jdbcType=TINYINT},
      blacklist_status = #{blacklistStatus,jdbcType=TINYINT},
      op_time = #{opTime,jdbcType=BIGINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into etc_device
    (merchant_id, store_id, vehicle_model_id, vehicle_id, third_vehicle_id, etc_no, ic_no, 
      online, hard_nink_status, work_status, service, remark, plate_color, axles, length, 
      width, height, total_weight, gross_wass, register_date, grant_date, owner_name, 
      activate_status, availability_status, create_time, sample_is, license, etc_apply_order_id, 
      biz_agreement_no, device_status_detail, etc_source, publisher, blacklist_status, 
      op_time, last_ver, deleted, op_user_id, gis)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.vehicleModelId,jdbcType=BIGINT}, 
        #{item.vehicleId,jdbcType=BIGINT}, #{item.thirdVehicleId,jdbcType=VARCHAR}, #{item.etcNo,jdbcType=VARCHAR}, 
        #{item.icNo,jdbcType=VARCHAR}, #{item.online,jdbcType=TINYINT}, #{item.hardNinkStatus,jdbcType=TINYINT}, 
        #{item.workStatus,jdbcType=TINYINT}, #{item.service,jdbcType=TINYINT}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.plateColor,jdbcType=TINYINT}, #{item.axles,jdbcType=VARCHAR}, #{item.length,jdbcType=VARCHAR}, 
        #{item.width,jdbcType=VARCHAR}, #{item.height,jdbcType=VARCHAR}, #{item.totalWeight,jdbcType=VARCHAR}, 
        #{item.grossWass,jdbcType=VARCHAR}, #{item.registerDate,jdbcType=TIMESTAMP}, #{item.grantDate,jdbcType=TIMESTAMP}, 
        #{item.ownerName,jdbcType=VARCHAR}, #{item.activateStatus,jdbcType=TINYINT}, #{item.availabilityStatus,jdbcType=TINYINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.sampleIs,jdbcType=TINYINT}, #{item.license,jdbcType=VARCHAR}, 
        #{item.etcApplyOrderId,jdbcType=VARCHAR}, #{item.bizAgreementNo,jdbcType=VARCHAR}, 
        #{item.deviceStatusDetail,jdbcType=VARCHAR}, #{item.etcSource,jdbcType=TINYINT}, 
        #{item.publisher,jdbcType=TINYINT}, #{item.blacklistStatus,jdbcType=TINYINT}, #{item.opTime,jdbcType=BIGINT}, 
        #{item.lastVer,jdbcType=INTEGER}, #{item.deleted,jdbcType=TINYINT}, #{item.opUserId,jdbcType=BIGINT}, 
        #{item.gis,jdbcType=BINARY})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into etc_device (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_id'.toString() == column.value">
          #{item.vehicleId,jdbcType=BIGINT}
        </if>
        <if test="'third_vehicle_id'.toString() == column.value">
          #{item.thirdVehicleId,jdbcType=VARCHAR}
        </if>
        <if test="'etc_no'.toString() == column.value">
          #{item.etcNo,jdbcType=VARCHAR}
        </if>
        <if test="'ic_no'.toString() == column.value">
          #{item.icNo,jdbcType=VARCHAR}
        </if>
        <if test="'online'.toString() == column.value">
          #{item.online,jdbcType=TINYINT}
        </if>
        <if test="'hard_nink_status'.toString() == column.value">
          #{item.hardNinkStatus,jdbcType=TINYINT}
        </if>
        <if test="'work_status'.toString() == column.value">
          #{item.workStatus,jdbcType=TINYINT}
        </if>
        <if test="'service'.toString() == column.value">
          #{item.service,jdbcType=TINYINT}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'plate_color'.toString() == column.value">
          #{item.plateColor,jdbcType=TINYINT}
        </if>
        <if test="'axles'.toString() == column.value">
          #{item.axles,jdbcType=VARCHAR}
        </if>
        <if test="'length'.toString() == column.value">
          #{item.length,jdbcType=VARCHAR}
        </if>
        <if test="'width'.toString() == column.value">
          #{item.width,jdbcType=VARCHAR}
        </if>
        <if test="'height'.toString() == column.value">
          #{item.height,jdbcType=VARCHAR}
        </if>
        <if test="'total_weight'.toString() == column.value">
          #{item.totalWeight,jdbcType=VARCHAR}
        </if>
        <if test="'gross_wass'.toString() == column.value">
          #{item.grossWass,jdbcType=VARCHAR}
        </if>
        <if test="'register_date'.toString() == column.value">
          #{item.registerDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'grant_date'.toString() == column.value">
          #{item.grantDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'owner_name'.toString() == column.value">
          #{item.ownerName,jdbcType=VARCHAR}
        </if>
        <if test="'activate_status'.toString() == column.value">
          #{item.activateStatus,jdbcType=TINYINT}
        </if>
        <if test="'availability_status'.toString() == column.value">
          #{item.availabilityStatus,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'sample_is'.toString() == column.value">
          #{item.sampleIs,jdbcType=TINYINT}
        </if>
        <if test="'license'.toString() == column.value">
          #{item.license,jdbcType=VARCHAR}
        </if>
        <if test="'etc_apply_order_id'.toString() == column.value">
          #{item.etcApplyOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'biz_agreement_no'.toString() == column.value">
          #{item.bizAgreementNo,jdbcType=VARCHAR}
        </if>
        <if test="'device_status_detail'.toString() == column.value">
          #{item.deviceStatusDetail,jdbcType=VARCHAR}
        </if>
        <if test="'etc_source'.toString() == column.value">
          #{item.etcSource,jdbcType=TINYINT}
        </if>
        <if test="'publisher'.toString() == column.value">
          #{item.publisher,jdbcType=TINYINT}
        </if>
        <if test="'blacklist_status'.toString() == column.value">
          #{item.blacklistStatus,jdbcType=TINYINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'gis'.toString() == column.value">
          #{item.gis,jdbcType=BINARY}
        </if>
      </foreach>
      )
    </foreach>
  </insert>

  <update id="updateGis" parameterType="map">
    update etc_device set gis = ST_GeomFromText(#{gis})
    where id = #{id}
  </update>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.VehicleChannelMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.VehicleChannel">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="audit_status" jdbcType="TINYINT" property="auditStatus" />
    <result column="audit_fail_reason" jdbcType="VARCHAR" property="auditFailReason" />
    <result column="vehicle_status" jdbcType="TINYINT" property="vehicleStatus" />
    <result column="vehicle_fail_reason" jdbcType="VARCHAR" property="vehicleFailReason" />
    <result column="sale_status" jdbcType="TINYINT" property="saleStatus" />
    <result column="sale_status_reason" jdbcType="VARCHAR" property="saleStatusReason" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vehicle_id, channel_id, audit_status, audit_fail_reason, vehicle_status, vehicle_fail_reason, 
    sale_status, sale_status_reason, merchant_id, last_ver, extra, deleted, create_time, 
    op_time, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.VehicleChannelExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_channel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_channel
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.VehicleChannel">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_channel (vehicle_id, channel_id, audit_status, 
      audit_fail_reason, vehicle_status, vehicle_fail_reason, 
      sale_status, sale_status_reason, merchant_id, 
      last_ver, extra, deleted, 
      create_time, op_time, op_user_id
      )
    values (#{vehicleId,jdbcType=BIGINT}, #{channelId,jdbcType=BIGINT}, #{auditStatus,jdbcType=TINYINT}, 
      #{auditFailReason,jdbcType=VARCHAR}, #{vehicleStatus,jdbcType=TINYINT}, #{vehicleFailReason,jdbcType=VARCHAR}, 
      #{saleStatus,jdbcType=TINYINT}, #{saleStatusReason,jdbcType=VARCHAR}, #{merchantId,jdbcType=BIGINT}, 
      #{lastVer,jdbcType=INTEGER}, #{extra,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, 
      #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.VehicleChannel">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_channel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="auditFailReason != null">
        audit_fail_reason,
      </if>
      <if test="vehicleStatus != null">
        vehicle_status,
      </if>
      <if test="vehicleFailReason != null">
        vehicle_fail_reason,
      </if>
      <if test="saleStatus != null">
        sale_status,
      </if>
      <if test="saleStatusReason != null">
        sale_status_reason,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="auditFailReason != null">
        #{auditFailReason,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStatus != null">
        #{vehicleStatus,jdbcType=TINYINT},
      </if>
      <if test="vehicleFailReason != null">
        #{vehicleFailReason,jdbcType=VARCHAR},
      </if>
      <if test="saleStatus != null">
        #{saleStatus,jdbcType=TINYINT},
      </if>
      <if test="saleStatusReason != null">
        #{saleStatusReason,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.VehicleChannelExample" resultType="java.lang.Long">
    select count(*) from vehicle_channel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_channel
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=BIGINT},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=TINYINT},
      </if>
      <if test="record.auditFailReason != null">
        audit_fail_reason = #{record.auditFailReason,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleStatus != null">
        vehicle_status = #{record.vehicleStatus,jdbcType=TINYINT},
      </if>
      <if test="record.vehicleFailReason != null">
        vehicle_fail_reason = #{record.vehicleFailReason,jdbcType=VARCHAR},
      </if>
      <if test="record.saleStatus != null">
        sale_status = #{record.saleStatus,jdbcType=TINYINT},
      </if>
      <if test="record.saleStatusReason != null">
        sale_status_reason = #{record.saleStatusReason,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_channel
    set id = #{record.id,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      channel_id = #{record.channelId,jdbcType=BIGINT},
      audit_status = #{record.auditStatus,jdbcType=TINYINT},
      audit_fail_reason = #{record.auditFailReason,jdbcType=VARCHAR},
      vehicle_status = #{record.vehicleStatus,jdbcType=TINYINT},
      vehicle_fail_reason = #{record.vehicleFailReason,jdbcType=VARCHAR},
      sale_status = #{record.saleStatus,jdbcType=TINYINT},
      sale_status_reason = #{record.saleStatusReason,jdbcType=VARCHAR},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      extra = #{record.extra,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.VehicleChannel">
    update vehicle_channel
    <set>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="auditFailReason != null">
        audit_fail_reason = #{auditFailReason,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStatus != null">
        vehicle_status = #{vehicleStatus,jdbcType=TINYINT},
      </if>
      <if test="vehicleFailReason != null">
        vehicle_fail_reason = #{vehicleFailReason,jdbcType=VARCHAR},
      </if>
      <if test="saleStatus != null">
        sale_status = #{saleStatus,jdbcType=TINYINT},
      </if>
      <if test="saleStatusReason != null">
        sale_status_reason = #{saleStatusReason,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.VehicleChannel">
    update vehicle_channel
    set vehicle_id = #{vehicleId,jdbcType=BIGINT},
      channel_id = #{channelId,jdbcType=BIGINT},
      audit_status = #{auditStatus,jdbcType=TINYINT},
      audit_fail_reason = #{auditFailReason,jdbcType=VARCHAR},
      vehicle_status = #{vehicleStatus,jdbcType=TINYINT},
      vehicle_fail_reason = #{vehicleFailReason,jdbcType=VARCHAR},
      sale_status = #{saleStatus,jdbcType=TINYINT},
      sale_status_reason = #{saleStatusReason,jdbcType=VARCHAR},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      extra = #{extra,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_channel
    (vehicle_id, channel_id, audit_status, audit_fail_reason, vehicle_status, vehicle_fail_reason, 
      sale_status, sale_status_reason, merchant_id, last_ver, extra, deleted, create_time, 
      op_time, op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.vehicleId,jdbcType=BIGINT}, #{item.channelId,jdbcType=BIGINT}, #{item.auditStatus,jdbcType=TINYINT}, 
        #{item.auditFailReason,jdbcType=VARCHAR}, #{item.vehicleStatus,jdbcType=TINYINT}, 
        #{item.vehicleFailReason,jdbcType=VARCHAR}, #{item.saleStatus,jdbcType=TINYINT}, 
        #{item.saleStatusReason,jdbcType=VARCHAR}, #{item.merchantId,jdbcType=BIGINT}, 
        #{item.lastVer,jdbcType=INTEGER}, #{item.extra,jdbcType=VARCHAR}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_channel (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'vehicle_id'.toString() == column.value">
          #{item.vehicleId,jdbcType=BIGINT}
        </if>
        <if test="'channel_id'.toString() == column.value">
          #{item.channelId,jdbcType=BIGINT}
        </if>
        <if test="'audit_status'.toString() == column.value">
          #{item.auditStatus,jdbcType=TINYINT}
        </if>
        <if test="'audit_fail_reason'.toString() == column.value">
          #{item.auditFailReason,jdbcType=VARCHAR}
        </if>
        <if test="'vehicle_status'.toString() == column.value">
          #{item.vehicleStatus,jdbcType=TINYINT}
        </if>
        <if test="'vehicle_fail_reason'.toString() == column.value">
          #{item.vehicleFailReason,jdbcType=VARCHAR}
        </if>
        <if test="'sale_status'.toString() == column.value">
          #{item.saleStatus,jdbcType=TINYINT}
        </if>
        <if test="'sale_status_reason'.toString() == column.value">
          #{item.saleStatusReason,jdbcType=VARCHAR}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'extra'.toString() == column.value">
          #{item.extra,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
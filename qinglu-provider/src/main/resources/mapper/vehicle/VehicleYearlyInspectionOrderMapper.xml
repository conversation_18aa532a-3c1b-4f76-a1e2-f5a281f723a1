<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.VehicleYearlyInspectionOrderMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.VehicleYearlyInspectionOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="work_order_no" jdbcType="VARCHAR" property="workOrderNo" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="license_no" jdbcType="VARCHAR" property="licenseNo" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="vehicle_model_name" jdbcType="VARCHAR" property="vehicleModelName" />
    <result column="inspection_time" jdbcType="BIGINT" property="inspectionTime" />
    <result column="inspection_end_time" jdbcType="BIGINT" property="inspectionEndTime" />
    <result column="next_inspection_time" jdbcType="BIGINT" property="nextInspectionTime" />
    <result column="completed_immediately" jdbcType="TINYINT" property="completedImmediately" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="handler_user_id" jdbcType="BIGINT" property="handlerUserId" />
    <result column="handler_user_name" jdbcType="VARCHAR" property="handlerUserName" />
    <result column="cancel_reason" jdbcType="VARCHAR" property="cancelReason" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, work_order_no, vehicle_id, license_no, vehicle_model_id, vehicle_model_name, 
    inspection_time, inspection_end_time, next_inspection_time, completed_immediately, 
    store_id, merchant_id, handler_user_id, handler_user_name, cancel_reason, status, 
    last_ver, create_time, op_time, op_user_id, deleted
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.VehicleYearlyInspectionOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_yearly_inspection_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_yearly_inspection_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_yearly_inspection_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.VehicleYearlyInspectionOrderExample">
    delete from vehicle_yearly_inspection_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.VehicleYearlyInspectionOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_yearly_inspection_order (work_order_no, vehicle_id, license_no, 
      vehicle_model_id, vehicle_model_name, inspection_time, 
      inspection_end_time, next_inspection_time, completed_immediately, 
      store_id, merchant_id, handler_user_id, 
      handler_user_name, cancel_reason, status, 
      last_ver, create_time, op_time, 
      op_user_id, deleted)
    values (#{workOrderNo,jdbcType=VARCHAR}, #{vehicleId,jdbcType=BIGINT}, #{licenseNo,jdbcType=VARCHAR}, 
      #{vehicleModelId,jdbcType=BIGINT}, #{vehicleModelName,jdbcType=VARCHAR}, #{inspectionTime,jdbcType=BIGINT}, 
      #{inspectionEndTime,jdbcType=BIGINT}, #{nextInspectionTime,jdbcType=BIGINT}, #{completedImmediately,jdbcType=TINYINT}, 
      #{storeId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, #{handlerUserId,jdbcType=BIGINT}, 
      #{handlerUserName,jdbcType=VARCHAR}, #{cancelReason,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{lastVer,jdbcType=INTEGER}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, 
      #{opUserId,jdbcType=BIGINT}, #{deleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.VehicleYearlyInspectionOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_yearly_inspection_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="workOrderNo != null">
        work_order_no,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="licenseNo != null">
        license_no,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name,
      </if>
      <if test="inspectionTime != null">
        inspection_time,
      </if>
      <if test="inspectionEndTime != null">
        inspection_end_time,
      </if>
      <if test="nextInspectionTime != null">
        next_inspection_time,
      </if>
      <if test="completedImmediately != null">
        completed_immediately,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="handlerUserId != null">
        handler_user_id,
      </if>
      <if test="handlerUserName != null">
        handler_user_name,
      </if>
      <if test="cancelReason != null">
        cancel_reason,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="workOrderNo != null">
        #{workOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="licenseNo != null">
        #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelName != null">
        #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="inspectionTime != null">
        #{inspectionTime,jdbcType=BIGINT},
      </if>
      <if test="inspectionEndTime != null">
        #{inspectionEndTime,jdbcType=BIGINT},
      </if>
      <if test="nextInspectionTime != null">
        #{nextInspectionTime,jdbcType=BIGINT},
      </if>
      <if test="completedImmediately != null">
        #{completedImmediately,jdbcType=TINYINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="handlerUserId != null">
        #{handlerUserId,jdbcType=BIGINT},
      </if>
      <if test="handlerUserName != null">
        #{handlerUserName,jdbcType=VARCHAR},
      </if>
      <if test="cancelReason != null">
        #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.VehicleYearlyInspectionOrderExample" resultType="java.lang.Long">
    select count(*) from vehicle_yearly_inspection_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_yearly_inspection_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.workOrderNo != null">
        work_order_no = #{record.workOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="record.licenseNo != null">
        license_no = #{record.licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelName != null">
        vehicle_model_name = #{record.vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="record.inspectionTime != null">
        inspection_time = #{record.inspectionTime,jdbcType=BIGINT},
      </if>
      <if test="record.inspectionEndTime != null">
        inspection_end_time = #{record.inspectionEndTime,jdbcType=BIGINT},
      </if>
      <if test="record.nextInspectionTime != null">
        next_inspection_time = #{record.nextInspectionTime,jdbcType=BIGINT},
      </if>
      <if test="record.completedImmediately != null">
        completed_immediately = #{record.completedImmediately,jdbcType=TINYINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.handlerUserId != null">
        handler_user_id = #{record.handlerUserId,jdbcType=BIGINT},
      </if>
      <if test="record.handlerUserName != null">
        handler_user_name = #{record.handlerUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.cancelReason != null">
        cancel_reason = #{record.cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_yearly_inspection_order
    set id = #{record.id,jdbcType=BIGINT},
      work_order_no = #{record.workOrderNo,jdbcType=VARCHAR},
      vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      license_no = #{record.licenseNo,jdbcType=VARCHAR},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      vehicle_model_name = #{record.vehicleModelName,jdbcType=VARCHAR},
      inspection_time = #{record.inspectionTime,jdbcType=BIGINT},
      inspection_end_time = #{record.inspectionEndTime,jdbcType=BIGINT},
      next_inspection_time = #{record.nextInspectionTime,jdbcType=BIGINT},
      completed_immediately = #{record.completedImmediately,jdbcType=TINYINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      handler_user_id = #{record.handlerUserId,jdbcType=BIGINT},
      handler_user_name = #{record.handlerUserName,jdbcType=VARCHAR},
      cancel_reason = #{record.cancelReason,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      deleted = #{record.deleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.VehicleYearlyInspectionOrder">
    update vehicle_yearly_inspection_order
    <set>
      <if test="workOrderNo != null">
        work_order_no = #{workOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="licenseNo != null">
        license_no = #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="inspectionTime != null">
        inspection_time = #{inspectionTime,jdbcType=BIGINT},
      </if>
      <if test="inspectionEndTime != null">
        inspection_end_time = #{inspectionEndTime,jdbcType=BIGINT},
      </if>
      <if test="nextInspectionTime != null">
        next_inspection_time = #{nextInspectionTime,jdbcType=BIGINT},
      </if>
      <if test="completedImmediately != null">
        completed_immediately = #{completedImmediately,jdbcType=TINYINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="handlerUserId != null">
        handler_user_id = #{handlerUserId,jdbcType=BIGINT},
      </if>
      <if test="handlerUserName != null">
        handler_user_name = #{handlerUserName,jdbcType=VARCHAR},
      </if>
      <if test="cancelReason != null">
        cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.VehicleYearlyInspectionOrder">
    update vehicle_yearly_inspection_order
    set work_order_no = #{workOrderNo,jdbcType=VARCHAR},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      license_no = #{licenseNo,jdbcType=VARCHAR},
      vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      inspection_time = #{inspectionTime,jdbcType=BIGINT},
      inspection_end_time = #{inspectionEndTime,jdbcType=BIGINT},
      next_inspection_time = #{nextInspectionTime,jdbcType=BIGINT},
      completed_immediately = #{completedImmediately,jdbcType=TINYINT},
      store_id = #{storeId,jdbcType=BIGINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      handler_user_id = #{handlerUserId,jdbcType=BIGINT},
      handler_user_name = #{handlerUserName,jdbcType=VARCHAR},
      cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_yearly_inspection_order
    (work_order_no, vehicle_id, license_no, vehicle_model_id, vehicle_model_name, inspection_time, 
      inspection_end_time, next_inspection_time, completed_immediately, store_id, merchant_id, 
      handler_user_id, handler_user_name, cancel_reason, status, last_ver, create_time, 
      op_time, op_user_id, deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.workOrderNo,jdbcType=VARCHAR}, #{item.vehicleId,jdbcType=BIGINT}, #{item.licenseNo,jdbcType=VARCHAR}, 
        #{item.vehicleModelId,jdbcType=BIGINT}, #{item.vehicleModelName,jdbcType=VARCHAR}, 
        #{item.inspectionTime,jdbcType=BIGINT}, #{item.inspectionEndTime,jdbcType=BIGINT}, 
        #{item.nextInspectionTime,jdbcType=BIGINT}, #{item.completedImmediately,jdbcType=TINYINT}, 
        #{item.storeId,jdbcType=BIGINT}, #{item.merchantId,jdbcType=BIGINT}, #{item.handlerUserId,jdbcType=BIGINT}, 
        #{item.handlerUserName,jdbcType=VARCHAR}, #{item.cancelReason,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT}, #{item.deleted,jdbcType=TINYINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_yearly_inspection_order (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'work_order_no'.toString() == column.value">
          #{item.workOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="'vehicle_id'.toString() == column.value">
          #{item.vehicleId,jdbcType=BIGINT}
        </if>
        <if test="'license_no'.toString() == column.value">
          #{item.licenseNo,jdbcType=VARCHAR}
        </if>
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_model_name'.toString() == column.value">
          #{item.vehicleModelName,jdbcType=VARCHAR}
        </if>
        <if test="'inspection_time'.toString() == column.value">
          #{item.inspectionTime,jdbcType=BIGINT}
        </if>
        <if test="'inspection_end_time'.toString() == column.value">
          #{item.inspectionEndTime,jdbcType=BIGINT}
        </if>
        <if test="'next_inspection_time'.toString() == column.value">
          #{item.nextInspectionTime,jdbcType=BIGINT}
        </if>
        <if test="'completed_immediately'.toString() == column.value">
          #{item.completedImmediately,jdbcType=TINYINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'handler_user_id'.toString() == column.value">
          #{item.handlerUserId,jdbcType=BIGINT}
        </if>
        <if test="'handler_user_name'.toString() == column.value">
          #{item.handlerUserName,jdbcType=VARCHAR}
        </if>
        <if test="'cancel_reason'.toString() == column.value">
          #{item.cancelReason,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
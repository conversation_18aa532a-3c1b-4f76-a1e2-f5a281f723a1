<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.VehicleFuelFormSettingMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.VehicleFuelFormSetting">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="fuel_form_type" jdbcType="TINYINT" property="fuelFormType" />
    <result column="oil_grade" jdbcType="VARCHAR" property="oilGrade" />
    <result column="setting_price" jdbcType="BIGINT" property="settingPrice" />
    <result column="service_price" jdbcType="BIGINT" property="servicePrice" />
    <result column="city_setting" jdbcType="VARCHAR" property="citySetting" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, fuel_form_type, oil_grade, setting_price, service_price, city_setting, 
    deleted, create_time, create_user_id, op_time, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.VehicleFuelFormSettingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_fuel_form_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_fuel_form_setting
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_fuel_form_setting
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.VehicleFuelFormSettingExample">
    delete from vehicle_fuel_form_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.VehicleFuelFormSetting">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_fuel_form_setting (merchant_id, fuel_form_type, oil_grade, 
      setting_price, service_price, city_setting, 
      deleted, create_time, create_user_id, 
      op_time, op_user_id)
    values (#{merchantId,jdbcType=BIGINT}, #{fuelFormType,jdbcType=TINYINT}, #{oilGrade,jdbcType=VARCHAR}, 
      #{settingPrice,jdbcType=BIGINT}, #{servicePrice,jdbcType=BIGINT}, #{citySetting,jdbcType=VARCHAR}, 
      #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, #{createUserId,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.VehicleFuelFormSetting">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_fuel_form_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="fuelFormType != null">
        fuel_form_type,
      </if>
      <if test="oilGrade != null">
        oil_grade,
      </if>
      <if test="settingPrice != null">
        setting_price,
      </if>
      <if test="servicePrice != null">
        service_price,
      </if>
      <if test="citySetting != null">
        city_setting,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="fuelFormType != null">
        #{fuelFormType,jdbcType=TINYINT},
      </if>
      <if test="oilGrade != null">
        #{oilGrade,jdbcType=VARCHAR},
      </if>
      <if test="settingPrice != null">
        #{settingPrice,jdbcType=BIGINT},
      </if>
      <if test="servicePrice != null">
        #{servicePrice,jdbcType=BIGINT},
      </if>
      <if test="citySetting != null">
        #{citySetting,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.VehicleFuelFormSettingExample" resultType="java.lang.Long">
    select count(*) from vehicle_fuel_form_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_fuel_form_setting
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.fuelFormType != null">
        fuel_form_type = #{record.fuelFormType,jdbcType=TINYINT},
      </if>
      <if test="record.oilGrade != null">
        oil_grade = #{record.oilGrade,jdbcType=VARCHAR},
      </if>
      <if test="record.settingPrice != null">
        setting_price = #{record.settingPrice,jdbcType=BIGINT},
      </if>
      <if test="record.servicePrice != null">
        service_price = #{record.servicePrice,jdbcType=BIGINT},
      </if>
      <if test="record.citySetting != null">
        city_setting = #{record.citySetting,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_fuel_form_setting
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      fuel_form_type = #{record.fuelFormType,jdbcType=TINYINT},
      oil_grade = #{record.oilGrade,jdbcType=VARCHAR},
      setting_price = #{record.settingPrice,jdbcType=BIGINT},
      service_price = #{record.servicePrice,jdbcType=BIGINT},
      city_setting = #{record.citySetting,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      create_user_id = #{record.createUserId,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.VehicleFuelFormSetting">
    update vehicle_fuel_form_setting
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="fuelFormType != null">
        fuel_form_type = #{fuelFormType,jdbcType=TINYINT},
      </if>
      <if test="oilGrade != null">
        oil_grade = #{oilGrade,jdbcType=VARCHAR},
      </if>
      <if test="settingPrice != null">
        setting_price = #{settingPrice,jdbcType=BIGINT},
      </if>
      <if test="servicePrice != null">
        service_price = #{servicePrice,jdbcType=BIGINT},
      </if>
      <if test="citySetting != null">
        city_setting = #{citySetting,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.VehicleFuelFormSetting">
    update vehicle_fuel_form_setting
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      fuel_form_type = #{fuelFormType,jdbcType=TINYINT},
      oil_grade = #{oilGrade,jdbcType=VARCHAR},
      setting_price = #{settingPrice,jdbcType=BIGINT},
      service_price = #{servicePrice,jdbcType=BIGINT},
      city_setting = #{citySetting,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      create_user_id = #{createUserId,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_fuel_form_setting
    (merchant_id, fuel_form_type, oil_grade, setting_price, service_price, city_setting, 
      deleted, create_time, create_user_id, op_time, op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.fuelFormType,jdbcType=TINYINT}, #{item.oilGrade,jdbcType=VARCHAR}, 
        #{item.settingPrice,jdbcType=BIGINT}, #{item.servicePrice,jdbcType=BIGINT}, #{item.citySetting,jdbcType=VARCHAR}, 
        #{item.deleted,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT}, #{item.createUserId,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_fuel_form_setting (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'fuel_form_type'.toString() == column.value">
          #{item.fuelFormType,jdbcType=TINYINT}
        </if>
        <if test="'oil_grade'.toString() == column.value">
          #{item.oilGrade,jdbcType=VARCHAR}
        </if>
        <if test="'setting_price'.toString() == column.value">
          #{item.settingPrice,jdbcType=BIGINT}
        </if>
        <if test="'service_price'.toString() == column.value">
          #{item.servicePrice,jdbcType=BIGINT}
        </if>
        <if test="'city_setting'.toString() == column.value">
          #{item.citySetting,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'create_user_id'.toString() == column.value">
          #{item.createUserId,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
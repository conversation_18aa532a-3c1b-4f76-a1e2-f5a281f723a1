<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.CtripPriceFilterInfoMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.CtripPriceFilterInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="channel_city_id" jdbcType="BIGINT" property="channelCityId" />
    <result column="sass_city_id" jdbcType="BIGINT" property="sassCityId" />
    <result column="vehicle_series_id" jdbcType="BIGINT" property="vehicleSeriesId" />
    <result column="vehicle_group_id" jdbcType="BIGINT" property="vehicleGroupId" />
    <result column="standard_product_id" jdbcType="BIGINT" property="standardProductId" />
    <result column="upper_price" jdbcType="INTEGER" property="upperPrice" />
    <result column="lower_price" jdbcType="INTEGER" property="lowerPrice" />
    <result column="baisc_insurance_price_upper" jdbcType="INTEGER" property="baiscInsurancePriceUpper" />
    <result column="baisc_insurance_price_lower" jdbcType="INTEGER" property="baiscInsurancePriceLower" />
    <result column="baisc_insurance_price_gap" jdbcType="INTEGER" property="baiscInsurancePriceGap" />
    <result column="poundage_upper" jdbcType="INTEGER" property="poundageUpper" />
    <result column="poundage_lower" jdbcType="INTEGER" property="poundageLower" />
    <result column="poundage_gap" jdbcType="INTEGER" property="poundageGap" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="premiumInsurance_price_upper" jdbcType="INTEGER" property="premiuminsurancePriceUpper" />
    <result column="premiumInsurance_price_lower" jdbcType="INTEGER" property="premiuminsurancePriceLower" />
    <result column="premiumInsurance_price_gap" jdbcType="INTEGER" property="premiuminsurancePriceGap" />
    <result column="exclusiveInsurance_price_upper" jdbcType="INTEGER" property="exclusiveinsurancePriceUpper" />
    <result column="exclusiveInsurancePriceLower" jdbcType="INTEGER" property="exclusiveinsurancepricelower" />
    <result column="exclusiveInsurancePriceGap" jdbcType="INTEGER" property="exclusiveinsurancepricegap" />
    <result column="car_rental_deposit_ceiling" jdbcType="INTEGER" property="carRentalDepositCeiling" />
    <result column="car_rental_deposit_lower" jdbcType="INTEGER" property="carRentalDepositLower" />
    <result column="illegal_deposit_ceiling" jdbcType="INTEGER" property="illegalDepositCeiling" />
    <result column="illegal_deposit_lower" jdbcType="INTEGER" property="illegalDepositLower" />
    <result column="ctrip_no_worried_ceiling" jdbcType="DECIMAL" property="ctripNoWorriedCeiling" />
    <result column="ctrip_no_worried_lower" jdbcType="DECIMAL" property="ctripNoWorriedLower" />
    <result column="other_no_worried_ceiling" jdbcType="DECIMAL" property="otherNoWorriedCeiling" />
    <result column="other_no_worried_lower" jdbcType="DECIMAL" property="otherNoWorriedLower" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, channel_id, store_id, channel_city_id, sass_city_id, vehicle_series_id,
    vehicle_group_id, standard_product_id, upper_price, lower_price, baisc_insurance_price_upper,
    baisc_insurance_price_lower, baisc_insurance_price_gap, poundage_upper, poundage_lower,
    poundage_gap, create_time, op_time, last_ver, premiumInsurance_price_upper, premiumInsurance_price_lower,
    premiumInsurance_price_gap, exclusiveInsurance_price_upper, exclusiveInsurancePriceLower,
    exclusiveInsurancePriceGap, car_rental_deposit_ceiling, car_rental_deposit_lower,
    illegal_deposit_ceiling, illegal_deposit_lower, ctrip_no_worried_ceiling, ctrip_no_worried_lower,
    other_no_worried_ceiling, other_no_worried_lower
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.CtripPriceFilterInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from ctrip_price_filter_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ctrip_price_filter_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ctrip_price_filter_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.CtripPriceFilterInfoExample">
    delete from ctrip_price_filter_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.CtripPriceFilterInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ctrip_price_filter_info (merchant_id, channel_id, store_id,
    channel_city_id, sass_city_id, vehicle_series_id,
    vehicle_group_id, standard_product_id, upper_price,
    lower_price, baisc_insurance_price_upper, baisc_insurance_price_lower,
    baisc_insurance_price_gap, poundage_upper,
    poundage_lower, poundage_gap, create_time,
    op_time, last_ver, premiumInsurance_price_upper,
    premiumInsurance_price_lower, premiumInsurance_price_gap,
    exclusiveInsurance_price_upper, exclusiveInsurancePriceLower,
    exclusiveInsurancePriceGap, car_rental_deposit_ceiling,
    car_rental_deposit_lower, illegal_deposit_ceiling,
    illegal_deposit_lower, ctrip_no_worried_ceiling,
    ctrip_no_worried_lower, other_no_worried_ceiling,
    other_no_worried_lower)
    values (#{merchantId,jdbcType=BIGINT}, #{channelId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT},
    #{channelCityId,jdbcType=BIGINT}, #{sassCityId,jdbcType=BIGINT}, #{vehicleSeriesId,jdbcType=BIGINT},
    #{vehicleGroupId,jdbcType=BIGINT}, #{standardProductId,jdbcType=BIGINT}, #{upperPrice,jdbcType=INTEGER},
    #{lowerPrice,jdbcType=INTEGER}, #{baiscInsurancePriceUpper,jdbcType=INTEGER}, #{baiscInsurancePriceLower,jdbcType=INTEGER},
    #{baiscInsurancePriceGap,jdbcType=INTEGER}, #{poundageUpper,jdbcType=INTEGER},
    #{poundageLower,jdbcType=INTEGER}, #{poundageGap,jdbcType=INTEGER}, #{createTime,jdbcType=BIGINT},
    #{opTime,jdbcType=BIGINT}, #{lastVer,jdbcType=INTEGER}, #{premiuminsurancePriceUpper,jdbcType=INTEGER},
    #{premiuminsurancePriceLower,jdbcType=INTEGER}, #{premiuminsurancePriceGap,jdbcType=INTEGER},
    #{exclusiveinsurancePriceUpper,jdbcType=INTEGER}, #{exclusiveinsurancepricelower,jdbcType=INTEGER},
    #{exclusiveinsurancepricegap,jdbcType=INTEGER}, #{carRentalDepositCeiling,jdbcType=INTEGER},
    #{carRentalDepositLower,jdbcType=INTEGER}, #{illegalDepositCeiling,jdbcType=INTEGER},
    #{illegalDepositLower,jdbcType=INTEGER}, #{ctripNoWorriedCeiling,jdbcType=DECIMAL},
    #{ctripNoWorriedLower,jdbcType=DECIMAL}, #{otherNoWorriedCeiling,jdbcType=DECIMAL},
    #{otherNoWorriedLower,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.CtripPriceFilterInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ctrip_price_filter_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="channelCityId != null">
        channel_city_id,
      </if>
      <if test="sassCityId != null">
        sass_city_id,
      </if>
      <if test="vehicleSeriesId != null">
        vehicle_series_id,
      </if>
      <if test="vehicleGroupId != null">
        vehicle_group_id,
      </if>
      <if test="standardProductId != null">
        standard_product_id,
      </if>
      <if test="upperPrice != null">
        upper_price,
      </if>
      <if test="lowerPrice != null">
        lower_price,
      </if>
      <if test="baiscInsurancePriceUpper != null">
        baisc_insurance_price_upper,
      </if>
      <if test="baiscInsurancePriceLower != null">
        baisc_insurance_price_lower,
      </if>
      <if test="baiscInsurancePriceGap != null">
        baisc_insurance_price_gap,
      </if>
      <if test="poundageUpper != null">
        poundage_upper,
      </if>
      <if test="poundageLower != null">
        poundage_lower,
      </if>
      <if test="poundageGap != null">
        poundage_gap,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="premiuminsurancePriceUpper != null">
        premiumInsurance_price_upper,
      </if>
      <if test="premiuminsurancePriceLower != null">
        premiumInsurance_price_lower,
      </if>
      <if test="premiuminsurancePriceGap != null">
        premiumInsurance_price_gap,
      </if>
      <if test="exclusiveinsurancePriceUpper != null">
        exclusiveInsurance_price_upper,
      </if>
      <if test="exclusiveinsurancepricelower != null">
        exclusiveInsurancePriceLower,
      </if>
      <if test="exclusiveinsurancepricegap != null">
        exclusiveInsurancePriceGap,
      </if>
      <if test="carRentalDepositCeiling != null">
        car_rental_deposit_ceiling,
      </if>
      <if test="carRentalDepositLower != null">
        car_rental_deposit_lower,
      </if>
      <if test="illegalDepositCeiling != null">
        illegal_deposit_ceiling,
      </if>
      <if test="illegalDepositLower != null">
        illegal_deposit_lower,
      </if>
      <if test="ctripNoWorriedCeiling != null">
        ctrip_no_worried_ceiling,
      </if>
      <if test="ctripNoWorriedLower != null">
        ctrip_no_worried_lower,
      </if>
      <if test="otherNoWorriedCeiling != null">
        other_no_worried_ceiling,
      </if>
      <if test="otherNoWorriedLower != null">
        other_no_worried_lower,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="channelCityId != null">
        #{channelCityId,jdbcType=BIGINT},
      </if>
      <if test="sassCityId != null">
        #{sassCityId,jdbcType=BIGINT},
      </if>
      <if test="vehicleSeriesId != null">
        #{vehicleSeriesId,jdbcType=BIGINT},
      </if>
      <if test="vehicleGroupId != null">
        #{vehicleGroupId,jdbcType=BIGINT},
      </if>
      <if test="standardProductId != null">
        #{standardProductId,jdbcType=BIGINT},
      </if>
      <if test="upperPrice != null">
        #{upperPrice,jdbcType=INTEGER},
      </if>
      <if test="lowerPrice != null">
        #{lowerPrice,jdbcType=INTEGER},
      </if>
      <if test="baiscInsurancePriceUpper != null">
        #{baiscInsurancePriceUpper,jdbcType=INTEGER},
      </if>
      <if test="baiscInsurancePriceLower != null">
        #{baiscInsurancePriceLower,jdbcType=INTEGER},
      </if>
      <if test="baiscInsurancePriceGap != null">
        #{baiscInsurancePriceGap,jdbcType=INTEGER},
      </if>
      <if test="poundageUpper != null">
        #{poundageUpper,jdbcType=INTEGER},
      </if>
      <if test="poundageLower != null">
        #{poundageLower,jdbcType=INTEGER},
      </if>
      <if test="poundageGap != null">
        #{poundageGap,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="premiuminsurancePriceUpper != null">
        #{premiuminsurancePriceUpper,jdbcType=INTEGER},
      </if>
      <if test="premiuminsurancePriceLower != null">
        #{premiuminsurancePriceLower,jdbcType=INTEGER},
      </if>
      <if test="premiuminsurancePriceGap != null">
        #{premiuminsurancePriceGap,jdbcType=INTEGER},
      </if>
      <if test="exclusiveinsurancePriceUpper != null">
        #{exclusiveinsurancePriceUpper,jdbcType=INTEGER},
      </if>
      <if test="exclusiveinsurancepricelower != null">
        #{exclusiveinsurancepricelower,jdbcType=INTEGER},
      </if>
      <if test="exclusiveinsurancepricegap != null">
        #{exclusiveinsurancepricegap,jdbcType=INTEGER},
      </if>
      <if test="carRentalDepositCeiling != null">
        #{carRentalDepositCeiling,jdbcType=INTEGER},
      </if>
      <if test="carRentalDepositLower != null">
        #{carRentalDepositLower,jdbcType=INTEGER},
      </if>
      <if test="illegalDepositCeiling != null">
        #{illegalDepositCeiling,jdbcType=INTEGER},
      </if>
      <if test="illegalDepositLower != null">
        #{illegalDepositLower,jdbcType=INTEGER},
      </if>
      <if test="ctripNoWorriedCeiling != null">
        #{ctripNoWorriedCeiling,jdbcType=DECIMAL},
      </if>
      <if test="ctripNoWorriedLower != null">
        #{ctripNoWorriedLower,jdbcType=DECIMAL},
      </if>
      <if test="otherNoWorriedCeiling != null">
        #{otherNoWorriedCeiling,jdbcType=DECIMAL},
      </if>
      <if test="otherNoWorriedLower != null">
        #{otherNoWorriedLower,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.CtripPriceFilterInfoExample" resultType="java.lang.Long">
    select count(*) from ctrip_price_filter_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ctrip_price_filter_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.channelCityId != null">
        channel_city_id = #{record.channelCityId,jdbcType=BIGINT},
      </if>
      <if test="record.sassCityId != null">
        sass_city_id = #{record.sassCityId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleSeriesId != null">
        vehicle_series_id = #{record.vehicleSeriesId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleGroupId != null">
        vehicle_group_id = #{record.vehicleGroupId,jdbcType=BIGINT},
      </if>
      <if test="record.standardProductId != null">
        standard_product_id = #{record.standardProductId,jdbcType=BIGINT},
      </if>
      <if test="record.upperPrice != null">
        upper_price = #{record.upperPrice,jdbcType=INTEGER},
      </if>
      <if test="record.lowerPrice != null">
        lower_price = #{record.lowerPrice,jdbcType=INTEGER},
      </if>
      <if test="record.baiscInsurancePriceUpper != null">
        baisc_insurance_price_upper = #{record.baiscInsurancePriceUpper,jdbcType=INTEGER},
      </if>
      <if test="record.baiscInsurancePriceLower != null">
        baisc_insurance_price_lower = #{record.baiscInsurancePriceLower,jdbcType=INTEGER},
      </if>
      <if test="record.baiscInsurancePriceGap != null">
        baisc_insurance_price_gap = #{record.baiscInsurancePriceGap,jdbcType=INTEGER},
      </if>
      <if test="record.poundageUpper != null">
        poundage_upper = #{record.poundageUpper,jdbcType=INTEGER},
      </if>
      <if test="record.poundageLower != null">
        poundage_lower = #{record.poundageLower,jdbcType=INTEGER},
      </if>
      <if test="record.poundageGap != null">
        poundage_gap = #{record.poundageGap,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.premiuminsurancePriceUpper != null">
        premiumInsurance_price_upper = #{record.premiuminsurancePriceUpper,jdbcType=INTEGER},
      </if>
      <if test="record.premiuminsurancePriceLower != null">
        premiumInsurance_price_lower = #{record.premiuminsurancePriceLower,jdbcType=INTEGER},
      </if>
      <if test="record.premiuminsurancePriceGap != null">
        premiumInsurance_price_gap = #{record.premiuminsurancePriceGap,jdbcType=INTEGER},
      </if>
      <if test="record.exclusiveinsurancePriceUpper != null">
        exclusiveInsurance_price_upper = #{record.exclusiveinsurancePriceUpper,jdbcType=INTEGER},
      </if>
      <if test="record.exclusiveinsurancepricelower != null">
        exclusiveInsurancePriceLower = #{record.exclusiveinsurancepricelower,jdbcType=INTEGER},
      </if>
      <if test="record.exclusiveinsurancepricegap != null">
        exclusiveInsurancePriceGap = #{record.exclusiveinsurancepricegap,jdbcType=INTEGER},
      </if>
      <if test="record.carRentalDepositCeiling != null">
        car_rental_deposit_ceiling = #{record.carRentalDepositCeiling,jdbcType=INTEGER},
      </if>
      <if test="record.carRentalDepositLower != null">
        car_rental_deposit_lower = #{record.carRentalDepositLower,jdbcType=INTEGER},
      </if>
      <if test="record.illegalDepositCeiling != null">
        illegal_deposit_ceiling = #{record.illegalDepositCeiling,jdbcType=INTEGER},
      </if>
      <if test="record.illegalDepositLower != null">
        illegal_deposit_lower = #{record.illegalDepositLower,jdbcType=INTEGER},
      </if>
      <if test="record.ctripNoWorriedCeiling != null">
        ctrip_no_worried_ceiling = #{record.ctripNoWorriedCeiling,jdbcType=DECIMAL},
      </if>
      <if test="record.ctripNoWorriedLower != null">
        ctrip_no_worried_lower = #{record.ctripNoWorriedLower,jdbcType=DECIMAL},
      </if>
      <if test="record.otherNoWorriedCeiling != null">
        other_no_worried_ceiling = #{record.otherNoWorriedCeiling,jdbcType=DECIMAL},
      </if>
      <if test="record.otherNoWorriedLower != null">
        other_no_worried_lower = #{record.otherNoWorriedLower,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ctrip_price_filter_info
    set id = #{record.id,jdbcType=BIGINT},
    merchant_id = #{record.merchantId,jdbcType=BIGINT},
    channel_id = #{record.channelId,jdbcType=BIGINT},
    store_id = #{record.storeId,jdbcType=BIGINT},
    channel_city_id = #{record.channelCityId,jdbcType=BIGINT},
    sass_city_id = #{record.sassCityId,jdbcType=BIGINT},
    vehicle_series_id = #{record.vehicleSeriesId,jdbcType=BIGINT},
    vehicle_group_id = #{record.vehicleGroupId,jdbcType=BIGINT},
    standard_product_id = #{record.standardProductId,jdbcType=BIGINT},
    upper_price = #{record.upperPrice,jdbcType=INTEGER},
    lower_price = #{record.lowerPrice,jdbcType=INTEGER},
    baisc_insurance_price_upper = #{record.baiscInsurancePriceUpper,jdbcType=INTEGER},
    baisc_insurance_price_lower = #{record.baiscInsurancePriceLower,jdbcType=INTEGER},
    baisc_insurance_price_gap = #{record.baiscInsurancePriceGap,jdbcType=INTEGER},
    poundage_upper = #{record.poundageUpper,jdbcType=INTEGER},
    poundage_lower = #{record.poundageLower,jdbcType=INTEGER},
    poundage_gap = #{record.poundageGap,jdbcType=INTEGER},
    create_time = #{record.createTime,jdbcType=BIGINT},
    op_time = #{record.opTime,jdbcType=BIGINT},
    last_ver = #{record.lastVer,jdbcType=INTEGER},
    premiumInsurance_price_upper = #{record.premiuminsurancePriceUpper,jdbcType=INTEGER},
    premiumInsurance_price_lower = #{record.premiuminsurancePriceLower,jdbcType=INTEGER},
    premiumInsurance_price_gap = #{record.premiuminsurancePriceGap,jdbcType=INTEGER},
    exclusiveInsurance_price_upper = #{record.exclusiveinsurancePriceUpper,jdbcType=INTEGER},
    exclusiveInsurancePriceLower = #{record.exclusiveinsurancepricelower,jdbcType=INTEGER},
    exclusiveInsurancePriceGap = #{record.exclusiveinsurancepricegap,jdbcType=INTEGER},
    car_rental_deposit_ceiling = #{record.carRentalDepositCeiling,jdbcType=INTEGER},
    car_rental_deposit_lower = #{record.carRentalDepositLower,jdbcType=INTEGER},
    illegal_deposit_ceiling = #{record.illegalDepositCeiling,jdbcType=INTEGER},
    illegal_deposit_lower = #{record.illegalDepositLower,jdbcType=INTEGER},
    ctrip_no_worried_ceiling = #{record.ctripNoWorriedCeiling,jdbcType=DECIMAL},
    ctrip_no_worried_lower = #{record.ctripNoWorriedLower,jdbcType=DECIMAL},
    other_no_worried_ceiling = #{record.otherNoWorriedCeiling,jdbcType=DECIMAL},
    other_no_worried_lower = #{record.otherNoWorriedLower,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.CtripPriceFilterInfo">
    update ctrip_price_filter_info
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="channelCityId != null">
        channel_city_id = #{channelCityId,jdbcType=BIGINT},
      </if>
      <if test="sassCityId != null">
        sass_city_id = #{sassCityId,jdbcType=BIGINT},
      </if>
      <if test="vehicleSeriesId != null">
        vehicle_series_id = #{vehicleSeriesId,jdbcType=BIGINT},
      </if>
      <if test="vehicleGroupId != null">
        vehicle_group_id = #{vehicleGroupId,jdbcType=BIGINT},
      </if>
      <if test="standardProductId != null">
        standard_product_id = #{standardProductId,jdbcType=BIGINT},
      </if>
      <if test="upperPrice != null">
        upper_price = #{upperPrice,jdbcType=INTEGER},
      </if>
      <if test="lowerPrice != null">
        lower_price = #{lowerPrice,jdbcType=INTEGER},
      </if>
      <if test="baiscInsurancePriceUpper != null">
        baisc_insurance_price_upper = #{baiscInsurancePriceUpper,jdbcType=INTEGER},
      </if>
      <if test="baiscInsurancePriceLower != null">
        baisc_insurance_price_lower = #{baiscInsurancePriceLower,jdbcType=INTEGER},
      </if>
      <if test="baiscInsurancePriceGap != null">
        baisc_insurance_price_gap = #{baiscInsurancePriceGap,jdbcType=INTEGER},
      </if>
      <if test="poundageUpper != null">
        poundage_upper = #{poundageUpper,jdbcType=INTEGER},
      </if>
      <if test="poundageLower != null">
        poundage_lower = #{poundageLower,jdbcType=INTEGER},
      </if>
      <if test="poundageGap != null">
        poundage_gap = #{poundageGap,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="premiuminsurancePriceUpper != null">
        premiumInsurance_price_upper = #{premiuminsurancePriceUpper,jdbcType=INTEGER},
      </if>
      <if test="premiuminsurancePriceLower != null">
        premiumInsurance_price_lower = #{premiuminsurancePriceLower,jdbcType=INTEGER},
      </if>
      <if test="premiuminsurancePriceGap != null">
        premiumInsurance_price_gap = #{premiuminsurancePriceGap,jdbcType=INTEGER},
      </if>
      <if test="exclusiveinsurancePriceUpper != null">
        exclusiveInsurance_price_upper = #{exclusiveinsurancePriceUpper,jdbcType=INTEGER},
      </if>
      <if test="exclusiveinsurancepricelower != null">
        exclusiveInsurancePriceLower = #{exclusiveinsurancepricelower,jdbcType=INTEGER},
      </if>
      <if test="exclusiveinsurancepricegap != null">
        exclusiveInsurancePriceGap = #{exclusiveinsurancepricegap,jdbcType=INTEGER},
      </if>
      <if test="carRentalDepositCeiling != null">
        car_rental_deposit_ceiling = #{carRentalDepositCeiling,jdbcType=INTEGER},
      </if>
      <if test="carRentalDepositLower != null">
        car_rental_deposit_lower = #{carRentalDepositLower,jdbcType=INTEGER},
      </if>
      <if test="illegalDepositCeiling != null">
        illegal_deposit_ceiling = #{illegalDepositCeiling,jdbcType=INTEGER},
      </if>
      <if test="illegalDepositLower != null">
        illegal_deposit_lower = #{illegalDepositLower,jdbcType=INTEGER},
      </if>
      <if test="ctripNoWorriedCeiling != null">
        ctrip_no_worried_ceiling = #{ctripNoWorriedCeiling,jdbcType=DECIMAL},
      </if>
      <if test="ctripNoWorriedLower != null">
        ctrip_no_worried_lower = #{ctripNoWorriedLower,jdbcType=DECIMAL},
      </if>
      <if test="otherNoWorriedCeiling != null">
        other_no_worried_ceiling = #{otherNoWorriedCeiling,jdbcType=DECIMAL},
      </if>
      <if test="otherNoWorriedLower != null">
        other_no_worried_lower = #{otherNoWorriedLower,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.CtripPriceFilterInfo">
    update ctrip_price_filter_info
    set merchant_id = #{merchantId,jdbcType=BIGINT},
        channel_id = #{channelId,jdbcType=BIGINT},
        store_id = #{storeId,jdbcType=BIGINT},
        channel_city_id = #{channelCityId,jdbcType=BIGINT},
        sass_city_id = #{sassCityId,jdbcType=BIGINT},
        vehicle_series_id = #{vehicleSeriesId,jdbcType=BIGINT},
        vehicle_group_id = #{vehicleGroupId,jdbcType=BIGINT},
        standard_product_id = #{standardProductId,jdbcType=BIGINT},
        upper_price = #{upperPrice,jdbcType=INTEGER},
        lower_price = #{lowerPrice,jdbcType=INTEGER},
        baisc_insurance_price_upper = #{baiscInsurancePriceUpper,jdbcType=INTEGER},
        baisc_insurance_price_lower = #{baiscInsurancePriceLower,jdbcType=INTEGER},
        baisc_insurance_price_gap = #{baiscInsurancePriceGap,jdbcType=INTEGER},
        poundage_upper = #{poundageUpper,jdbcType=INTEGER},
        poundage_lower = #{poundageLower,jdbcType=INTEGER},
        poundage_gap = #{poundageGap,jdbcType=INTEGER},
        create_time = #{createTime,jdbcType=BIGINT},
        op_time = #{opTime,jdbcType=BIGINT},
        last_ver = #{lastVer,jdbcType=INTEGER},
        premiumInsurance_price_upper = #{premiuminsurancePriceUpper,jdbcType=INTEGER},
        premiumInsurance_price_lower = #{premiuminsurancePriceLower,jdbcType=INTEGER},
        premiumInsurance_price_gap = #{premiuminsurancePriceGap,jdbcType=INTEGER},
        exclusiveInsurance_price_upper = #{exclusiveinsurancePriceUpper,jdbcType=INTEGER},
        exclusiveInsurancePriceLower = #{exclusiveinsurancepricelower,jdbcType=INTEGER},
        exclusiveInsurancePriceGap = #{exclusiveinsurancepricegap,jdbcType=INTEGER},
        car_rental_deposit_ceiling = #{carRentalDepositCeiling,jdbcType=INTEGER},
        car_rental_deposit_lower = #{carRentalDepositLower,jdbcType=INTEGER},
        illegal_deposit_ceiling = #{illegalDepositCeiling,jdbcType=INTEGER},
        illegal_deposit_lower = #{illegalDepositLower,jdbcType=INTEGER},
        ctrip_no_worried_ceiling = #{ctripNoWorriedCeiling,jdbcType=DECIMAL},
        ctrip_no_worried_lower = #{ctripNoWorriedLower,jdbcType=DECIMAL},
        other_no_worried_ceiling = #{otherNoWorriedCeiling,jdbcType=DECIMAL},
        other_no_worried_lower = #{otherNoWorriedLower,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into ctrip_price_filter_info
    (merchant_id, channel_id, store_id, channel_city_id, sass_city_id, vehicle_series_id,
    vehicle_group_id, standard_product_id, upper_price, lower_price, baisc_insurance_price_upper,
    baisc_insurance_price_lower, baisc_insurance_price_gap, poundage_upper, poundage_lower,
    poundage_gap, create_time, op_time, last_ver, premiumInsurance_price_upper, premiumInsurance_price_lower,
    premiumInsurance_price_gap, exclusiveInsurance_price_upper, exclusiveInsurancePriceLower,
    exclusiveInsurancePriceGap, car_rental_deposit_ceiling, car_rental_deposit_lower,
    illegal_deposit_ceiling, illegal_deposit_lower, ctrip_no_worried_ceiling, ctrip_no_worried_lower,
    other_no_worried_ceiling, other_no_worried_lower)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.channelId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT},
      #{item.channelCityId,jdbcType=BIGINT}, #{item.sassCityId,jdbcType=BIGINT}, #{item.vehicleSeriesId,jdbcType=BIGINT},
      #{item.vehicleGroupId,jdbcType=BIGINT}, #{item.standardProductId,jdbcType=BIGINT},
      #{item.upperPrice,jdbcType=INTEGER}, #{item.lowerPrice,jdbcType=INTEGER}, #{item.baiscInsurancePriceUpper,jdbcType=INTEGER},
      #{item.baiscInsurancePriceLower,jdbcType=INTEGER}, #{item.baiscInsurancePriceGap,jdbcType=INTEGER},
      #{item.poundageUpper,jdbcType=INTEGER}, #{item.poundageLower,jdbcType=INTEGER},
      #{item.poundageGap,jdbcType=INTEGER}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT},
      #{item.lastVer,jdbcType=INTEGER}, #{item.premiuminsurancePriceUpper,jdbcType=INTEGER},
      #{item.premiuminsurancePriceLower,jdbcType=INTEGER}, #{item.premiuminsurancePriceGap,jdbcType=INTEGER},
      #{item.exclusiveinsurancePriceUpper,jdbcType=INTEGER}, #{item.exclusiveinsurancepricelower,jdbcType=INTEGER},
      #{item.exclusiveinsurancepricegap,jdbcType=INTEGER}, #{item.carRentalDepositCeiling,jdbcType=INTEGER},
      #{item.carRentalDepositLower,jdbcType=INTEGER}, #{item.illegalDepositCeiling,jdbcType=INTEGER},
      #{item.illegalDepositLower,jdbcType=INTEGER}, #{item.ctripNoWorriedCeiling,jdbcType=DECIMAL},
      #{item.ctripNoWorriedLower,jdbcType=DECIMAL}, #{item.otherNoWorriedCeiling,jdbcType=DECIMAL},
      #{item.otherNoWorriedLower,jdbcType=DECIMAL})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into ctrip_price_filter_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'channel_id'.toString() == column.value">
          #{item.channelId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'channel_city_id'.toString() == column.value">
          #{item.channelCityId,jdbcType=BIGINT}
        </if>
        <if test="'sass_city_id'.toString() == column.value">
          #{item.sassCityId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_series_id'.toString() == column.value">
          #{item.vehicleSeriesId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_group_id'.toString() == column.value">
          #{item.vehicleGroupId,jdbcType=BIGINT}
        </if>
        <if test="'standard_product_id'.toString() == column.value">
          #{item.standardProductId,jdbcType=BIGINT}
        </if>
        <if test="'upper_price'.toString() == column.value">
          #{item.upperPrice,jdbcType=INTEGER}
        </if>
        <if test="'lower_price'.toString() == column.value">
          #{item.lowerPrice,jdbcType=INTEGER}
        </if>
        <if test="'baisc_insurance_price_upper'.toString() == column.value">
          #{item.baiscInsurancePriceUpper,jdbcType=INTEGER}
        </if>
        <if test="'baisc_insurance_price_lower'.toString() == column.value">
          #{item.baiscInsurancePriceLower,jdbcType=INTEGER}
        </if>
        <if test="'baisc_insurance_price_gap'.toString() == column.value">
          #{item.baiscInsurancePriceGap,jdbcType=INTEGER}
        </if>
        <if test="'poundage_upper'.toString() == column.value">
          #{item.poundageUpper,jdbcType=INTEGER}
        </if>
        <if test="'poundage_lower'.toString() == column.value">
          #{item.poundageLower,jdbcType=INTEGER}
        </if>
        <if test="'poundage_gap'.toString() == column.value">
          #{item.poundageGap,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'premiumInsurance_price_upper'.toString() == column.value">
          #{item.premiuminsurancePriceUpper,jdbcType=INTEGER}
        </if>
        <if test="'premiumInsurance_price_lower'.toString() == column.value">
          #{item.premiuminsurancePriceLower,jdbcType=INTEGER}
        </if>
        <if test="'premiumInsurance_price_gap'.toString() == column.value">
          #{item.premiuminsurancePriceGap,jdbcType=INTEGER}
        </if>
        <if test="'exclusiveInsurance_price_upper'.toString() == column.value">
          #{item.exclusiveinsurancePriceUpper,jdbcType=INTEGER}
        </if>
        <if test="'exclusiveInsurancePriceLower'.toString() == column.value">
          #{item.exclusiveinsurancepricelower,jdbcType=INTEGER}
        </if>
        <if test="'exclusiveInsurancePriceGap'.toString() == column.value">
          #{item.exclusiveinsurancepricegap,jdbcType=INTEGER}
        </if>
        <if test="'car_rental_deposit_ceiling'.toString() == column.value">
          #{item.carRentalDepositCeiling,jdbcType=INTEGER}
        </if>
        <if test="'car_rental_deposit_lower'.toString() == column.value">
          #{item.carRentalDepositLower,jdbcType=INTEGER}
        </if>
        <if test="'illegal_deposit_ceiling'.toString() == column.value">
          #{item.illegalDepositCeiling,jdbcType=INTEGER}
        </if>
        <if test="'illegal_deposit_lower'.toString() == column.value">
          #{item.illegalDepositLower,jdbcType=INTEGER}
        </if>
        <if test="'ctrip_no_worried_ceiling'.toString() == column.value">
          #{item.ctripNoWorriedCeiling,jdbcType=DECIMAL}
        </if>
        <if test="'ctrip_no_worried_lower'.toString() == column.value">
          #{item.ctripNoWorriedLower,jdbcType=DECIMAL}
        </if>
        <if test="'other_no_worried_ceiling'.toString() == column.value">
          #{item.otherNoWorriedCeiling,jdbcType=DECIMAL}
        </if>
        <if test="'other_no_worried_lower'.toString() == column.value">
          #{item.otherNoWorriedLower,jdbcType=DECIMAL}
        </if>
      </foreach>
      )
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List">
    <foreach close="" collection="list" index="index" item="record" open="" separator=";">
      update ctrip_price_filter_info
      <set>
        <if test="record.merchantId != null">
          merchant_id = #{record.merchantId,jdbcType=BIGINT},
        </if>
        <if test="record.channelId != null">
          channel_id = #{record.channelId,jdbcType=BIGINT},
        </if>
        <if test="record.storeId != null">
          store_id = #{record.storeId,jdbcType=BIGINT},
        </if>
        <if test="record.channelCityId != null">
          channel_city_id = #{record.channelCityId,jdbcType=BIGINT},
        </if>
        <if test="record.sassCityId != null">
          sass_city_id = #{record.sassCityId,jdbcType=BIGINT},
        </if>
        <if test="record.vehicleSeriesId != null">
          vehicle_series_id = #{record.vehicleSeriesId,jdbcType=BIGINT},
        </if>
        <if test="record.vehicleGroupId != null">
          vehicle_group_id = #{record.vehicleGroupId,jdbcType=BIGINT},
        </if>
        <if test="record.standardProductId != null">
          standard_product_id = #{record.standardProductId,jdbcType=BIGINT},
        </if>
        <if test="record.upperPrice != null">
          upper_price = #{record.upperPrice,jdbcType=INTEGER},
        </if>
        <if test="record.lowerPrice != null">
          lower_price = #{record.lowerPrice,jdbcType=INTEGER},
        </if>
        <if test="record.baiscInsurancePriceUpper != null">
          baisc_insurance_price_upper = #{record.baiscInsurancePriceUpper,jdbcType=INTEGER},
        </if>
        <if test="record.baiscInsurancePriceLower != null">
          baisc_insurance_price_lower = #{record.baiscInsurancePriceLower,jdbcType=INTEGER},
        </if>
        <if test="record.baiscInsurancePriceGap != null">
          baisc_insurance_price_gap = #{record.baiscInsurancePriceGap,jdbcType=INTEGER},
        </if>
        <if test="record.poundageUpper != null">
          poundage_upper = #{record.poundageUpper,jdbcType=INTEGER},
        </if>
        <if test="record.poundageLower != null">
          poundage_lower = #{record.poundageLower,jdbcType=INTEGER},
        </if>
        <if test="record.poundageGap != null">
          poundage_gap = #{record.poundageGap,jdbcType=INTEGER},
        </if>
        <if test="record.createTime != null">
          create_time = #{record.createTime,jdbcType=BIGINT},
        </if>
        <if test="record.opTime != null">
          op_time = #{record.opTime,jdbcType=BIGINT},
        </if>
        <if test="record.lastVer != null">
          last_ver = #{record.lastVer,jdbcType=INTEGER},
        </if>
        <if test="'record.premiumInsurance_price_upper'!= null">
          premiumInsurance_price_upper = #{record.premiuminsurancePriceUpper,jdbcType=INTEGER},
        </if>
        <if test="'record.premiumInsurance_price_lower'!= null">
          premiumInsurance_price_lower =  #{record.premiuminsurancePriceLower,jdbcType=INTEGER},
        </if>
        <if test="'record.premiumInsurance_price_gap'!= null">
          premiumInsurance_price_gap = #{record.premiuminsurancePriceGap,jdbcType=INTEGER},
        </if>
        <if test="'record.exclusiveInsurance_price_upper'!= null">
          exclusiveInsurance_price_upper = #{record.exclusiveinsurancePriceUpper,jdbcType=INTEGER},
        </if>
        <if test="'record.exclusiveInsurancePriceLower'!= null">
          exclusiveInsurancePriceLower = #{record.exclusiveinsurancepricelower,jdbcType=INTEGER},
        </if>
        <if test="'record.exclusiveInsurancePriceGap'!= null">
          exclusiveInsurancePriceGap = #{record.exclusiveinsurancepricegap,jdbcType=INTEGER},
        </if>
        <if test="record.carRentalDepositCeiling != null">
          car_rental_deposit_ceiling = #{record.carRentalDepositCeiling,jdbcType=INTEGER},
        </if>
        <if test="record.carRentalDepositLower != null">
          car_rental_deposit_lower = #{record.carRentalDepositLower,jdbcType=INTEGER},
        </if>
        <if test="record.illegalDepositCeiling != null">
          illegal_deposit_ceiling = #{record.illegalDepositCeiling,jdbcType=INTEGER},
        </if>
        <if test="record.illegalDepositLower != null">
          illegal_deposit_lower = #{record.illegalDepositLower,jdbcType=INTEGER},
        </if>
        <if test="record.ctripNoWorriedCeiling != null">
          ctrip_no_worried_ceiling = #{record.ctripNoWorriedCeiling,jdbcType=DECIMAL},
        </if>
        <if test="record.ctripNoWorriedLower != null">
          ctrip_no_worried_lower = #{record.ctripNoWorriedLower,jdbcType=DECIMAL},
        </if>
        <if test="record.otherNoWorriedCeiling != null">
          other_no_worried_ceiling = #{record.otherNoWorriedCeiling,jdbcType=DECIMAL},
        </if>
        <if test="record.otherNoWorriedLower != null">
          other_no_worried_lower = #{record.otherNoWorriedLower,jdbcType=DECIMAL},
        </if>
      </set>
      where id = #{record.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>
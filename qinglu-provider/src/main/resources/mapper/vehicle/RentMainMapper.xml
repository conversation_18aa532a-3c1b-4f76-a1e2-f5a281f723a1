<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.RentMainMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.RentMain">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="mileage_limit" jdbcType="TINYINT" property="mileageLimit" />
    <result column="mileage" jdbcType="SMALLINT" property="mileage" />
    <result column="mileage_rent" jdbcType="SMALLINT" property="mileageRent" />
    <result column="rent_deposit" jdbcType="INTEGER" property="rentDeposit" />
    <result column="illegal_deposit" jdbcType="INTEGER" property="illegalDeposit" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vehicle_model_id, store_id, mileage_limit, mileage, mileage_rent, rent_deposit, 
    illegal_deposit, status, ext, deleted, last_ver, create_time, op_time, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.RentMainExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from rent_main
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from rent_main
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from rent_main
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.RentMainExample">
    delete from rent_main
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.RentMain">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into rent_main (vehicle_model_id, store_id, mileage_limit, 
      mileage, mileage_rent, rent_deposit, 
      illegal_deposit, status, ext, 
      deleted, last_ver, create_time, 
      op_time, op_user_id)
    values (#{vehicleModelId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{mileageLimit,jdbcType=TINYINT}, 
      #{mileage,jdbcType=SMALLINT}, #{mileageRent,jdbcType=SMALLINT}, #{rentDeposit,jdbcType=INTEGER}, 
      #{illegalDeposit,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, #{ext,jdbcType=VARCHAR}, 
      #{deleted,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.RentMain">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into rent_main
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="mileageLimit != null">
        mileage_limit,
      </if>
      <if test="mileage != null">
        mileage,
      </if>
      <if test="mileageRent != null">
        mileage_rent,
      </if>
      <if test="rentDeposit != null">
        rent_deposit,
      </if>
      <if test="illegalDeposit != null">
        illegal_deposit,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="mileageLimit != null">
        #{mileageLimit,jdbcType=TINYINT},
      </if>
      <if test="mileage != null">
        #{mileage,jdbcType=SMALLINT},
      </if>
      <if test="mileageRent != null">
        #{mileageRent,jdbcType=SMALLINT},
      </if>
      <if test="rentDeposit != null">
        #{rentDeposit,jdbcType=INTEGER},
      </if>
      <if test="illegalDeposit != null">
        #{illegalDeposit,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.RentMainExample" resultType="java.lang.Long">
    select count(*) from rent_main
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update rent_main
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.mileageLimit != null">
        mileage_limit = #{record.mileageLimit,jdbcType=TINYINT},
      </if>
      <if test="record.mileage != null">
        mileage = #{record.mileage,jdbcType=SMALLINT},
      </if>
      <if test="record.mileageRent != null">
        mileage_rent = #{record.mileageRent,jdbcType=SMALLINT},
      </if>
      <if test="record.rentDeposit != null">
        rent_deposit = #{record.rentDeposit,jdbcType=INTEGER},
      </if>
      <if test="record.illegalDeposit != null">
        illegal_deposit = #{record.illegalDeposit,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.ext != null">
        ext = #{record.ext,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update rent_main
    set id = #{record.id,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      mileage_limit = #{record.mileageLimit,jdbcType=TINYINT},
      mileage = #{record.mileage,jdbcType=SMALLINT},
      mileage_rent = #{record.mileageRent,jdbcType=SMALLINT},
      rent_deposit = #{record.rentDeposit,jdbcType=INTEGER},
      illegal_deposit = #{record.illegalDeposit,jdbcType=INTEGER},
      status = #{record.status,jdbcType=TINYINT},
      ext = #{record.ext,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.RentMain">
    update rent_main
    <set>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="mileageLimit != null">
        mileage_limit = #{mileageLimit,jdbcType=TINYINT},
      </if>
      <if test="mileage != null">
        mileage = #{mileage,jdbcType=SMALLINT},
      </if>
      <if test="mileageRent != null">
        mileage_rent = #{mileageRent,jdbcType=SMALLINT},
      </if>
      <if test="rentDeposit != null">
        rent_deposit = #{rentDeposit,jdbcType=INTEGER},
      </if>
      <if test="illegalDeposit != null">
        illegal_deposit = #{illegalDeposit,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.RentMain">
    update rent_main
    set vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      mileage_limit = #{mileageLimit,jdbcType=TINYINT},
      mileage = #{mileage,jdbcType=SMALLINT},
      mileage_rent = #{mileageRent,jdbcType=SMALLINT},
      rent_deposit = #{rentDeposit,jdbcType=INTEGER},
      illegal_deposit = #{illegalDeposit,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      ext = #{ext,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into rent_main
    (vehicle_model_id, store_id, mileage_limit, mileage, mileage_rent, rent_deposit, 
      illegal_deposit, status, ext, deleted, last_ver, create_time, op_time, op_user_id
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.vehicleModelId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.mileageLimit,jdbcType=TINYINT}, 
        #{item.mileage,jdbcType=SMALLINT}, #{item.mileageRent,jdbcType=SMALLINT}, #{item.rentDeposit,jdbcType=INTEGER}, 
        #{item.illegalDeposit,jdbcType=INTEGER}, #{item.status,jdbcType=TINYINT}, #{item.ext,jdbcType=VARCHAR}, 
        #{item.deleted,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into rent_main (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'mileage_limit'.toString() == column.value">
          #{item.mileageLimit,jdbcType=TINYINT}
        </if>
        <if test="'mileage'.toString() == column.value">
          #{item.mileage,jdbcType=SMALLINT}
        </if>
        <if test="'mileage_rent'.toString() == column.value">
          #{item.mileageRent,jdbcType=SMALLINT}
        </if>
        <if test="'rent_deposit'.toString() == column.value">
          #{item.rentDeposit,jdbcType=INTEGER}
        </if>
        <if test="'illegal_deposit'.toString() == column.value">
          #{item.illegalDeposit,jdbcType=INTEGER}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'ext'.toString() == column.value">
          #{item.ext,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
  <update id="batchUpdate" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <foreach collection="list" item="item" separator=";">
      update rent_main
      <set>
        <if test="item.vehicleModelId != null">
          vehicle_model_id = #{item.vehicleModelId,jdbcType=BIGINT},
        </if>
        <if test="item.storeId != null">
          store_id = #{item.storeId,jdbcType=BIGINT},
        </if>
        <if test="item.mileageLimit != null">
          mileage_limit = #{item.mileageLimit,jdbcType=TINYINT},
        </if>
        <if test="item.mileage != null">
          mileage = #{item.mileage,jdbcType=SMALLINT},
        </if>
        <if test="item.mileageRent != null">
          mileage_rent = #{item.mileageRent,jdbcType=SMALLINT},
        </if>
        <if test="item.rentDeposit != null">
          rent_deposit = #{item.rentDeposit,jdbcType=INTEGER},
        </if>
        <if test="item.illegalDeposit != null">
          illegal_deposit = #{item.illegalDeposit,jdbcType=INTEGER},
        </if>
        <if test="item.status != null">
          status = #{item.status,jdbcType=TINYINT},
        </if>
        <if test="item.deleted != null">
          deleted = #{item.deleted,jdbcType=TINYINT},
        </if>
        <if test="item.lastVer != null">
          last_ver = #{item.lastVer,jdbcType=INTEGER},
        </if>
        <if test="item.createTime != null">
          create_time = #{item.createTime,jdbcType=BIGINT},
        </if>
        <if test="item.opTime != null">
          op_time = #{item.opTime,jdbcType=BIGINT},
        </if>
        <if test="item.opUserId != null">
          op_user_id = #{item.opUserId,jdbcType=BIGINT},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>

    </update>
</mapper>
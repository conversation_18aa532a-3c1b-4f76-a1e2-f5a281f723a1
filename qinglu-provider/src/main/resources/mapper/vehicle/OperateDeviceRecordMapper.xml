<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.OperateDeviceRecordMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.OperateDeviceRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="device_no" jdbcType="VARCHAR" property="deviceNo" />
    <result column="control_command" jdbcType="VARCHAR" property="controlCommand" />
    <result column="result" jdbcType="VARCHAR" property="result" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="curr_time" jdbcType="BIGINT" property="currTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, order_id, vehicle_id, device_no, control_command, result, op_time, 
    curr_time, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.OperateDeviceRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from operate_device_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from operate_device_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from operate_device_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.OperateDeviceRecordExample">
    delete from operate_device_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.OperateDeviceRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into operate_device_record (merchant_id, order_id, vehicle_id, 
      device_no, control_command, result, 
      op_time, curr_time, op_user_id
      )
    values (#{merchantId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, 
      #{deviceNo,jdbcType=VARCHAR}, #{controlCommand,jdbcType=VARCHAR}, #{result,jdbcType=VARCHAR}, 
      #{opTime,jdbcType=BIGINT}, #{currTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.OperateDeviceRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into operate_device_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="deviceNo != null">
        device_no,
      </if>
      <if test="controlCommand != null">
        control_command,
      </if>
      <if test="result != null">
        result,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="currTime != null">
        curr_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="deviceNo != null">
        #{deviceNo,jdbcType=VARCHAR},
      </if>
      <if test="controlCommand != null">
        #{controlCommand,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="currTime != null">
        #{currTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.OperateDeviceRecordExample" resultType="java.lang.Long">
    select count(*) from operate_device_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update operate_device_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="record.deviceNo != null">
        device_no = #{record.deviceNo,jdbcType=VARCHAR},
      </if>
      <if test="record.controlCommand != null">
        control_command = #{record.controlCommand,jdbcType=VARCHAR},
      </if>
      <if test="record.result != null">
        result = #{record.result,jdbcType=VARCHAR},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.currTime != null">
        curr_time = #{record.currTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update operate_device_record
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      device_no = #{record.deviceNo,jdbcType=VARCHAR},
      control_command = #{record.controlCommand,jdbcType=VARCHAR},
      result = #{record.result,jdbcType=VARCHAR},
      op_time = #{record.opTime,jdbcType=BIGINT},
      curr_time = #{record.currTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.OperateDeviceRecord">
    update operate_device_record
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="deviceNo != null">
        device_no = #{deviceNo,jdbcType=VARCHAR},
      </if>
      <if test="controlCommand != null">
        control_command = #{controlCommand,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        result = #{result,jdbcType=VARCHAR},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="currTime != null">
        curr_time = #{currTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.OperateDeviceRecord">
    update operate_device_record
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      device_no = #{deviceNo,jdbcType=VARCHAR},
      control_command = #{controlCommand,jdbcType=VARCHAR},
      result = #{result,jdbcType=VARCHAR},
      op_time = #{opTime,jdbcType=BIGINT},
      curr_time = #{currTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into operate_device_record
    (merchant_id, order_id, vehicle_id, device_no, control_command, result, op_time, 
      curr_time, op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.orderId,jdbcType=BIGINT}, #{item.vehicleId,jdbcType=BIGINT}, 
        #{item.deviceNo,jdbcType=VARCHAR}, #{item.controlCommand,jdbcType=VARCHAR}, #{item.result,jdbcType=VARCHAR}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.currTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into operate_device_record (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_id'.toString() == column.value">
          #{item.vehicleId,jdbcType=BIGINT}
        </if>
        <if test="'device_no'.toString() == column.value">
          #{item.deviceNo,jdbcType=VARCHAR}
        </if>
        <if test="'control_command'.toString() == column.value">
          #{item.controlCommand,jdbcType=VARCHAR}
        </if>
        <if test="'result'.toString() == column.value">
          #{item.result,jdbcType=VARCHAR}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'curr_time'.toString() == column.value">
          #{item.currTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.VehicleSeryBindMapper">

  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.VehicleSeryBind">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="inner_id" jdbcType="BIGINT" property="innerId" />
    <result column="inner_type" jdbcType="TINYINT" property="innerType" />
    <result column="relation_outer" jdbcType="VARCHAR" property="relationOuter" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, inner_id, inner_type, relation_outer, channel_id, deleted, op_user_id, create_time, op_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from vehicle_sery_bind
    where id = #{id,jdbcType=BIGINT}
  </select>

  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.VehicleSeryBind" useGeneratedKeys="true"
    keyProperty="id">
    insert into vehicle_sery_bind
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="innerId != null">
        inner_id,
      </if>
      <if test="innerType != null">
        inner_type,
      </if>
      <if test="relationOuter != null">
        relation_outer,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="innerId != null">
        #{innerId,jdbcType=BIGINT},
      </if>
      <if test="innerType != null">
        #{innerType,jdbcType=TINYINT},
      </if>
      <if test="relationOuter != null">
        #{relationOuter,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.VehicleSeryBind">
    update vehicle_sery_bind
    <set>
      <if test="innerId != null">
        inner_id = #{innerId,jdbcType=BIGINT},
      </if>
      <if test="innerType != null">
        inner_type = #{innerType,jdbcType=TINYINT},
      </if>
      <if test="relationOuter != null">
        relation_outer = #{relationOuter,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByInnerIdAndChannelId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from vehicle_sery_bind
    where inner_id = #{innerId,jdbcType=BIGINT}
    and channel_id = #{channelId,jdbcType=BIGINT}
    and inner_type = #{innerType,jdbcType=TINYINT}
    and deleted = 0
  </select>

  <select id="selectBrandBindsByChannelId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from vehicle_sery_bind
    where channel_id = #{channelId,jdbcType=BIGINT}
    and inner_type = 0
    and deleted = 0
    <if test="brandId != null">
      and inner_id = #{brandId,jdbcType=BIGINT}
    </if>
    order by op_time desc
    <if test="offset != null and pageSize != null">
      limit #{offset}, #{pageSize}
    </if>
  </select>

  <select id="countBrandBindsByChannelId" resultType="java.lang.Long">
    select count(1)
    from vehicle_sery_bind
    where channel_id = #{channelId,jdbcType=BIGINT}
    and inner_type = 0
    and deleted = 0
    <if test="brandId != null">
      and inner_id = #{brandId,jdbcType=BIGINT}
    </if>
  </select>

  <update id="deleteByInnerIdAndChannelId">
    update vehicle_sery_bind
    set deleted = 1,
    op_user_id = #{opUserId,jdbcType=BIGINT},
    op_time = #{opTime,jdbcType=BIGINT}
    where inner_id = #{innerId,jdbcType=BIGINT}
    and channel_id = #{channelId,jdbcType=BIGINT}
    and inner_type = #{innerType,jdbcType=TINYINT}
    and deleted = 0
  </update>

  <select id="selectByBrandIdsAndChannelId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from vehicle_sery_bind
    where channel_id = #{channelId,jdbcType=BIGINT}
    and inner_type = 0
    and deleted = 0
    and inner_id in
    <foreach collection="brandIds" item="brandId" open="(" separator="," close=")">
      #{brandId,jdbcType=BIGINT}
    </foreach>
    order by op_time desc
  </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.VehicleSubSeryMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.VehicleSubSery">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="sery_id" jdbcType="BIGINT" property="seryId" />
    <result column="brand_id" jdbcType="BIGINT" property="brandId" />
    <result column="doors" jdbcType="VARCHAR" property="doors" />
    <result column="passengers" jdbcType="VARCHAR" property="passengers" />
    <result column="displacement" jdbcType="VARCHAR" property="displacement" />
    <result column="transmission" jdbcType="VARCHAR" property="transmission" />
    <result column="gearbox" jdbcType="VARCHAR" property="gearbox" />
    <result column="years" jdbcType="VARCHAR" property="years" />
    <result column="carriage" jdbcType="VARCHAR" property="carriage" />
    <result column="vehicle_level" jdbcType="VARCHAR" property="vehicleLevel" />
    <result column="max_oil_liter" jdbcType="INTEGER" property="maxOilLiter" />
    <result column="fuel_form" jdbcType="VARCHAR" property="fuelForm" />
    <result column="fuel_form_detail" jdbcType="VARCHAR" property="fuelFormDetail" />
    <result column="fuel_num" jdbcType="VARCHAR" property="fuelNum" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="drive_type" jdbcType="VARCHAR" property="driveType" />
    <result column="length" jdbcType="SMALLINT" property="length" />
    <result column="width" jdbcType="SMALLINT" property="width" />
    <result column="height" jdbcType="SMALLINT" property="height" />
    <result column="wheelbase" jdbcType="SMALLINT" property="wheelbase" />
    <result column="luggage" jdbcType="TINYINT" property="luggage" />
    <result column="sunroof" jdbcType="VARCHAR" property="sunroof" />
    <result column="aircontition" jdbcType="VARCHAR" property="aircontition" />
    <result column="screen" jdbcType="VARCHAR" property="screen" />
    <result column="fast_charge_time" jdbcType="TINYINT" property="fastChargeTime" />
    <result column="ctrip_vehicle_model_id" jdbcType="BIGINT" property="ctripVehicleModelId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="preset" jdbcType="TINYINT" property="preset" />
    <result column="ctrip_sub_sery_id" jdbcType="BIGINT" property="ctripSubSeryId" />
    <result column="luxury" jdbcType="TINYINT" property="luxury" />
    <result column="model_group_id" jdbcType="BIGINT" property="modelGroupId" />
    <result column="model_group_name" jdbcType="VARCHAR" property="modelGroupName" />
    <result column="endurance_mileage" jdbcType="INTEGER" property="enduranceMileage" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, sery_id, brand_id, doors, passengers, displacement, transmission, gearbox, 
    years, carriage, vehicle_level, max_oil_liter, fuel_form, fuel_form_detail, fuel_num, 
    price, drive_type, length, width, height, wheelbase, luggage, sunroof, aircontition, 
    screen, fast_charge_time, ctrip_vehicle_model_id, store_id, merchant_id, preset, 
    ctrip_sub_sery_id, luxury, model_group_id, model_group_name, endurance_mileage, deleted,
    create_time, op_time, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.VehicleSubSeryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_sub_sery
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_sub_sery
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_sub_sery
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.VehicleSubSeryExample">
    delete from vehicle_sub_sery
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.VehicleSubSery">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_sub_sery (name, sery_id, brand_id, 
      doors, passengers, displacement, 
      transmission, gearbox, years, 
      carriage, vehicle_level, max_oil_liter, 
      fuel_form, fuel_form_detail, fuel_num, 
      price, drive_type, length, 
      width, height, wheelbase, 
      luggage, sunroof, aircontition, 
      screen, fast_charge_time, ctrip_vehicle_model_id, 
      store_id, merchant_id, preset, 
      ctrip_sub_sery_id, luxury, model_group_id,
      model_group_name, endurance_mileage, deleted,
      create_time, op_time, op_user_id
      )
    values (#{name,jdbcType=VARCHAR}, #{seryId,jdbcType=BIGINT}, #{brandId,jdbcType=BIGINT}, 
      #{doors,jdbcType=VARCHAR}, #{passengers,jdbcType=VARCHAR}, #{displacement,jdbcType=VARCHAR}, 
      #{transmission,jdbcType=VARCHAR}, #{gearbox,jdbcType=VARCHAR}, #{years,jdbcType=VARCHAR}, 
      #{carriage,jdbcType=VARCHAR}, #{vehicleLevel,jdbcType=VARCHAR}, #{maxOilLiter,jdbcType=INTEGER}, 
      #{fuelForm,jdbcType=VARCHAR}, #{fuelFormDetail,jdbcType=VARCHAR}, #{fuelNum,jdbcType=VARCHAR}, 
      #{price,jdbcType=DECIMAL}, #{driveType,jdbcType=VARCHAR}, #{length,jdbcType=SMALLINT}, 
      #{width,jdbcType=SMALLINT}, #{height,jdbcType=SMALLINT}, #{wheelbase,jdbcType=SMALLINT}, 
      #{luggage,jdbcType=TINYINT}, #{sunroof,jdbcType=VARCHAR}, #{aircontition,jdbcType=VARCHAR}, 
      #{screen,jdbcType=VARCHAR}, #{fastChargeTime,jdbcType=TINYINT}, #{ctripVehicleModelId,jdbcType=BIGINT}, 
      #{storeId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, #{preset,jdbcType=TINYINT}, 
      #{ctripSubSeryId,jdbcType=BIGINT}, #{luxury,jdbcType=TINYINT}, #{modelGroupId,jdbcType=BIGINT},
      #{modelGroupName,jdbcType=VARCHAR}, #{enduranceMileage,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT},
      #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.VehicleSubSery">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_sub_sery
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="seryId != null">
        sery_id,
      </if>
      <if test="brandId != null">
        brand_id,
      </if>
      <if test="doors != null">
        doors,
      </if>
      <if test="passengers != null">
        passengers,
      </if>
      <if test="displacement != null">
        displacement,
      </if>
      <if test="transmission != null">
        transmission,
      </if>
      <if test="gearbox != null">
        gearbox,
      </if>
      <if test="years != null">
        years,
      </if>
      <if test="carriage != null">
        carriage,
      </if>
      <if test="vehicleLevel != null">
        vehicle_level,
      </if>
      <if test="maxOilLiter != null">
        max_oil_liter,
      </if>
      <if test="fuelForm != null">
        fuel_form,
      </if>
      <if test="fuelFormDetail != null">
        fuel_form_detail,
      </if>
      <if test="fuelNum != null">
        fuel_num,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="driveType != null">
        drive_type,
      </if>
      <if test="length != null">
        length,
      </if>
      <if test="width != null">
        width,
      </if>
      <if test="height != null">
        height,
      </if>
      <if test="wheelbase != null">
        wheelbase,
      </if>
      <if test="luggage != null">
        luggage,
      </if>
      <if test="sunroof != null">
        sunroof,
      </if>
      <if test="aircontition != null">
        aircontition,
      </if>
      <if test="screen != null">
        screen,
      </if>
      <if test="fastChargeTime != null">
        fast_charge_time,
      </if>
      <if test="ctripVehicleModelId != null">
        ctrip_vehicle_model_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="preset != null">
        preset,
      </if>
      <if test="ctripSubSeryId != null">
        ctrip_sub_sery_id,
      </if>
      <if test="luxury != null">
        luxury,
      </if>
      <if test="modelGroupId != null">
        model_group_id,
      </if>
      <if test="modelGroupName != null">
        model_group_name,
      </if>
      <if test="enduranceMileage != null">
        endurance_mileage,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="seryId != null">
        #{seryId,jdbcType=BIGINT},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=BIGINT},
      </if>
      <if test="doors != null">
        #{doors,jdbcType=VARCHAR},
      </if>
      <if test="passengers != null">
        #{passengers,jdbcType=VARCHAR},
      </if>
      <if test="displacement != null">
        #{displacement,jdbcType=VARCHAR},
      </if>
      <if test="transmission != null">
        #{transmission,jdbcType=VARCHAR},
      </if>
      <if test="gearbox != null">
        #{gearbox,jdbcType=VARCHAR},
      </if>
      <if test="years != null">
        #{years,jdbcType=VARCHAR},
      </if>
      <if test="carriage != null">
        #{carriage,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLevel != null">
        #{vehicleLevel,jdbcType=VARCHAR},
      </if>
      <if test="maxOilLiter != null">
        #{maxOilLiter,jdbcType=INTEGER},
      </if>
      <if test="fuelForm != null">
        #{fuelForm,jdbcType=VARCHAR},
      </if>
      <if test="fuelFormDetail != null">
        #{fuelFormDetail,jdbcType=VARCHAR},
      </if>
      <if test="fuelNum != null">
        #{fuelNum,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="driveType != null">
        #{driveType,jdbcType=VARCHAR},
      </if>
      <if test="length != null">
        #{length,jdbcType=SMALLINT},
      </if>
      <if test="width != null">
        #{width,jdbcType=SMALLINT},
      </if>
      <if test="height != null">
        #{height,jdbcType=SMALLINT},
      </if>
      <if test="wheelbase != null">
        #{wheelbase,jdbcType=SMALLINT},
      </if>
      <if test="luggage != null">
        #{luggage,jdbcType=TINYINT},
      </if>
      <if test="sunroof != null">
        #{sunroof,jdbcType=VARCHAR},
      </if>
      <if test="aircontition != null">
        #{aircontition,jdbcType=VARCHAR},
      </if>
      <if test="screen != null">
        #{screen,jdbcType=VARCHAR},
      </if>
      <if test="fastChargeTime != null">
        #{fastChargeTime,jdbcType=TINYINT},
      </if>
      <if test="ctripVehicleModelId != null">
        #{ctripVehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="preset != null">
        #{preset,jdbcType=TINYINT},
      </if>
      <if test="ctripSubSeryId != null">
        #{ctripSubSeryId,jdbcType=BIGINT},
      </if>
      <if test="luxury != null">
        #{luxury,jdbcType=TINYINT},
      </if>
      <if test="modelGroupId != null">
        #{modelGroupId,jdbcType=BIGINT},
      </if>
      <if test="modelGroupName != null">
        #{modelGroupName,jdbcType=VARCHAR},
      </if>
      <if test="enduranceMileage != null">
        #{enduranceMileage,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.VehicleSubSeryExample" resultType="java.lang.Long">
    select count(*) from vehicle_sub_sery
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_sub_sery
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.seryId != null">
        sery_id = #{record.seryId,jdbcType=BIGINT},
      </if>
      <if test="record.brandId != null">
        brand_id = #{record.brandId,jdbcType=BIGINT},
      </if>
      <if test="record.doors != null">
        doors = #{record.doors,jdbcType=VARCHAR},
      </if>
      <if test="record.passengers != null">
        passengers = #{record.passengers,jdbcType=VARCHAR},
      </if>
      <if test="record.displacement != null">
        displacement = #{record.displacement,jdbcType=VARCHAR},
      </if>
      <if test="record.transmission != null">
        transmission = #{record.transmission,jdbcType=VARCHAR},
      </if>
      <if test="record.gearbox != null">
        gearbox = #{record.gearbox,jdbcType=VARCHAR},
      </if>
      <if test="record.years != null">
        years = #{record.years,jdbcType=VARCHAR},
      </if>
      <if test="record.carriage != null">
        carriage = #{record.carriage,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleLevel != null">
        vehicle_level = #{record.vehicleLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.maxOilLiter != null">
        max_oil_liter = #{record.maxOilLiter,jdbcType=INTEGER},
      </if>
      <if test="record.fuelForm != null">
        fuel_form = #{record.fuelForm,jdbcType=VARCHAR},
      </if>
      <if test="record.fuelFormDetail != null">
        fuel_form_detail = #{record.fuelFormDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.fuelNum != null">
        fuel_num = #{record.fuelNum,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.driveType != null">
        drive_type = #{record.driveType,jdbcType=VARCHAR},
      </if>
      <if test="record.length != null">
        length = #{record.length,jdbcType=SMALLINT},
      </if>
      <if test="record.width != null">
        width = #{record.width,jdbcType=SMALLINT},
      </if>
      <if test="record.height != null">
        height = #{record.height,jdbcType=SMALLINT},
      </if>
      <if test="record.wheelbase != null">
        wheelbase = #{record.wheelbase,jdbcType=SMALLINT},
      </if>
      <if test="record.luggage != null">
        luggage = #{record.luggage,jdbcType=TINYINT},
      </if>
      <if test="record.sunroof != null">
        sunroof = #{record.sunroof,jdbcType=VARCHAR},
      </if>
      <if test="record.aircontition != null">
        aircontition = #{record.aircontition,jdbcType=VARCHAR},
      </if>
      <if test="record.screen != null">
        screen = #{record.screen,jdbcType=VARCHAR},
      </if>
      <if test="record.fastChargeTime != null">
        fast_charge_time = #{record.fastChargeTime,jdbcType=TINYINT},
      </if>
      <if test="record.ctripVehicleModelId != null">
        ctrip_vehicle_model_id = #{record.ctripVehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.preset != null">
        preset = #{record.preset,jdbcType=TINYINT},
      </if>
      <if test="record.ctripSubSeryId != null">
        ctrip_sub_sery_id = #{record.ctripSubSeryId,jdbcType=BIGINT},
      </if>
      <if test="record.luxury != null">
        luxury = #{record.luxury,jdbcType=TINYINT},
      </if>
      <if test="record.modelGroupId != null">
        model_group_id = #{record.modelGroupId,jdbcType=BIGINT},
      </if>
      <if test="record.modelGroupName != null">
        model_group_name = #{record.modelGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.enduranceMileage != null">
        endurance_mileage = #{record.enduranceMileage,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_sub_sery
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      sery_id = #{record.seryId,jdbcType=BIGINT},
      brand_id = #{record.brandId,jdbcType=BIGINT},
      doors = #{record.doors,jdbcType=VARCHAR},
      passengers = #{record.passengers,jdbcType=VARCHAR},
      displacement = #{record.displacement,jdbcType=VARCHAR},
      transmission = #{record.transmission,jdbcType=VARCHAR},
      gearbox = #{record.gearbox,jdbcType=VARCHAR},
      years = #{record.years,jdbcType=VARCHAR},
      carriage = #{record.carriage,jdbcType=VARCHAR},
      vehicle_level = #{record.vehicleLevel,jdbcType=VARCHAR},
      max_oil_liter = #{record.maxOilLiter,jdbcType=INTEGER},
      fuel_form = #{record.fuelForm,jdbcType=VARCHAR},
      fuel_form_detail = #{record.fuelFormDetail,jdbcType=VARCHAR},
      fuel_num = #{record.fuelNum,jdbcType=VARCHAR},
      price = #{record.price,jdbcType=DECIMAL},
      drive_type = #{record.driveType,jdbcType=VARCHAR},
      length = #{record.length,jdbcType=SMALLINT},
      width = #{record.width,jdbcType=SMALLINT},
      height = #{record.height,jdbcType=SMALLINT},
      wheelbase = #{record.wheelbase,jdbcType=SMALLINT},
      luggage = #{record.luggage,jdbcType=TINYINT},
      sunroof = #{record.sunroof,jdbcType=VARCHAR},
      aircontition = #{record.aircontition,jdbcType=VARCHAR},
      screen = #{record.screen,jdbcType=VARCHAR},
      fast_charge_time = #{record.fastChargeTime,jdbcType=TINYINT},
      ctrip_vehicle_model_id = #{record.ctripVehicleModelId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      preset = #{record.preset,jdbcType=TINYINT},
      ctrip_sub_sery_id = #{record.ctripSubSeryId,jdbcType=BIGINT},
      luxury = #{record.luxury,jdbcType=TINYINT},
      model_group_id = #{record.modelGroupId,jdbcType=BIGINT},
      model_group_name = #{record.modelGroupName,jdbcType=VARCHAR},
      endurance_mileage = #{record.enduranceMileage,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.VehicleSubSery">
    update vehicle_sub_sery
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="seryId != null">
        sery_id = #{seryId,jdbcType=BIGINT},
      </if>
      <if test="brandId != null">
        brand_id = #{brandId,jdbcType=BIGINT},
      </if>
      <if test="doors != null">
        doors = #{doors,jdbcType=VARCHAR},
      </if>
      <if test="passengers != null">
        passengers = #{passengers,jdbcType=VARCHAR},
      </if>
      <if test="displacement != null">
        displacement = #{displacement,jdbcType=VARCHAR},
      </if>
      <if test="transmission != null">
        transmission = #{transmission,jdbcType=VARCHAR},
      </if>
      <if test="gearbox != null">
        gearbox = #{gearbox,jdbcType=VARCHAR},
      </if>
      <if test="years != null">
        years = #{years,jdbcType=VARCHAR},
      </if>
      <if test="carriage != null">
        carriage = #{carriage,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLevel != null">
        vehicle_level = #{vehicleLevel,jdbcType=VARCHAR},
      </if>
      <if test="maxOilLiter != null">
        max_oil_liter = #{maxOilLiter,jdbcType=INTEGER},
      </if>
      <if test="fuelForm != null">
        fuel_form = #{fuelForm,jdbcType=VARCHAR},
      </if>
      <if test="fuelFormDetail != null">
        fuel_form_detail = #{fuelFormDetail,jdbcType=VARCHAR},
      </if>
      <if test="fuelNum != null">
        fuel_num = #{fuelNum,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="driveType != null">
        drive_type = #{driveType,jdbcType=VARCHAR},
      </if>
      <if test="length != null">
        length = #{length,jdbcType=SMALLINT},
      </if>
      <if test="width != null">
        width = #{width,jdbcType=SMALLINT},
      </if>
      <if test="height != null">
        height = #{height,jdbcType=SMALLINT},
      </if>
      <if test="wheelbase != null">
        wheelbase = #{wheelbase,jdbcType=SMALLINT},
      </if>
      <if test="luggage != null">
        luggage = #{luggage,jdbcType=TINYINT},
      </if>
      <if test="sunroof != null">
        sunroof = #{sunroof,jdbcType=VARCHAR},
      </if>
      <if test="aircontition != null">
        aircontition = #{aircontition,jdbcType=VARCHAR},
      </if>
      <if test="screen != null">
        screen = #{screen,jdbcType=VARCHAR},
      </if>
      <if test="fastChargeTime != null">
        fast_charge_time = #{fastChargeTime,jdbcType=TINYINT},
      </if>
      <if test="ctripVehicleModelId != null">
        ctrip_vehicle_model_id = #{ctripVehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="preset != null">
        preset = #{preset,jdbcType=TINYINT},
      </if>
      <if test="ctripSubSeryId != null">
        ctrip_sub_sery_id = #{ctripSubSeryId,jdbcType=BIGINT},
      </if>
      <if test="luxury != null">
        luxury = #{luxury,jdbcType=TINYINT},
      </if>
      <if test="modelGroupId != null">
        model_group_id = #{modelGroupId,jdbcType=BIGINT},
      </if>
      <if test="modelGroupName != null">
        model_group_name = #{modelGroupName,jdbcType=VARCHAR},
      </if>
      <if test="enduranceMileage != null">
        endurance_mileage = #{enduranceMileage,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.VehicleSubSery">
    update vehicle_sub_sery
    set name = #{name,jdbcType=VARCHAR},
      sery_id = #{seryId,jdbcType=BIGINT},
      brand_id = #{brandId,jdbcType=BIGINT},
      doors = #{doors,jdbcType=VARCHAR},
      passengers = #{passengers,jdbcType=VARCHAR},
      displacement = #{displacement,jdbcType=VARCHAR},
      transmission = #{transmission,jdbcType=VARCHAR},
      gearbox = #{gearbox,jdbcType=VARCHAR},
      years = #{years,jdbcType=VARCHAR},
      carriage = #{carriage,jdbcType=VARCHAR},
      vehicle_level = #{vehicleLevel,jdbcType=VARCHAR},
      max_oil_liter = #{maxOilLiter,jdbcType=INTEGER},
      fuel_form = #{fuelForm,jdbcType=VARCHAR},
      fuel_form_detail = #{fuelFormDetail,jdbcType=VARCHAR},
      fuel_num = #{fuelNum,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      drive_type = #{driveType,jdbcType=VARCHAR},
      length = #{length,jdbcType=SMALLINT},
      width = #{width,jdbcType=SMALLINT},
      height = #{height,jdbcType=SMALLINT},
      wheelbase = #{wheelbase,jdbcType=SMALLINT},
      luggage = #{luggage,jdbcType=TINYINT},
      sunroof = #{sunroof,jdbcType=VARCHAR},
      aircontition = #{aircontition,jdbcType=VARCHAR},
      screen = #{screen,jdbcType=VARCHAR},
      fast_charge_time = #{fastChargeTime,jdbcType=TINYINT},
      ctrip_vehicle_model_id = #{ctripVehicleModelId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      preset = #{preset,jdbcType=TINYINT},
      ctrip_sub_sery_id = #{ctripSubSeryId,jdbcType=BIGINT},
      luxury = #{luxury,jdbcType=TINYINT},
      model_group_id = #{modelGroupId,jdbcType=BIGINT},
      model_group_name = #{modelGroupName,jdbcType=VARCHAR},
      endurance_mileage = #{enduranceMileage,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_sub_sery
    (name, sery_id, brand_id, doors, passengers, displacement, transmission, gearbox, 
      years, carriage, vehicle_level, max_oil_liter, fuel_form, fuel_form_detail, fuel_num, 
      price, drive_type, length, width, height, wheelbase, luggage, sunroof, aircontition, 
      screen, fast_charge_time, ctrip_vehicle_model_id, store_id, merchant_id, preset, 
      ctrip_sub_sery_id, luxury, model_group_id, model_group_name, endurance_mileage,
      deleted, create_time, op_time, op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.seryId,jdbcType=BIGINT}, #{item.brandId,jdbcType=BIGINT}, 
        #{item.doors,jdbcType=VARCHAR}, #{item.passengers,jdbcType=VARCHAR}, #{item.displacement,jdbcType=VARCHAR}, 
        #{item.transmission,jdbcType=VARCHAR}, #{item.gearbox,jdbcType=VARCHAR}, #{item.years,jdbcType=VARCHAR}, 
        #{item.carriage,jdbcType=VARCHAR}, #{item.vehicleLevel,jdbcType=VARCHAR}, #{item.maxOilLiter,jdbcType=INTEGER}, 
        #{item.fuelForm,jdbcType=VARCHAR}, #{item.fuelFormDetail,jdbcType=VARCHAR}, #{item.fuelNum,jdbcType=VARCHAR}, 
        #{item.price,jdbcType=DECIMAL}, #{item.driveType,jdbcType=VARCHAR}, #{item.length,jdbcType=SMALLINT}, 
        #{item.width,jdbcType=SMALLINT}, #{item.height,jdbcType=SMALLINT}, #{item.wheelbase,jdbcType=SMALLINT}, 
        #{item.luggage,jdbcType=TINYINT}, #{item.sunroof,jdbcType=VARCHAR}, #{item.aircontition,jdbcType=VARCHAR}, 
        #{item.screen,jdbcType=VARCHAR}, #{item.fastChargeTime,jdbcType=TINYINT}, #{item.ctripVehicleModelId,jdbcType=BIGINT}, 
        #{item.storeId,jdbcType=BIGINT}, #{item.merchantId,jdbcType=BIGINT}, #{item.preset,jdbcType=TINYINT}, 
        #{item.ctripSubSeryId,jdbcType=BIGINT}, #{item.luxury,jdbcType=TINYINT}, #{item.modelGroupId,jdbcType=BIGINT},
        #{item.modelGroupName,jdbcType=VARCHAR}, #{item.enduranceMileage,jdbcType=INTEGER},
        #{item.deleted,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT},
        #{item.opUserId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_sub_sery (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'sery_id'.toString() == column.value">
          #{item.seryId,jdbcType=BIGINT}
        </if>
        <if test="'brand_id'.toString() == column.value">
          #{item.brandId,jdbcType=BIGINT}
        </if>
        <if test="'doors'.toString() == column.value">
          #{item.doors,jdbcType=VARCHAR}
        </if>
        <if test="'passengers'.toString() == column.value">
          #{item.passengers,jdbcType=VARCHAR}
        </if>
        <if test="'displacement'.toString() == column.value">
          #{item.displacement,jdbcType=VARCHAR}
        </if>
        <if test="'transmission'.toString() == column.value">
          #{item.transmission,jdbcType=VARCHAR}
        </if>
        <if test="'gearbox'.toString() == column.value">
          #{item.gearbox,jdbcType=VARCHAR}
        </if>
        <if test="'years'.toString() == column.value">
          #{item.years,jdbcType=VARCHAR}
        </if>
        <if test="'carriage'.toString() == column.value">
          #{item.carriage,jdbcType=VARCHAR}
        </if>
        <if test="'vehicle_level'.toString() == column.value">
          #{item.vehicleLevel,jdbcType=VARCHAR}
        </if>
        <if test="'max_oil_liter'.toString() == column.value">
          #{item.maxOilLiter,jdbcType=INTEGER}
        </if>
        <if test="'fuel_form'.toString() == column.value">
          #{item.fuelForm,jdbcType=VARCHAR}
        </if>
        <if test="'fuel_form_detail'.toString() == column.value">
          #{item.fuelFormDetail,jdbcType=VARCHAR}
        </if>
        <if test="'fuel_num'.toString() == column.value">
          #{item.fuelNum,jdbcType=VARCHAR}
        </if>
        <if test="'price'.toString() == column.value">
          #{item.price,jdbcType=DECIMAL}
        </if>
        <if test="'drive_type'.toString() == column.value">
          #{item.driveType,jdbcType=VARCHAR}
        </if>
        <if test="'length'.toString() == column.value">
          #{item.length,jdbcType=SMALLINT}
        </if>
        <if test="'width'.toString() == column.value">
          #{item.width,jdbcType=SMALLINT}
        </if>
        <if test="'height'.toString() == column.value">
          #{item.height,jdbcType=SMALLINT}
        </if>
        <if test="'wheelbase'.toString() == column.value">
          #{item.wheelbase,jdbcType=SMALLINT}
        </if>
        <if test="'luggage'.toString() == column.value">
          #{item.luggage,jdbcType=TINYINT}
        </if>
        <if test="'sunroof'.toString() == column.value">
          #{item.sunroof,jdbcType=VARCHAR}
        </if>
        <if test="'aircontition'.toString() == column.value">
          #{item.aircontition,jdbcType=VARCHAR}
        </if>
        <if test="'screen'.toString() == column.value">
          #{item.screen,jdbcType=VARCHAR}
        </if>
        <if test="'fast_charge_time'.toString() == column.value">
          #{item.fastChargeTime,jdbcType=TINYINT}
        </if>
        <if test="'ctrip_vehicle_model_id'.toString() == column.value">
          #{item.ctripVehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'preset'.toString() == column.value">
          #{item.preset,jdbcType=TINYINT}
        </if>
        <if test="'ctrip_sub_sery_id'.toString() == column.value">
          #{item.ctripSubSeryId,jdbcType=BIGINT}
        </if>
        <if test="'luxury'.toString() == column.value">
          #{item.luxury,jdbcType=TINYINT}
        </if>
        <if test="'model_group_id'.toString() == column.value">
          #{item.modelGroupId,jdbcType=BIGINT}
        </if>
        <if test="'model_group_name'.toString() == column.value">
          #{item.modelGroupName,jdbcType=VARCHAR}
        </if>
        <if test="'endurance_mileage'.toString() == column.value">
          #{item.enduranceMileage,jdbcType=INTEGER}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
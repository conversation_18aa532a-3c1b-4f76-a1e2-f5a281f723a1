<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.VehicleRepairMaintenanceOrderMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.VehicleRepairMaintenanceOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="work_order_no" jdbcType="VARCHAR" property="workOrderNo" />
    <result column="vehicle_info_id" jdbcType="BIGINT" property="vehicleInfoId" />
    <result column="license_no" jdbcType="VARCHAR" property="licenseNo" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="vehicle_model_name" jdbcType="VARCHAR" property="vehicleModelName" />
    <result column="work_order_type" jdbcType="TINYINT" property="workOrderType" />
    <result column="depot_id" jdbcType="BIGINT" property="depotId" />
    <result column="depot_name" jdbcType="VARCHAR" property="depotName" />
    <result column="before_mileage" jdbcType="INTEGER" property="beforeMileage" />
    <result column="after_mileage" jdbcType="INTEGER" property="afterMileage" />
    <result column="start_time" jdbcType="BIGINT" property="startTime" />
    <result column="end_time" jdbcType="BIGINT" property="endTime" />
    <result column="occupied_vehicle" jdbcType="TINYINT" property="occupiedVehicle" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="relation_order_id" jdbcType="BIGINT" property="relationOrderId" />
    <result column="handler_user_id" jdbcType="BIGINT" property="handlerUserId" />
    <result column="handler_user_name" jdbcType="VARCHAR" property="handlerUserName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="cancel_reason" jdbcType="VARCHAR" property="cancelReason" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, work_order_no, vehicle_info_id, license_no, vehicle_model_id, vehicle_model_name, 
    work_order_type, depot_id, depot_name, before_mileage, after_mileage, start_time, 
    end_time, occupied_vehicle, remark, relation_order_id, handler_user_id, handler_user_name, 
    status, cancel_reason, merchant_id, store_id, last_ver, deleted, create_time, op_time, 
    op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.VehicleRepairMaintenanceOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_repair_maintenance_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_repair_maintenance_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_repair_maintenance_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.VehicleRepairMaintenanceOrderExample">
    delete from vehicle_repair_maintenance_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.VehicleRepairMaintenanceOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_repair_maintenance_order (work_order_no, vehicle_info_id, license_no, 
      vehicle_model_id, vehicle_model_name, work_order_type, 
      depot_id, depot_name, before_mileage, 
      after_mileage, start_time, end_time, 
      occupied_vehicle, remark, relation_order_id, 
      handler_user_id, handler_user_name, status, 
      cancel_reason, merchant_id, store_id, 
      last_ver, deleted, create_time, 
      op_time, op_user_id)
    values (#{workOrderNo,jdbcType=VARCHAR}, #{vehicleInfoId,jdbcType=BIGINT}, #{licenseNo,jdbcType=VARCHAR}, 
      #{vehicleModelId,jdbcType=BIGINT}, #{vehicleModelName,jdbcType=VARCHAR}, #{workOrderType,jdbcType=TINYINT}, 
      #{depotId,jdbcType=BIGINT}, #{depotName,jdbcType=VARCHAR}, #{beforeMileage,jdbcType=INTEGER}, 
      #{afterMileage,jdbcType=INTEGER}, #{startTime,jdbcType=BIGINT}, #{endTime,jdbcType=BIGINT}, 
      #{occupiedVehicle,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, #{relationOrderId,jdbcType=BIGINT}, 
      #{handlerUserId,jdbcType=BIGINT}, #{handlerUserName,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{cancelReason,jdbcType=VARCHAR}, #{merchantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, 
      #{lastVer,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.VehicleRepairMaintenanceOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_repair_maintenance_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="workOrderNo != null">
        work_order_no,
      </if>
      <if test="vehicleInfoId != null">
        vehicle_info_id,
      </if>
      <if test="licenseNo != null">
        license_no,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name,
      </if>
      <if test="workOrderType != null">
        work_order_type,
      </if>
      <if test="depotId != null">
        depot_id,
      </if>
      <if test="depotName != null">
        depot_name,
      </if>
      <if test="beforeMileage != null">
        before_mileage,
      </if>
      <if test="afterMileage != null">
        after_mileage,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="occupiedVehicle != null">
        occupied_vehicle,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="relationOrderId != null">
        relation_order_id,
      </if>
      <if test="handlerUserId != null">
        handler_user_id,
      </if>
      <if test="handlerUserName != null">
        handler_user_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="cancelReason != null">
        cancel_reason,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="workOrderNo != null">
        #{workOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleInfoId != null">
        #{vehicleInfoId,jdbcType=BIGINT},
      </if>
      <if test="licenseNo != null">
        #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelName != null">
        #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="workOrderType != null">
        #{workOrderType,jdbcType=TINYINT},
      </if>
      <if test="depotId != null">
        #{depotId,jdbcType=BIGINT},
      </if>
      <if test="depotName != null">
        #{depotName,jdbcType=VARCHAR},
      </if>
      <if test="beforeMileage != null">
        #{beforeMileage,jdbcType=INTEGER},
      </if>
      <if test="afterMileage != null">
        #{afterMileage,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=BIGINT},
      </if>
      <if test="occupiedVehicle != null">
        #{occupiedVehicle,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="relationOrderId != null">
        #{relationOrderId,jdbcType=BIGINT},
      </if>
      <if test="handlerUserId != null">
        #{handlerUserId,jdbcType=BIGINT},
      </if>
      <if test="handlerUserName != null">
        #{handlerUserName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="cancelReason != null">
        #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.VehicleRepairMaintenanceOrderExample" resultType="java.lang.Long">
    select count(*) from vehicle_repair_maintenance_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_repair_maintenance_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.workOrderNo != null">
        work_order_no = #{record.workOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleInfoId != null">
        vehicle_info_id = #{record.vehicleInfoId,jdbcType=BIGINT},
      </if>
      <if test="record.licenseNo != null">
        license_no = #{record.licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelName != null">
        vehicle_model_name = #{record.vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="record.workOrderType != null">
        work_order_type = #{record.workOrderType,jdbcType=TINYINT},
      </if>
      <if test="record.depotId != null">
        depot_id = #{record.depotId,jdbcType=BIGINT},
      </if>
      <if test="record.depotName != null">
        depot_name = #{record.depotName,jdbcType=VARCHAR},
      </if>
      <if test="record.beforeMileage != null">
        before_mileage = #{record.beforeMileage,jdbcType=INTEGER},
      </if>
      <if test="record.afterMileage != null">
        after_mileage = #{record.afterMileage,jdbcType=INTEGER},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=BIGINT},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=BIGINT},
      </if>
      <if test="record.occupiedVehicle != null">
        occupied_vehicle = #{record.occupiedVehicle,jdbcType=TINYINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.relationOrderId != null">
        relation_order_id = #{record.relationOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.handlerUserId != null">
        handler_user_id = #{record.handlerUserId,jdbcType=BIGINT},
      </if>
      <if test="record.handlerUserName != null">
        handler_user_name = #{record.handlerUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.cancelReason != null">
        cancel_reason = #{record.cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_repair_maintenance_order
    set id = #{record.id,jdbcType=BIGINT},
      work_order_no = #{record.workOrderNo,jdbcType=VARCHAR},
      vehicle_info_id = #{record.vehicleInfoId,jdbcType=BIGINT},
      license_no = #{record.licenseNo,jdbcType=VARCHAR},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      vehicle_model_name = #{record.vehicleModelName,jdbcType=VARCHAR},
      work_order_type = #{record.workOrderType,jdbcType=TINYINT},
      depot_id = #{record.depotId,jdbcType=BIGINT},
      depot_name = #{record.depotName,jdbcType=VARCHAR},
      before_mileage = #{record.beforeMileage,jdbcType=INTEGER},
      after_mileage = #{record.afterMileage,jdbcType=INTEGER},
      start_time = #{record.startTime,jdbcType=BIGINT},
      end_time = #{record.endTime,jdbcType=BIGINT},
      occupied_vehicle = #{record.occupiedVehicle,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      relation_order_id = #{record.relationOrderId,jdbcType=BIGINT},
      handler_user_id = #{record.handlerUserId,jdbcType=BIGINT},
      handler_user_name = #{record.handlerUserName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      cancel_reason = #{record.cancelReason,jdbcType=VARCHAR},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.VehicleRepairMaintenanceOrder">
    update vehicle_repair_maintenance_order
    <set>
      <if test="workOrderNo != null">
        work_order_no = #{workOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleInfoId != null">
        vehicle_info_id = #{vehicleInfoId,jdbcType=BIGINT},
      </if>
      <if test="licenseNo != null">
        license_no = #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="workOrderType != null">
        work_order_type = #{workOrderType,jdbcType=TINYINT},
      </if>
      <if test="depotId != null">
        depot_id = #{depotId,jdbcType=BIGINT},
      </if>
      <if test="depotName != null">
        depot_name = #{depotName,jdbcType=VARCHAR},
      </if>
      <if test="beforeMileage != null">
        before_mileage = #{beforeMileage,jdbcType=INTEGER},
      </if>
      <if test="afterMileage != null">
        after_mileage = #{afterMileage,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=BIGINT},
      </if>
      <if test="occupiedVehicle != null">
        occupied_vehicle = #{occupiedVehicle,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="relationOrderId != null">
        relation_order_id = #{relationOrderId,jdbcType=BIGINT},
      </if>
      <if test="handlerUserId != null">
        handler_user_id = #{handlerUserId,jdbcType=BIGINT},
      </if>
      <if test="handlerUserName != null">
        handler_user_name = #{handlerUserName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="cancelReason != null">
        cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.VehicleRepairMaintenanceOrder">
    update vehicle_repair_maintenance_order
    set work_order_no = #{workOrderNo,jdbcType=VARCHAR},
      vehicle_info_id = #{vehicleInfoId,jdbcType=BIGINT},
      license_no = #{licenseNo,jdbcType=VARCHAR},
      vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      work_order_type = #{workOrderType,jdbcType=TINYINT},
      depot_id = #{depotId,jdbcType=BIGINT},
      depot_name = #{depotName,jdbcType=VARCHAR},
      before_mileage = #{beforeMileage,jdbcType=INTEGER},
      after_mileage = #{afterMileage,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=BIGINT},
      end_time = #{endTime,jdbcType=BIGINT},
      occupied_vehicle = #{occupiedVehicle,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      relation_order_id = #{relationOrderId,jdbcType=BIGINT},
      handler_user_id = #{handlerUserId,jdbcType=BIGINT},
      handler_user_name = #{handlerUserName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_repair_maintenance_order
    (work_order_no, vehicle_info_id, license_no, vehicle_model_id, vehicle_model_name, 
      work_order_type, depot_id, depot_name, before_mileage, after_mileage, start_time, 
      end_time, occupied_vehicle, remark, relation_order_id, handler_user_id, handler_user_name, 
      status, cancel_reason, merchant_id, store_id, last_ver, deleted, create_time, op_time, 
      op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.workOrderNo,jdbcType=VARCHAR}, #{item.vehicleInfoId,jdbcType=BIGINT}, #{item.licenseNo,jdbcType=VARCHAR}, 
        #{item.vehicleModelId,jdbcType=BIGINT}, #{item.vehicleModelName,jdbcType=VARCHAR}, 
        #{item.workOrderType,jdbcType=TINYINT}, #{item.depotId,jdbcType=BIGINT}, #{item.depotName,jdbcType=VARCHAR}, 
        #{item.beforeMileage,jdbcType=INTEGER}, #{item.afterMileage,jdbcType=INTEGER}, 
        #{item.startTime,jdbcType=BIGINT}, #{item.endTime,jdbcType=BIGINT}, #{item.occupiedVehicle,jdbcType=TINYINT}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.relationOrderId,jdbcType=BIGINT}, #{item.handlerUserId,jdbcType=BIGINT}, 
        #{item.handlerUserName,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT}, #{item.cancelReason,jdbcType=VARCHAR}, 
        #{item.merchantId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.lastVer,jdbcType=INTEGER}, 
        #{item.deleted,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, 
        #{item.opUserId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_repair_maintenance_order (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'work_order_no'.toString() == column.value">
          #{item.workOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="'vehicle_info_id'.toString() == column.value">
          #{item.vehicleInfoId,jdbcType=BIGINT}
        </if>
        <if test="'license_no'.toString() == column.value">
          #{item.licenseNo,jdbcType=VARCHAR}
        </if>
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_model_name'.toString() == column.value">
          #{item.vehicleModelName,jdbcType=VARCHAR}
        </if>
        <if test="'work_order_type'.toString() == column.value">
          #{item.workOrderType,jdbcType=TINYINT}
        </if>
        <if test="'depot_id'.toString() == column.value">
          #{item.depotId,jdbcType=BIGINT}
        </if>
        <if test="'depot_name'.toString() == column.value">
          #{item.depotName,jdbcType=VARCHAR}
        </if>
        <if test="'before_mileage'.toString() == column.value">
          #{item.beforeMileage,jdbcType=INTEGER}
        </if>
        <if test="'after_mileage'.toString() == column.value">
          #{item.afterMileage,jdbcType=INTEGER}
        </if>
        <if test="'start_time'.toString() == column.value">
          #{item.startTime,jdbcType=BIGINT}
        </if>
        <if test="'end_time'.toString() == column.value">
          #{item.endTime,jdbcType=BIGINT}
        </if>
        <if test="'occupied_vehicle'.toString() == column.value">
          #{item.occupiedVehicle,jdbcType=TINYINT}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'relation_order_id'.toString() == column.value">
          #{item.relationOrderId,jdbcType=BIGINT}
        </if>
        <if test="'handler_user_id'.toString() == column.value">
          #{item.handlerUserId,jdbcType=BIGINT}
        </if>
        <if test="'handler_user_name'.toString() == column.value">
          #{item.handlerUserName,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'cancel_reason'.toString() == column.value">
          #{item.cancelReason,jdbcType=VARCHAR}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.CtrIpPriceDBMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.CtrIpPriceDB">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="vendor_vehicle_code" jdbcType="VARCHAR" property="vendorVehicleCode" />
    <result column="daily_price_weekday_list" jdbcType="VARCHAR" property="dailyPriceWeekdayList" />
    <result column="daily_price_holiday_list" jdbcType="VARCHAR" property="dailyPriceHolidayList" />
    <result column="daily_price_special_list" jdbcType="VARCHAR" property="dailyPriceSpecialList" />
    <result column="car_dealer_fee_fist" jdbcType="VARCHAR" property="carDealerFeeFist" />
    <result column="car_rental_deposit_list" jdbcType="VARCHAR" property="carRentalDepositList" />
    <result column="illegal_deposit_list" jdbcType="VARCHAR" property="illegalDepositList" />
    <result column="night_fee_list" jdbcType="VARCHAR" property="nightFeeList" />
    <result column="nightFee" jdbcType="TINYINT" property="nightfee" />
    <result column="offsite_price_list" jdbcType="VARCHAR" property="offsitePriceList" />
    <result column="open_no_worried" jdbcType="TINYINT" property="openNoWorried" />
    <result column="no_worried_makeup_rateList" jdbcType="VARCHAR" property="noWorriedMakeupRatelist" />
    <result column="package_price_list" jdbcType="VARCHAR" property="packagePriceList" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, store_id, vehicle_model_id, vendor_vehicle_code, daily_price_weekday_list, 
    daily_price_holiday_list, daily_price_special_list, car_dealer_fee_fist, car_rental_deposit_list, 
    illegal_deposit_list, night_fee_list, nightFee, offsite_price_list, open_no_worried, 
    no_worried_makeup_rateList, package_price_list, deleted, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.CtrIpPriceDBExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from ctrip_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ctrip_price
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ctrip_price
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.CtrIpPriceDBExample">
    delete from ctrip_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.CtrIpPriceDB">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ctrip_price (merchant_id, store_id, vehicle_model_id, 
      vendor_vehicle_code, daily_price_weekday_list, 
      daily_price_holiday_list, daily_price_special_list, 
      car_dealer_fee_fist, car_rental_deposit_list, 
      illegal_deposit_list, night_fee_list, nightFee, 
      offsite_price_list, open_no_worried, no_worried_makeup_rateList, 
      package_price_list, deleted, create_time, 
      op_time)
    values (#{merchantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{vehicleModelId,jdbcType=BIGINT}, 
      #{vendorVehicleCode,jdbcType=VARCHAR}, #{dailyPriceWeekdayList,jdbcType=VARCHAR}, 
      #{dailyPriceHolidayList,jdbcType=VARCHAR}, #{dailyPriceSpecialList,jdbcType=VARCHAR}, 
      #{carDealerFeeFist,jdbcType=VARCHAR}, #{carRentalDepositList,jdbcType=VARCHAR}, 
      #{illegalDepositList,jdbcType=VARCHAR}, #{nightFeeList,jdbcType=VARCHAR}, #{nightfee,jdbcType=TINYINT}, 
      #{offsitePriceList,jdbcType=VARCHAR}, #{openNoWorried,jdbcType=TINYINT}, #{noWorriedMakeupRatelist,jdbcType=VARCHAR}, 
      #{packagePriceList,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.CtrIpPriceDB">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ctrip_price
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="vendorVehicleCode != null">
        vendor_vehicle_code,
      </if>
      <if test="dailyPriceWeekdayList != null">
        daily_price_weekday_list,
      </if>
      <if test="dailyPriceHolidayList != null">
        daily_price_holiday_list,
      </if>
      <if test="dailyPriceSpecialList != null">
        daily_price_special_list,
      </if>
      <if test="carDealerFeeFist != null">
        car_dealer_fee_fist,
      </if>
      <if test="carRentalDepositList != null">
        car_rental_deposit_list,
      </if>
      <if test="illegalDepositList != null">
        illegal_deposit_list,
      </if>
      <if test="nightFeeList != null">
        night_fee_list,
      </if>
      <if test="nightfee != null">
        nightFee,
      </if>
      <if test="offsitePriceList != null">
        offsite_price_list,
      </if>
      <if test="openNoWorried != null">
        open_no_worried,
      </if>
      <if test="noWorriedMakeupRatelist != null">
        no_worried_makeup_rateList,
      </if>
      <if test="packagePriceList != null">
        package_price_list,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vendorVehicleCode != null">
        #{vendorVehicleCode,jdbcType=VARCHAR},
      </if>
      <if test="dailyPriceWeekdayList != null">
        #{dailyPriceWeekdayList,jdbcType=VARCHAR},
      </if>
      <if test="dailyPriceHolidayList != null">
        #{dailyPriceHolidayList,jdbcType=VARCHAR},
      </if>
      <if test="dailyPriceSpecialList != null">
        #{dailyPriceSpecialList,jdbcType=VARCHAR},
      </if>
      <if test="carDealerFeeFist != null">
        #{carDealerFeeFist,jdbcType=VARCHAR},
      </if>
      <if test="carRentalDepositList != null">
        #{carRentalDepositList,jdbcType=VARCHAR},
      </if>
      <if test="illegalDepositList != null">
        #{illegalDepositList,jdbcType=VARCHAR},
      </if>
      <if test="nightFeeList != null">
        #{nightFeeList,jdbcType=VARCHAR},
      </if>
      <if test="nightfee != null">
        #{nightfee,jdbcType=TINYINT},
      </if>
      <if test="offsitePriceList != null">
        #{offsitePriceList,jdbcType=VARCHAR},
      </if>
      <if test="openNoWorried != null">
        #{openNoWorried,jdbcType=TINYINT},
      </if>
      <if test="noWorriedMakeupRatelist != null">
        #{noWorriedMakeupRatelist,jdbcType=VARCHAR},
      </if>
      <if test="packagePriceList != null">
        #{packagePriceList,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.CtrIpPriceDBExample" resultType="java.lang.Long">
    select count(*) from ctrip_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ctrip_price
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.vendorVehicleCode != null">
        vendor_vehicle_code = #{record.vendorVehicleCode,jdbcType=VARCHAR},
      </if>
      <if test="record.dailyPriceWeekdayList != null">
        daily_price_weekday_list = #{record.dailyPriceWeekdayList,jdbcType=VARCHAR},
      </if>
      <if test="record.dailyPriceHolidayList != null">
        daily_price_holiday_list = #{record.dailyPriceHolidayList,jdbcType=VARCHAR},
      </if>
      <if test="record.dailyPriceSpecialList != null">
        daily_price_special_list = #{record.dailyPriceSpecialList,jdbcType=VARCHAR},
      </if>
      <if test="record.carDealerFeeFist != null">
        car_dealer_fee_fist = #{record.carDealerFeeFist,jdbcType=VARCHAR},
      </if>
      <if test="record.carRentalDepositList != null">
        car_rental_deposit_list = #{record.carRentalDepositList,jdbcType=VARCHAR},
      </if>
      <if test="record.illegalDepositList != null">
        illegal_deposit_list = #{record.illegalDepositList,jdbcType=VARCHAR},
      </if>
      <if test="record.nightFeeList != null">
        night_fee_list = #{record.nightFeeList,jdbcType=VARCHAR},
      </if>
      <if test="record.nightfee != null">
        nightFee = #{record.nightfee,jdbcType=TINYINT},
      </if>
      <if test="record.offsitePriceList != null">
        offsite_price_list = #{record.offsitePriceList,jdbcType=VARCHAR},
      </if>
      <if test="record.openNoWorried != null">
        open_no_worried = #{record.openNoWorried,jdbcType=TINYINT},
      </if>
      <if test="record.noWorriedMakeupRatelist != null">
        no_worried_makeup_rateList = #{record.noWorriedMakeupRatelist,jdbcType=VARCHAR},
      </if>
      <if test="record.packagePriceList != null">
        package_price_list = #{record.packagePriceList,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ctrip_price
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      vendor_vehicle_code = #{record.vendorVehicleCode,jdbcType=VARCHAR},
      daily_price_weekday_list = #{record.dailyPriceWeekdayList,jdbcType=VARCHAR},
      daily_price_holiday_list = #{record.dailyPriceHolidayList,jdbcType=VARCHAR},
      daily_price_special_list = #{record.dailyPriceSpecialList,jdbcType=VARCHAR},
      car_dealer_fee_fist = #{record.carDealerFeeFist,jdbcType=VARCHAR},
      car_rental_deposit_list = #{record.carRentalDepositList,jdbcType=VARCHAR},
      illegal_deposit_list = #{record.illegalDepositList,jdbcType=VARCHAR},
      night_fee_list = #{record.nightFeeList,jdbcType=VARCHAR},
      nightFee = #{record.nightfee,jdbcType=TINYINT},
      offsite_price_list = #{record.offsitePriceList,jdbcType=VARCHAR},
      open_no_worried = #{record.openNoWorried,jdbcType=TINYINT},
      no_worried_makeup_rateList = #{record.noWorriedMakeupRatelist,jdbcType=VARCHAR},
      package_price_list = #{record.packagePriceList,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.CtrIpPriceDB">
    update ctrip_price
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vendorVehicleCode != null">
        vendor_vehicle_code = #{vendorVehicleCode,jdbcType=VARCHAR},
      </if>
      <if test="dailyPriceWeekdayList != null">
        daily_price_weekday_list = #{dailyPriceWeekdayList,jdbcType=VARCHAR},
      </if>
      <if test="dailyPriceHolidayList != null">
        daily_price_holiday_list = #{dailyPriceHolidayList,jdbcType=VARCHAR},
      </if>
      <if test="dailyPriceSpecialList != null">
        daily_price_special_list = #{dailyPriceSpecialList,jdbcType=VARCHAR},
      </if>
      <if test="carDealerFeeFist != null">
        car_dealer_fee_fist = #{carDealerFeeFist,jdbcType=VARCHAR},
      </if>
      <if test="carRentalDepositList != null">
        car_rental_deposit_list = #{carRentalDepositList,jdbcType=VARCHAR},
      </if>
      <if test="illegalDepositList != null">
        illegal_deposit_list = #{illegalDepositList,jdbcType=VARCHAR},
      </if>
      <if test="nightFeeList != null">
        night_fee_list = #{nightFeeList,jdbcType=VARCHAR},
      </if>
      <if test="nightfee != null">
        nightFee = #{nightfee,jdbcType=TINYINT},
      </if>
      <if test="offsitePriceList != null">
        offsite_price_list = #{offsitePriceList,jdbcType=VARCHAR},
      </if>
      <if test="openNoWorried != null">
        open_no_worried = #{openNoWorried,jdbcType=TINYINT},
      </if>
      <if test="noWorriedMakeupRatelist != null">
        no_worried_makeup_rateList = #{noWorriedMakeupRatelist,jdbcType=VARCHAR},
      </if>
      <if test="packagePriceList != null">
        package_price_list = #{packagePriceList,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.CtrIpPriceDB">
    update ctrip_price
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      vendor_vehicle_code = #{vendorVehicleCode,jdbcType=VARCHAR},
      daily_price_weekday_list = #{dailyPriceWeekdayList,jdbcType=VARCHAR},
      daily_price_holiday_list = #{dailyPriceHolidayList,jdbcType=VARCHAR},
      daily_price_special_list = #{dailyPriceSpecialList,jdbcType=VARCHAR},
      car_dealer_fee_fist = #{carDealerFeeFist,jdbcType=VARCHAR},
      car_rental_deposit_list = #{carRentalDepositList,jdbcType=VARCHAR},
      illegal_deposit_list = #{illegalDepositList,jdbcType=VARCHAR},
      night_fee_list = #{nightFeeList,jdbcType=VARCHAR},
      nightFee = #{nightfee,jdbcType=TINYINT},
      offsite_price_list = #{offsitePriceList,jdbcType=VARCHAR},
      open_no_worried = #{openNoWorried,jdbcType=TINYINT},
      no_worried_makeup_rateList = #{noWorriedMakeupRatelist,jdbcType=VARCHAR},
      package_price_list = #{packagePriceList,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into ctrip_price
    (merchant_id, store_id, vehicle_model_id, vendor_vehicle_code, daily_price_weekday_list, 
      daily_price_holiday_list, daily_price_special_list, car_dealer_fee_fist, car_rental_deposit_list, 
      illegal_deposit_list, night_fee_list, nightFee, offsite_price_list, open_no_worried, 
      no_worried_makeup_rateList, package_price_list, deleted, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.vehicleModelId,jdbcType=BIGINT}, 
        #{item.vendorVehicleCode,jdbcType=VARCHAR}, #{item.dailyPriceWeekdayList,jdbcType=VARCHAR}, 
        #{item.dailyPriceHolidayList,jdbcType=VARCHAR}, #{item.dailyPriceSpecialList,jdbcType=VARCHAR}, 
        #{item.carDealerFeeFist,jdbcType=VARCHAR}, #{item.carRentalDepositList,jdbcType=VARCHAR}, 
        #{item.illegalDepositList,jdbcType=VARCHAR}, #{item.nightFeeList,jdbcType=VARCHAR}, 
        #{item.nightfee,jdbcType=TINYINT}, #{item.offsitePriceList,jdbcType=VARCHAR}, #{item.openNoWorried,jdbcType=TINYINT}, 
        #{item.noWorriedMakeupRatelist,jdbcType=VARCHAR}, #{item.packagePriceList,jdbcType=VARCHAR}, 
        #{item.deleted,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into ctrip_price (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'vendor_vehicle_code'.toString() == column.value">
          #{item.vendorVehicleCode,jdbcType=VARCHAR}
        </if>
        <if test="'daily_price_weekday_list'.toString() == column.value">
          #{item.dailyPriceWeekdayList,jdbcType=VARCHAR}
        </if>
        <if test="'daily_price_holiday_list'.toString() == column.value">
          #{item.dailyPriceHolidayList,jdbcType=VARCHAR}
        </if>
        <if test="'daily_price_special_list'.toString() == column.value">
          #{item.dailyPriceSpecialList,jdbcType=VARCHAR}
        </if>
        <if test="'car_dealer_fee_fist'.toString() == column.value">
          #{item.carDealerFeeFist,jdbcType=VARCHAR}
        </if>
        <if test="'car_rental_deposit_list'.toString() == column.value">
          #{item.carRentalDepositList,jdbcType=VARCHAR}
        </if>
        <if test="'illegal_deposit_list'.toString() == column.value">
          #{item.illegalDepositList,jdbcType=VARCHAR}
        </if>
        <if test="'night_fee_list'.toString() == column.value">
          #{item.nightFeeList,jdbcType=VARCHAR}
        </if>
        <if test="'nightFee'.toString() == column.value">
          #{item.nightfee,jdbcType=TINYINT}
        </if>
        <if test="'offsite_price_list'.toString() == column.value">
          #{item.offsitePriceList,jdbcType=VARCHAR}
        </if>
        <if test="'open_no_worried'.toString() == column.value">
          #{item.openNoWorried,jdbcType=TINYINT}
        </if>
        <if test="'no_worried_makeup_rateList'.toString() == column.value">
          #{item.noWorriedMakeupRatelist,jdbcType=VARCHAR}
        </if>
        <if test="'package_price_list'.toString() == column.value">
          #{item.packagePriceList,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>

  <update id="batchUpdate"  keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <foreach collection="list" item="item" separator=";">
    update ctrip_price
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vendorVehicleCode != null">
        vendor_vehicle_code = #{vendorVehicleCode,jdbcType=VARCHAR},
      </if>
      <if test="dailyPriceWeekdayList != null">
        daily_price_weekday_list = #{dailyPriceWeekdayList,jdbcType=VARCHAR},
      </if>
      <if test="dailyPriceHolidayList != null">
        daily_price_holiday_list = #{dailyPriceHolidayList,jdbcType=VARCHAR},
      </if>
      <if test="dailyPriceSpecialList != null">
        daily_price_special_list = #{dailyPriceSpecialList,jdbcType=VARCHAR},
      </if>
      <if test="carDealerFeeFist != null">
        car_dealer_fee_fist = #{carDealerFeeFist,jdbcType=VARCHAR},
      </if>
      <if test="carRentalDepositList != null">
        car_rental_deposit_list = #{carRentalDepositList,jdbcType=VARCHAR},
      </if>
      <if test="illegalDepositList != null">
        illegal_deposit_list = #{illegalDepositList,jdbcType=VARCHAR},
      </if>
      <if test="nightFeeList != null">
        night_fee_list = #{nightFeeList,jdbcType=VARCHAR},
      </if>
      <if test="nightfee != null">
        nightFee = #{nightfee,jdbcType=TINYINT},
      </if>
      <if test="offsitePriceList != null">
        offsite_price_list = #{offsitePriceList,jdbcType=VARCHAR},
      </if>
      <if test="openNoWorried != null">
        open_no_worried = #{openNoWorried,jdbcType=TINYINT},
      </if>
      <if test="noWorriedMakeupRatelist != null">
        no_worried_makeup_rateList = #{noWorriedMakeupRatelist,jdbcType=VARCHAR},
      </if>
      <if test="packagePriceList != null">
        package_price_list = #{packagePriceList,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    </foreach>
  </update>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.VehicleBindMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.VehicleBind">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="bind_channel_vehicle_id" jdbcType="VARCHAR" property="bindChannelVehicleId" />
    <result column="bind_channel_vehicle_name" jdbcType="VARCHAR" property="bindChannelVehicleName" />
    <result column="bind_channel_vehicle_sery_id" jdbcType="VARCHAR" property="bindChannelVehicleSeryId" />
    <result column="synced" jdbcType="TINYINT" property="synced" />
    <result column="retry_sync_num" jdbcType="INTEGER" property="retrySyncNum" />
    <result column="sync_result_code" jdbcType="VARCHAR" property="syncResultCode" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vehicle_model_id, channel_id, merchant_id, bind_channel_vehicle_id, bind_channel_vehicle_name, 
    bind_channel_vehicle_sery_id, synced, retry_sync_num, sync_result_code, deleted, 
    last_ver, op_user_id, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.VehicleBindExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_bind
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_bind
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_bind
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.VehicleBindExample">
    delete from vehicle_bind
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.VehicleBind">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_bind (vehicle_model_id, channel_id, merchant_id, 
      bind_channel_vehicle_id, bind_channel_vehicle_name, 
      bind_channel_vehicle_sery_id, synced, retry_sync_num, 
      sync_result_code, deleted, last_ver, 
      op_user_id, create_time, op_time
      )
    values (#{vehicleModelId,jdbcType=BIGINT}, #{channelId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, 
      #{bindChannelVehicleId,jdbcType=VARCHAR}, #{bindChannelVehicleName,jdbcType=VARCHAR}, 
      #{bindChannelVehicleSeryId,jdbcType=VARCHAR}, #{synced,jdbcType=TINYINT}, #{retrySyncNum,jdbcType=INTEGER}, 
      #{syncResultCode,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER}, 
      #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.VehicleBind">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_bind
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="bindChannelVehicleId != null">
        bind_channel_vehicle_id,
      </if>
      <if test="bindChannelVehicleName != null">
        bind_channel_vehicle_name,
      </if>
      <if test="bindChannelVehicleSeryId != null">
        bind_channel_vehicle_sery_id,
      </if>
      <if test="synced != null">
        synced,
      </if>
      <if test="retrySyncNum != null">
        retry_sync_num,
      </if>
      <if test="syncResultCode != null">
        sync_result_code,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="bindChannelVehicleId != null">
        #{bindChannelVehicleId,jdbcType=VARCHAR},
      </if>
      <if test="bindChannelVehicleName != null">
        #{bindChannelVehicleName,jdbcType=VARCHAR},
      </if>
      <if test="bindChannelVehicleSeryId != null">
        #{bindChannelVehicleSeryId,jdbcType=VARCHAR},
      </if>
      <if test="synced != null">
        #{synced,jdbcType=TINYINT},
      </if>
      <if test="retrySyncNum != null">
        #{retrySyncNum,jdbcType=INTEGER},
      </if>
      <if test="syncResultCode != null">
        #{syncResultCode,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.VehicleBindExample" resultType="java.lang.Long">
    select count(*) from vehicle_bind
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_bind
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.bindChannelVehicleId != null">
        bind_channel_vehicle_id = #{record.bindChannelVehicleId,jdbcType=VARCHAR},
      </if>
      <if test="record.bindChannelVehicleName != null">
        bind_channel_vehicle_name = #{record.bindChannelVehicleName,jdbcType=VARCHAR},
      </if>
      <if test="record.bindChannelVehicleSeryId != null">
        bind_channel_vehicle_sery_id = #{record.bindChannelVehicleSeryId,jdbcType=VARCHAR},
      </if>
      <if test="record.synced != null">
        synced = #{record.synced,jdbcType=TINYINT},
      </if>
      <if test="record.retrySyncNum != null">
        retry_sync_num = #{record.retrySyncNum,jdbcType=INTEGER},
      </if>
      <if test="record.syncResultCode != null">
        sync_result_code = #{record.syncResultCode,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_bind
    set id = #{record.id,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      channel_id = #{record.channelId,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      bind_channel_vehicle_id = #{record.bindChannelVehicleId,jdbcType=VARCHAR},
      bind_channel_vehicle_name = #{record.bindChannelVehicleName,jdbcType=VARCHAR},
      bind_channel_vehicle_sery_id = #{record.bindChannelVehicleSeryId,jdbcType=VARCHAR},
      synced = #{record.synced,jdbcType=TINYINT},
      retry_sync_num = #{record.retrySyncNum,jdbcType=INTEGER},
      sync_result_code = #{record.syncResultCode,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.VehicleBind">
    update vehicle_bind
    <set>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="bindChannelVehicleId != null">
        bind_channel_vehicle_id = #{bindChannelVehicleId,jdbcType=VARCHAR},
      </if>
      <if test="bindChannelVehicleName != null">
        bind_channel_vehicle_name = #{bindChannelVehicleName,jdbcType=VARCHAR},
      </if>
      <if test="bindChannelVehicleSeryId != null">
        bind_channel_vehicle_sery_id = #{bindChannelVehicleSeryId,jdbcType=VARCHAR},
      </if>
      <if test="synced != null">
        synced = #{synced,jdbcType=TINYINT},
      </if>
      <if test="retrySyncNum != null">
        retry_sync_num = #{retrySyncNum,jdbcType=INTEGER},
      </if>
      <if test="syncResultCode != null">
        sync_result_code = #{syncResultCode,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.VehicleBind">
    update vehicle_bind
    set vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      channel_id = #{channelId,jdbcType=BIGINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      bind_channel_vehicle_id = #{bindChannelVehicleId,jdbcType=VARCHAR},
      bind_channel_vehicle_name = #{bindChannelVehicleName,jdbcType=VARCHAR},
      bind_channel_vehicle_sery_id = #{bindChannelVehicleSeryId,jdbcType=VARCHAR},
      synced = #{synced,jdbcType=TINYINT},
      retry_sync_num = #{retrySyncNum,jdbcType=INTEGER},
      sync_result_code = #{syncResultCode,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_bind
    (vehicle_model_id, channel_id, merchant_id, bind_channel_vehicle_id, bind_channel_vehicle_name, 
      bind_channel_vehicle_sery_id, synced, retry_sync_num, sync_result_code, deleted, 
      last_ver, op_user_id, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.vehicleModelId,jdbcType=BIGINT}, #{item.channelId,jdbcType=BIGINT}, #{item.merchantId,jdbcType=BIGINT}, 
        #{item.bindChannelVehicleId,jdbcType=VARCHAR}, #{item.bindChannelVehicleName,jdbcType=VARCHAR}, 
        #{item.bindChannelVehicleSeryId,jdbcType=VARCHAR}, #{item.synced,jdbcType=TINYINT}, 
        #{item.retrySyncNum,jdbcType=INTEGER}, #{item.syncResultCode,jdbcType=VARCHAR}, 
        #{item.deleted,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER}, #{item.opUserId,jdbcType=BIGINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_bind (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'channel_id'.toString() == column.value">
          #{item.channelId,jdbcType=BIGINT}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'bind_channel_vehicle_id'.toString() == column.value">
          #{item.bindChannelVehicleId,jdbcType=VARCHAR}
        </if>
        <if test="'bind_channel_vehicle_name'.toString() == column.value">
          #{item.bindChannelVehicleName,jdbcType=VARCHAR}
        </if>
        <if test="'bind_channel_vehicle_sery_id'.toString() == column.value">
          #{item.bindChannelVehicleSeryId,jdbcType=VARCHAR}
        </if>
        <if test="'synced'.toString() == column.value">
          #{item.synced,jdbcType=TINYINT}
        </if>
        <if test="'retry_sync_num'.toString() == column.value">
          #{item.retrySyncNum,jdbcType=INTEGER}
        </if>
        <if test="'sync_result_code'.toString() == column.value">
          #{item.syncResultCode,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
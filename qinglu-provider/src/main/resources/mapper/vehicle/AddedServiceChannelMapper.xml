<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.AddedServiceChannelMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.AddedServiceChannel">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="added_service_id" jdbcType="BIGINT" property="addedServiceId" />
    <result column="channel" jdbcType="BIGINT" property="channel" />
    <result column="price" jdbcType="INTEGER" property="price" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="rent_base_id" jdbcType="BIGINT" property="rentBaseId" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="added_service_setting_id" jdbcType="BIGINT" property="addedServiceSettingId" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, added_service_id, channel, price, status, op_user_id, merchant_id, store_id, 
    rent_base_id, vehicle_model_id, added_service_setting_id, last_ver, create_time, 
    op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.AddedServiceChannelExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from added_service_channel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from added_service_channel
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from added_service_channel
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.AddedServiceChannelExample">
    delete from added_service_channel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.AddedServiceChannel">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into added_service_channel (added_service_id, channel, price, 
      status, op_user_id, merchant_id, 
      store_id, rent_base_id, vehicle_model_id, 
      added_service_setting_id, last_ver, create_time, 
      op_time)
    values (#{addedServiceId,jdbcType=BIGINT}, #{channel,jdbcType=BIGINT}, #{price,jdbcType=INTEGER}, 
      #{status,jdbcType=TINYINT}, #{opUserId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, 
      #{storeId,jdbcType=BIGINT}, #{rentBaseId,jdbcType=BIGINT}, #{vehicleModelId,jdbcType=BIGINT}, 
      #{addedServiceSettingId,jdbcType=BIGINT}, #{lastVer,jdbcType=INTEGER}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.AddedServiceChannel">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into added_service_channel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addedServiceId != null">
        added_service_id,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="rentBaseId != null">
        rent_base_id,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="addedServiceSettingId != null">
        added_service_setting_id,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addedServiceId != null">
        #{addedServiceId,jdbcType=BIGINT},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=BIGINT},
      </if>
      <if test="price != null">
        #{price,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="rentBaseId != null">
        #{rentBaseId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="addedServiceSettingId != null">
        #{addedServiceSettingId,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.AddedServiceChannelExample" resultType="java.lang.Long">
    select count(*) from added_service_channel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update added_service_channel
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.addedServiceId != null">
        added_service_id = #{record.addedServiceId,jdbcType=BIGINT},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=BIGINT},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.rentBaseId != null">
        rent_base_id = #{record.rentBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.addedServiceSettingId != null">
        added_service_setting_id = #{record.addedServiceSettingId,jdbcType=BIGINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update added_service_channel
    set id = #{record.id,jdbcType=BIGINT},
      added_service_id = #{record.addedServiceId,jdbcType=BIGINT},
      channel = #{record.channel,jdbcType=BIGINT},
      price = #{record.price,jdbcType=INTEGER},
      status = #{record.status,jdbcType=TINYINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      rent_base_id = #{record.rentBaseId,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      added_service_setting_id = #{record.addedServiceSettingId,jdbcType=BIGINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.AddedServiceChannel">
    update added_service_channel
    <set>
      <if test="addedServiceId != null">
        added_service_id = #{addedServiceId,jdbcType=BIGINT},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=BIGINT},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="rentBaseId != null">
        rent_base_id = #{rentBaseId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="addedServiceSettingId != null">
        added_service_setting_id = #{addedServiceSettingId,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.AddedServiceChannel">
    update added_service_channel
    set added_service_id = #{addedServiceId,jdbcType=BIGINT},
      channel = #{channel,jdbcType=BIGINT},
      price = #{price,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      rent_base_id = #{rentBaseId,jdbcType=BIGINT},
      vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      added_service_setting_id = #{addedServiceSettingId,jdbcType=BIGINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into added_service_channel
    (added_service_id, channel, price, status, op_user_id, merchant_id, store_id, rent_base_id, 
      vehicle_model_id, added_service_setting_id, last_ver, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addedServiceId,jdbcType=BIGINT}, #{item.channel,jdbcType=BIGINT}, #{item.price,jdbcType=INTEGER}, 
        #{item.status,jdbcType=TINYINT}, #{item.opUserId,jdbcType=BIGINT}, #{item.merchantId,jdbcType=BIGINT}, 
        #{item.storeId,jdbcType=BIGINT}, #{item.rentBaseId,jdbcType=BIGINT}, #{item.vehicleModelId,jdbcType=BIGINT}, 
        #{item.addedServiceSettingId,jdbcType=BIGINT}, #{item.lastVer,jdbcType=INTEGER}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into added_service_channel (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'added_service_id'.toString() == column.value">
          #{item.addedServiceId,jdbcType=BIGINT}
        </if>
        <if test="'channel'.toString() == column.value">
          #{item.channel,jdbcType=BIGINT}
        </if>
        <if test="'price'.toString() == column.value">
          #{item.price,jdbcType=INTEGER}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'rent_base_id'.toString() == column.value">
          #{item.rentBaseId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'added_service_setting_id'.toString() == column.value">
          #{item.addedServiceSettingId,jdbcType=BIGINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>

  <update id="batchUpdate"  keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <foreach collection="list" item="item" separator=";">
      update added_service_channel
      <set>
        <if test="item.addedServiceId != null">
          added_service_id = #{item.addedServiceId,jdbcType=BIGINT},
        </if>
        <if test="item.channel != null">
          channel = #{item.channel,jdbcType=BIGINT},
        </if>
        <if test="item.price != null">
          price = #{item.price,jdbcType=INTEGER},
        </if>
        <if test="item.status != null">
          status = #{item.status,jdbcType=TINYINT},
        </if>
        <if test="item.opUserId != null">
          op_user_id = #{item.opUserId,jdbcType=BIGINT},
        </if>
        <if test="item.merchantId != null">
          merchant_id = #{item.merchantId,jdbcType=BIGINT},
        </if>
        <if test="item.storeId != null">
          store_id = #{item.storeId,jdbcType=BIGINT},
        </if>
        <if test="item.rentBaseId != null">
          rent_base_id = #{item.rentBaseId,jdbcType=BIGINT},
        </if>
        <if test="item.vehicleModelId != null">
          vehicle_model_id = #{item.vehicleModelId,jdbcType=BIGINT},
        </if>
        <if test="item.addedServiceSettingId != null">
          added_service_setting_id = #{item.addedServiceSettingId,jdbcType=BIGINT},
        </if>
        <if test="item.lastVer != null">
          last_ver = #{item.lastVer,jdbcType=INTEGER},
        </if>
        <if test="item.createTime != null">
          create_time = #{item.createTime,jdbcType=BIGINT},
        </if>
        <if test="item.opTime != null">
          op_time = #{item.opTime,jdbcType=BIGINT},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.VehicleModelMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.VehicleModel">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vehicle_sery_id" jdbcType="BIGINT" property="vehicleSeryId" />
    <result column="vehicle_sery_name" jdbcType="VARCHAR" property="vehicleSeryName" />
    <result column="vehicle_sub_sery_id" jdbcType="BIGINT" property="vehicleSubSeryId" />
    <result column="vehicle_sub_sery_name" jdbcType="VARCHAR" property="vehicleSubSeryName" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="vehicle_year_style" jdbcType="VARCHAR" property="vehicleYearStyle" />
    <result column="displacement" jdbcType="VARCHAR" property="displacement" />
    <result column="gearbox" jdbcType="VARCHAR" property="gearbox" />
    <result column="seat_num" jdbcType="SMALLINT" property="seatNum" />
    <result column="carriage" jdbcType="VARCHAR" property="carriage" />
    <result column="doors" jdbcType="TINYINT" property="doors" />
    <result column="car_structure" jdbcType="VARCHAR" property="carStructure" />
    <result column="fuel_form" jdbcType="VARCHAR" property="fuelForm" />
    <result column="vehicle_model_group_id" jdbcType="BIGINT" property="vehicleModelGroupId" />
    <result column="license_type_id" jdbcType="BIGINT" property="licenseTypeId" />
    <result column="license_type" jdbcType="VARCHAR" property="licenseType" />
    <result column="has_sunroof" jdbcType="TINYINT" property="hasSunroof" />
    <result column="drive_type" jdbcType="TINYINT" property="driveType" />
    <result column="has_snow_tires" jdbcType="TINYINT" property="hasSnowTires" />
    <result column="self_service_return" jdbcType="TINYINT" property="selfServiceReturn" />
    <result column="fast_charge_time" jdbcType="TINYINT" property="fastChargeTime" />
    <result column="inventory" jdbcType="SMALLINT" property="inventory" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_test" jdbcType="TINYINT" property="isTest" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vehicle_sery_id, vehicle_sery_name, vehicle_sub_sery_id, vehicle_sub_sery_name, 
    store_id, merchant_id, vehicle_year_style, displacement, gearbox, seat_num, carriage, 
    doors, car_structure, fuel_form, vehicle_model_group_id, license_type_id, license_type, 
    has_sunroof, drive_type, has_snow_tires, self_service_return, fast_charge_time, inventory, 
    status, is_test, last_ver, op_user_id, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.VehicleModelExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_model
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_model
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_model
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.VehicleModelExample">
    delete from vehicle_model
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.VehicleModel">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_model (vehicle_sery_id, vehicle_sery_name, vehicle_sub_sery_id, 
      vehicle_sub_sery_name, store_id, merchant_id, 
      vehicle_year_style, displacement, gearbox, 
      seat_num, carriage, doors, 
      car_structure, fuel_form, vehicle_model_group_id, 
      license_type_id, license_type, has_sunroof, 
      drive_type, has_snow_tires, self_service_return, 
      fast_charge_time, inventory, status, 
      is_test, last_ver, op_user_id, 
      create_time, op_time)
    values (#{vehicleSeryId,jdbcType=BIGINT}, #{vehicleSeryName,jdbcType=VARCHAR}, #{vehicleSubSeryId,jdbcType=BIGINT}, 
      #{vehicleSubSeryName,jdbcType=VARCHAR}, #{storeId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, 
      #{vehicleYearStyle,jdbcType=VARCHAR}, #{displacement,jdbcType=VARCHAR}, #{gearbox,jdbcType=VARCHAR}, 
      #{seatNum,jdbcType=SMALLINT}, #{carriage,jdbcType=VARCHAR}, #{doors,jdbcType=TINYINT}, 
      #{carStructure,jdbcType=VARCHAR}, #{fuelForm,jdbcType=VARCHAR}, #{vehicleModelGroupId,jdbcType=BIGINT}, 
      #{licenseTypeId,jdbcType=BIGINT}, #{licenseType,jdbcType=VARCHAR}, #{hasSunroof,jdbcType=TINYINT}, 
      #{driveType,jdbcType=TINYINT}, #{hasSnowTires,jdbcType=TINYINT}, #{selfServiceReturn,jdbcType=TINYINT}, 
      #{fastChargeTime,jdbcType=TINYINT}, #{inventory,jdbcType=SMALLINT}, #{status,jdbcType=TINYINT}, 
      #{isTest,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER}, #{opUserId,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.VehicleModel">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_model
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="vehicleSeryId != null">
        vehicle_sery_id,
      </if>
      <if test="vehicleSeryName != null">
        vehicle_sery_name,
      </if>
      <if test="vehicleSubSeryId != null">
        vehicle_sub_sery_id,
      </if>
      <if test="vehicleSubSeryName != null">
        vehicle_sub_sery_name,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="vehicleYearStyle != null">
        vehicle_year_style,
      </if>
      <if test="displacement != null">
        displacement,
      </if>
      <if test="gearbox != null">
        gearbox,
      </if>
      <if test="seatNum != null">
        seat_num,
      </if>
      <if test="carriage != null">
        carriage,
      </if>
      <if test="doors != null">
        doors,
      </if>
      <if test="carStructure != null">
        car_structure,
      </if>
      <if test="fuelForm != null">
        fuel_form,
      </if>
      <if test="vehicleModelGroupId != null">
        vehicle_model_group_id,
      </if>
      <if test="licenseTypeId != null">
        license_type_id,
      </if>
      <if test="licenseType != null">
        license_type,
      </if>
      <if test="hasSunroof != null">
        has_sunroof,
      </if>
      <if test="driveType != null">
        drive_type,
      </if>
      <if test="hasSnowTires != null">
        has_snow_tires,
      </if>
      <if test="selfServiceReturn != null">
        self_service_return,
      </if>
      <if test="fastChargeTime != null">
        fast_charge_time,
      </if>
      <if test="inventory != null">
        inventory,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isTest != null">
        is_test,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="vehicleSeryId != null">
        #{vehicleSeryId,jdbcType=BIGINT},
      </if>
      <if test="vehicleSeryName != null">
        #{vehicleSeryName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSubSeryId != null">
        #{vehicleSubSeryId,jdbcType=BIGINT},
      </if>
      <if test="vehicleSubSeryName != null">
        #{vehicleSubSeryName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="vehicleYearStyle != null">
        #{vehicleYearStyle,jdbcType=VARCHAR},
      </if>
      <if test="displacement != null">
        #{displacement,jdbcType=VARCHAR},
      </if>
      <if test="gearbox != null">
        #{gearbox,jdbcType=VARCHAR},
      </if>
      <if test="seatNum != null">
        #{seatNum,jdbcType=SMALLINT},
      </if>
      <if test="carriage != null">
        #{carriage,jdbcType=VARCHAR},
      </if>
      <if test="doors != null">
        #{doors,jdbcType=TINYINT},
      </if>
      <if test="carStructure != null">
        #{carStructure,jdbcType=VARCHAR},
      </if>
      <if test="fuelForm != null">
        #{fuelForm,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelGroupId != null">
        #{vehicleModelGroupId,jdbcType=BIGINT},
      </if>
      <if test="licenseTypeId != null">
        #{licenseTypeId,jdbcType=BIGINT},
      </if>
      <if test="licenseType != null">
        #{licenseType,jdbcType=VARCHAR},
      </if>
      <if test="hasSunroof != null">
        #{hasSunroof,jdbcType=TINYINT},
      </if>
      <if test="driveType != null">
        #{driveType,jdbcType=TINYINT},
      </if>
      <if test="hasSnowTires != null">
        #{hasSnowTires,jdbcType=TINYINT},
      </if>
      <if test="selfServiceReturn != null">
        #{selfServiceReturn,jdbcType=TINYINT},
      </if>
      <if test="fastChargeTime != null">
        #{fastChargeTime,jdbcType=TINYINT},
      </if>
      <if test="inventory != null">
        #{inventory,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isTest != null">
        #{isTest,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.VehicleModelExample" resultType="java.lang.Long">
    select count(*) from vehicle_model
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_model
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleSeryId != null">
        vehicle_sery_id = #{record.vehicleSeryId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleSeryName != null">
        vehicle_sery_name = #{record.vehicleSeryName,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleSubSeryId != null">
        vehicle_sub_sery_id = #{record.vehicleSubSeryId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleSubSeryName != null">
        vehicle_sub_sery_name = #{record.vehicleSubSeryName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleYearStyle != null">
        vehicle_year_style = #{record.vehicleYearStyle,jdbcType=VARCHAR},
      </if>
      <if test="record.displacement != null">
        displacement = #{record.displacement,jdbcType=VARCHAR},
      </if>
      <if test="record.gearbox != null">
        gearbox = #{record.gearbox,jdbcType=VARCHAR},
      </if>
      <if test="record.seatNum != null">
        seat_num = #{record.seatNum,jdbcType=SMALLINT},
      </if>
      <if test="record.carriage != null">
        carriage = #{record.carriage,jdbcType=VARCHAR},
      </if>
      <if test="record.doors != null">
        doors = #{record.doors,jdbcType=TINYINT},
      </if>
      <if test="record.carStructure != null">
        car_structure = #{record.carStructure,jdbcType=VARCHAR},
      </if>
      <if test="record.fuelForm != null">
        fuel_form = #{record.fuelForm,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleModelGroupId != null">
        vehicle_model_group_id = #{record.vehicleModelGroupId,jdbcType=BIGINT},
      </if>
      <if test="record.licenseTypeId != null">
        license_type_id = #{record.licenseTypeId,jdbcType=BIGINT},
      </if>
      <if test="record.licenseType != null">
        license_type = #{record.licenseType,jdbcType=VARCHAR},
      </if>
      <if test="record.hasSunroof != null">
        has_sunroof = #{record.hasSunroof,jdbcType=TINYINT},
      </if>
      <if test="record.driveType != null">
        drive_type = #{record.driveType,jdbcType=TINYINT},
      </if>
      <if test="record.hasSnowTires != null">
        has_snow_tires = #{record.hasSnowTires,jdbcType=TINYINT},
      </if>
      <if test="record.selfServiceReturn != null">
        self_service_return = #{record.selfServiceReturn,jdbcType=TINYINT},
      </if>
      <if test="record.fastChargeTime != null">
        fast_charge_time = #{record.fastChargeTime,jdbcType=TINYINT},
      </if>
      <if test="record.inventory != null">
        inventory = #{record.inventory,jdbcType=SMALLINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.isTest != null">
        is_test = #{record.isTest,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_model
    set id = #{record.id,jdbcType=BIGINT},
      vehicle_sery_id = #{record.vehicleSeryId,jdbcType=BIGINT},
      vehicle_sery_name = #{record.vehicleSeryName,jdbcType=VARCHAR},
      vehicle_sub_sery_id = #{record.vehicleSubSeryId,jdbcType=BIGINT},
      vehicle_sub_sery_name = #{record.vehicleSubSeryName,jdbcType=VARCHAR},
      store_id = #{record.storeId,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      vehicle_year_style = #{record.vehicleYearStyle,jdbcType=VARCHAR},
      displacement = #{record.displacement,jdbcType=VARCHAR},
      gearbox = #{record.gearbox,jdbcType=VARCHAR},
      seat_num = #{record.seatNum,jdbcType=SMALLINT},
      carriage = #{record.carriage,jdbcType=VARCHAR},
      doors = #{record.doors,jdbcType=TINYINT},
      car_structure = #{record.carStructure,jdbcType=VARCHAR},
      fuel_form = #{record.fuelForm,jdbcType=VARCHAR},
      vehicle_model_group_id = #{record.vehicleModelGroupId,jdbcType=BIGINT},
      license_type_id = #{record.licenseTypeId,jdbcType=BIGINT},
      license_type = #{record.licenseType,jdbcType=VARCHAR},
      has_sunroof = #{record.hasSunroof,jdbcType=TINYINT},
      drive_type = #{record.driveType,jdbcType=TINYINT},
      has_snow_tires = #{record.hasSnowTires,jdbcType=TINYINT},
      self_service_return = #{record.selfServiceReturn,jdbcType=TINYINT},
      fast_charge_time = #{record.fastChargeTime,jdbcType=TINYINT},
      inventory = #{record.inventory,jdbcType=SMALLINT},
      status = #{record.status,jdbcType=TINYINT},
      is_test = #{record.isTest,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.VehicleModel">
    update vehicle_model
    <set>
      <if test="vehicleSeryId != null">
        vehicle_sery_id = #{vehicleSeryId,jdbcType=BIGINT},
      </if>
      <if test="vehicleSeryName != null">
        vehicle_sery_name = #{vehicleSeryName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSubSeryId != null">
        vehicle_sub_sery_id = #{vehicleSubSeryId,jdbcType=BIGINT},
      </if>
      <if test="vehicleSubSeryName != null">
        vehicle_sub_sery_name = #{vehicleSubSeryName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="vehicleYearStyle != null">
        vehicle_year_style = #{vehicleYearStyle,jdbcType=VARCHAR},
      </if>
      <if test="displacement != null">
        displacement = #{displacement,jdbcType=VARCHAR},
      </if>
      <if test="gearbox != null">
        gearbox = #{gearbox,jdbcType=VARCHAR},
      </if>
      <if test="seatNum != null">
        seat_num = #{seatNum,jdbcType=SMALLINT},
      </if>
      <if test="carriage != null">
        carriage = #{carriage,jdbcType=VARCHAR},
      </if>
      <if test="doors != null">
        doors = #{doors,jdbcType=TINYINT},
      </if>
      <if test="carStructure != null">
        car_structure = #{carStructure,jdbcType=VARCHAR},
      </if>
      <if test="fuelForm != null">
        fuel_form = #{fuelForm,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelGroupId != null">
        vehicle_model_group_id = #{vehicleModelGroupId,jdbcType=BIGINT},
      </if>
      <if test="licenseTypeId != null">
        license_type_id = #{licenseTypeId,jdbcType=BIGINT},
      </if>
      <if test="licenseType != null">
        license_type = #{licenseType,jdbcType=VARCHAR},
      </if>
      <if test="hasSunroof != null">
        has_sunroof = #{hasSunroof,jdbcType=TINYINT},
      </if>
      <if test="driveType != null">
        drive_type = #{driveType,jdbcType=TINYINT},
      </if>
      <if test="hasSnowTires != null">
        has_snow_tires = #{hasSnowTires,jdbcType=TINYINT},
      </if>
      <if test="selfServiceReturn != null">
        self_service_return = #{selfServiceReturn,jdbcType=TINYINT},
      </if>
      <if test="fastChargeTime != null">
        fast_charge_time = #{fastChargeTime,jdbcType=TINYINT},
      </if>
      <if test="inventory != null">
        inventory = #{inventory,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="isTest != null">
        is_test = #{isTest,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.VehicleModel">
    update vehicle_model
    set vehicle_sery_id = #{vehicleSeryId,jdbcType=BIGINT},
      vehicle_sery_name = #{vehicleSeryName,jdbcType=VARCHAR},
      vehicle_sub_sery_id = #{vehicleSubSeryId,jdbcType=BIGINT},
      vehicle_sub_sery_name = #{vehicleSubSeryName,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=BIGINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      vehicle_year_style = #{vehicleYearStyle,jdbcType=VARCHAR},
      displacement = #{displacement,jdbcType=VARCHAR},
      gearbox = #{gearbox,jdbcType=VARCHAR},
      seat_num = #{seatNum,jdbcType=SMALLINT},
      carriage = #{carriage,jdbcType=VARCHAR},
      doors = #{doors,jdbcType=TINYINT},
      car_structure = #{carStructure,jdbcType=VARCHAR},
      fuel_form = #{fuelForm,jdbcType=VARCHAR},
      vehicle_model_group_id = #{vehicleModelGroupId,jdbcType=BIGINT},
      license_type_id = #{licenseTypeId,jdbcType=BIGINT},
      license_type = #{licenseType,jdbcType=VARCHAR},
      has_sunroof = #{hasSunroof,jdbcType=TINYINT},
      drive_type = #{driveType,jdbcType=TINYINT},
      has_snow_tires = #{hasSnowTires,jdbcType=TINYINT},
      self_service_return = #{selfServiceReturn,jdbcType=TINYINT},
      fast_charge_time = #{fastChargeTime,jdbcType=TINYINT},
      inventory = #{inventory,jdbcType=SMALLINT},
      status = #{status,jdbcType=TINYINT},
      is_test = #{isTest,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_model
    (vehicle_sery_id, vehicle_sery_name, vehicle_sub_sery_id, vehicle_sub_sery_name, 
      store_id, merchant_id, vehicle_year_style, displacement, gearbox, seat_num, carriage, 
      doors, car_structure, fuel_form, vehicle_model_group_id, license_type_id, license_type, 
      has_sunroof, drive_type, has_snow_tires, self_service_return, fast_charge_time, 
      inventory, status, is_test, last_ver, op_user_id, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.vehicleSeryId,jdbcType=BIGINT}, #{item.vehicleSeryName,jdbcType=VARCHAR}, 
        #{item.vehicleSubSeryId,jdbcType=BIGINT}, #{item.vehicleSubSeryName,jdbcType=VARCHAR}, 
        #{item.storeId,jdbcType=BIGINT}, #{item.merchantId,jdbcType=BIGINT}, #{item.vehicleYearStyle,jdbcType=VARCHAR}, 
        #{item.displacement,jdbcType=VARCHAR}, #{item.gearbox,jdbcType=VARCHAR}, #{item.seatNum,jdbcType=SMALLINT}, 
        #{item.carriage,jdbcType=VARCHAR}, #{item.doors,jdbcType=TINYINT}, #{item.carStructure,jdbcType=VARCHAR}, 
        #{item.fuelForm,jdbcType=VARCHAR}, #{item.vehicleModelGroupId,jdbcType=BIGINT}, 
        #{item.licenseTypeId,jdbcType=BIGINT}, #{item.licenseType,jdbcType=VARCHAR}, #{item.hasSunroof,jdbcType=TINYINT}, 
        #{item.driveType,jdbcType=TINYINT}, #{item.hasSnowTires,jdbcType=TINYINT}, #{item.selfServiceReturn,jdbcType=TINYINT}, 
        #{item.fastChargeTime,jdbcType=TINYINT}, #{item.inventory,jdbcType=SMALLINT}, #{item.status,jdbcType=TINYINT}, 
        #{item.isTest,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER}, #{item.opUserId,jdbcType=BIGINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_model (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'vehicle_sery_id'.toString() == column.value">
          #{item.vehicleSeryId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_sery_name'.toString() == column.value">
          #{item.vehicleSeryName,jdbcType=VARCHAR}
        </if>
        <if test="'vehicle_sub_sery_id'.toString() == column.value">
          #{item.vehicleSubSeryId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_sub_sery_name'.toString() == column.value">
          #{item.vehicleSubSeryName,jdbcType=VARCHAR}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_year_style'.toString() == column.value">
          #{item.vehicleYearStyle,jdbcType=VARCHAR}
        </if>
        <if test="'displacement'.toString() == column.value">
          #{item.displacement,jdbcType=VARCHAR}
        </if>
        <if test="'gearbox'.toString() == column.value">
          #{item.gearbox,jdbcType=VARCHAR}
        </if>
        <if test="'seat_num'.toString() == column.value">
          #{item.seatNum,jdbcType=SMALLINT}
        </if>
        <if test="'carriage'.toString() == column.value">
          #{item.carriage,jdbcType=VARCHAR}
        </if>
        <if test="'doors'.toString() == column.value">
          #{item.doors,jdbcType=TINYINT}
        </if>
        <if test="'car_structure'.toString() == column.value">
          #{item.carStructure,jdbcType=VARCHAR}
        </if>
        <if test="'fuel_form'.toString() == column.value">
          #{item.fuelForm,jdbcType=VARCHAR}
        </if>
        <if test="'vehicle_model_group_id'.toString() == column.value">
          #{item.vehicleModelGroupId,jdbcType=BIGINT}
        </if>
        <if test="'license_type_id'.toString() == column.value">
          #{item.licenseTypeId,jdbcType=BIGINT}
        </if>
        <if test="'license_type'.toString() == column.value">
          #{item.licenseType,jdbcType=VARCHAR}
        </if>
        <if test="'has_sunroof'.toString() == column.value">
          #{item.hasSunroof,jdbcType=TINYINT}
        </if>
        <if test="'drive_type'.toString() == column.value">
          #{item.driveType,jdbcType=TINYINT}
        </if>
        <if test="'has_snow_tires'.toString() == column.value">
          #{item.hasSnowTires,jdbcType=TINYINT}
        </if>
        <if test="'self_service_return'.toString() == column.value">
          #{item.selfServiceReturn,jdbcType=TINYINT}
        </if>
        <if test="'fast_charge_time'.toString() == column.value">
          #{item.fastChargeTime,jdbcType=TINYINT}
        </if>
        <if test="'inventory'.toString() == column.value">
          #{item.inventory,jdbcType=SMALLINT}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'is_test'.toString() == column.value">
          #{item.isTest,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.LicenseTypeMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.LicenseType">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="license_type_name" jdbcType="VARCHAR" property="licenseTypeName" />
    <result column="license_pinyin" jdbcType="VARCHAR" property="licensePinyin" />
    <result column="city_id" jdbcType="BIGINT" property="cityId" />
    <result column="preset" jdbcType="TINYINT" property="preset" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, store_id, merchant_id, license_type_name, license_pinyin, city_id, preset, deleted, 
    last_ver, create_time, op_time, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.LicenseTypeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from license_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from license_type
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from license_type
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.LicenseTypeExample">
    delete from license_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.LicenseType">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into license_type (store_id, merchant_id, license_type_name, 
      license_pinyin, city_id, preset, 
      deleted, last_ver, create_time, 
      op_time, op_user_id)
    values (#{storeId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, #{licenseTypeName,jdbcType=VARCHAR}, 
      #{licensePinyin,jdbcType=VARCHAR}, #{cityId,jdbcType=BIGINT}, #{preset,jdbcType=TINYINT}, 
      #{deleted,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.LicenseType">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into license_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        store_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="licenseTypeName != null">
        license_type_name,
      </if>
      <if test="licensePinyin != null">
        license_pinyin,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="preset != null">
        preset,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="licenseTypeName != null">
        #{licenseTypeName,jdbcType=VARCHAR},
      </if>
      <if test="licensePinyin != null">
        #{licensePinyin,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=BIGINT},
      </if>
      <if test="preset != null">
        #{preset,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.LicenseTypeExample" resultType="java.lang.Long">
    select count(*) from license_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update license_type
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.licenseTypeName != null">
        license_type_name = #{record.licenseTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.licensePinyin != null">
        license_pinyin = #{record.licensePinyin,jdbcType=VARCHAR},
      </if>
      <if test="record.cityId != null">
        city_id = #{record.cityId,jdbcType=BIGINT},
      </if>
      <if test="record.preset != null">
        preset = #{record.preset,jdbcType=TINYINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update license_type
    set id = #{record.id,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      license_type_name = #{record.licenseTypeName,jdbcType=VARCHAR},
      license_pinyin = #{record.licensePinyin,jdbcType=VARCHAR},
      city_id = #{record.cityId,jdbcType=BIGINT},
      preset = #{record.preset,jdbcType=TINYINT},
      deleted = #{record.deleted,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.LicenseType">
    update license_type
    <set>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="licenseTypeName != null">
        license_type_name = #{licenseTypeName,jdbcType=VARCHAR},
      </if>
      <if test="licensePinyin != null">
        license_pinyin = #{licensePinyin,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=BIGINT},
      </if>
      <if test="preset != null">
        preset = #{preset,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.LicenseType">
    update license_type
    set store_id = #{storeId,jdbcType=BIGINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      license_type_name = #{licenseTypeName,jdbcType=VARCHAR},
      license_pinyin = #{licensePinyin,jdbcType=VARCHAR},
      city_id = #{cityId,jdbcType=BIGINT},
      preset = #{preset,jdbcType=TINYINT},
      deleted = #{deleted,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into license_type
    (store_id, merchant_id, license_type_name, license_pinyin, city_id, preset, deleted, 
      last_ver, create_time, op_time, op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.storeId,jdbcType=BIGINT}, #{item.merchantId,jdbcType=BIGINT}, #{item.licenseTypeName,jdbcType=VARCHAR}, 
        #{item.licensePinyin,jdbcType=VARCHAR}, #{item.cityId,jdbcType=BIGINT}, #{item.preset,jdbcType=TINYINT}, 
        #{item.deleted,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into license_type (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'license_type_name'.toString() == column.value">
          #{item.licenseTypeName,jdbcType=VARCHAR}
        </if>
        <if test="'license_pinyin'.toString() == column.value">
          #{item.licensePinyin,jdbcType=VARCHAR}
        </if>
        <if test="'city_id'.toString() == column.value">
          #{item.cityId,jdbcType=BIGINT}
        </if>
        <if test="'preset'.toString() == column.value">
          #{item.preset,jdbcType=TINYINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
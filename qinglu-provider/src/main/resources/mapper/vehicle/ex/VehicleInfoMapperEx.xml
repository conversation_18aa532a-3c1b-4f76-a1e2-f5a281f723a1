<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.ex.VehicleInfoMapperEx">

     <resultMap id="VehicleResultMap" extends="com.ql.rent.dao.vehicle.VehicleInfoMapper.BaseResultMap"
                type="com.ql.rent.entity.vehicle.VehicleInfo">
     </resultMap>


    <select id="listExpiringMaintenanceVehicle" resultMap="VehicleResultMap">
        SELECT id, store_id, store_name,
               mileage, license, maintenance_interval,
               next_maintenance_mileage, merchant_id, last_ver
        FROM vehicle_info
        WHERE deleted = 0 AND next_maintenance_mileage > 0 AND mileage > next_maintenance_mileage
        ORDER BY id
        LIMIT #{param.startPos} , #{param.pageSize}
    </select>

    <select id="countVehicle" resultType="long">
        SELECT count(*)
        <include refid="vehicle_condition"/>
    </select>

    <select id="listVehiclePage" resultMap="VehicleResultMap">
        SELECT vi.*
        <include refid="vehicle_condition"/>
        ORDER BY vi.op_time DESC
        limit #{param.startPos}, #{param.pageSize}
    </select>

    <!--  查询过保车辆 定义过保：某个险种的所有保险都过期
     1、GROUP BY vii.insurance_type,
     2、然后max中的case语句 （如果有未过期的，则返回1，否则返回0），如果相同险种的都过期了，max之后的结果为0
     3、用having = 0所有的条件都为0，则表示所有险种都过期

    <if test="param.insuranceExpireDate != null">
            AND EXISTS (
                SELECT 1
                FROM vehicle_info_insurance vii
                WHERE vii.vehicle_id = vi.id
                AND vii.deleted = 0
                AND vii.end_date &gt; 0
                GROUP BY vii.insurance_type
                HAVING MAX(CASE WHEN vii.end_date > #{param.insuranceExpireDate} THEN 1 ELSE 0 END) = 0
            )
        </if>
    -->
    <sql id="vehicle_condition">
        FROM vehicle_info vi
        <if test="param.ctripVehicleAuditStatus != null or param.ctripVehicleStatus != null">
            INNER JOIN vehicle_channel va ON va.channel_id = 2 AND va.vehicle_id = vi.id AND va.deleted = 0
            <if test="param.ctripVehicleAuditStatus != null">
                AND va.audit_status = #{param.ctripVehicleAuditStatus}
            </if>
            <if test="param.ctripVehicleStatus != null">
                AND va.vehicle_status = #{param.ctripVehicleStatus}
            </if>
        </if>
        <if test="param.shareholder != null and param.shareholder != ''">
            LEFT JOIN vehicle_purchase vp ON vp.vehicle_id = vi.id AND vp.deleted = 0
        </if>
        WHERE vi.merchant_id = #{merchantId}
        <if test="param.includeDel == null or !param.includeDel">
            AND vi.deleted = 0
        </if>
        <if test="param.vehicleStatus != null">
            AND vi.vehicle_status = #{param.vehicleStatus}
        </if>
        <if test="param.licenses != null and param.licenses.size() > 0">
            AND vi.license IN <foreach collection="param.licenses" open="(" item="item" separator="," close=")">#{item}</foreach>
        </if>
        <if test="param.frameNumList != null and param.frameNumList.size() > 0">
            AND vi.frame_num IN <foreach collection="param.frameNumList" open="(" item="item" separator="," close=")">#{item}</foreach>
        </if>
        <if test="param.vehicleSource">
            AND vi.vehicle_source = #{param.vehicleSource}
        </if>
        <if test="param.vehicleModelId">
            AND vi.vehicle_model_id = #{param.vehicleModelId}
        </if>
        <if test="param.storeId">
            AND vi.store_id = #{param.storeId}
        </if>
        <if test="param.storeIdList and param.storeIdList.size() > 0">
            AND vi.store_id IN <foreach collection="param.storeIdList" open="(" item="item" separator="," close=")"> #{item}</foreach>
        </if>
        <if test="param.idList and param.idList.size() > 0">
            AND vi.id IN <foreach collection="param.idList" open="(" item="item" separator="," close=")"> #{item}</foreach>
        </if>
        <if test="param.shareholder != null and param.shareholder != ''">
            AND vp.shareholder = #{param.shareholder}
        </if>
        <if test="param.vehicleSourcesNotIn != null and param.vehicleSourcesNotIn.size() > 0">
            AND vi.vehicle_source NOT IN <foreach collection="param.vehicleSourcesNotIn" open="(" item="item" separator="," close=")">#{item}</foreach>
        </if>
        <if test="param.maintenanceExpired != null and param.maintenanceExpired == 1">
            AND vi.next_maintenance_mileage &gt; 0 AND vi.mileage &gt;= vi.next_maintenance_mileage
        </if>
        <if test="param.platformSold != null">
            AND vi.platform_sold = #{param.platformSold}
        </if>
        <if test="param.yearInspectionExpireDate != null">
            AND vi.yearly_inspection_date &lt;= #{param.yearInspectionExpireDate} AND vi.yearly_inspection_date &gt; 0
        </if>
        <if test="param.insuranceExpireDate != null">
            AND EXISTS (
                SELECT 1
                FROM vehicle_info_insurance vii
                WHERE vii.vehicle_id = vi.id
                AND vii.deleted = 0
                AND vii.end_date &gt; 0
                GROUP BY vii.insurance_type
                HAVING MAX(CASE WHEN vii.end_date > #{param.insuranceExpireDate} THEN 1 ELSE 0 END) = 0
            )
        </if>
        <if test="param.leasedStatus != null">
             AND vi.vehicle_status = #{param.leasedStatus}
        </if>
        <if test="param.etcPublishSearch != null and param.etcPublishSearch==1">
            AND not exists (SELECT 1 FROM etc_device ved
             WHERE ved.vehicle_id = vi.id AND ved.activate_status = 1)
        </if>

    </sql>

    <select id="listSimpleVehicleInfo" resultMap="VehicleResultMap">
        SELECT id, license, store_id, vehicle_model_id, vehicle_status, vehicle_source, platform_sold,
               self_service_return, ti_include_etc, sale_status, reg_date
        FROM vehicle_info  WHERE deleted = 0 AND store_id = #{storeId}
        <if test="ids != null and ids.size > 0">
            AND id IN
            <foreach collection="ids" open="(" item="item" separator="," close=")">#{item} </foreach>
        </if>
    </select>

    <select id="listVehicle" resultMap="VehicleResultMap">
        SELECT id, license, store_id, vehicle_model_id, vehicle_status, vehicle_source, self_service_return, sale_status
        FROM vehicle_info  WHERE store_id = #{storeId}
        <if test="ids != null and ids.size > 0">
            AND id IN
            <foreach collection="ids" open="(" item="item" separator="," close=")">#{item} </foreach>
        </if>
        <if test="isDeleted != null and isDeleted">
            AND deleted = 0
        </if>
    </select>

    <select id="listVehicleWithoutPrice" resultMap="com.ql.rent.dao.vehicle.VehicleInfoMapper.BaseResultMap">
        SELECT iv.vehicle_model_id, iv.id, iv.store_id
        FROM vehicle_info iv
        WHERE iv.merchant_id = #{merchantId}
          AND NOT EXISTS (SELECT 1 FROM rent_main rm WHERE rm.vehicle_model_id = iv.vehicle_model_id AND rm.store_id = iv.store_id AND rm.deleted = 0)
    </select>

    <select id="selectBySubSeryAndPlateId" resultMap="com.ql.rent.dao.vehicle.RentMainMapper.BaseResultMap">
        SELECT rm.store_id, rm.vehicle_model_id
         FROM vehicle_model vm
        INNER JOIN rent_main rm ON rm.vehicle_model_id = vm.id AND rm.store_id = #{storeId} AND rm.deleted = 0
        WHERE vm.merchant_id = #{merchantId}
          AND vm.license_type_id = #{licenseTypeId}
          AND vm.`status` IN <foreach collection="enableVehicleModelStatus" open="(" item="item" separator="," close=")">#{item}</foreach>
          AND vm.vehicle_sub_sery_id IN <foreach collection="subSeryIds" open="(" item="item" separator="," close=")">#{item}</foreach>
        limit 1;
    </select>

    <select id="listUselessVehicleModel" resultType="long">
        SELECT vm.id
          FROM vehicle_model vm
         WHERE vm.id IN <foreach collection="vehicleModelIds" open="(" item="item" separator="," close=")">#{item}</foreach>
           AND vm.status IN  <foreach collection="enableVehicleModelStatus" open="(" item="item" separator="," close=")">#{item}</foreach>
           ANd vm.merchant_id = #{merchantId}
           AND NOT EXISTS (SELECT 1 FROM vehicle_info vi WHERE vi.vehicle_model_id = vm.id AND deleted = 0 AND vi.merchant_id = #{merchantId})
    </select>

    <select id="listAuditFailedVehicle" resultType="com.ql.rent.entity.vehicle.ex.AuditFailedVehicle">
        SELECT
            vi.id, vi.license, vi.store_id
        FROM vehicle_info vi
        INNER JOIN vehicle_channel vc on vi.id = vc.vehicle_id AND vc.channel_id = #{channelId} AND vc.deleted = 0
            AND vc.audit_status = #{auditFailedStatus} AND vc.merchant_id = #{merchantId}
        WHERE vi.deleted = 0 AND vi.merchant_id = #{merchantId} AND vi.platform_sold = 1
    </select>

    <select id="listBaseInfo" resultType="com.ql.rent.entity.vehicle.ex.VehicleBaseInfo">
        SELECT vm.id as vehicleModelId, vi.license, vm.license_type_id, vss.id as vehicleSubSeryId
        FROM vehicle_model vm
        INNER JOIN vehicle_info vi on vm.id = vi.vehicle_model_id and vi.deleted = 0 and vi.merchant_id = #{merchantId}
        INNER JOIN vehicle_sub_sery vss on vss.id = vm.vehicle_sub_sery_id and vss.preset = 1 and vss.deleted = 0
        WHERE vm.status IN <foreach collection="enableVehicleModelStatus" open="(" item="item" separator="," close=")">#{item}</foreach>
            AND vm.merchant_id = #{merchantId}
    </select>
</mapper>
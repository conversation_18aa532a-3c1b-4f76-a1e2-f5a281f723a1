<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.ex.FeizhuVehicleMapperEx">

    <select id="selectFeiZhuSkuByVehicleId" resultType="string">
        select feizhu_sub_sery_id
        from feizhu_vehicle_relation
        where feizhu_vehicle_id = #{feizhuVehicleId} and `status` =  1
        order by id asc limit 1
    </select>

    <select id="findRentMainStoreId" resultType="long">
        SELECT DISTINCT rm.store_id
        FROM `rent_main` rm
        INNER JOIN `vehicle_model` vm on vm.id = rm.`vehicle_model_id` and vm.status in (0,1)
        <if test="notInMerchantIds != null and notInMerchantIds.size() > 0">
            and vm.`merchant_id` not in
            <foreach collection="notInMerchantIds" open="(" item="item" separator="," close=")"> #{item}</foreach>
        </if>
    </select>

    <select id="findRentMainStoreIdByMerchantId" resultType="long">
        SELECT DISTINCT rm.store_id
        FROM `rent_main` rm
        INNER JOIN `vehicle_model` vm on vm.id = rm.`vehicle_model_id` and vm.status in (0,1)
        WHERE vm.merchant_id = #{merchantId}
    </select>

    <select id="findWrongShunntVehicle" resultType="long">
        SELECT DISTINCT vi.id
        FROM `shunting_list` sl
        INNER JOIN `common`.`api_conn` ac on ac.`merchant_id`  = sl.`merchant_id`  and ac.`channel_id` = 11 and ac.`status` = 3
        INNER JOIN `vehicle_info` vi on vi.`deleted` = 0 and sl.`vehicle_info_id`  = vi.id
        INNER JOIN `vehicle_info_tag` vit on vit.`vehicle_id` = vi.id  and vit.`deleted`=  0
        INNER JOIN `vehicle_tag_prop` vtp on vit.`tag_id` = vtp.`id`  and vtp.`tag_name`  = '铁行合格' and `vtp`.`tag_type`  = 2 and vtp.`deleted`  = 0
        WHERE  sl.deleted = 0 and sl.`create_time` &gt; 1744265014695
        <if test="merchantId != null">
            AND sl.merchant_id = #{merchantId}
        </if>
        <if test="vehicleId != null">
            AND sl.vehicle_info_id = #{vehicleId}
        </if>
    </select>
</mapper>
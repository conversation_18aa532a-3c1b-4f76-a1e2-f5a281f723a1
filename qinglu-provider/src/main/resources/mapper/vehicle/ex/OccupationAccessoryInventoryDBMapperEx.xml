<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.ex.OccupationAccessoryInventoryDBExMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.OccupationAccessoryInventoryDB">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="service_product_type" jdbcType="TINYINT" property="serviceProductType" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="virtual_device_id" jdbcType="BIGINT" property="virtualDeviceId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, store_id, vehicle_model_id, vehicle_id, start_time, end_time, service_product_type, 
    op_user_id, deleted, create_time, op_time, virtual_device_id
  </sql>
  <select id="childrenSeatInventoryCheck" resultType="com.ql.rent.vo.price.ChildrenSeatInventoryVo">
    select DISTINCT merchantId, storeId, virtualDeviceId
    from (select p.merchant_id merchantId,
                 p.store_id    storeId,
                 p.id          virtualDeviceId,
    IFNULL(o.virtual_device_id, 0) busyvmid
    from
    (
    select
    id,
    merchant_id,
    store_id
    from
    accessory_products_inventory
    where
    deleted = 0
    and store_id = #{storeId}
    and service_product_type = 1) p
    left join (
    select
    merchant_id,
    store_id,
    start_time,
    end_time,
    virtual_device_id
    from
    occupation_accessory_inventory
    where
    store_id = #{storeId}
    and deleted = 0
    and end_time <![CDATA[ > ]]> #{startTime}
    and start_time <![CDATA[ < ]]> #{endTime}
    ) o on
    p.id = o.virtual_device_id) vie where vie.busyvmid = 0
  </select>


</mapper>
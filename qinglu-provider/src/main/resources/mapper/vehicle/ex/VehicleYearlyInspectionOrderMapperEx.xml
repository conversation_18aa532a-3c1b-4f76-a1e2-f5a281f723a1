<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.ex.VehicleYearlyInspectionOrderMapperEx">

    <select id="listInspectionPeriod" resultType="com.ql.rent.vo.vehicle.VehicleYearlyInspectionVO">
        SELECT vehicle_id AS vehicleInfoId, max(next_inspection_time) AS nextInspectionTime
        FROM vehicle_yearly_inspection_order
        where deleted = 0 AND merchant_id = #{merchantId}
          AND vehicle_id IN <foreach collection="vehicleIdList" open="(" item="item" separator="," close=")"> #{item}</foreach>
          AND `status` = #{status}
        GROUP BY vehicle_id
    </select>
</mapper>
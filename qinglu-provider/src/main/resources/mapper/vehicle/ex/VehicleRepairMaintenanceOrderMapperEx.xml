<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.ex.VehicleRepairMaintenanceOrderMapperEx">
    <resultMap id="BaseResultMap" extends="com.ql.rent.dao.vehicle.VehicleRepairMaintenanceOrderMapper.BaseResultMap"
               type="com.ql.rent.entity.vehicle.VehicleRepairMaintenanceOrder">
    </resultMap>

    <sql id="Base_Column_List">
                id, work_order_no, vehicle_info_id, license_no, vehicle_model_id, vehicle_model_name,
                work_order_type, depot_id, depot_name, before_mileage, after_mileage, start_time,
                end_time, occupied_vehicle, remark, relation_order_id, handler_user_id, handler_user_name,
                status, cancel_reason, merchant_id, store_id, last_ver, deleted, create_time, op_time,
                op_user_id
    </sql>

    <select id="selectLastRepairMaintenance" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM vehicle_repair_maintenance_order
        WHERE merchant_id = #{merchantId} AND (vehicle_info_id, start_time) IN (
        SELECT vehicle_info_id, MAX(start_time) AS start_time
        FROM vehicle_repair_maintenance_order
        WHERE merchant_id = #{merchantId} AND deleted = 0 AND vehicle_info_id IN
        <foreach collection="vehicleIds" item="vehicleId" open="(" close=")" separator=",">
            #{vehicleId}
        </foreach>
        AND work_order_type = #{workOrderType} AND status = #{status}
        GROUP BY vehicle_info_id
        );
    </select>
</mapper>
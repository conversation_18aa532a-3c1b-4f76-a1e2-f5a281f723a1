<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.ex.VehicleBusyMapperEx">
    <update id="updateOptime">
        UPDATE vehicle_busy vb
        SET op_time = vb.op_time + 1
        WHERE id IN
        <foreach collection="ids" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectList" resultType="com.ql.rent.vo.vehicle.VehicleBusyEntityVO">
        SELECT id,merchant_id merchantId, store_id storeId, vehicle_model_id vehicleModelId, vehicle_id vehicleId, channel_id channelId, start_time startTime, end_time endTime,
        end_interval_time endIntervalTime, auto_schedule autoSchedule, source_type sourceType, source_id sourceId, parent_source_id parentSourceId, third_source_id thirdSourceId,
        third_parent_source_id thirdParentSourceId, lock_flg lockFlg, deleted, create_time createTime, op_time opTime, busy_desc busyDesc
        FROM vehicle_busy vb
        WHERE merchant_id = #{merchantId}
        AND deleted = 0
        AND vb.vehicle_id in (
            SELECT vi.id
            FROM vehicle_info vi
            WHERE merchant_id = #{merchantId}
            AND deleted = 0
            AND license = #{license}
        )
        AND vb.end_interval_time >= #{localTime}
    </select>


    <!--<select id="selectFeeModelIds" resultType="java.lang.Long">-->
        <!--SELECT DISTINCT allvmid feevmid from (-->
            <!--select id, v.vehicle_model_id allvmid, IFNULL(b.vehicle_model_id, 0) busyvmid from (-->
                <!--select id, vehicle_model_id-->
                  <!--from vehicle_info-->
                 <!--where store_id = #{storeId}-->
                   <!--and deleted = 0-->
                   <!--and vehicle_status > 1-->
            <!--) v LEFT JOIN (-->
                <!--select DISTINCT vehicle_id, vehicle_model_id-->
                  <!--from vehicle_busy b-->
                 <!--where store_id =  #{storeId}-->
                   <!--and deleted = 0-->
                   <!--and end_interval_time <![CDATA[ >= ]]> #{startTime}-->
                   <!--and start_time <![CDATA[ <= ]]> #{endTime}-->
            <!--) b on v.id = b.vehicle_id-->
        <!--) u where u.busyvmid = 0-->
    <!--</select>-->

    <select id="selectFeeModelIds" resultType="com.ql.rent.vo.vehicle.VehicleBusyFreeVO">
        SELECT DISTINCT store_id storeId, allvmid vehicleModelId, selfServiceReturn, licenseType
          <if test="channelId != null and channelId == 3">
              ,count(id) stockNum
          </if>
          from (
            select id, v.store_id, v.vehicle_model_id allvmid, IFNULL(b.vehicle_model_id, 0) busyvmid, v.self_service_return selfServiceReturn, v.license_type AS licenseType from (
                SELECT i.id, i.store_id, i.vehicle_model_id, m.self_service_return, m.license_type from
                  (select vi.id, vi.store_id, vi.vehicle_model_id, vi.self_service_return
                     from vehicle_info vi
                     <if test="channelId != null and channelId == 6">
                      INNER JOIN vehicle_channel ch
                         on ch.merchant_id = #{merchantId}
                        and ch.channel_id = #{channelId}
                        and ch.vehicle_id = vi.id
                        and ch.audit_status = 1
                        and ch.deleted = 0
                     </if>
                    where vi.store_id = #{storeId}
                      and vi.deleted = 0
                      and vi.sale_status = 1
                      and vi.vehicle_status > 1
                  ) i,
                  (select id, self_service_return, license_type
                     from vehicle_model
                    where merchant_id = #{merchantId}
                      and status = 1
                  ) m
                where i.vehicle_model_id = m.id
                  and case
                      when m.self_service_return = 0 then
                        i.self_service_return > -1
                      else
                        i.self_service_return = 1
                  end
            ) v LEFT JOIN (
                select DISTINCT vehicle_id, vehicle_model_id
                  from vehicle_busy b
                 where store_id =  #{storeId}
                   and deleted = 0
                   and end_interval_time <![CDATA[ >= ]]> #{startTime}
                   and start_time <![CDATA[ <= ]]> #{endTime}
            ) b on v.id = b.vehicle_id
        ) u where u.busyvmid = 0
        <if test="channelId != null and channelId == 3">
            GROUP BY store_id, allvmid, selfServiceReturn,licenseType
        </if>
    </select>
</mapper>
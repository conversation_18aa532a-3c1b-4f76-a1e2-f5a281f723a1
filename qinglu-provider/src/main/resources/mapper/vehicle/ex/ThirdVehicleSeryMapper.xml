<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.ex.ThirdVehicleSeryMapper">

  <!-- 查询飞猪品牌列表（去重） -->
  <select id="selectFeizhuBrandNames" resultType="java.lang.String">
    SELECT DISTINCT brand_name
    FROM feizhu_vehicle_sub_sery
    WHERE brand_name IS NOT NULL
    AND brand_name != ''
    ORDER BY brand_name
  </select>

  <!-- 查询哈啰品牌列表（去重） -->
  <select id="selectHelloBrandNames" resultType="java.lang.String">
    SELECT DISTINCT brand_name
    FROM hello_sub_sery
    WHERE brand_name IS NOT NULL
    AND brand_name != ''
    ORDER BY brand_name
  </select>

  <!-- 查询铁行品牌列表（去重） -->
  <select id="selectTiexingBrandNames" resultType="java.lang.String">
    SELECT DISTINCT brand_name
    FROM tiexing_vehicle
    WHERE brand_name IS NOT NULL
    AND brand_name != ''
    ORDER BY brand_name
  </select>

  <!-- 查询悟空品牌列表（去重） -->
  <select id="selectWukongBrandNames" resultType="java.lang.String">
    SELECT DISTINCT brand_name
    FROM wk_car_type_info
    WHERE brand_name IS NOT NULL
    AND brand_name != ''
    ORDER BY brand_name
  </select>

  <!-- 查询滴滴品牌列表（去重） -->
  <select id="selectDidiBrandNames" resultType="java.lang.String">
    SELECT DISTINCT brand_name
    FROM didi_vehicle
    WHERE brand_name IS NOT NULL
    AND brand_name != ''
    AND status = 1
    ORDER BY brand_name
  </select>

</mapper>
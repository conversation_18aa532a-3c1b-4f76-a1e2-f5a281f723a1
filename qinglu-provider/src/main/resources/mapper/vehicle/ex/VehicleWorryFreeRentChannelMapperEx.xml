<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.ex.VehicleWorryFreeRentChannelMapperEx">
    <resultMap id="vehicleWorryFreeRentVoMap" type="com.ql.rent.vo.price.VehicleWorryFreeRentChannelVo">
        <id column="id" property="id"/>
        <!-- 车型相关mapping -->
        <result column="m_vehicle_model_id" property="rentVehicleVo.vehicleModelId"/>
        <!-- channel相关mapping -->
        <result column="merchant_id" property="merchantId"/>
        <result column="m_store_id" property="storeId"/>
        <result column="m_vehicle_model_id" property="vehicleModelId"/>
        <result column="channel_id" property="channelId"/>
        <result column="channel_name" property="channelName"/>
        <result column="worry_free_name" property="worryFreeName"/>
        <result column="percentage_increase" property="percentageIncrease"/>
        <result column="status" property="status"/>
        <!-- rent main -->
        <result column="m_id" property="rentBaseId"/>
    </resultMap>

    <!-- 根据 查询条条件 查询列表 -->
    <select id="selectVehicleWorryFreeRentByQuery" resultMap="vehicleWorryFreeRentVoMap">
        SELECT
        <include refid="column"/>
        <include refid="byQueryClause"/>
        ORDER BY ${query.orderBy}
        LIMIT #{query.startPos},#{query.pageSize}
    </select>
    <!-- 根据 查询条条件 查询数量 -->
    <select id="selectVehicleWorryFreeRentCountByQuery" resultType="java.lang.Long">
        SELECT COUNT(*)
        <include refid="byQueryClause"/>
    </select>

    <!-- 查询的列 -->
    <sql id="column">
        <!-- channel相关 -->
        vwfrc.id, vwfrc.merchant_id, vwfrc.store_id, vwfrc.vehicle_model_id,
        vwfrc.percentage_increase, vwfrc.`status`,
        <!-- rentMain相关 -->
        m.store_id m_store_id, m.vehicle_model_id m_vehicle_model_id, m.id m_id,
        <!-- channel相关虚拟表 -->
        sss.channel_id, sss.channel_name, sss.worry_free_name
    </sql>

    <sql id="byQueryClause">
        FROM rent_main m
        <!-- 虚拟表 -->
        INNER JOIN (
        <foreach collection="query.channels" open="" item="item" separator=" union " close="">
            select #{item.id} channel_id, #{item.channelName} channel_name, #{item.worryFreeName} worry_free_name FROM DUAL
        </foreach>
        ) sss
        ON m.store_id = #{query.storeId} AND m.deleted = 0
        <!-- 渠道id -->
        <if test="query.channelId != null">
            AND sss.channel_id = #{query.channelId}
        </if>
        LEFT JOIN vehicle_worry_free_rent_channel vwfrc
        ON sss.channel_id = vwfrc.channel_id AND m.vehicle_model_id = vwfrc.vehicle_model_id
        AND m.id = vwfrc.rent_base_id
        AND vwfrc.deleted &lt; 1
        <!-- 商家id -->
        <if test="query.merchantId != null">
            AND vwfrc.merchant_id = #{query.merchantId}
        </if>
        <!-- 只查没有被删除的数据 -->
        WHERE m.STATUS = 1
        <!-- 车型id -->
        <if test="query.vehicleModelId != null">
            AND m.vehicle_model_id = #{query.vehicleModelId}
        </if>
        <if test="query.vehicleModelIds != null and query.vehicleModelIds.size() > 0">
            AND m.vehicle_model_id IN
            <foreach collection="query.vehicleModelIds" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>
    <!-- sql原型 -->
    <!--
    SELECT *
    FROM rent_main m
    INNER JOIN ( SELECT 2 channel_id, '携程' channelName FROM DUAL UNION SELECT 10 channel_id, '携程分销' channelName FROM DUAL ) sss
    ON m.store_id = 6 AND m.deleted = 0
    LEFT JOIN vehicle_worry_free_rent_channel vwfrc
    ON sss.channel_id = vwfrc.channel_id AND m.vehicle_model_id = vwfrc.vehicle_model_id AND vwfrc.merchant_id = 3
    WHERE m.STATUS = 1
    AND m.vehicle_model_id = 1618
    ORDER BY
        m.vehicle_model_id;
    -->
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.ex.VehicleModelMapperEx">

    <resultMap id="modelResultMap" extends="com.ql.rent.dao.vehicle.VehicleModelMapper.BaseResultMap"
               type="com.ql.rent.entity.vehicle.ex.VehicleModelEx"/>

    <!-- 根据 车型ID 查询车辆数量 -->
    <select id="selectVehicleCount" resultType="com.ql.rent.vo.vehicle.VehicleCountVO">
        SELECT vi.vehicle_model_id vehicleModelId, count(vi.vehicle_model_id) vehicleCount
        FROM vehicle_info vi
        <if test="vehicleSource != null and shareholder != null and shareholder != ''">
            LEFT JOIN vehicle_purchase vp ON vp.vehicle_id = vi.id AND vp.deleted = 0
        </if>
        <where>
            <if test="storeId != null">
                and vi.store_id = #{storeId}
            </if>
            <if test="vehicleSource != null">
                and vi.vehicle_source = #{vehicleSource}
                <if test="shareholder != null and shareholder != ''">
                    AND vp.shareholder = #{shareholder}
                </if>
            </if>
            and vi.vehicle_model_id IN
            <foreach collection="vehicleModelIdList" open="(" item="item" separator="," close=")">#{item}</foreach>
            and vi.deleted = 0
        </where>
        GROUP BY vi.vehicle_model_id
    </select>

    <!-- 查询车辆数量 门店分组 -->
    <select id="selectVehicleCountGroupStoreId" resultType="com.ql.rent.vo.vehicle.VehicleCountVO">
        SELECT store_id storeId, count(id) vehicleCount
        FROM vehicle_info
        <where>
            and merchant_id = #{merchantId}
            and deleted = 0
        </where>
        GROUP BY store_id
    </select>

    <select id="selectCtripVehicle" resultType="com.ql.rent.vo.vehicle.VehicleCtripVO">
        SELECT DISTINCT i.store_id storeId, t.license_pinyin ctripPlateType, m.id vehicleModelId,
                        s.ctrip_sub_sery_id ctripSubSeryId
        FROM  vehicle_model m
        INNER JOIN vehicle_info i ON i.vehicle_model_id = m.id AND i.merchant_id = #{merchantId} AND i.deleted = 0
        INNER JOIN vehicle_sub_sery s ON s.id = m.vehicle_sub_sery_id
        INNER JOIN license_type t ON t.id = m.license_type_id
        WHERE m.`merchant_id` = #{merchantId}
          AND m.`status` IN <foreach collection="vehicleModelStatus" open="(" item="item" separator="," close=")"> #{item}</foreach>
    </select>

    <select id="selectCtripVehicleV2" resultType="com.ql.rent.vo.vehicle.VehicleCtripVO">
        SELECT DISTINCT #{storeId} storeId, t.license_pinyin ctripPlateType, m.id vehicleModelId, s.ctrip_sub_sery_id ctripSubSeryId, s.ctrip_vehicle_model_id standardProductId -- ，id.third_id  third_store_id
        from vehicle_model m
                 <if test="vehicleModelIds == null or vehicleModelIds.size() == 0">
                  INNER JOIN rent_main r
                     on r.store_id = #{storeId}
                    and r.vehicle_model_id = m.id
                 </if>
                 LEFT JOIN vehicle_sub_sery s on s.id = m.vehicle_sub_sery_id
                 LEFT JOIN license_type t on t.id = m.license_type_id
        where m.merchant_id = #{merchantId} AND m.status = 1
        <if test="vehicleModelIds != null and vehicleModelIds.size() > 0">
            AND m.id IN <foreach collection="vehicleModelIds" open="(" item="item" separator="," close=")">#{item}</foreach>
        </if>
    </select>

    <select id="pageList" resultType="com.ql.rent.vo.vehicle.VehicleModelListVO">
        SELECT vm.id, vm.vehicle_sery_id AS vehicleSubSeryId,  vm.vehicle_sub_sery_name AS vehicleSubSeryName,
               vm.vehicle_sery_name AS vehicleSeryName, vm.vehicle_sery_id AS vehicleSeryId, vm.fast_charge_time AS fastChargeTime,
               vm.self_service_return AS selfServiceReturn, vm.has_snow_tires AS hasSnowTires, vm.drive_type AS driveType,
               vss.brand_id AS vehicleBrandId, vm.has_sunroof AS hasSunroof,
               vm.displacement AS displacement, vm.vehicle_year_style AS vehicleYearStyle, vm.gearbox AS gearbox,
               vm.license_type AS licenseType, vm.doors, vm.fuel_form AS fuelForm, vm.seat_num AS seatNum, vm.carriage, vm.`status`,
               vm.merchant_id AS merchantId, vmg.name AS vehicleModelGroupName
        <include refid="byClause"/>
        ORDER BY vm.op_time DESC LIMIT #{query.startPos}, #{query.pageSize}
    </select>

    <select id="pageCount" resultType="long">
        SELECT count(1)
        FROM  ( SELECT 1 <include refid="byClause"/>) s
    </select>

    <select id="listAllCtripSubSery" resultMap="com.ql.rent.dao.vehicle.VehicleSubSeryMapper.BaseResultMap">
        SELECT DISTINCT `model_group_name`, `model_group_id`
        FROM `vehicle_sub_sery`
        WHERE `preset` = 1 AND deleted = 0
        ORDER BY `model_group_id` ;
    </select>

    <select id="selectVehicleModelIdStoreIds" resultType="long">
        SELECT distinct vi.vehicle_model_id
        from vehicle_info vi
        WHERE vi.merchant_id = #{merchantId} AND vi.deleted = 0
          <if test="storeIds != null and storeIds.size() > 0">
            AND vi.store_id IN
            <foreach collection="storeIds" open="(" item="item" separator="," close=")">#{item}</foreach>
          </if>
          <if test="storeId != null">
            AND vi.store_id = #{storeId}
        </if>
    </select>

    <select id="selectVehicleAttach" resultType="com.ql.rent.vo.vehicle.VehicleAttachDTO">
        SELECT
            (select `media_path` from `vehicle_media` where `vehicle_model_id` = vm.`id` and `media_type` = 1 LIMIT 1 ) AS mediaPath,
            ( select `ctrip_url` from `vehicle_sery_file` where `sub_sery_id` = vm.`vehicle_sub_sery_id` and `file_type` = 0 LIMIT  1 ) AS ctripUrl,
            vm.`id` as vehicleModelId,
            vs.`id` as seryId,
            vs.`sery_name` as seryName,
            vm.`vehicle_sub_sery_name` as subSeryName,
            vm.`vehicle_year_style` as vehicleYearStyle,
            vb.`brand_name` as brandName
        FROM
            `vehicle_model` vm
            INNER JOIN `vehicle_sery` vs
        ON vm.`vehicle_sery_id` = vs.`id`
            INNER JOIN `vehicle_brand` vb ON vs.`brand_id` = vb.`id`
        WHERE
            vm.`merchant_id` = #{merchantId}
            and vm.`status` = 1
    </select>

    <sql id="byClause">
        FROM vehicle_model vm
        INNER JOIN vehicle_sub_sery vss ON vm.vehicle_sub_sery_id = vss.id
        LEFT JOIN vehicle_bind vb ON vb.vehicle_model_id = vm.id AND vb.deleted = 0
        LEFT JOIN vehicle_model_group vmg ON vm.vehicle_model_group_id = vmg.id AND vmg.merchant_id = vm.merchant_id
        <if test="vehicleSource != null">
            LEFT JOIN vehicle_info vi ON vi.vehicle_model_id = vm.id AND vi.deleted = 0 AND vi.merchant_id = #{query.merchantId}
            <if test="shareholder != null and shareholder != ''">
                LEFT JOIN vehicle_purchase vp ON vp.vehicle_id = vi.id AND vp.deleted = 0
            </if>
        </if>
        WHERE vm.merchant_id = #{query.merchantId}
        AND vm.`status` IN <foreach collection="modelValidStatus" open="(" item="item" separator="," close=")"> #{item}</foreach>
        <if test="query.idList != null and query.idList.size() > 0">
            AND vm.id IN
            <foreach collection="query.idList" open="(" item="item" separator="," close=")"> #{item}</foreach>
        </if>
        <if test="query.vehicleModelGroup != null">
            AND vm.vehicle_model_group_id = #{query.vehicleModelGroup}
        </if>
        <if test="query.vehicleBrandIdList != null and query.vehicleBrandIdList.size() > 0">
            AND vss.brand_id IN <foreach collection="query.vehicleBrandIdList" open="(" item="item" separator="," close=")"> #{item}</foreach>
        </if>
        <if test="query.licenseType != null and query.licenseType != ''"> AND vm.license_type_id = #{query.licenseType}</if>
        <if test="query.channelId != null"> AND vb.channel_id = #{query.channelId}</if>
        <if test="query.bindChannelVehicleId != null and query.bindChannelVehicleId != ''">
            AND vb.bind_channel_vehicle_id = #{query.bindChannelVehicleId}
        </if>
        <if test="vehicleSource != null">
            AND vi.vehicle_source = #{vehicleSource}
            <if test="shareholder != null and shareholder != ''">
                AND vp.shareholder = #{shareholder}
            </if>
        </if>
        <if test="ctripSynced != null and ctripSynced == 1">
            AND vb.channel_id = #{ctripChannelId} AND vb.synced = 1
        </if>
         <if test="ctripSynced != null and ctripSynced != 1">
            AND vb.channel_id = #{ctripChannelId} AND vb.synced != 1
        </if>
        GROUP BY vm.id
    </sql>
</mapper>
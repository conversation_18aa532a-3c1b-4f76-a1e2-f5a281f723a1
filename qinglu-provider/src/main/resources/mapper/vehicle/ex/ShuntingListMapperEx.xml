<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.ex.ShuntingListMapperEx">
    <resultMap id="shuntingListResultMap" type="com.ql.rent.entity.vehicle.ShuntingList">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="vehicle_info_id" jdbcType="BIGINT" property="vehicleInfoId"/>
        <result column="transfer_out_store_id" jdbcType="BIGINT" property="transferOutStoreId"/>
        <result column="transfer_in_store_id" jdbcType="BIGINT" property="transferInStoreId"/>
        <result column="transfer_out_time" jdbcType="BIGINT" property="transferOutTime"/>
        <result column="transfer_in_time" jdbcType="BIGINT" property="transferInTime"/>
        <result column="shunter" jdbcType="VARCHAR" property="shunter"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="shunting_status" jdbcType="TINYINT" property="shuntingStatus"/>
        <result column="completed_immediately" jdbcType="TINYINT" property="completedImmediately"/>
        <result column="occupied_vehicle" jdbcType="TINYINT" property="occupiedVehicle"/>
        <result column="cancel_reason" jdbcType="VARCHAR" property="cancelReason"/>
        <result column="merchant_id" jdbcType="BIGINT" property="merchantId"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="last_ver" jdbcType="INTEGER" property="lastVer"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="op_user_id" jdbcType="BIGINT" property="opUserId"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="op_time" jdbcType="BIGINT" property="opTime"/>
    </resultMap>

    <sql id="shuntingListWhereClause">
        WHERE merchant_id = #{param.merchantId}
        AND deleted = 0
        <if test="param.shuntingStatus != null">
            AND sl.shunting_status = #{param.shuntingStatus}
        </if>
        <if test="param.shunter != null">
            AND sl.shunter LIKE CONCAT('%', #{param.shunter}, '%')
        </if>
        <if test="param.vehicleInfoIdList != null">
            AND sl.vehicle_info_id IN
            <foreach item="id" index="index" collection="param.vehicleInfoIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="param.storeIdList != null">
        AND (
        sl.transfer_out_store_id IN
        <foreach item="id" index="index" collection="param.storeIdList" open="(" separator="," close=")">
            #{id}
        </foreach>
        OR sl.transfer_in_store_id IN
        <foreach item="id" index="index" collection="param.storeIdList" open="(" separator="," close=")">
            #{id}
        </foreach>
        )
        </if>
    </sql>

    <select id="selectList" parameterType="com.ql.rent.dao.vehicle.param.ShuntingListSqlParam" resultMap="shuntingListResultMap">
        SELECT *
        FROM shunting_list sl
        <include refid="shuntingListWhereClause"/>
        <if test="param.orderByClause != null">
            order by ${param.orderByClause}
        </if>
    </select>

    <select id="countByParam" parameterType="com.ql.rent.dao.vehicle.param.ShuntingListSqlParam" resultType="java.lang.Long">
        SELECT count(*)
        FROM shunting_list sl
        <include refid="shuntingListWhereClause"/>
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.ex.VehicleBusySecretMapperEx">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.VehicleBusySecret">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="env" jdbcType="VARCHAR" property="env" />
    <result column="source_form" jdbcType="VARCHAR" property="sourceForm" />
    <result column="secret" jdbcType="VARCHAR" property="secret" />
    <result column="is_open" jdbcType="TINYINT" property="isOpen" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>

  <select id="selectList" resultMap="BaseResultMap">
    SELECT id, env, source_form, secret, is_open, last_ver, op_user_id, create_time, op_time
    FROM vehicle_busy_secret
    WHERE source_form = #{sourceForm}
      AND env = #{env}
  </select>

</mapper>
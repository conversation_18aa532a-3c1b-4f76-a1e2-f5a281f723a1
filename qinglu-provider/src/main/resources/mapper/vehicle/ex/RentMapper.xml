<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.ex.RentMainMapperEx">

    <resultMap id="modelMainResultMap" type="com.ql.rent.entity.vehicle.ex.RentMainEx">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId"/>
        <result column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="mileage_limit" jdbcType="TINYINT" property="mileageLimit"/>
        <result column="mileage" jdbcType="SMALLINT" property="mileage"/>
        <result column="mileage_rent" jdbcType="SMALLINT" property="mileageRent"/>
        <result column="rent_deposit" jdbcType="INTEGER" property="rentDeposit"/>
        <result column="illegal_deposit" jdbcType="INTEGER" property="illegalDeposit"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="last_ver" jdbcType="INTEGER" property="lastVer"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="op_time" jdbcType="BIGINT" property="opTime"/>
        <result column="op_user_id" jdbcType="BIGINT" property="opUserId"/>
    </resultMap>

    <sql id="fileMainList">
        m.*
    </sql>
    <!-- 根据 查询条件统计数量-->
    <select id="countMainByQuery" resultType="java.lang.Long">
        SELECT COUNT(1)
        <include refid="byMainClause"/>
    </select>

    <!-- 根据 查询条条件 查询列表-->
    <select id="selectMainByQuery" resultMap="modelMainResultMap">
        SELECT
        <include refid="fileMainList"/>
        <include refid="byMainClause"/>
        ORDER BY ${query.orderBy}
        LIMIT #{query.startPos},#{query.pageSize}
    </select>

    <sql id="byMainClause">
        FROM rent_main m
        INNER JOIN vehicle_model v ON m.vehicle_model_id = v.id AND v.status = 1 <!--v.deleted = 0 -->
        <if test="query.storeIdList != null">
            AND m.store_id in
            <foreach collection="query.storeIdList" open="(" item="item" separator="," close=")">#{item}</foreach>
        </if>
        <if test="query.vehicleModelIdList != null">
            AND m.vehicle_model_id in
            <foreach collection="query.vehicleModelIdList" open="(" item="item" separator="," close=")">#{item}
            </foreach>
        </if>
        <if test="query.status != null">
            AND m.status = ${query.status}
        </if>
        WHERE m.deleted = 0
    </sql>


    <!-- 按手续费查询车型 -->
    <resultMap id="addedResultMap" type="com.ql.rent.vo.price.AddedServiceVo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="added_service_setting_id" jdbcType="BIGINT" property="addedServiceSettingId"/>
        <result column="rent_base_id" jdbcType="BIGINT" property="rentBaseId"/>
        <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId"/>
        <result column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="on_charge" jdbcType="TINYINT" property="onCharge"/>
        <result column="price" jdbcType="INTEGER" property="price"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="last_ver" jdbcType="INTEGER" property="lastVer"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="op_time" jdbcType="BIGINT" property="opTime"/>
        <result column="op_user_id" jdbcType="BIGINT" property="opUserId"/>
        <result column="added_service_setting_name" jdbcType="VARCHAR" property="addedServiceSettingName"/>
        
        <!--<result column="addedServiceChannelId" jdbcType="BIGINT" property="addedServiceChannelId"/>
        <result column="merchantChannelId" jdbcType="BIGINT" property="merchantChannelId"/>
        <result column="channelId" property="channel"/>
        <result column="addedServiceId" property="addedServiceId"/>
        <result column="addedServiceChannelPrice" property="addedServiceChannelPrice"/>
        <result column="addedServiceChannelStatus" property="addedServiceChannelStatus"/>-->
        <!-- channel相关映射，开始 -->
        <result column="addedServiceChannelId" property="addedServiceChannelVo.id"/>
        <result column="channelName" property="addedServiceChannelVo.channelName"/>
        <result column="merchantChannelId" property="addedServiceChannelVo.channel"/>
        <result column="addedServiceChannelStatus" property="addedServiceChannelVo.status"/>
        <result column="addedServiceChannelPrice" property="addedServiceChannelVo.price"/>
        <result column="id" property="addedServiceChannelVo.addedServiceId"/>
        <result column="added_service_setting_id" property="addedServiceChannelVo.addedServiceSettingId"/>
        <result column="rent_base_id" property="addedServiceChannelVo.rentBaseId"/>
        <result column="store_id" property="addedServiceChannelVo.storeId"/>
        <result column="vehicle_model_id" property="addedServiceChannelVo.vehicleModelId"/>
        <!-- channel相关映射，结束 -->
    </resultMap>

    <sql id="fileAddedList">
        ad.id, ss.id added_service_setting_id, m.id rent_base_id, m.vehicle_model_id, m.store_id, ad.on_charge, ad.price, IFNULL(ad.`status`,0) `status`, ss.name added_service_setting_name
        <!-- channelId -->
        ,sss.id merchantChannelId, sss.channelName channelName, adsc.id addedServiceChannelId, adsc.channel channelId, adsc.added_service_id addedServiceId, adsc.price addedServiceChannelPrice, adsc.status addedServiceChannelStatus
    </sql>
    <!-- 根据 查询条件统计数量-->
    <select id="countAddedByQuery" resultType="java.lang.Long">
        SELECT COUNT(1)
        <include refid="byAddedClause"/>
    </select>

    <!-- 根据 查询条条件 查询列表-->
    <select id="selectAddedByQuery" resultMap="addedResultMap">
        SELECT
        <include refid="fileAddedList"/>
        <include refid="byAddedClause"/>
        ORDER BY ${query.orderBy}
        LIMIT #{query.startPos},#{query.pageSize}
    </select>

    <sql id="byAddedClause">
        FROM added_service_setting ss
        INNER JOIN rent_main m
           ON m.store_id = ${query.storeId}
          and m.deleted = 0
          <if test="query.vehicleModelId != null">
            AND m.vehicle_model_id = ${query.vehicleModelId}
          </if>
          <if test="query.vehicleModelIds != null and query.vehicleModelIds.size() > 0">
            AND m.vehicle_model_id IN
            <foreach collection="query.vehicleModelIds" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
          </if>
        LEFT JOIN added_service ad
           ON ad.store_id = ${query.storeId}
          AND ad.vehicle_model_id = m.vehicle_model_id
          AND ad.rent_base_id = m.id
          AND ad.added_service_setting_id = ss.id
          AND ad.status <![CDATA[ <= ]]> 1

        <!-- Channel相关，开始 -->
        INNER JOIN (
        <foreach collection="query.channelList" open="" item="item" separator=" union " close="">
            select #{item.id} id, #{item.channelName} channelName FROM DUAL
        </foreach>
        ) sss
        LEFT JOIN added_service_channel adsc ON ad.id = adsc.added_service_id AND sss.id = adsc.channel
        AND adsc.status <![CDATA[ <= ]]> 1

        <!-- Channel相关，结束 -->

        WHERE (ss.merchant_id = ${query.merchantId})
          and ss.status = 1
        --         过滤儿童座椅不查询
        AND ss.parent_id !=2
          <if test="query.addedServiceSettingId != null">
            AND ss.id = ${query.addedServiceSettingId}
          </if>
    </sql>


    <!-- 按保险服务查询车型 -->
    <resultMap id="insuranceResultMap" type="com.ql.rent.vo.price.InsuranceServicePriceVo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="insurance_service_setting_id" jdbcType="BIGINT" property="insuranceServiceSettingId"/>
        <result column="rent_base_id" jdbcType="BIGINT" property="rentBaseId"/>
        <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId"/>
        <result column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="price" jdbcType="INTEGER" property="price"/>
        <result column="on_highest_price" jdbcType="TINYINT" property="onHighestPrice"/>
        <result column="highest_price" jdbcType="INTEGER" property="highestPrice"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="op_time" jdbcType="BIGINT" property="opTime"/>
        <result column="op_user_id" jdbcType="BIGINT" property="opUserId"/>
        <result column="last_ver" jdbcType="INTEGER" property="lastVer"/>
        <result column="insurance_service_setting_name" jdbcType="VARCHAR" property="insuranceServiceSettingName"/>

        <!-- 设置channel信息，开始 -->
        <result column="vehicle_model_id" property="insuranceServicePriceChannelVo.vehicleModelId"/>
        <result column="store_id" property="insuranceServicePriceChannelVo.storeId"/>
        <result column="rent_base_id" property="insuranceServicePriceChannelVo.rentBaseId"/>
        <result column="ispc_price" property="insuranceServicePriceChannelVo.price"/>
        <result column="ispc_status" property="insuranceServicePriceChannelVo.status"/>
        <result column="channelName" property="insuranceServicePriceChannelVo.channelName"/>
        <result column="merchant_channel_id" property="insuranceServicePriceChannelVo.channel"/>
        <result column="ispc_id" property="insuranceServicePriceChannelVo.id"/>
        <result column="id" property="insuranceServicePriceChannelVo.insuranceServicePriceId"/>
        <result column="insurance_service_setting_id" property="insuranceServicePriceChannelVo.insuranceServiceSettingId"/>
        <!-- 设置channel信息，结束 -->
    </resultMap>

    <!-- 根据 查询条件统计数量-->
    <sql id="fileInsuranceList">
        ad.id, ss.id insurance_service_setting_id, m.id rent_base_id, m.vehicle_model_id, m.store_id, ad.on_highest_price, ad.price, IFNULL(ad.`status`,0) `status`, ss.name insurance_service_setting_name
        <!-- 设置channel信息，开始 -->
        ,sss.id merchant_channel_id, sss.channelName channelName, ispc.id ispc_id, ad.id ipsc_insuranceServicePriceId, ispc.channel ispc_channel, ispc.price ispc_price, ispc.status ispc_status
        <!-- 设置channel信息，结束 -->
    </sql>
    <select id="countInsuranceByQuery" resultType="java.lang.Long">
        SELECT COUNT(1)
        <include refid="byInsuranceClause"/>
    </select>

    <!-- 根据 查询条条件 查询列表-->
    <select id="selectInsuranceByQuery" resultMap="insuranceResultMap">
        SELECT
        <include refid="fileInsuranceList"/>
        <include refid="byInsuranceClause"/>
        ORDER BY ${query.orderBy}
        LIMIT #{query.startPos},#{query.pageSize}
    </select>

    <sql id="byInsuranceClause">
        FROM insurance_service_setting ss
        INNER JOIN rent_main m
           ON m.store_id = ${query.storeId}
          and m.deleted = 0
          <if test="query.vehicleModelId != null">
            AND m.vehicle_model_id = ${query.vehicleModelId}
          </if>
          <if test="query.vehicleModelIds != null and query.vehicleModelIds.size() > 0">
            AND m.vehicle_model_id IN
            <foreach collection="query.vehicleModelIds" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
          </if>
        LEFT JOIN insurance_service_price ad
        ON ad.store_id = ${query.storeId}
        AND ad.vehicle_model_id = m.vehicle_model_id
        AND ad.rent_base_id = m.id
        AND ad.insurance_service_setting_id = ss.id
        AND ad.status <![CDATA[ <= ]]> 1
        <!-- Channel相关，开始 -->
        INNER JOIN(
        <foreach collection="query.channelList" open="" item="item" separator=" union" close="">
            select #{item.id} id, #{item.channelName} channelName FROM DUAL 
        </foreach>
        ) sss
        LEFT JOIN insurance_service_price_channel ispc ON ad.id = ispc.insurance_service_price_id AND sss.id = ispc.channel
        AND ispc.status <![CDATA[ <= ]]> 1
        <!-- Channel相关，结束 -->
        WHERE ss.merchant_id = ${query.merchantId}
          and ss.status = 1
          <if test="query.insuranceServiceSettingId != null">
            AND ss.id = ${query.insuranceServiceSettingId}
          </if>
    </sql>


    <!-- 按渠道查询车型 -->
    <resultMap id="undepositResultMap" type="com.ql.rent.vo.price.BatchUndepostitVo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="rent_base_id" jdbcType="BIGINT" property="rentBaseId"/>
        <result column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="op_user_id" jdbcType="BIGINT" property="opUserId"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="op_time" jdbcType="BIGINT" property="opTime"/>
    </resultMap>

    <sql id="fileUndepositList">
        ad.id, ss.id channel_id, m.id rent_base_id, m.vehicle_model_id, m.store_id, IFNULL(ad.`status`,0) `status`
    </sql>
    <!-- 根据 查询条件统计数量-->
    <select id="countUndepositByQuery" resultType="java.lang.Long">
        SELECT COUNT(1)
        <include refid="byUndepositClause"/>
    </select>

    <!-- 根据 查询条条件 查询列表-->
    <select id="selectUndepositByQuery" resultMap="undepositResultMap">
        SELECT
        <include refid="fileUndepositList"/>
        <include refid="byUndepositClause"/>
        ORDER BY ${query.orderBy}
        LIMIT #{query.startPos},#{query.pageSize}
    </select>

    <select id="selectUndeposit" resultType="com.ql.rent.entity.vehicle.ex.RentMainEx">
        SELECT rm.id, rm.store_id storeId, rm.vehicle_model_id vehicleModelId, ru.channel_id channelId,
               rm.mileage_limit mileageLimit, rm.mileage, rm.mileage_rent mileageRent,
               rm.rent_deposit rentDeposit, rm.illegal_deposit illegalDeposit, rm.status
               , IF(ISNULL(ru.channel_id),0,1) supportCredit
        FROM rent_main rm
        left JOIN rent_undeposit ru ON rm.id = ru.rent_base_id and ru.status = 1
        <if test="channelId != null">
            AND ru.channel_id = #{channelId}
        </if>
        WHERE  rm.deleted = 0
            <if test="storeIds != null and storeIds.size() > 0">
                AND rm.store_id in <foreach collection="storeIds" open="(" item="item" separator="," close=")">#{item}</foreach>
            </if>
            <if test="vehicleModelIds != null and vehicleModelIds.size() > 0">
                AND rm.vehicle_model_id in <foreach collection="vehicleModelIds" open="(" item="item" separator="," close=")">#{item}</foreach>
            </if>

    </select>

    <sql id="byUndepositClause">
        FROM (
        <foreach collection="query.channelList" open="" item="item" separator=" union " close="">
            select #{item} id FROM DUAL
        </foreach>
        ) ss
        INNER JOIN rent_main m
           ON m.store_id = ${query.storeId}
          and m.deleted = 0
        <if test="query.vehicleModelId != null">
            and m.vehicle_model_id = #{query.vehicleModelId}
        </if>
        <if test="query.vehicleModelIds != null and query.vehicleModelIds.size() > 0">
            AND m.vehicle_model_id IN
            <foreach collection="query.vehicleModelIds" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        LEFT JOIN rent_undeposit ad
        ON ad.store_id = ${query.storeId}
        AND ad.vehicle_model_id = m.vehicle_model_id
        AND ad.rent_base_id = m.id
        AND ad.channel_id = ss.id
        AND ad.`status` <![CDATA[ < ]]> 2
        <if test="query.channelId != null">
            AND ad.channel_id = #{query.channelId}
        </if>
        <if test="query.channelId != null">
            WHERE ss.id = #{query.channelId}
        </if>
    </sql>

    <!-- 查询保险&附加服务返回对象 -->
    <resultMap id="insuranceAddMap" type="com.ql.rent.vo.price.InsuranceAddApiVo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="setting_id" jdbcType="BIGINT" property="settingId" />
        <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId"/>
        <result column="price" jdbcType="INTEGER" property="price" />
        <result column="on_highest_price" jdbcType="TINYINT" property="onHighestPrice" />
        <result column="on_charge" jdbcType="TINYINT" property="onCharge" />
        <result column="parent_id" jdbcType="BIGINT" property="parentId" />
        <result column="parent_name" jdbcType="VARCHAR" property="parentName" />
        <result column="required" jdbcType="TINYINT" property="required" />
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
    </resultMap>

    <select id="selectInsuranceForApi" resultMap="insuranceAddMap">
        SELECT i.id, i.store_id, i.vehicle_model_id, i.insurance_service_setting_id setting_id, i.on_highest_price, i.price, b.required, b.parent_id, b.parent_name, i.channelPrice, i.channel_id
        from (
            SELECT s.id, s.store_id, s.vehicle_model_id, s.insurance_service_setting_id, s.on_highest_price, s.price, c.channelPrice, c.channel_id
              from insurance_service_price s
              left join (
                SELECT insurance_service_price_id, price channelPrice, channel channel_id
                  from insurance_service_price_channel
                 where merchant_id = ${query.merchantId}
                    <if test="query.channelId != null">
                        and channel = ${query.channelId}
                    </if>
                    <if test="query.channelIds != null">
                       AND channel in
                       <foreach collection="query.channelIds" open="(" item="item" separator="," close=")">#{item}
                       </foreach>
                    </if>
                    <if test="query.storeId != null">
                        and store_id = ${query.storeId}
                    </if>
                    <if test="query.vehicleModelId != null">
                        AND vehicle_model_id = ${query.vehicleModelId}
                    </if>
                    <if test="query.vehicleModelIds != null">
                        AND vehicle_model_id in
                        <foreach collection="query.vehicleModelIds" open="(" item="item" separator="," close=")">#{item}
                        </foreach>
                    </if>
                   and `status` = 1
              ) c
              on s.id = c.insurance_service_price_id
            <where>
                <if test="query.storeId != null">
                    AND store_id = ${query.storeId}
                </if>
                <if test="query.storeIds != null">
                    AND store_id in
                    <foreach collection="query.storeIds" open="(" item="item" separator="," close=")">#{item}
                    </foreach>
                </if>
                <if test="query.vehicleModelId != null">
                    AND vehicle_model_id = ${query.vehicleModelId}
                </if>
                <if test="query.vehicleModelIds != null">
                    AND vehicle_model_id in
                    <foreach collection="query.vehicleModelIds" open="(" item="item" separator="," close=")">#{item}
                    </foreach>
                </if>
                and `status` = 1
            </where>
         ) i,
        (
          SELECT id, required, parent_id, `name` parent_name
            from insurance_service_setting
           where merchant_id = ${query.merchantId}
             and `status` = 1
        ) b
        where i.insurance_service_setting_id = b.id
        ORDER BY i.vehicle_model_id, b.parent_id
    </select>

    <select id="selectAddServiceForApi" resultMap="insuranceAddMap">
        SELECT i.id, i.store_id, i.vehicle_model_id, i.added_service_setting_id setting_id, i.on_charge, i.price, b.required, b.parent_id, b.parent_name, i.channelPrice, i.channel_id
        from (
        SELECT s.id, s.store_id, s.vehicle_model_id, s.added_service_setting_id, s.on_charge, s.price, c.channelPrice, c.channel_id
          from added_service s
          left join (
            SELECT added_service_id, price channelPrice, channel channel_id
              from added_service_channel
              where merchant_id = ${query.merchantId}
                <if test="query.channelId != null">
                    and channel = ${query.channelId}
                </if>
                <if test="query.channelIds != null">
                    AND channel in
                    <foreach collection="query.channelIds" open="(" item="item" separator="," close=")">#{item}
                    </foreach>
                </if>
                <if test="query.storeId != null">
                    and store_id = ${query.storeId}
                </if>
                <if test="query.vehicleModelId != null">
                    AND vehicle_model_id = ${query.vehicleModelId}
                </if>
                <if test="query.vehicleModelIds != null">
                    AND vehicle_model_id in
                    <foreach collection="query.vehicleModelIds" open="(" item="item" separator="," close=")">#{item}
                    </foreach>
                </if>
                and `status` = 1
          ) c
          on s.id = c.added_service_id
        <where>
            <if test="query.storeId != null">
                AND store_id = ${query.storeId}
            </if>
            <if test="query.storeIds != null">
                AND store_id in
                <foreach collection="query.storeIds" open="(" item="item" separator="," close=")">#{item}
                </foreach>
            </if>
            <if test="query.vehicleModelId != null">
                AND vehicle_model_id = ${query.vehicleModelId}
            </if>
            <if test="query.vehicleModelIds != null">
                AND vehicle_model_id in
                <foreach collection="query.vehicleModelIds" open="(" item="item" separator="," close=")">#{item}
                </foreach>
            </if>
            and `status` = 1
         </where>
         ) i,
        (
            SELECT id, required, parent_id, `name` parent_name
              from added_service_setting
             where merchant_id = ${query.merchantId}
               and `status` = 1
--                and parent_id !=2
        ) b
        where i.added_service_setting_id = b.id
        ORDER BY i.vehicle_model_id, b.parent_id
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.VehicleChannelExtMapper">
    <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.VehicleChannelExt">
         <id column="id" jdbcType="BIGINT" property="id" />
        <result column="vehicle_id" property="vehicleId"   jdbcType="BIGINT"/>
        <result property="channelId" column="channel_id" jdbcType="BIGINT" />
        <result property="lastVer" column="last_ver" jdbcType="INTEGER" />
        <result property="content" column="content" jdbcType="VARCHAR" />
        <result property="deleted" column="deleted" jdbcType="TINYINT" />
        <result property="createTime" column="create_time" jdbcType="BIGINT" />
        <result property="opTime" column="op_time" jdbcType="BIGINT" />
    </resultMap>

    <sql id="columnList">
        id, vehicle_id, channel_id, last_ver, content, deleted, create_time, op_time
    </sql>

    <select id="selectByVehicleIdAndChannelId" resultMap="BaseResultMap">
        select <include refid="columnList"/>
        from vehicle_channel_ext where vehicle_id = #{vehicleId} AND channel_id = #{channelId} AND deleted = 0 limit 1
    </select>

    <update id="updateBIdAndLastVer">
        update vehicle_channel_ext
            set last_ver = last_ver + 1 ,content = #{record.content}, op_time = #{record.opTime}
        where id = #{record.id}  AND deleted = 0 AND last_ver = #{record.lastVer}
    </update>

    <select id="selectPage" resultMap="BaseResultMap">
        select <include refid="columnList"/>
        from vehicle_channel_ext where id &gt;= #{startId} order by id limit 1000
    </select>

    <!--  插入数据 -->
    <insert id="insertSelective" >
        insert into vehicle_channel_ext
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vehicleId != null">
                vehicle_id,
            </if>
            <if test="channelId != null">
                channel_id,
            </if>
            <if test="lastVer != null">
                last_ver,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="opTime != null">
                op_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vehicleId != null">
                #{vehicleId},
            </if>
            <if test="channelId != null">
                #{channelId},
            </if>
            <if test="lastVer != null">
                #{lastVer},
            </if>
            <if test="content != null">
                #{content},
            </if>
            <if test="deleted != null">
                #{deleted},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="opTime != null">
                #{opTime},
            </if>
        </trim>
    </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.ShuntingListMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.ShuntingList">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vehicle_info_id" jdbcType="BIGINT" property="vehicleInfoId" />
    <result column="transfer_out_store_id" jdbcType="BIGINT" property="transferOutStoreId" />
    <result column="transfer_in_store_id" jdbcType="BIGINT" property="transferInStoreId" />
    <result column="transfer_out_time" jdbcType="BIGINT" property="transferOutTime" />
    <result column="transfer_in_time" jdbcType="BIGINT" property="transferInTime" />
    <result column="shunter" jdbcType="VARCHAR" property="shunter" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="shunting_status" jdbcType="TINYINT" property="shuntingStatus" />
    <result column="completed_immediately" jdbcType="TINYINT" property="completedImmediately" />
    <result column="occupied_vehicle" jdbcType="TINYINT" property="occupiedVehicle" />
    <result column="cancel_reason" jdbcType="VARCHAR" property="cancelReason" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vehicle_info_id, transfer_out_store_id, transfer_in_store_id, transfer_out_time, 
    transfer_in_time, shunter, remark, shunting_status, completed_immediately, occupied_vehicle, 
    cancel_reason, merchant_id, deleted, last_ver, create_user_id, op_user_id, create_time, 
    op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.ShuntingListExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from shunting_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from shunting_list
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from shunting_list
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.ShuntingListExample">
    delete from shunting_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.ShuntingList">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into shunting_list (vehicle_info_id, transfer_out_store_id, 
      transfer_in_store_id, transfer_out_time, transfer_in_time, 
      shunter, remark, shunting_status, 
      completed_immediately, occupied_vehicle, 
      cancel_reason, merchant_id, deleted, 
      last_ver, create_user_id, op_user_id, 
      create_time, op_time)
    values (#{vehicleInfoId,jdbcType=BIGINT}, #{transferOutStoreId,jdbcType=BIGINT}, 
      #{transferInStoreId,jdbcType=BIGINT}, #{transferOutTime,jdbcType=BIGINT}, #{transferInTime,jdbcType=BIGINT}, 
      #{shunter,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{shuntingStatus,jdbcType=TINYINT}, 
      #{completedImmediately,jdbcType=TINYINT}, #{occupiedVehicle,jdbcType=TINYINT}, 
      #{cancelReason,jdbcType=VARCHAR}, #{merchantId,jdbcType=BIGINT}, #{deleted,jdbcType=TINYINT}, 
      #{lastVer,jdbcType=INTEGER}, #{createUserId,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.ShuntingList">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into shunting_list
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="vehicleInfoId != null">
        vehicle_info_id,
      </if>
      <if test="transferOutStoreId != null">
        transfer_out_store_id,
      </if>
      <if test="transferInStoreId != null">
        transfer_in_store_id,
      </if>
      <if test="transferOutTime != null">
        transfer_out_time,
      </if>
      <if test="transferInTime != null">
        transfer_in_time,
      </if>
      <if test="shunter != null">
        shunter,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="shuntingStatus != null">
        shunting_status,
      </if>
      <if test="completedImmediately != null">
        completed_immediately,
      </if>
      <if test="occupiedVehicle != null">
        occupied_vehicle,
      </if>
      <if test="cancelReason != null">
        cancel_reason,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="vehicleInfoId != null">
        #{vehicleInfoId,jdbcType=BIGINT},
      </if>
      <if test="transferOutStoreId != null">
        #{transferOutStoreId,jdbcType=BIGINT},
      </if>
      <if test="transferInStoreId != null">
        #{transferInStoreId,jdbcType=BIGINT},
      </if>
      <if test="transferOutTime != null">
        #{transferOutTime,jdbcType=BIGINT},
      </if>
      <if test="transferInTime != null">
        #{transferInTime,jdbcType=BIGINT},
      </if>
      <if test="shunter != null">
        #{shunter,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="shuntingStatus != null">
        #{shuntingStatus,jdbcType=TINYINT},
      </if>
      <if test="completedImmediately != null">
        #{completedImmediately,jdbcType=TINYINT},
      </if>
      <if test="occupiedVehicle != null">
        #{occupiedVehicle,jdbcType=TINYINT},
      </if>
      <if test="cancelReason != null">
        #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.ShuntingListExample" resultType="java.lang.Long">
    select count(*) from shunting_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update shunting_list
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleInfoId != null">
        vehicle_info_id = #{record.vehicleInfoId,jdbcType=BIGINT},
      </if>
      <if test="record.transferOutStoreId != null">
        transfer_out_store_id = #{record.transferOutStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.transferInStoreId != null">
        transfer_in_store_id = #{record.transferInStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.transferOutTime != null">
        transfer_out_time = #{record.transferOutTime,jdbcType=BIGINT},
      </if>
      <if test="record.transferInTime != null">
        transfer_in_time = #{record.transferInTime,jdbcType=BIGINT},
      </if>
      <if test="record.shunter != null">
        shunter = #{record.shunter,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.shuntingStatus != null">
        shunting_status = #{record.shuntingStatus,jdbcType=TINYINT},
      </if>
      <if test="record.completedImmediately != null">
        completed_immediately = #{record.completedImmediately,jdbcType=TINYINT},
      </if>
      <if test="record.occupiedVehicle != null">
        occupied_vehicle = #{record.occupiedVehicle,jdbcType=TINYINT},
      </if>
      <if test="record.cancelReason != null">
        cancel_reason = #{record.cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update shunting_list
    set id = #{record.id,jdbcType=BIGINT},
      vehicle_info_id = #{record.vehicleInfoId,jdbcType=BIGINT},
      transfer_out_store_id = #{record.transferOutStoreId,jdbcType=BIGINT},
      transfer_in_store_id = #{record.transferInStoreId,jdbcType=BIGINT},
      transfer_out_time = #{record.transferOutTime,jdbcType=BIGINT},
      transfer_in_time = #{record.transferInTime,jdbcType=BIGINT},
      shunter = #{record.shunter,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      shunting_status = #{record.shuntingStatus,jdbcType=TINYINT},
      completed_immediately = #{record.completedImmediately,jdbcType=TINYINT},
      occupied_vehicle = #{record.occupiedVehicle,jdbcType=TINYINT},
      cancel_reason = #{record.cancelReason,jdbcType=VARCHAR},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      deleted = #{record.deleted,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      create_user_id = #{record.createUserId,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.ShuntingList">
    update shunting_list
    <set>
      <if test="vehicleInfoId != null">
        vehicle_info_id = #{vehicleInfoId,jdbcType=BIGINT},
      </if>
      <if test="transferOutStoreId != null">
        transfer_out_store_id = #{transferOutStoreId,jdbcType=BIGINT},
      </if>
      <if test="transferInStoreId != null">
        transfer_in_store_id = #{transferInStoreId,jdbcType=BIGINT},
      </if>
      <if test="transferOutTime != null">
        transfer_out_time = #{transferOutTime,jdbcType=BIGINT},
      </if>
      <if test="transferInTime != null">
        transfer_in_time = #{transferInTime,jdbcType=BIGINT},
      </if>
      <if test="shunter != null">
        shunter = #{shunter,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="shuntingStatus != null">
        shunting_status = #{shuntingStatus,jdbcType=TINYINT},
      </if>
      <if test="completedImmediately != null">
        completed_immediately = #{completedImmediately,jdbcType=TINYINT},
      </if>
      <if test="occupiedVehicle != null">
        occupied_vehicle = #{occupiedVehicle,jdbcType=TINYINT},
      </if>
      <if test="cancelReason != null">
        cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.ShuntingList">
    update shunting_list
    set vehicle_info_id = #{vehicleInfoId,jdbcType=BIGINT},
      transfer_out_store_id = #{transferOutStoreId,jdbcType=BIGINT},
      transfer_in_store_id = #{transferInStoreId,jdbcType=BIGINT},
      transfer_out_time = #{transferOutTime,jdbcType=BIGINT},
      transfer_in_time = #{transferInTime,jdbcType=BIGINT},
      shunter = #{shunter,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      shunting_status = #{shuntingStatus,jdbcType=TINYINT},
      completed_immediately = #{completedImmediately,jdbcType=TINYINT},
      occupied_vehicle = #{occupiedVehicle,jdbcType=TINYINT},
      cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      create_user_id = #{createUserId,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into shunting_list
    (vehicle_info_id, transfer_out_store_id, transfer_in_store_id, transfer_out_time, 
      transfer_in_time, shunter, remark, shunting_status, completed_immediately, occupied_vehicle, 
      cancel_reason, merchant_id, deleted, last_ver, create_user_id, op_user_id, create_time, 
      op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.vehicleInfoId,jdbcType=BIGINT}, #{item.transferOutStoreId,jdbcType=BIGINT}, 
        #{item.transferInStoreId,jdbcType=BIGINT}, #{item.transferOutTime,jdbcType=BIGINT}, 
        #{item.transferInTime,jdbcType=BIGINT}, #{item.shunter,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.shuntingStatus,jdbcType=TINYINT}, #{item.completedImmediately,jdbcType=TINYINT}, 
        #{item.occupiedVehicle,jdbcType=TINYINT}, #{item.cancelReason,jdbcType=VARCHAR}, 
        #{item.merchantId,jdbcType=BIGINT}, #{item.deleted,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER}, 
        #{item.createUserId,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into shunting_list (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'vehicle_info_id'.toString() == column.value">
          #{item.vehicleInfoId,jdbcType=BIGINT}
        </if>
        <if test="'transfer_out_store_id'.toString() == column.value">
          #{item.transferOutStoreId,jdbcType=BIGINT}
        </if>
        <if test="'transfer_in_store_id'.toString() == column.value">
          #{item.transferInStoreId,jdbcType=BIGINT}
        </if>
        <if test="'transfer_out_time'.toString() == column.value">
          #{item.transferOutTime,jdbcType=BIGINT}
        </if>
        <if test="'transfer_in_time'.toString() == column.value">
          #{item.transferInTime,jdbcType=BIGINT}
        </if>
        <if test="'shunter'.toString() == column.value">
          #{item.shunter,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'shunting_status'.toString() == column.value">
          #{item.shuntingStatus,jdbcType=TINYINT}
        </if>
        <if test="'completed_immediately'.toString() == column.value">
          #{item.completedImmediately,jdbcType=TINYINT}
        </if>
        <if test="'occupied_vehicle'.toString() == column.value">
          #{item.occupiedVehicle,jdbcType=TINYINT}
        </if>
        <if test="'cancel_reason'.toString() == column.value">
          #{item.cancelReason,jdbcType=VARCHAR}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'create_user_id'.toString() == column.value">
          #{item.createUserId,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
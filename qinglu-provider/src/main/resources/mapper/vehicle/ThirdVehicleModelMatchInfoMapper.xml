<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.ThirdVehicleModelMatchInfoMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.ThirdVehicleModelMatchInfo">
    <!--@mbg.generated-->
    <!--@Table third_vehicle_model_match_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="third_vehicle_model_id" jdbcType="VARCHAR" property="thirdVehicleModelId" />
    <result column="third_vehicle_model_name" jdbcType="VARCHAR" property="thirdVehicleModelName" />
    <result column="matched" jdbcType="TINYINT" property="matched" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, vehicle_model_id, channel_id, merchant_id, third_vehicle_model_id, third_vehicle_model_name, 
    `matched`, create_time, op_time, last_ver, deleted
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.ThirdVehicleModelMatchInfoExample" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from third_vehicle_model_match_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from third_vehicle_model_match_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from third_vehicle_model_match_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.ThirdVehicleModelMatchInfoExample">
    <!--@mbg.generated-->
    delete from third_vehicle_model_match_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ql.rent.entity.vehicle.ThirdVehicleModelMatchInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into third_vehicle_model_match_info (vehicle_model_id, channel_id, merchant_id, 
      third_vehicle_model_id, third_vehicle_model_name, 
      `matched`, create_time, op_time, 
      last_ver, deleted)
    values (#{vehicleModelId,jdbcType=BIGINT}, #{channelId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, 
      #{thirdVehicleModelId,jdbcType=VARCHAR}, #{thirdVehicleModelName,jdbcType=VARCHAR}, 
      #{matched,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, 
      #{lastVer,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ql.rent.entity.vehicle.ThirdVehicleModelMatchInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into third_vehicle_model_match_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="thirdVehicleModelId != null">
        third_vehicle_model_id,
      </if>
      <if test="thirdVehicleModelName != null">
        third_vehicle_model_name,
      </if>
      <if test="matched != null">
        `matched`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="thirdVehicleModelId != null">
        #{thirdVehicleModelId,jdbcType=VARCHAR},
      </if>
      <if test="thirdVehicleModelName != null">
        #{thirdVehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="matched != null">
        #{matched,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.ThirdVehicleModelMatchInfoExample" resultType="java.lang.Long">
    <!--@mbg.generated-->
    select count(*) from third_vehicle_model_match_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--@mbg.generated-->
    update third_vehicle_model_match_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.thirdVehicleModelId != null">
        third_vehicle_model_id = #{record.thirdVehicleModelId,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdVehicleModelName != null">
        third_vehicle_model_name = #{record.thirdVehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="record.matched != null">
        `matched` = #{record.matched,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--@mbg.generated-->
    update third_vehicle_model_match_info
    set id = #{record.id,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      channel_id = #{record.channelId,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      third_vehicle_model_id = #{record.thirdVehicleModelId,jdbcType=VARCHAR},
      third_vehicle_model_name = #{record.thirdVehicleModelName,jdbcType=VARCHAR},
      `matched` = #{record.matched,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.ThirdVehicleModelMatchInfo">
    <!--@mbg.generated-->
    update third_vehicle_model_match_info
    <set>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="thirdVehicleModelId != null">
        third_vehicle_model_id = #{thirdVehicleModelId,jdbcType=VARCHAR},
      </if>
      <if test="thirdVehicleModelName != null">
        third_vehicle_model_name = #{thirdVehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="matched != null">
        `matched` = #{matched,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.ThirdVehicleModelMatchInfo">
    <!--@mbg.generated-->
    update third_vehicle_model_match_info
    set vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      channel_id = #{channelId,jdbcType=BIGINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      third_vehicle_model_id = #{thirdVehicleModelId,jdbcType=VARCHAR},
      third_vehicle_model_name = #{thirdVehicleModelName,jdbcType=VARCHAR},
      `matched` = #{matched,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.InsuranceServicePriceMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.InsuranceServicePrice">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="insurance_service_setting_id" jdbcType="BIGINT" property="insuranceServiceSettingId" />
    <result column="rent_base_id" jdbcType="BIGINT" property="rentBaseId" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="price" jdbcType="INTEGER" property="price" />
    <result column="on_highest_price" jdbcType="TINYINT" property="onHighestPrice" />
    <result column="highest_price" jdbcType="INTEGER" property="highestPrice" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, insurance_service_setting_id, rent_base_id, vehicle_model_id, store_id, price,
    on_highest_price, highest_price, status, create_time, op_time, op_user_id, last_ver
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.InsuranceServicePriceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from insurance_service_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from insurance_service_price
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from insurance_service_price
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.InsuranceServicePriceExample">
    delete from insurance_service_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.InsuranceServicePrice">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into insurance_service_price (insurance_service_setting_id, rent_base_id,
      vehicle_model_id, store_id, price,
      on_highest_price, highest_price, status,
      create_time, op_time, op_user_id,
      last_ver)
    values (#{insuranceServiceSettingId,jdbcType=BIGINT}, #{rentBaseId,jdbcType=BIGINT},
      #{vehicleModelId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{price,jdbcType=INTEGER},
      #{onHighestPrice,jdbcType=TINYINT}, #{highestPrice,jdbcType=INTEGER}, #{status,jdbcType=TINYINT},
      #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT},
      #{lastVer,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.InsuranceServicePrice">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into insurance_service_price
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="insuranceServiceSettingId != null">
        insurance_service_setting_id,
      </if>
      <if test="rentBaseId != null">
        rent_base_id,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="onHighestPrice != null">
        on_highest_price,
      </if>
      <if test="highestPrice != null">
        highest_price,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="insuranceServiceSettingId != null">
        #{insuranceServiceSettingId,jdbcType=BIGINT},
      </if>
      <if test="rentBaseId != null">
        #{rentBaseId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="price != null">
        #{price,jdbcType=INTEGER},
      </if>
      <if test="onHighestPrice != null">
        #{onHighestPrice,jdbcType=TINYINT},
      </if>
      <if test="highestPrice != null">
        #{highestPrice,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.InsuranceServicePriceExample" resultType="java.lang.Long">
    select count(*) from insurance_service_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update insurance_service_price
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.insuranceServiceSettingId != null">
        insurance_service_setting_id = #{record.insuranceServiceSettingId,jdbcType=BIGINT},
      </if>
      <if test="record.rentBaseId != null">
        rent_base_id = #{record.rentBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=INTEGER},
      </if>
      <if test="record.onHighestPrice != null">
        on_highest_price = #{record.onHighestPrice,jdbcType=TINYINT},
      </if>
      <if test="record.highestPrice != null">
        highest_price = #{record.highestPrice,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update insurance_service_price
    set id = #{record.id,jdbcType=BIGINT},
      insurance_service_setting_id = #{record.insuranceServiceSettingId,jdbcType=BIGINT},
      rent_base_id = #{record.rentBaseId,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      price = #{record.price,jdbcType=INTEGER},
      on_highest_price = #{record.onHighestPrice,jdbcType=TINYINT},
      highest_price = #{record.highestPrice,jdbcType=INTEGER},
      status = #{record.status,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.InsuranceServicePrice">
    update insurance_service_price
    <set>
      <if test="insuranceServiceSettingId != null">
        insurance_service_setting_id = #{insuranceServiceSettingId,jdbcType=BIGINT},
      </if>
      <if test="rentBaseId != null">
        rent_base_id = #{rentBaseId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=INTEGER},
      </if>
      <if test="onHighestPrice != null">
        on_highest_price = #{onHighestPrice,jdbcType=TINYINT},
      </if>
      <if test="highestPrice != null">
        highest_price = #{highestPrice,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.InsuranceServicePrice">
    update insurance_service_price
    set insurance_service_setting_id = #{insuranceServiceSettingId,jdbcType=BIGINT},
      rent_base_id = #{rentBaseId,jdbcType=BIGINT},
      vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      price = #{price,jdbcType=INTEGER},
      on_highest_price = #{onHighestPrice,jdbcType=TINYINT},
      highest_price = #{highestPrice,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      last_ver = #{lastVer,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into insurance_service_price
    (insurance_service_setting_id, rent_base_id, vehicle_model_id, store_id, price, on_highest_price,
      highest_price, status, create_time, op_time, op_user_id, last_ver)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.insuranceServiceSettingId,jdbcType=BIGINT}, #{item.rentBaseId,jdbcType=BIGINT},
        #{item.vehicleModelId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.price,jdbcType=INTEGER},
        #{item.onHighestPrice,jdbcType=TINYINT}, #{item.highestPrice,jdbcType=INTEGER},
        #{item.status,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT},
        #{item.opUserId,jdbcType=BIGINT}, #{item.lastVer,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into insurance_service_price (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'insurance_service_setting_id'.toString() == column.value">
          #{item.insuranceServiceSettingId,jdbcType=BIGINT}
        </if>
        <if test="'rent_base_id'.toString() == column.value">
          #{item.rentBaseId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'price'.toString() == column.value">
          #{item.price,jdbcType=INTEGER}
        </if>
        <if test="'on_highest_price'.toString() == column.value">
          #{item.onHighestPrice,jdbcType=TINYINT}
        </if>
        <if test="'highest_price'.toString() == column.value">
          #{item.highestPrice,jdbcType=INTEGER}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>

  <select id="getModelAndServiceNum" resultType="java.util.HashMap">
    select vehicle_model_id as 'key', count(*) as 'value'
    from insurance_service_price
    where store_id = #{storeId,jdbcType=BIGINT}
    and status = 1 and price > 0
    <if test="vehicleModelIdList!=null">
      and  vehicle_model_id in
      <foreach item="item" collection="vehicleModelIdList" separator="," open="(" close=")" index="">
        #{item, jdbcType=BIGINT}
      </foreach>
    </if>
    <if test="insuranceServiceIdList!=null">
      and  insurance_service_setting_id in
      <foreach item="item" collection="insuranceServiceIdList" separator="," open="(" close=")" index="">
        #{item, jdbcType=BIGINT}
      </foreach>
    </if>
    group by vehicle_model_id
  </select>

  <select id="getModelAndServiceNumChannel" resultType="java.util.HashMap">
    select vehicle_model_id as 'key', count(*) as 'value'
    from insurance_service_price_channel
    where store_id = #{storeId,jdbcType=BIGINT}
    and channel = #{channelId,jdbcType=BIGINT}
    and status = 1 and price > 0
    <if test="vehicleModelIdList!=null">
      and  vehicle_model_id in
      <foreach item="item" collection="vehicleModelIdList" separator="," open="(" close=")" index="">
        #{item, jdbcType=BIGINT}
      </foreach>
    </if>
    <if test="insuranceSettingIdList!=null">
      and  insurance_service_setting_id in
      <foreach item="item" collection="insuranceSettingIdList" separator="," open="(" close=")" index="">
        #{item, jdbcType=BIGINT}
      </foreach>
    </if>
    group by vehicle_model_id
  </select>

  <update id="batchUpdate" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <foreach collection="list" item="item" separator=";">
      update insurance_service_price
      <set>
        <if test="item.insuranceServiceSettingId != null">
          insurance_service_setting_id = #{item.insuranceServiceSettingId,jdbcType=BIGINT},
        </if>
        <if test="item.rentBaseId != null">
          rent_base_id = #{item.rentBaseId,jdbcType=BIGINT},
        </if>
        <if test="item.vehicleModelId != null">
          vehicle_model_id = #{item.vehicleModelId,jdbcType=BIGINT},
        </if>
        <if test="item.storeId != null">
          store_id = #{item.storeId,jdbcType=BIGINT},
        </if>
        <if test="item.price != null">
          price = #{item.price,jdbcType=INTEGER},
        </if>
        <if test="item.onHighestPrice != null">
          on_highest_price = #{item.onHighestPrice,jdbcType=TINYINT},
        </if>
        <if test="item.highestPrice != null">
          highest_price = #{item.highestPrice,jdbcType=INTEGER},
        </if>
        <if test="item.status != null">
          status = #{item.status,jdbcType=TINYINT},
        </if>
        <if test="item.createTime != null">
          create_time = #{item.createTime,jdbcType=BIGINT},
        </if>
        <if test="item.opTime != null">
          op_time = #{item.opTime,jdbcType=BIGINT},
        </if>
        <if test="item.opUserId != null">
          op_user_id = #{item.opUserId,jdbcType=BIGINT},
        </if>
        <if test="item.lastVer != null">
          last_ver = #{item.lastVer,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>

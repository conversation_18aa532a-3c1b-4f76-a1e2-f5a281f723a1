<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.VehicleBusyMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.VehicleBusy">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="start_time" jdbcType="BIGINT" property="startTime" />
    <result column="end_time" jdbcType="BIGINT" property="endTime" />
    <result column="end_interval_time" jdbcType="BIGINT" property="endIntervalTime" />
    <result column="auto_schedule" jdbcType="TINYINT" property="autoSchedule" />
    <result column="source_type" jdbcType="INTEGER" property="sourceType" />
    <result column="source_id" jdbcType="BIGINT" property="sourceId" />
    <result column="parent_source_id" jdbcType="BIGINT" property="parentSourceId" />
    <result column="third_source_id" jdbcType="VARCHAR" property="thirdSourceId" />
    <result column="third_parent_source_id" jdbcType="VARCHAR" property="thirdParentSourceId" />
    <result column="lock_flg" jdbcType="TINYINT" property="lockFlg" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="busy_desc" jdbcType="VARCHAR" property="busyDesc" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, store_id, vehicle_model_id, vehicle_id, channel_id, start_time, 
    end_time, end_interval_time, auto_schedule, source_type, source_id, parent_source_id, 
    third_source_id, third_parent_source_id, lock_flg, deleted, create_time, op_time, 
    busy_desc, ext
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.VehicleBusyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_busy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_busy
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_busy
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.VehicleBusyExample">
    delete from vehicle_busy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.VehicleBusy">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_busy (merchant_id, store_id, vehicle_model_id, 
      vehicle_id, channel_id, start_time, 
      end_time, end_interval_time, auto_schedule, 
      source_type, source_id, parent_source_id, 
      third_source_id, third_parent_source_id, lock_flg, 
      deleted, create_time, op_time, 
      busy_desc, ext)
    values (#{merchantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{vehicleModelId,jdbcType=BIGINT}, 
      #{vehicleId,jdbcType=BIGINT}, #{channelId,jdbcType=BIGINT}, #{startTime,jdbcType=BIGINT}, 
      #{endTime,jdbcType=BIGINT}, #{endIntervalTime,jdbcType=BIGINT}, #{autoSchedule,jdbcType=TINYINT}, 
      #{sourceType,jdbcType=INTEGER}, #{sourceId,jdbcType=BIGINT}, #{parentSourceId,jdbcType=BIGINT}, 
      #{thirdSourceId,jdbcType=VARCHAR}, #{thirdParentSourceId,jdbcType=VARCHAR}, #{lockFlg,jdbcType=TINYINT}, 
      #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, 
      #{busyDesc,jdbcType=VARCHAR}, #{ext,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.VehicleBusy">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_busy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="endIntervalTime != null">
        end_interval_time,
      </if>
      <if test="autoSchedule != null">
        auto_schedule,
      </if>
      <if test="sourceType != null">
        source_type,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="parentSourceId != null">
        parent_source_id,
      </if>
      <if test="thirdSourceId != null">
        third_source_id,
      </if>
      <if test="thirdParentSourceId != null">
        third_parent_source_id,
      </if>
      <if test="lockFlg != null">
        lock_flg,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="busyDesc != null">
        busy_desc,
      </if>
      <if test="ext != null">
        ext,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=BIGINT},
      </if>
      <if test="endIntervalTime != null">
        #{endIntervalTime,jdbcType=BIGINT},
      </if>
      <if test="autoSchedule != null">
        #{autoSchedule,jdbcType=TINYINT},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=INTEGER},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="parentSourceId != null">
        #{parentSourceId,jdbcType=BIGINT},
      </if>
      <if test="thirdSourceId != null">
        #{thirdSourceId,jdbcType=VARCHAR},
      </if>
      <if test="thirdParentSourceId != null">
        #{thirdParentSourceId,jdbcType=VARCHAR},
      </if>
      <if test="lockFlg != null">
        #{lockFlg,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="busyDesc != null">
        #{busyDesc,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.VehicleBusyExample" resultType="java.lang.Long">
    select count(*) from vehicle_busy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_busy
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=BIGINT},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=BIGINT},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=BIGINT},
      </if>
      <if test="record.endIntervalTime != null">
        end_interval_time = #{record.endIntervalTime,jdbcType=BIGINT},
      </if>
      <if test="record.autoSchedule != null">
        auto_schedule = #{record.autoSchedule,jdbcType=TINYINT},
      </if>
      <if test="record.sourceType != null">
        source_type = #{record.sourceType,jdbcType=INTEGER},
      </if>
      <if test="record.sourceId != null">
        source_id = #{record.sourceId,jdbcType=BIGINT},
      </if>
      <if test="record.parentSourceId != null">
        parent_source_id = #{record.parentSourceId,jdbcType=BIGINT},
      </if>
      <if test="record.thirdSourceId != null">
        third_source_id = #{record.thirdSourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdParentSourceId != null">
        third_parent_source_id = #{record.thirdParentSourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.lockFlg != null">
        lock_flg = #{record.lockFlg,jdbcType=TINYINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.busyDesc != null">
        busy_desc = #{record.busyDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.ext != null">
        ext = #{record.ext,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_busy
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      channel_id = #{record.channelId,jdbcType=BIGINT},
      start_time = #{record.startTime,jdbcType=BIGINT},
      end_time = #{record.endTime,jdbcType=BIGINT},
      end_interval_time = #{record.endIntervalTime,jdbcType=BIGINT},
      auto_schedule = #{record.autoSchedule,jdbcType=TINYINT},
      source_type = #{record.sourceType,jdbcType=INTEGER},
      source_id = #{record.sourceId,jdbcType=BIGINT},
      parent_source_id = #{record.parentSourceId,jdbcType=BIGINT},
      third_source_id = #{record.thirdSourceId,jdbcType=VARCHAR},
      third_parent_source_id = #{record.thirdParentSourceId,jdbcType=VARCHAR},
      lock_flg = #{record.lockFlg,jdbcType=TINYINT},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      busy_desc = #{record.busyDesc,jdbcType=VARCHAR},
      ext = #{record.ext,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.VehicleBusy">
    update vehicle_busy
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=BIGINT},
      </if>
      <if test="endIntervalTime != null">
        end_interval_time = #{endIntervalTime,jdbcType=BIGINT},
      </if>
      <if test="autoSchedule != null">
        auto_schedule = #{autoSchedule,jdbcType=TINYINT},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType,jdbcType=INTEGER},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="parentSourceId != null">
        parent_source_id = #{parentSourceId,jdbcType=BIGINT},
      </if>
      <if test="thirdSourceId != null">
        third_source_id = #{thirdSourceId,jdbcType=VARCHAR},
      </if>
      <if test="thirdParentSourceId != null">
        third_parent_source_id = #{thirdParentSourceId,jdbcType=VARCHAR},
      </if>
      <if test="lockFlg != null">
        lock_flg = #{lockFlg,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="busyDesc != null">
        busy_desc = #{busyDesc,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.VehicleBusy">
    update vehicle_busy
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      channel_id = #{channelId,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=BIGINT},
      end_time = #{endTime,jdbcType=BIGINT},
      end_interval_time = #{endIntervalTime,jdbcType=BIGINT},
      auto_schedule = #{autoSchedule,jdbcType=TINYINT},
      source_type = #{sourceType,jdbcType=INTEGER},
      source_id = #{sourceId,jdbcType=BIGINT},
      parent_source_id = #{parentSourceId,jdbcType=BIGINT},
      third_source_id = #{thirdSourceId,jdbcType=VARCHAR},
      third_parent_source_id = #{thirdParentSourceId,jdbcType=VARCHAR},
      lock_flg = #{lockFlg,jdbcType=TINYINT},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      busy_desc = #{busyDesc,jdbcType=VARCHAR},
      ext = #{ext,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_busy
    (merchant_id, store_id, vehicle_model_id, vehicle_id, channel_id, start_time, end_time, 
      end_interval_time, auto_schedule, source_type, source_id, parent_source_id, third_source_id, 
      third_parent_source_id, lock_flg, deleted, create_time, op_time, busy_desc, ext
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.vehicleModelId,jdbcType=BIGINT}, 
        #{item.vehicleId,jdbcType=BIGINT}, #{item.channelId,jdbcType=BIGINT}, #{item.startTime,jdbcType=BIGINT}, 
        #{item.endTime,jdbcType=BIGINT}, #{item.endIntervalTime,jdbcType=BIGINT}, #{item.autoSchedule,jdbcType=TINYINT}, 
        #{item.sourceType,jdbcType=INTEGER}, #{item.sourceId,jdbcType=BIGINT}, #{item.parentSourceId,jdbcType=BIGINT}, 
        #{item.thirdSourceId,jdbcType=VARCHAR}, #{item.thirdParentSourceId,jdbcType=VARCHAR}, 
        #{item.lockFlg,jdbcType=TINYINT}, #{item.deleted,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.busyDesc,jdbcType=VARCHAR}, #{item.ext,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_busy (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_id'.toString() == column.value">
          #{item.vehicleId,jdbcType=BIGINT}
        </if>
        <if test="'channel_id'.toString() == column.value">
          #{item.channelId,jdbcType=BIGINT}
        </if>
        <if test="'start_time'.toString() == column.value">
          #{item.startTime,jdbcType=BIGINT}
        </if>
        <if test="'end_time'.toString() == column.value">
          #{item.endTime,jdbcType=BIGINT}
        </if>
        <if test="'end_interval_time'.toString() == column.value">
          #{item.endIntervalTime,jdbcType=BIGINT}
        </if>
        <if test="'auto_schedule'.toString() == column.value">
          #{item.autoSchedule,jdbcType=TINYINT}
        </if>
        <if test="'source_type'.toString() == column.value">
          #{item.sourceType,jdbcType=INTEGER}
        </if>
        <if test="'source_id'.toString() == column.value">
          #{item.sourceId,jdbcType=BIGINT}
        </if>
        <if test="'parent_source_id'.toString() == column.value">
          #{item.parentSourceId,jdbcType=BIGINT}
        </if>
        <if test="'third_source_id'.toString() == column.value">
          #{item.thirdSourceId,jdbcType=VARCHAR}
        </if>
        <if test="'third_parent_source_id'.toString() == column.value">
          #{item.thirdParentSourceId,jdbcType=VARCHAR}
        </if>
        <if test="'lock_flg'.toString() == column.value">
          #{item.lockFlg,jdbcType=TINYINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'busy_desc'.toString() == column.value">
          #{item.busyDesc,jdbcType=VARCHAR}
        </if>
        <if test="'ext'.toString() == column.value">
          #{item.ext,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
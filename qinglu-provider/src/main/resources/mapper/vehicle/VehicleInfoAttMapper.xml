<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.VehicleInfoAttMapper">
    <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.VehicleInfoAtt">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId"/>
        <result column="vehicle_insurance_id" jdbcType="BIGINT" property="vehicleInsuranceId"/>
        <result column="file_path" jdbcType="VARCHAR" property="filePath"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="op_time" jdbcType="BIGINT" property="opTime"/>
        <result column="op_user_id" jdbcType="BIGINT" property="opUserId"/>
        <result column="file_type" jdbcType="TINYINT" property="fileType"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
    id, vehicle_id, vehicle_insurance_id, file_path, deleted, create_time, op_time, op_user_id, 
    file_type
  </sql>
    <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.VehicleInfoAttExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from vehicle_info_att
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from vehicle_info_att
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_info_att
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.VehicleInfoAttExample">
        delete from vehicle_info_att
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.ql.rent.entity.vehicle.VehicleInfoAtt">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into vehicle_info_att (vehicle_id, vehicle_insurance_id, file_path,
        deleted, create_time, op_time,
        op_user_id, file_type)
        values (#{vehicleId,jdbcType=BIGINT}, #{vehicleInsuranceId,jdbcType=BIGINT}, #{filePath,jdbcType=VARCHAR},
        #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT},
        #{opUserId,jdbcType=BIGINT}, #{fileType,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.VehicleInfoAtt">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into vehicle_info_att
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vehicleId != null">
                vehicle_id,
            </if>
            <if test="vehicleInsuranceId != null">
                vehicle_insurance_id,
            </if>
            <if test="filePath != null">
                file_path,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="opTime != null">
                op_time,
            </if>
            <if test="opUserId != null">
                op_user_id,
            </if>
            <if test="fileType != null">
                file_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vehicleId != null">
                #{vehicleId,jdbcType=BIGINT},
            </if>
            <if test="vehicleInsuranceId != null">
                #{vehicleInsuranceId,jdbcType=BIGINT},
            </if>
            <if test="filePath != null">
                #{filePath,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="opTime != null">
                #{opTime,jdbcType=BIGINT},
            </if>
            <if test="opUserId != null">
                #{opUserId,jdbcType=BIGINT},
            </if>
            <if test="fileType != null">
                #{fileType,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.VehicleInfoAttExample"
            resultType="java.lang.Long">
        select count(*) from vehicle_info_att
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update vehicle_info_att
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.vehicleId != null">
                vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
            </if>
            <if test="record.vehicleInsuranceId != null">
                vehicle_insurance_id = #{record.vehicleInsuranceId,jdbcType=BIGINT},
            </if>
            <if test="record.filePath != null">
                file_path = #{record.filePath,jdbcType=VARCHAR},
            </if>
            <if test="record.deleted != null">
                deleted = #{record.deleted,jdbcType=TINYINT},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=BIGINT},
            </if>
            <if test="record.opTime != null">
                op_time = #{record.opTime,jdbcType=BIGINT},
            </if>
            <if test="record.opUserId != null">
                op_user_id = #{record.opUserId,jdbcType=BIGINT},
            </if>
            <if test="record.fileType != null">
                file_type = #{record.fileType,jdbcType=TINYINT},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update vehicle_info_att
        set id = #{record.id,jdbcType=BIGINT},
        vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
        vehicle_insurance_id = #{record.vehicleInsuranceId,jdbcType=BIGINT},
        file_path = #{record.filePath,jdbcType=VARCHAR},
        deleted = #{record.deleted,jdbcType=TINYINT},
        create_time = #{record.createTime,jdbcType=BIGINT},
        op_time = #{record.opTime,jdbcType=BIGINT},
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
        file_type = #{record.fileType,jdbcType=TINYINT}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.VehicleInfoAtt">
        update vehicle_info_att
        <set>
            <if test="vehicleId != null">
                vehicle_id = #{vehicleId,jdbcType=BIGINT},
            </if>
            <if test="vehicleInsuranceId != null">
                vehicle_insurance_id = #{vehicleInsuranceId,jdbcType=BIGINT},
            </if>
            <if test="filePath != null">
                file_path = #{filePath,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="opTime != null">
                op_time = #{opTime,jdbcType=BIGINT},
            </if>
            <if test="opUserId != null">
                op_user_id = #{opUserId,jdbcType=BIGINT},
            </if>
            <if test="fileType != null">
                file_type = #{fileType,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.VehicleInfoAtt">
    update vehicle_info_att
    set vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_insurance_id = #{vehicleInsuranceId,jdbcType=BIGINT},
      file_path = #{filePath,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      file_type = #{fileType,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        insert into vehicle_info_att
        (vehicle_id, vehicle_insurance_id, file_path, deleted, create_time, op_time, op_user_id,
        file_type)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.vehicleId,jdbcType=BIGINT}, #{item.vehicleInsuranceId,jdbcType=BIGINT},
            #{item.filePath,jdbcType=VARCHAR},
            #{item.deleted,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT},
            #{item.opUserId,jdbcType=BIGINT}, #{item.fileType,jdbcType=TINYINT})
        </foreach>
    </insert>
    <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          @project https://github.com/itfsw/mybatis-generator-plugin
        -->
        insert into vehicle_info_att (
        <foreach collection="selective" item="column" separator=",">
            ${column.escapedColumnName}
        </foreach>
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            <foreach collection="selective" item="column" separator=",">
                <if test="'vehicle_id'.toString() == column.value">
                    #{item.vehicleId,jdbcType=BIGINT}
                </if>
                <if test="'vehicle_insurance_id'.toString() == column.value">
                    #{item.vehicleInsuranceId,jdbcType=BIGINT}
                </if>
                <if test="'file_path'.toString() == column.value">
                    #{item.filePath,jdbcType=VARCHAR}
                </if>
                <if test="'deleted'.toString() == column.value">
                    #{item.deleted,jdbcType=TINYINT}
                </if>
                <if test="'create_time'.toString() == column.value">
                    #{item.createTime,jdbcType=BIGINT}
                </if>
                <if test="'op_time'.toString() == column.value">
                    #{item.opTime,jdbcType=BIGINT}
                </if>
                <if test="'op_user_id'.toString() == column.value">
                    #{item.opUserId,jdbcType=BIGINT}
                </if>
                <if test="'file_type'.toString() == column.value">
                    #{item.fileType,jdbcType=TINYINT}
                </if>
            </foreach>
            )
        </foreach>
    </insert>
</mapper>
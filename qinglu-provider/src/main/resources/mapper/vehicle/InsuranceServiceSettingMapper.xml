<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.InsuranceServiceSettingMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.InsuranceServiceSetting">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="preset" jdbcType="TINYINT" property="preset" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="damage_insurance" jdbcType="TINYINT" property="damageInsurance" />
    <result column="damage_insurance_amount" jdbcType="INTEGER" property="damageInsuranceAmount" />
    <result column="glass" jdbcType="TINYINT" property="glass" />
    <result column="tire" jdbcType="TINYINT" property="tire" />
    <result column="third_party_insurance" jdbcType="INTEGER" property="thirdPartyInsurance" />
    <result column="outage_fee" jdbcType="TINYINT" property="outageFee" />
    <result column="outage_fee_ratio" jdbcType="DECIMAL" property="outageFeeRatio" />
    <result column="outage_fee_amount" jdbcType="INTEGER" property="outageFeeAmount" />
    <result column="depreciation" jdbcType="TINYINT" property="depreciation" />
    <result column="depreciation_fee" jdbcType="INTEGER" property="depreciationFee" />
    <result column="repair_fee_ratio" jdbcType="DECIMAL" property="repairFeeRatio" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="required" jdbcType="TINYINT" property="required" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, store_id, merchant_id, preset, parent_id, name, damage_insurance, damage_insurance_amount, 
    glass, tire, third_party_insurance, outage_fee, outage_fee_ratio, outage_fee_amount, 
    depreciation, depreciation_fee, repair_fee_ratio, status, required, last_ver, op_user_id, 
    create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.InsuranceServiceSettingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from insurance_service_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from insurance_service_setting
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.InsuranceServiceSetting">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into insurance_service_setting (store_id, merchant_id, preset, 
      parent_id, name, damage_insurance, 
      damage_insurance_amount, glass, tire, 
      third_party_insurance, outage_fee, outage_fee_ratio, 
      outage_fee_amount, depreciation, depreciation_fee, 
      repair_fee_ratio, status, required, 
      last_ver, op_user_id, create_time, 
      op_time)
    values (#{storeId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, #{preset,jdbcType=TINYINT}, 
      #{parentId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{damageInsurance,jdbcType=TINYINT}, 
      #{damageInsuranceAmount,jdbcType=INTEGER}, #{glass,jdbcType=TINYINT}, #{tire,jdbcType=TINYINT}, 
      #{thirdPartyInsurance,jdbcType=INTEGER}, #{outageFee,jdbcType=TINYINT}, #{outageFeeRatio,jdbcType=DECIMAL}, 
      #{outageFeeAmount,jdbcType=INTEGER}, #{depreciation,jdbcType=TINYINT}, #{depreciationFee,jdbcType=INTEGER}, 
      #{repairFeeRatio,jdbcType=DECIMAL}, #{status,jdbcType=TINYINT}, #{required,jdbcType=TINYINT}, 
      #{lastVer,jdbcType=INTEGER}, #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.InsuranceServiceSetting">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into insurance_service_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        store_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="preset != null">
        preset,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="damageInsurance != null">
        damage_insurance,
      </if>
      <if test="damageInsuranceAmount != null">
        damage_insurance_amount,
      </if>
      <if test="glass != null">
        glass,
      </if>
      <if test="tire != null">
        tire,
      </if>
      <if test="thirdPartyInsurance != null">
        third_party_insurance,
      </if>
      <if test="outageFee != null">
        outage_fee,
      </if>
      <if test="outageFeeRatio != null">
        outage_fee_ratio,
      </if>
      <if test="outageFeeAmount != null">
        outage_fee_amount,
      </if>
      <if test="depreciation != null">
        depreciation,
      </if>
      <if test="depreciationFee != null">
        depreciation_fee,
      </if>
      <if test="repairFeeRatio != null">
        repair_fee_ratio,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="required != null">
        required,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="preset != null">
        #{preset,jdbcType=TINYINT},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="damageInsurance != null">
        #{damageInsurance,jdbcType=TINYINT},
      </if>
      <if test="damageInsuranceAmount != null">
        #{damageInsuranceAmount,jdbcType=INTEGER},
      </if>
      <if test="glass != null">
        #{glass,jdbcType=TINYINT},
      </if>
      <if test="tire != null">
        #{tire,jdbcType=TINYINT},
      </if>
      <if test="thirdPartyInsurance != null">
        #{thirdPartyInsurance,jdbcType=INTEGER},
      </if>
      <if test="outageFee != null">
        #{outageFee,jdbcType=TINYINT},
      </if>
      <if test="outageFeeRatio != null">
        #{outageFeeRatio,jdbcType=DECIMAL},
      </if>
      <if test="outageFeeAmount != null">
        #{outageFeeAmount,jdbcType=INTEGER},
      </if>
      <if test="depreciation != null">
        #{depreciation,jdbcType=TINYINT},
      </if>
      <if test="depreciationFee != null">
        #{depreciationFee,jdbcType=INTEGER},
      </if>
      <if test="repairFeeRatio != null">
        #{repairFeeRatio,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="required != null">
        #{required,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.InsuranceServiceSettingExample" resultType="java.lang.Long">
    select count(*) from insurance_service_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update insurance_service_setting
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.preset != null">
        preset = #{record.preset,jdbcType=TINYINT},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.damageInsurance != null">
        damage_insurance = #{record.damageInsurance,jdbcType=TINYINT},
      </if>
      <if test="record.damageInsuranceAmount != null">
        damage_insurance_amount = #{record.damageInsuranceAmount,jdbcType=INTEGER},
      </if>
      <if test="record.glass != null">
        glass = #{record.glass,jdbcType=TINYINT},
      </if>
      <if test="record.tire != null">
        tire = #{record.tire,jdbcType=TINYINT},
      </if>
      <if test="record.thirdPartyInsurance != null">
        third_party_insurance = #{record.thirdPartyInsurance,jdbcType=INTEGER},
      </if>
      <if test="record.outageFee != null">
        outage_fee = #{record.outageFee,jdbcType=TINYINT},
      </if>
      <if test="record.outageFeeRatio != null">
        outage_fee_ratio = #{record.outageFeeRatio,jdbcType=DECIMAL},
      </if>
      <if test="record.outageFeeAmount != null">
        outage_fee_amount = #{record.outageFeeAmount,jdbcType=INTEGER},
      </if>
      <if test="record.depreciation != null">
        depreciation = #{record.depreciation,jdbcType=TINYINT},
      </if>
      <if test="record.depreciationFee != null">
        depreciation_fee = #{record.depreciationFee,jdbcType=INTEGER},
      </if>
      <if test="record.repairFeeRatio != null">
        repair_fee_ratio = #{record.repairFeeRatio,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.required != null">
        required = #{record.required,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update insurance_service_setting
    set id = #{record.id,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      preset = #{record.preset,jdbcType=TINYINT},
      parent_id = #{record.parentId,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      damage_insurance = #{record.damageInsurance,jdbcType=TINYINT},
      damage_insurance_amount = #{record.damageInsuranceAmount,jdbcType=INTEGER},
      glass = #{record.glass,jdbcType=TINYINT},
      tire = #{record.tire,jdbcType=TINYINT},
      third_party_insurance = #{record.thirdPartyInsurance,jdbcType=INTEGER},
      outage_fee = #{record.outageFee,jdbcType=TINYINT},
      outage_fee_ratio = #{record.outageFeeRatio,jdbcType=DECIMAL},
      outage_fee_amount = #{record.outageFeeAmount,jdbcType=INTEGER},
      depreciation = #{record.depreciation,jdbcType=TINYINT},
      depreciation_fee = #{record.depreciationFee,jdbcType=INTEGER},
      repair_fee_ratio = #{record.repairFeeRatio,jdbcType=DECIMAL},
      status = #{record.status,jdbcType=TINYINT},
      required = #{record.required,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.InsuranceServiceSetting">
    update insurance_service_setting
    <set>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="preset != null">
        preset = #{preset,jdbcType=TINYINT},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="damageInsurance != null">
        damage_insurance = #{damageInsurance,jdbcType=TINYINT},
      </if>
      <if test="damageInsuranceAmount != null">
        damage_insurance_amount = #{damageInsuranceAmount,jdbcType=INTEGER},
      </if>
      <if test="glass != null">
        glass = #{glass,jdbcType=TINYINT},
      </if>
      <if test="tire != null">
        tire = #{tire,jdbcType=TINYINT},
      </if>
      <if test="thirdPartyInsurance != null">
        third_party_insurance = #{thirdPartyInsurance,jdbcType=INTEGER},
      </if>
      <if test="outageFee != null">
        outage_fee = #{outageFee,jdbcType=TINYINT},
      </if>
      <if test="outageFeeRatio != null">
        outage_fee_ratio = #{outageFeeRatio,jdbcType=DECIMAL},
      </if>
      <if test="outageFeeAmount != null">
        outage_fee_amount = #{outageFeeAmount,jdbcType=INTEGER},
      </if>
      <if test="depreciation != null">
        depreciation = #{depreciation,jdbcType=TINYINT},
      </if>
      <if test="depreciationFee != null">
        depreciation_fee = #{depreciationFee,jdbcType=INTEGER},
      </if>
      <if test="repairFeeRatio != null">
        repair_fee_ratio = #{repairFeeRatio,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="required != null">
        required = #{required,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.InsuranceServiceSetting">
    update insurance_service_setting
    set store_id = #{storeId,jdbcType=BIGINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      preset = #{preset,jdbcType=TINYINT},
      parent_id = #{parentId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      damage_insurance = #{damageInsurance,jdbcType=TINYINT},
      damage_insurance_amount = #{damageInsuranceAmount,jdbcType=INTEGER},
      glass = #{glass,jdbcType=TINYINT},
      tire = #{tire,jdbcType=TINYINT},
      third_party_insurance = #{thirdPartyInsurance,jdbcType=INTEGER},
      outage_fee = #{outageFee,jdbcType=TINYINT},
      outage_fee_ratio = #{outageFeeRatio,jdbcType=DECIMAL},
      outage_fee_amount = #{outageFeeAmount,jdbcType=INTEGER},
      depreciation = #{depreciation,jdbcType=TINYINT},
      depreciation_fee = #{depreciationFee,jdbcType=INTEGER},
      repair_fee_ratio = #{repairFeeRatio,jdbcType=DECIMAL},
      status = #{status,jdbcType=TINYINT},
      required = #{required,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into insurance_service_setting
    (store_id, merchant_id, preset, parent_id, name, damage_insurance, damage_insurance_amount, 
      glass, tire, third_party_insurance, outage_fee, outage_fee_ratio, outage_fee_amount, 
      depreciation, depreciation_fee, repair_fee_ratio, status, required, last_ver, op_user_id, 
      create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.storeId,jdbcType=BIGINT}, #{item.merchantId,jdbcType=BIGINT}, #{item.preset,jdbcType=TINYINT}, 
        #{item.parentId,jdbcType=BIGINT}, #{item.name,jdbcType=VARCHAR}, #{item.damageInsurance,jdbcType=TINYINT}, 
        #{item.damageInsuranceAmount,jdbcType=INTEGER}, #{item.glass,jdbcType=TINYINT}, 
        #{item.tire,jdbcType=TINYINT}, #{item.thirdPartyInsurance,jdbcType=INTEGER}, #{item.outageFee,jdbcType=TINYINT}, 
        #{item.outageFeeRatio,jdbcType=DECIMAL}, #{item.outageFeeAmount,jdbcType=INTEGER}, 
        #{item.depreciation,jdbcType=TINYINT}, #{item.depreciationFee,jdbcType=INTEGER}, 
        #{item.repairFeeRatio,jdbcType=DECIMAL}, #{item.status,jdbcType=TINYINT}, #{item.required,jdbcType=TINYINT}, 
        #{item.lastVer,jdbcType=INTEGER}, #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into insurance_service_setting (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'preset'.toString() == column.value">
          #{item.preset,jdbcType=TINYINT}
        </if>
        <if test="'parent_id'.toString() == column.value">
          #{item.parentId,jdbcType=BIGINT}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'damage_insurance'.toString() == column.value">
          #{item.damageInsurance,jdbcType=TINYINT}
        </if>
        <if test="'damage_insurance_amount'.toString() == column.value">
          #{item.damageInsuranceAmount,jdbcType=INTEGER}
        </if>
        <if test="'glass'.toString() == column.value">
          #{item.glass,jdbcType=TINYINT}
        </if>
        <if test="'tire'.toString() == column.value">
          #{item.tire,jdbcType=TINYINT}
        </if>
        <if test="'third_party_insurance'.toString() == column.value">
          #{item.thirdPartyInsurance,jdbcType=INTEGER}
        </if>
        <if test="'outage_fee'.toString() == column.value">
          #{item.outageFee,jdbcType=TINYINT}
        </if>
        <if test="'outage_fee_ratio'.toString() == column.value">
          #{item.outageFeeRatio,jdbcType=DECIMAL}
        </if>
        <if test="'outage_fee_amount'.toString() == column.value">
          #{item.outageFeeAmount,jdbcType=INTEGER}
        </if>
        <if test="'depreciation'.toString() == column.value">
          #{item.depreciation,jdbcType=TINYINT}
        </if>
        <if test="'depreciation_fee'.toString() == column.value">
          #{item.depreciationFee,jdbcType=INTEGER}
        </if>
        <if test="'repair_fee_ratio'.toString() == column.value">
          #{item.repairFeeRatio,jdbcType=DECIMAL}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'required'.toString() == column.value">
          #{item.required,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
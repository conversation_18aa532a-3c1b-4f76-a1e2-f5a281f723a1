<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.FeiZhuVehicleMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.FeiZhuVehicle">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="feizhu_id" jdbcType="VARCHAR" property="feizhuId" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="vehicle_sery" jdbcType="VARCHAR" property="vehicleSery" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
    <result column="displacement" jdbcType="VARCHAR" property="displacement" />
    <result column="displacement_type" jdbcType="TINYINT" property="displacementType" />
    <result column="transmission_type" jdbcType="TINYINT" property="transmissionType" />
    <result column="door_num" jdbcType="SMALLINT" property="doorNum" />
    <result column="seat_num" jdbcType="SMALLINT" property="seatNum" />
    <result column="year" jdbcType="VARCHAR" property="year" />
    <result column="is_hybrid" jdbcType="TINYINT" property="isHybrid" />
    <result column="is_import" jdbcType="TINYINT" property="isImport" />
    <result column="is_convertible" jdbcType="TINYINT" property="isConvertible" />
    <result column="vehicle_type" jdbcType="SMALLINT" property="vehicleType" />
    <result column="car_tag" jdbcType="VARCHAR" property="carTag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, feizhu_id, brand_name, vehicle_sery, show_name, displacement, displacement_type, 
    transmission_type, door_num, seat_num, year, is_hybrid, is_import, is_convertible, 
    vehicle_type, car_tag
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.FeiZhuVehicleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from feizhu_vehicle
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from feizhu_vehicle
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from feizhu_vehicle
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.FeiZhuVehicleExample">
    delete from feizhu_vehicle
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.FeiZhuVehicle">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into feizhu_vehicle (feizhu_id, brand_name, vehicle_sery, 
      show_name, displacement, displacement_type, 
      transmission_type, door_num, seat_num, 
      year, is_hybrid, is_import, 
      is_convertible, vehicle_type, car_tag
      )
    values (#{feizhuId,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR}, #{vehicleSery,jdbcType=VARCHAR}, 
      #{showName,jdbcType=VARCHAR}, #{displacement,jdbcType=VARCHAR}, #{displacementType,jdbcType=TINYINT}, 
      #{transmissionType,jdbcType=TINYINT}, #{doorNum,jdbcType=SMALLINT}, #{seatNum,jdbcType=SMALLINT}, 
      #{year,jdbcType=VARCHAR}, #{isHybrid,jdbcType=TINYINT}, #{isImport,jdbcType=TINYINT}, 
      #{isConvertible,jdbcType=TINYINT}, #{vehicleType,jdbcType=SMALLINT}, #{carTag,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.FeiZhuVehicle">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into feizhu_vehicle
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="feizhuId != null">
        feizhu_id,
      </if>
      <if test="brandName != null">
        brand_name,
      </if>
      <if test="vehicleSery != null">
        vehicle_sery,
      </if>
      <if test="showName != null">
        show_name,
      </if>
      <if test="displacement != null">
        displacement,
      </if>
      <if test="displacementType != null">
        displacement_type,
      </if>
      <if test="transmissionType != null">
        transmission_type,
      </if>
      <if test="doorNum != null">
        door_num,
      </if>
      <if test="seatNum != null">
        seat_num,
      </if>
      <if test="year != null">
        year,
      </if>
      <if test="isHybrid != null">
        is_hybrid,
      </if>
      <if test="isImport != null">
        is_import,
      </if>
      <if test="isConvertible != null">
        is_convertible,
      </if>
      <if test="vehicleType != null">
        vehicle_type,
      </if>
      <if test="carTag != null">
        car_tag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="feizhuId != null">
        #{feizhuId,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSery != null">
        #{vehicleSery,jdbcType=VARCHAR},
      </if>
      <if test="showName != null">
        #{showName,jdbcType=VARCHAR},
      </if>
      <if test="displacement != null">
        #{displacement,jdbcType=VARCHAR},
      </if>
      <if test="displacementType != null">
        #{displacementType,jdbcType=TINYINT},
      </if>
      <if test="transmissionType != null">
        #{transmissionType,jdbcType=TINYINT},
      </if>
      <if test="doorNum != null">
        #{doorNum,jdbcType=SMALLINT},
      </if>
      <if test="seatNum != null">
        #{seatNum,jdbcType=SMALLINT},
      </if>
      <if test="year != null">
        #{year,jdbcType=VARCHAR},
      </if>
      <if test="isHybrid != null">
        #{isHybrid,jdbcType=TINYINT},
      </if>
      <if test="isImport != null">
        #{isImport,jdbcType=TINYINT},
      </if>
      <if test="isConvertible != null">
        #{isConvertible,jdbcType=TINYINT},
      </if>
      <if test="vehicleType != null">
        #{vehicleType,jdbcType=SMALLINT},
      </if>
      <if test="carTag != null">
        #{carTag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.FeiZhuVehicleExample" resultType="java.lang.Long">
    select count(*) from feizhu_vehicle
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update feizhu_vehicle
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.feizhuId != null">
        feizhu_id = #{record.feizhuId,jdbcType=VARCHAR},
      </if>
      <if test="record.brandName != null">
        brand_name = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleSery != null">
        vehicle_sery = #{record.vehicleSery,jdbcType=VARCHAR},
      </if>
      <if test="record.showName != null">
        show_name = #{record.showName,jdbcType=VARCHAR},
      </if>
      <if test="record.displacement != null">
        displacement = #{record.displacement,jdbcType=VARCHAR},
      </if>
      <if test="record.displacementType != null">
        displacement_type = #{record.displacementType,jdbcType=TINYINT},
      </if>
      <if test="record.transmissionType != null">
        transmission_type = #{record.transmissionType,jdbcType=TINYINT},
      </if>
      <if test="record.doorNum != null">
        door_num = #{record.doorNum,jdbcType=SMALLINT},
      </if>
      <if test="record.seatNum != null">
        seat_num = #{record.seatNum,jdbcType=SMALLINT},
      </if>
      <if test="record.year != null">
        year = #{record.year,jdbcType=VARCHAR},
      </if>
      <if test="record.isHybrid != null">
        is_hybrid = #{record.isHybrid,jdbcType=TINYINT},
      </if>
      <if test="record.isImport != null">
        is_import = #{record.isImport,jdbcType=TINYINT},
      </if>
      <if test="record.isConvertible != null">
        is_convertible = #{record.isConvertible,jdbcType=TINYINT},
      </if>
      <if test="record.vehicleType != null">
        vehicle_type = #{record.vehicleType,jdbcType=SMALLINT},
      </if>
      <if test="record.carTag != null">
        car_tag = #{record.carTag,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update feizhu_vehicle
    set id = #{record.id,jdbcType=BIGINT},
      feizhu_id = #{record.feizhuId,jdbcType=VARCHAR},
      brand_name = #{record.brandName,jdbcType=VARCHAR},
      vehicle_sery = #{record.vehicleSery,jdbcType=VARCHAR},
      show_name = #{record.showName,jdbcType=VARCHAR},
      displacement = #{record.displacement,jdbcType=VARCHAR},
      displacement_type = #{record.displacementType,jdbcType=TINYINT},
      transmission_type = #{record.transmissionType,jdbcType=TINYINT},
      door_num = #{record.doorNum,jdbcType=SMALLINT},
      seat_num = #{record.seatNum,jdbcType=SMALLINT},
      year = #{record.year,jdbcType=VARCHAR},
      is_hybrid = #{record.isHybrid,jdbcType=TINYINT},
      is_import = #{record.isImport,jdbcType=TINYINT},
      is_convertible = #{record.isConvertible,jdbcType=TINYINT},
      vehicle_type = #{record.vehicleType,jdbcType=SMALLINT},
      car_tag = #{record.carTag,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.FeiZhuVehicle">
    update feizhu_vehicle
    <set>
      <if test="feizhuId != null">
        feizhu_id = #{feizhuId,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        brand_name = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSery != null">
        vehicle_sery = #{vehicleSery,jdbcType=VARCHAR},
      </if>
      <if test="showName != null">
        show_name = #{showName,jdbcType=VARCHAR},
      </if>
      <if test="displacement != null">
        displacement = #{displacement,jdbcType=VARCHAR},
      </if>
      <if test="displacementType != null">
        displacement_type = #{displacementType,jdbcType=TINYINT},
      </if>
      <if test="transmissionType != null">
        transmission_type = #{transmissionType,jdbcType=TINYINT},
      </if>
      <if test="doorNum != null">
        door_num = #{doorNum,jdbcType=SMALLINT},
      </if>
      <if test="seatNum != null">
        seat_num = #{seatNum,jdbcType=SMALLINT},
      </if>
      <if test="year != null">
        year = #{year,jdbcType=VARCHAR},
      </if>
      <if test="isHybrid != null">
        is_hybrid = #{isHybrid,jdbcType=TINYINT},
      </if>
      <if test="isImport != null">
        is_import = #{isImport,jdbcType=TINYINT},
      </if>
      <if test="isConvertible != null">
        is_convertible = #{isConvertible,jdbcType=TINYINT},
      </if>
      <if test="vehicleType != null">
        vehicle_type = #{vehicleType,jdbcType=SMALLINT},
      </if>
      <if test="carTag != null">
        car_tag = #{carTag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.FeiZhuVehicle">
    update feizhu_vehicle
    set feizhu_id = #{feizhuId,jdbcType=VARCHAR},
      brand_name = #{brandName,jdbcType=VARCHAR},
      vehicle_sery = #{vehicleSery,jdbcType=VARCHAR},
      show_name = #{showName,jdbcType=VARCHAR},
      displacement = #{displacement,jdbcType=VARCHAR},
      displacement_type = #{displacementType,jdbcType=TINYINT},
      transmission_type = #{transmissionType,jdbcType=TINYINT},
      door_num = #{doorNum,jdbcType=SMALLINT},
      seat_num = #{seatNum,jdbcType=SMALLINT},
      year = #{year,jdbcType=VARCHAR},
      is_hybrid = #{isHybrid,jdbcType=TINYINT},
      is_import = #{isImport,jdbcType=TINYINT},
      is_convertible = #{isConvertible,jdbcType=TINYINT},
      vehicle_type = #{vehicleType,jdbcType=SMALLINT},
      car_tag = #{carTag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into feizhu_vehicle
    (feizhu_id, brand_name, vehicle_sery, show_name, displacement, displacement_type, 
      transmission_type, door_num, seat_num, year, is_hybrid, is_import, is_convertible, 
      vehicle_type, car_tag)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.feizhuId,jdbcType=VARCHAR}, #{item.brandName,jdbcType=VARCHAR}, #{item.vehicleSery,jdbcType=VARCHAR}, 
        #{item.showName,jdbcType=VARCHAR}, #{item.displacement,jdbcType=VARCHAR}, #{item.displacementType,jdbcType=TINYINT}, 
        #{item.transmissionType,jdbcType=TINYINT}, #{item.doorNum,jdbcType=SMALLINT}, #{item.seatNum,jdbcType=SMALLINT}, 
        #{item.year,jdbcType=VARCHAR}, #{item.isHybrid,jdbcType=TINYINT}, #{item.isImport,jdbcType=TINYINT}, 
        #{item.isConvertible,jdbcType=TINYINT}, #{item.vehicleType,jdbcType=SMALLINT}, 
        #{item.carTag,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into feizhu_vehicle (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'feizhu_id'.toString() == column.value">
          #{item.feizhuId,jdbcType=VARCHAR}
        </if>
        <if test="'brand_name'.toString() == column.value">
          #{item.brandName,jdbcType=VARCHAR}
        </if>
        <if test="'vehicle_sery'.toString() == column.value">
          #{item.vehicleSery,jdbcType=VARCHAR}
        </if>
        <if test="'show_name'.toString() == column.value">
          #{item.showName,jdbcType=VARCHAR}
        </if>
        <if test="'displacement'.toString() == column.value">
          #{item.displacement,jdbcType=VARCHAR}
        </if>
        <if test="'displacement_type'.toString() == column.value">
          #{item.displacementType,jdbcType=TINYINT}
        </if>
        <if test="'transmission_type'.toString() == column.value">
          #{item.transmissionType,jdbcType=TINYINT}
        </if>
        <if test="'door_num'.toString() == column.value">
          #{item.doorNum,jdbcType=SMALLINT}
        </if>
        <if test="'seat_num'.toString() == column.value">
          #{item.seatNum,jdbcType=SMALLINT}
        </if>
        <if test="'year'.toString() == column.value">
          #{item.year,jdbcType=VARCHAR}
        </if>
        <if test="'is_hybrid'.toString() == column.value">
          #{item.isHybrid,jdbcType=TINYINT}
        </if>
        <if test="'is_import'.toString() == column.value">
          #{item.isImport,jdbcType=TINYINT}
        </if>
        <if test="'is_convertible'.toString() == column.value">
          #{item.isConvertible,jdbcType=TINYINT}
        </if>
        <if test="'vehicle_type'.toString() == column.value">
          #{item.vehicleType,jdbcType=SMALLINT}
        </if>
        <if test="'car_tag'.toString() == column.value">
          #{item.carTag,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
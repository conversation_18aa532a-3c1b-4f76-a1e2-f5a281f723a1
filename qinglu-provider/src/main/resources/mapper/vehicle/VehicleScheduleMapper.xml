<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.VehicleScheduleMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.VehicleSchedule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="pickup_date" jdbcType="VARCHAR" property="pickupDate" />
    <result column="return_date" jdbcType="VARCHAR" property="returnDate" />
    <result column="schedule_status" jdbcType="TINYINT" property="scheduleStatus" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vehicle_id, vehicle_model_id, order_id, pickup_date, return_date, schedule_status, 
    deleted, last_ver, create_time, op_time, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.VehicleScheduleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_schedule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_schedule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_schedule
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.VehicleScheduleExample">
    delete from vehicle_schedule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.VehicleSchedule">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_schedule (vehicle_id, vehicle_model_id, order_id, 
      pickup_date, return_date, schedule_status, 
      deleted, last_ver, create_time, 
      op_time, op_user_id)
    values (#{vehicleId,jdbcType=BIGINT}, #{vehicleModelId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, 
      #{pickupDate,jdbcType=VARCHAR}, #{returnDate,jdbcType=VARCHAR}, #{scheduleStatus,jdbcType=TINYINT}, 
      #{deleted,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.VehicleSchedule">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_schedule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="pickupDate != null">
        pickup_date,
      </if>
      <if test="returnDate != null">
        return_date,
      </if>
      <if test="scheduleStatus != null">
        schedule_status,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="pickupDate != null">
        #{pickupDate,jdbcType=VARCHAR},
      </if>
      <if test="returnDate != null">
        #{returnDate,jdbcType=VARCHAR},
      </if>
      <if test="scheduleStatus != null">
        #{scheduleStatus,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.VehicleScheduleExample" resultType="java.lang.Long">
    select count(*) from vehicle_schedule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_schedule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.pickupDate != null">
        pickup_date = #{record.pickupDate,jdbcType=VARCHAR},
      </if>
      <if test="record.returnDate != null">
        return_date = #{record.returnDate,jdbcType=VARCHAR},
      </if>
      <if test="record.scheduleStatus != null">
        schedule_status = #{record.scheduleStatus,jdbcType=TINYINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_schedule
    set id = #{record.id,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      pickup_date = #{record.pickupDate,jdbcType=VARCHAR},
      return_date = #{record.returnDate,jdbcType=VARCHAR},
      schedule_status = #{record.scheduleStatus,jdbcType=TINYINT},
      deleted = #{record.deleted,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.VehicleSchedule">
    update vehicle_schedule
    <set>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="pickupDate != null">
        pickup_date = #{pickupDate,jdbcType=VARCHAR},
      </if>
      <if test="returnDate != null">
        return_date = #{returnDate,jdbcType=VARCHAR},
      </if>
      <if test="scheduleStatus != null">
        schedule_status = #{scheduleStatus,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.VehicleSchedule">
    update vehicle_schedule
    set vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      pickup_date = #{pickupDate,jdbcType=VARCHAR},
      return_date = #{returnDate,jdbcType=VARCHAR},
      schedule_status = #{scheduleStatus,jdbcType=TINYINT},
      deleted = #{deleted,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_schedule
    (vehicle_id, vehicle_model_id, order_id, pickup_date, return_date, schedule_status, 
      deleted, last_ver, create_time, op_time, op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.vehicleId,jdbcType=BIGINT}, #{item.vehicleModelId,jdbcType=BIGINT}, #{item.orderId,jdbcType=BIGINT}, 
        #{item.pickupDate,jdbcType=VARCHAR}, #{item.returnDate,jdbcType=VARCHAR}, #{item.scheduleStatus,jdbcType=TINYINT}, 
        #{item.deleted,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_schedule (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'vehicle_id'.toString() == column.value">
          #{item.vehicleId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=BIGINT}
        </if>
        <if test="'pickup_date'.toString() == column.value">
          #{item.pickupDate,jdbcType=VARCHAR}
        </if>
        <if test="'return_date'.toString() == column.value">
          #{item.returnDate,jdbcType=VARCHAR}
        </if>
        <if test="'schedule_status'.toString() == column.value">
          #{item.scheduleStatus,jdbcType=TINYINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
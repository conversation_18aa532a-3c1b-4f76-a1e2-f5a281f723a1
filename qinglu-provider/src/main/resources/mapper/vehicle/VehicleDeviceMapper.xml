<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.VehicleDeviceMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.VehicleDevice">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehilce_model_id" jdbcType="BIGINT" property="vehilceModelId" />
    <result column="third_vehicle_sery" jdbcType="VARCHAR" property="thirdVehicleSery" />
    <result column="third_vehicle_model" jdbcType="VARCHAR" property="thirdVehicleModel" />
    <result column="device_no" jdbcType="VARCHAR" property="deviceNo" />
    <result column="code_no" jdbcType="VARCHAR" property="codeNo" />
    <result column="bind_status" jdbcType="TINYINT" property="bindStatus" />
    <result column="intercept_status" jdbcType="TINYINT" property="interceptStatus" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, store_id, vehicle_id, vehilce_model_id, third_vehicle_sery, third_vehicle_model, 
    device_no, code_no, bind_status, intercept_status, deleted, create_time, create_user_id, 
    op_time, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.VehicleDeviceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_device
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_device
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_device
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.VehicleDeviceExample">
    delete from vehicle_device
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.VehicleDevice">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_device (merchant_id, store_id, vehicle_id, 
      vehilce_model_id, third_vehicle_sery, third_vehicle_model, 
      device_no, code_no, bind_status, 
      intercept_status, deleted, create_time, 
      create_user_id, op_time, op_user_id
      )
    values (#{merchantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, 
      #{vehilceModelId,jdbcType=BIGINT}, #{thirdVehicleSery,jdbcType=VARCHAR}, #{thirdVehicleModel,jdbcType=VARCHAR}, 
      #{deviceNo,jdbcType=VARCHAR}, #{codeNo,jdbcType=VARCHAR}, #{bindStatus,jdbcType=TINYINT}, 
      #{interceptStatus,jdbcType=TINYINT}, #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, 
      #{createUserId,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.VehicleDevice">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_device
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehilceModelId != null">
        vehilce_model_id,
      </if>
      <if test="thirdVehicleSery != null">
        third_vehicle_sery,
      </if>
      <if test="thirdVehicleModel != null">
        third_vehicle_model,
      </if>
      <if test="deviceNo != null">
        device_no,
      </if>
      <if test="codeNo != null">
        code_no,
      </if>
      <if test="bindStatus != null">
        bind_status,
      </if>
      <if test="interceptStatus != null">
        intercept_status,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehilceModelId != null">
        #{vehilceModelId,jdbcType=BIGINT},
      </if>
      <if test="thirdVehicleSery != null">
        #{thirdVehicleSery,jdbcType=VARCHAR},
      </if>
      <if test="thirdVehicleModel != null">
        #{thirdVehicleModel,jdbcType=VARCHAR},
      </if>
      <if test="deviceNo != null">
        #{deviceNo,jdbcType=VARCHAR},
      </if>
      <if test="codeNo != null">
        #{codeNo,jdbcType=VARCHAR},
      </if>
      <if test="bindStatus != null">
        #{bindStatus,jdbcType=TINYINT},
      </if>
      <if test="interceptStatus != null">
        #{interceptStatus,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.VehicleDeviceExample" resultType="java.lang.Long">
    select count(*) from vehicle_device
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_device
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="record.vehilceModelId != null">
        vehilce_model_id = #{record.vehilceModelId,jdbcType=BIGINT},
      </if>
      <if test="record.thirdVehicleSery != null">
        third_vehicle_sery = #{record.thirdVehicleSery,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdVehicleModel != null">
        third_vehicle_model = #{record.thirdVehicleModel,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceNo != null">
        device_no = #{record.deviceNo,jdbcType=VARCHAR},
      </if>
      <if test="record.codeNo != null">
        code_no = #{record.codeNo,jdbcType=VARCHAR},
      </if>
      <if test="record.bindStatus != null">
        bind_status = #{record.bindStatus,jdbcType=TINYINT},
      </if>
      <if test="record.interceptStatus != null">
        intercept_status = #{record.interceptStatus,jdbcType=TINYINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_device
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      vehilce_model_id = #{record.vehilceModelId,jdbcType=BIGINT},
      third_vehicle_sery = #{record.thirdVehicleSery,jdbcType=VARCHAR},
      third_vehicle_model = #{record.thirdVehicleModel,jdbcType=VARCHAR},
      device_no = #{record.deviceNo,jdbcType=VARCHAR},
      code_no = #{record.codeNo,jdbcType=VARCHAR},
      bind_status = #{record.bindStatus,jdbcType=TINYINT},
      intercept_status = #{record.interceptStatus,jdbcType=TINYINT},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      create_user_id = #{record.createUserId,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.VehicleDevice">
    update vehicle_device
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehilceModelId != null">
        vehilce_model_id = #{vehilceModelId,jdbcType=BIGINT},
      </if>
      <if test="thirdVehicleSery != null">
        third_vehicle_sery = #{thirdVehicleSery,jdbcType=VARCHAR},
      </if>
      <if test="thirdVehicleModel != null">
        third_vehicle_model = #{thirdVehicleModel,jdbcType=VARCHAR},
      </if>
      <if test="deviceNo != null">
        device_no = #{deviceNo,jdbcType=VARCHAR},
      </if>
      <if test="codeNo != null">
        code_no = #{codeNo,jdbcType=VARCHAR},
      </if>
      <if test="bindStatus != null">
        bind_status = #{bindStatus,jdbcType=TINYINT},
      </if>
      <if test="interceptStatus != null">
        intercept_status = #{interceptStatus,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.VehicleDevice">
    update vehicle_device
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehilce_model_id = #{vehilceModelId,jdbcType=BIGINT},
      third_vehicle_sery = #{thirdVehicleSery,jdbcType=VARCHAR},
      third_vehicle_model = #{thirdVehicleModel,jdbcType=VARCHAR},
      device_no = #{deviceNo,jdbcType=VARCHAR},
      code_no = #{codeNo,jdbcType=VARCHAR},
      bind_status = #{bindStatus,jdbcType=TINYINT},
      intercept_status = #{interceptStatus,jdbcType=TINYINT},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      create_user_id = #{createUserId,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_device
    (merchant_id, store_id, vehicle_id, vehilce_model_id, third_vehicle_sery, third_vehicle_model, 
      device_no, code_no, bind_status, intercept_status, deleted, create_time, create_user_id, 
      op_time, op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.vehicleId,jdbcType=BIGINT}, 
        #{item.vehilceModelId,jdbcType=BIGINT}, #{item.thirdVehicleSery,jdbcType=VARCHAR}, 
        #{item.thirdVehicleModel,jdbcType=VARCHAR}, #{item.deviceNo,jdbcType=VARCHAR}, 
        #{item.codeNo,jdbcType=VARCHAR}, #{item.bindStatus,jdbcType=TINYINT}, #{item.interceptStatus,jdbcType=TINYINT}, 
        #{item.deleted,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT}, #{item.createUserId,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_device (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_id'.toString() == column.value">
          #{item.vehicleId,jdbcType=BIGINT}
        </if>
        <if test="'vehilce_model_id'.toString() == column.value">
          #{item.vehilceModelId,jdbcType=BIGINT}
        </if>
        <if test="'third_vehicle_sery'.toString() == column.value">
          #{item.thirdVehicleSery,jdbcType=VARCHAR}
        </if>
        <if test="'third_vehicle_model'.toString() == column.value">
          #{item.thirdVehicleModel,jdbcType=VARCHAR}
        </if>
        <if test="'device_no'.toString() == column.value">
          #{item.deviceNo,jdbcType=VARCHAR}
        </if>
        <if test="'code_no'.toString() == column.value">
          #{item.codeNo,jdbcType=VARCHAR}
        </if>
        <if test="'bind_status'.toString() == column.value">
          #{item.bindStatus,jdbcType=TINYINT}
        </if>
        <if test="'intercept_status'.toString() == column.value">
          #{item.interceptStatus,jdbcType=TINYINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'create_user_id'.toString() == column.value">
          #{item.createUserId,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
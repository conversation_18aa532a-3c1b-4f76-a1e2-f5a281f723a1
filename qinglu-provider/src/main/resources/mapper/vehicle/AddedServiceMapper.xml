<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.AddedServiceMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.AddedService">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="added_service_setting_id" jdbcType="BIGINT" property="addedServiceSettingId" />
    <result column="rent_base_id" jdbcType="BIGINT" property="rentBaseId" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="on_charge" jdbcType="TINYINT" property="onCharge" />
    <result column="price" jdbcType="INTEGER" property="price" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, added_service_setting_id, rent_base_id, vehicle_model_id, store_id, on_charge,
    price, status, last_ver, create_time, op_time, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.AddedServiceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from added_service
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from added_service
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from added_service
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.AddedServiceExample">
    delete from added_service
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.AddedService">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into added_service (added_service_setting_id, rent_base_id,
      vehicle_model_id, store_id, on_charge,
      price, status, last_ver,
      create_time, op_time, op_user_id
      )
    values (#{addedServiceSettingId,jdbcType=BIGINT}, #{rentBaseId,jdbcType=BIGINT},
      #{vehicleModelId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{onCharge,jdbcType=TINYINT},
      #{price,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER},
      #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.AddedService">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into added_service
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addedServiceSettingId != null">
        added_service_setting_id,
      </if>
      <if test="rentBaseId != null">
        rent_base_id,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="onCharge != null">
        on_charge,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addedServiceSettingId != null">
        #{addedServiceSettingId,jdbcType=BIGINT},
      </if>
      <if test="rentBaseId != null">
        #{rentBaseId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="onCharge != null">
        #{onCharge,jdbcType=TINYINT},
      </if>
      <if test="price != null">
        #{price,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.AddedServiceExample" resultType="java.lang.Long">
    select count(*) from added_service
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update added_service
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.addedServiceSettingId != null">
        added_service_setting_id = #{record.addedServiceSettingId,jdbcType=BIGINT},
      </if>
      <if test="record.rentBaseId != null">
        rent_base_id = #{record.rentBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.onCharge != null">
        on_charge = #{record.onCharge,jdbcType=TINYINT},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update added_service
    set id = #{record.id,jdbcType=BIGINT},
      added_service_setting_id = #{record.addedServiceSettingId,jdbcType=BIGINT},
      rent_base_id = #{record.rentBaseId,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      on_charge = #{record.onCharge,jdbcType=TINYINT},
      price = #{record.price,jdbcType=INTEGER},
      status = #{record.status,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.AddedService">
    update added_service
    <set>
      <if test="addedServiceSettingId != null">
        added_service_setting_id = #{addedServiceSettingId,jdbcType=BIGINT},
      </if>
      <if test="rentBaseId != null">
        rent_base_id = #{rentBaseId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="onCharge != null">
        on_charge = #{onCharge,jdbcType=TINYINT},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.AddedService">
    update added_service
    set added_service_setting_id = #{addedServiceSettingId,jdbcType=BIGINT},
      rent_base_id = #{rentBaseId,jdbcType=BIGINT},
      vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      on_charge = #{onCharge,jdbcType=TINYINT},
      price = #{price,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into added_service
    (added_service_setting_id, rent_base_id, vehicle_model_id, store_id, on_charge, price,
      status, last_ver, create_time, op_time, op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addedServiceSettingId,jdbcType=BIGINT}, #{item.rentBaseId,jdbcType=BIGINT},
        #{item.vehicleModelId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.onCharge,jdbcType=TINYINT},
        #{item.price,jdbcType=INTEGER}, #{item.status,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER},
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into added_service (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'added_service_setting_id'.toString() == column.value">
          #{item.addedServiceSettingId,jdbcType=BIGINT}
        </if>
        <if test="'rent_base_id'.toString() == column.value">
          #{item.rentBaseId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'on_charge'.toString() == column.value">
          #{item.onCharge,jdbcType=TINYINT}
        </if>
        <if test="'price'.toString() == column.value">
          #{item.price,jdbcType=INTEGER}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>

  <select id="getModelAndServiceNum" resultType="java.util.HashMap">
    select vehicle_model_id as 'key', count(*) as 'value'
    from added_service
    where store_id = #{storeId,jdbcType=BIGINT}
    and status = 1 and price > 0
    <if test="vehicleModelIdList!=null">
      and  vehicle_model_id in
      <foreach item="item" collection="vehicleModelIdList" separator="," open="(" close=")" index="">
        #{item, jdbcType=BIGINT}
      </foreach>
    </if>
    <if test="addedServiceIdList!=null">
      and  added_service_setting_id in
      <foreach item="item" collection="addedServiceIdList" separator="," open="(" close=")" index="">
        #{item, jdbcType=BIGINT}
      </foreach>
    </if>
    group by vehicle_model_id
  </select>

  <select id="getModelAndServiceNumChannel" resultType="java.util.HashMap">
    select vehicle_model_id as 'key', count(*) as 'value'
    from added_service_channel
    where store_id = #{storeId,jdbcType=BIGINT}
    and channel = #{channelId,jdbcType=BIGINT}
    and status = 1 and price > 0
    <if test="vehicleModelIdList!=null">
      and  vehicle_model_id in
      <foreach item="item" collection="vehicleModelIdList" separator="," open="(" close=")" index="">
        #{item, jdbcType=BIGINT}
      </foreach>
    </if>
    <if test="addedServiceSettingIds!=null">
      and  added_service_setting_id in
      <foreach item="item" collection="addedServiceSettingIds" separator="," open="(" close=")" index="">
        #{item, jdbcType=BIGINT}
      </foreach>
    </if>
    group by vehicle_model_id
  </select>
  <update id="batchUpdate"  keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <foreach collection="list" item="item" separator=";">
      update added_service
      <set>
        <if test="item.addedServiceSettingId != null">
          added_service_setting_id = #{item.addedServiceSettingId,jdbcType=BIGINT},
        </if>
        <if test="item.rentBaseId != null">
          rent_base_id = #{item.rentBaseId,jdbcType=BIGINT},
        </if>
        <if test="item.vehicleModelId != null">
          vehicle_model_id = #{item.vehicleModelId,jdbcType=BIGINT},
        </if>
        <if test="item.storeId != null">
          store_id = #{item.storeId,jdbcType=BIGINT},
        </if>
        <if test="item.onCharge != null">
          on_charge = #{item.onCharge,jdbcType=TINYINT},
        </if>
        <if test="item.price != null">
          price = #{item.price,jdbcType=INTEGER},
        </if>
        <if test="item.status != null">
          status = #{item.status,jdbcType=TINYINT},
        </if>
        <if test="item.lastVer != null">
          last_ver = #{item.lastVer,jdbcType=INTEGER},
        </if>
        <if test="item.createTime != null">
          create_time = #{item.createTime,jdbcType=BIGINT},
        </if>
        <if test="item.opTime != null">
          op_time = #{item.opTime,jdbcType=BIGINT},
        </if>
        <if test="item.opUserId != null">
          op_user_id = #{item.opUserId,jdbcType=BIGINT},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>

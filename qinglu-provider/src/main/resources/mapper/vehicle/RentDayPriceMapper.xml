<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.RentDayPriceMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.RentDayPrice">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="rent_date" jdbcType="DATE" property="rentDate" />
    <result column="price" jdbcType="INTEGER" property="price" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, store_id, channel_id, vehicle_model_id, rent_date, price, deleted, 
    last_ver, create_time, op_time, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.RentDayPriceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from rent_day_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from rent_day_price
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from rent_day_price
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.RentDayPriceExample">
    delete from rent_day_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.RentDayPrice">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into rent_day_price (merchant_id, store_id, channel_id, 
      vehicle_model_id, rent_date, price, 
      deleted, last_ver, create_time, 
      op_time, op_user_id)
    values (#{merchantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{channelId,jdbcType=BIGINT}, 
      #{vehicleModelId,jdbcType=BIGINT}, #{rentDate,jdbcType=DATE}, #{price,jdbcType=INTEGER}, 
      #{deleted,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.RentDayPrice">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into rent_day_price
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="rentDate != null">
        rent_date,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="rentDate != null">
        #{rentDate,jdbcType=DATE},
      </if>
      <if test="price != null">
        #{price,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.RentDayPriceExample" resultType="java.lang.Long">
    select count(*) from rent_day_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update rent_day_price
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.rentDate != null">
        rent_date = #{record.rentDate,jdbcType=DATE},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update rent_day_price
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      channel_id = #{record.channelId,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      rent_date = #{record.rentDate,jdbcType=DATE},
      price = #{record.price,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.RentDayPrice">
    update rent_day_price
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="rentDate != null">
        rent_date = #{rentDate,jdbcType=DATE},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.RentDayPrice">
    update rent_day_price
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      channel_id = #{channelId,jdbcType=BIGINT},
      vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      rent_date = #{rentDate,jdbcType=DATE},
      price = #{price,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into rent_day_price
    (merchant_id, store_id, channel_id, vehicle_model_id, rent_date, price, deleted, 
      last_ver, create_time, op_time, op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.channelId,jdbcType=BIGINT}, 
        #{item.vehicleModelId,jdbcType=BIGINT}, #{item.rentDate,jdbcType=DATE}, #{item.price,jdbcType=INTEGER}, 
        #{item.deleted,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into rent_day_price (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'channel_id'.toString() == column.value">
          #{item.channelId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'rent_date'.toString() == column.value">
          #{item.rentDate,jdbcType=DATE}
        </if>
        <if test="'price'.toString() == column.value">
          #{item.price,jdbcType=INTEGER}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>


  <update id="batchUpdate" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->

    <foreach collection="list" item="item" separator=";">
      update rent_day_price
      <set>
        <if test="item.merchantId != null">
          merchant_id = #{item.merchantId,jdbcType=BIGINT},
        </if>
        <if test="item.storeId != null">
          store_id = #{item.storeId,jdbcType=BIGINT},
        </if>
        <if test="item.channelId != null">
          channel_id = #{item.channelId,jdbcType=BIGINT},
        </if>
        <if test="item.vehicleModelId != null">
          vehicle_model_id = #{item.vehicleModelId,jdbcType=BIGINT},
        </if>
        <if test="item.rentDate != null">
          rent_date = #{item.rentDate,jdbcType=DATE},
        </if>
        <if test="item.price != null">
          price = #{item.price,jdbcType=INTEGER},
        </if>
        <if test="item.deleted != null">
          deleted = #{item.deleted,jdbcType=TINYINT},
        </if>
        <if test="item.lastVer != null">
          last_ver = #{item.lastVer,jdbcType=INTEGER},
        </if>
        <if test="item.createTime != null">
          create_time = #{item.createTime,jdbcType=BIGINT},
        </if>
        <if test="item.opTime != null">
          op_time = #{item.opTime,jdbcType=BIGINT},
        </if>
        <if test="item.opUserId != null">
          op_user_id = #{item.opUserId,jdbcType=BIGINT},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>
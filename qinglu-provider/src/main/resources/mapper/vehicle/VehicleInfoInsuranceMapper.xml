<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.VehicleInfoInsuranceMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.VehicleInfoInsurance">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="insurance_type" jdbcType="TINYINT" property="insuranceType" />
    <result column="insurance_company" jdbcType="VARCHAR" property="insuranceCompany" />
    <result column="insurance_num" jdbcType="VARCHAR" property="insuranceNum" />
    <result column="insurance_holder" jdbcType="VARCHAR" property="insuranceHolder" />
    <result column="insurance_idcard" jdbcType="VARCHAR" property="insuranceIdcard" />
    <result column="insurance_start_date" jdbcType="VARCHAR" property="insuranceStartDate" />
    <result column="insurance_end_date" jdbcType="VARCHAR" property="insuranceEndDate" />
    <result column="start_date" jdbcType="BIGINT" property="startDate" />
    <result column="end_date" jdbcType="BIGINT" property="endDate" />
    <result column="third_insurance_type" jdbcType="TINYINT" property="thirdInsuranceType" />
    <result column="damage_amount" jdbcType="INTEGER" property="damageAmount" />
    <result column="frame_num" jdbcType="VARCHAR" property="frameNum" />
    <result column="engine_num" jdbcType="VARCHAR" property="engineNum" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, store_id, vehicle_id, insurance_type, insurance_company, insurance_num, insurance_holder, 
    insurance_idcard, insurance_start_date, insurance_end_date, start_date, end_date,
    third_insurance_type, damage_amount, frame_num, engine_num, last_ver, deleted, create_time,
    op_time, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.VehicleInfoInsuranceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_info_insurance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_info_insurance
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.VehicleInfoInsurance">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_info_insurance (store_id, vehicle_id, insurance_type, 
      insurance_company, insurance_num, insurance_holder, 
      insurance_idcard, insurance_start_date, insurance_end_date, 
      start_date, end_date, third_insurance_type,
      damage_amount, frame_num, engine_num,
      last_ver, deleted, create_time,
      op_time, op_user_id)
    values (#{storeId,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, #{insuranceType,jdbcType=TINYINT}, 
      #{insuranceCompany,jdbcType=VARCHAR}, #{insuranceNum,jdbcType=VARCHAR}, #{insuranceHolder,jdbcType=VARCHAR}, 
      #{insuranceIdcard,jdbcType=VARCHAR}, #{insuranceStartDate,jdbcType=VARCHAR}, #{insuranceEndDate,jdbcType=VARCHAR}, 
      #{startDate,jdbcType=BIGINT}, #{endDate,jdbcType=BIGINT}, #{thirdInsuranceType,jdbcType=TINYINT},
      #{damageAmount,jdbcType=INTEGER}, #{frameNum,jdbcType=VARCHAR}, #{engineNum,jdbcType=VARCHAR},
      #{lastVer,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT},
      #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.VehicleInfoInsurance">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_info_insurance
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        store_id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="insuranceType != null">
        insurance_type,
      </if>
      <if test="insuranceCompany != null">
        insurance_company,
      </if>
      <if test="insuranceNum != null">
        insurance_num,
      </if>
      <if test="insuranceHolder != null">
        insurance_holder,
      </if>
      <if test="insuranceIdcard != null">
        insurance_idcard,
      </if>
      <if test="insuranceStartDate != null">
        insurance_start_date,
      </if>
      <if test="insuranceEndDate != null">
        insurance_end_date,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="thirdInsuranceType != null">
        third_insurance_type,
      </if>
      <if test="damageAmount != null">
        damage_amount,
      </if>
      <if test="frameNum != null">
        frame_num,
      </if>
      <if test="engineNum != null">
        engine_num,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="insuranceType != null">
        #{insuranceType,jdbcType=TINYINT},
      </if>
      <if test="insuranceCompany != null">
        #{insuranceCompany,jdbcType=VARCHAR},
      </if>
      <if test="insuranceNum != null">
        #{insuranceNum,jdbcType=VARCHAR},
      </if>
      <if test="insuranceHolder != null">
        #{insuranceHolder,jdbcType=VARCHAR},
      </if>
      <if test="insuranceIdcard != null">
        #{insuranceIdcard,jdbcType=VARCHAR},
      </if>
      <if test="insuranceStartDate != null">
        #{insuranceStartDate,jdbcType=VARCHAR},
      </if>
      <if test="insuranceEndDate != null">
        #{insuranceEndDate,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=BIGINT},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=BIGINT},
      </if>
      <if test="thirdInsuranceType != null">
        #{thirdInsuranceType,jdbcType=TINYINT},
      </if>
      <if test="damageAmount != null">
        #{damageAmount,jdbcType=INTEGER},
      </if>
      <if test="frameNum != null">
        #{frameNum,jdbcType=VARCHAR},
      </if>
      <if test="engineNum != null">
        #{engineNum,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.VehicleInfoInsuranceExample" resultType="java.lang.Long">
    select count(*) from vehicle_info_insurance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_info_insurance
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="record.insuranceType != null">
        insurance_type = #{record.insuranceType,jdbcType=TINYINT},
      </if>
      <if test="record.insuranceCompany != null">
        insurance_company = #{record.insuranceCompany,jdbcType=VARCHAR},
      </if>
      <if test="record.insuranceNum != null">
        insurance_num = #{record.insuranceNum,jdbcType=VARCHAR},
      </if>
      <if test="record.insuranceHolder != null">
        insurance_holder = #{record.insuranceHolder,jdbcType=VARCHAR},
      </if>
      <if test="record.insuranceIdcard != null">
        insurance_idcard = #{record.insuranceIdcard,jdbcType=VARCHAR},
      </if>
      <if test="record.insuranceStartDate != null">
        insurance_start_date = #{record.insuranceStartDate,jdbcType=VARCHAR},
      </if>
      <if test="record.insuranceEndDate != null">
        insurance_end_date = #{record.insuranceEndDate,jdbcType=VARCHAR},
      </if>
      <if test="record.startDate != null">
        start_date = #{record.startDate,jdbcType=BIGINT},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=BIGINT},
      </if>
      <if test="record.thirdInsuranceType != null">
        third_insurance_type = #{record.thirdInsuranceType,jdbcType=TINYINT},
      </if>
      <if test="record.damageAmount != null">
        damage_amount = #{record.damageAmount,jdbcType=INTEGER},
      </if>
      <if test="record.frameNum != null">
        frame_num = #{record.frameNum,jdbcType=VARCHAR},
      </if>
      <if test="record.engineNum != null">
        engine_num = #{record.engineNum,jdbcType=VARCHAR},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_info_insurance
    set id = #{record.id,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      insurance_type = #{record.insuranceType,jdbcType=TINYINT},
      insurance_company = #{record.insuranceCompany,jdbcType=VARCHAR},
      insurance_num = #{record.insuranceNum,jdbcType=VARCHAR},
      insurance_holder = #{record.insuranceHolder,jdbcType=VARCHAR},
      insurance_idcard = #{record.insuranceIdcard,jdbcType=VARCHAR},
      insurance_start_date = #{record.insuranceStartDate,jdbcType=VARCHAR},
      insurance_end_date = #{record.insuranceEndDate,jdbcType=VARCHAR},
      start_date = #{record.startDate,jdbcType=BIGINT},
      end_date = #{record.endDate,jdbcType=BIGINT},
      third_insurance_type = #{record.thirdInsuranceType,jdbcType=TINYINT},
      damage_amount = #{record.damageAmount,jdbcType=INTEGER},
      frame_num = #{record.frameNum,jdbcType=VARCHAR},
      engine_num = #{record.engineNum,jdbcType=VARCHAR},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.VehicleInfoInsurance">
    update vehicle_info_insurance
    <set>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="insuranceType != null">
        insurance_type = #{insuranceType,jdbcType=TINYINT},
      </if>
      <if test="insuranceCompany != null">
        insurance_company = #{insuranceCompany,jdbcType=VARCHAR},
      </if>
      <if test="insuranceNum != null">
        insurance_num = #{insuranceNum,jdbcType=VARCHAR},
      </if>
      <if test="insuranceHolder != null">
        insurance_holder = #{insuranceHolder,jdbcType=VARCHAR},
      </if>
      <if test="insuranceIdcard != null">
        insurance_idcard = #{insuranceIdcard,jdbcType=VARCHAR},
      </if>
      <if test="insuranceStartDate != null">
        insurance_start_date = #{insuranceStartDate,jdbcType=VARCHAR},
      </if>
      <if test="insuranceEndDate != null">
        insurance_end_date = #{insuranceEndDate,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=BIGINT},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=BIGINT},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=BIGINT},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=BIGINT},
      </if>
      <if test="thirdInsuranceType != null">
        third_insurance_type = #{thirdInsuranceType,jdbcType=TINYINT},
      </if>
      <if test="damageAmount != null">
        damage_amount = #{damageAmount,jdbcType=INTEGER},
      </if>
      <if test="frameNum != null">
        frame_num = #{frameNum,jdbcType=VARCHAR},
      </if>
      <if test="engineNum != null">
        engine_num = #{engineNum,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.VehicleInfoInsurance">
    update vehicle_info_insurance
    set store_id = #{storeId,jdbcType=BIGINT},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      insurance_type = #{insuranceType,jdbcType=TINYINT},
      insurance_company = #{insuranceCompany,jdbcType=VARCHAR},
      insurance_num = #{insuranceNum,jdbcType=VARCHAR},
      insurance_holder = #{insuranceHolder,jdbcType=VARCHAR},
      insurance_idcard = #{insuranceIdcard,jdbcType=VARCHAR},
      insurance_start_date = #{insuranceStartDate,jdbcType=VARCHAR},
      insurance_end_date = #{insuranceEndDate,jdbcType=VARCHAR},
      start_date = #{startDate,jdbcType=BIGINT},
      end_date = #{endDate,jdbcType=BIGINT},
      third_insurance_type = #{thirdInsuranceType,jdbcType=TINYINT},
      damage_amount = #{damageAmount,jdbcType=INTEGER},
      frame_num = #{frameNum,jdbcType=VARCHAR},
      engine_num = #{engineNum,jdbcType=VARCHAR},
      last_ver = #{lastVer,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_info_insurance
    (store_id, vehicle_id, insurance_type, insurance_company, insurance_num, insurance_holder, 
      insurance_idcard, insurance_start_date, insurance_end_date, start_date, end_date,
      third_insurance_type, damage_amount, frame_num, engine_num, last_ver, deleted,
      create_time, op_time, op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.storeId,jdbcType=BIGINT}, #{item.vehicleId,jdbcType=BIGINT}, #{item.insuranceType,jdbcType=TINYINT}, 
        #{item.insuranceCompany,jdbcType=VARCHAR}, #{item.insuranceNum,jdbcType=VARCHAR}, 
        #{item.insuranceHolder,jdbcType=VARCHAR}, #{item.insuranceIdcard,jdbcType=VARCHAR}, 
        #{item.insuranceStartDate,jdbcType=VARCHAR}, #{item.insuranceEndDate,jdbcType=VARCHAR}, 
        #{item.startDate,jdbcType=BIGINT}, #{item.endDate,jdbcType=BIGINT}, #{item.thirdInsuranceType,jdbcType=TINYINT},
        #{item.damageAmount,jdbcType=INTEGER}, #{item.frameNum,jdbcType=VARCHAR}, #{item.engineNum,jdbcType=VARCHAR},
        #{item.lastVer,jdbcType=INTEGER}, #{item.deleted,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_info_insurance (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_id'.toString() == column.value">
          #{item.vehicleId,jdbcType=BIGINT}
        </if>
        <if test="'insurance_type'.toString() == column.value">
          #{item.insuranceType,jdbcType=TINYINT}
        </if>
        <if test="'insurance_company'.toString() == column.value">
          #{item.insuranceCompany,jdbcType=VARCHAR}
        </if>
        <if test="'insurance_num'.toString() == column.value">
          #{item.insuranceNum,jdbcType=VARCHAR}
        </if>
        <if test="'insurance_holder'.toString() == column.value">
          #{item.insuranceHolder,jdbcType=VARCHAR}
        </if>
        <if test="'insurance_idcard'.toString() == column.value">
          #{item.insuranceIdcard,jdbcType=VARCHAR}
        </if>
        <if test="'insurance_start_date'.toString() == column.value">
          #{item.insuranceStartDate,jdbcType=VARCHAR}
        </if>
        <if test="'insurance_end_date'.toString() == column.value">
          #{item.insuranceEndDate,jdbcType=VARCHAR}
        </if>
        <if test="'start_date'.toString() == column.value">
          #{item.startDate,jdbcType=BIGINT}
        </if>
        <if test="'end_date'.toString() == column.value">
          #{item.endDate,jdbcType=BIGINT}
        </if>
        <if test="'third_insurance_type'.toString() == column.value">
          #{item.thirdInsuranceType,jdbcType=TINYINT}
        </if>
        <if test="'damage_amount'.toString() == column.value">
          #{item.damageAmount,jdbcType=INTEGER}
        </if>
        <if test="'frame_num'.toString() == column.value">
          #{item.frameNum,jdbcType=VARCHAR}
        </if>
        <if test="'engine_num'.toString() == column.value">
          #{item.engineNum,jdbcType=VARCHAR}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.FeizhuVehicleSubSeryMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.FeizhuVehicleSubSery">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="feizhu_id" jdbcType="VARCHAR" property="feizhuId" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="vehicle_sery" jdbcType="VARCHAR" property="vehicleSery" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
    <result column="displacement" jdbcType="VARCHAR" property="displacement" />
    <result column="displacement_type" jdbcType="TINYINT" property="displacementType" />
    <result column="transmission_type" jdbcType="TINYINT" property="transmissionType" />
    <result column="door_num" jdbcType="SMALLINT" property="doorNum" />
    <result column="seat_num" jdbcType="SMALLINT" property="seatNum" />
    <result column="year" jdbcType="VARCHAR" property="year" />
    <result column="is_hybrid" jdbcType="TINYINT" property="isHybrid" />
    <result column="is_import" jdbcType="TINYINT" property="isImport" />
    <result column="is_convertible" jdbcType="TINYINT" property="isConvertible" />
    <result column="vehicle_type" jdbcType="SMALLINT" property="vehicleType" />
    <result column="car_tag" jdbcType="VARCHAR" property="carTag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, feizhu_id, brand_name, vehicle_sery, show_name, displacement, displacement_type,
    transmission_type, door_num, seat_num, year, is_hybrid, is_import, is_convertible,
    vehicle_type, car_tag
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.FeizhuVehicleSubSeryExample"
    resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from feizhu_vehicle_sub_sery
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from feizhu_vehicle_sub_sery
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.FeizhuVehicleSubSeryExample"
    resultType="java.lang.Long">
    select count(*) from feizhu_vehicle_sub_sery
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <select id="searchByContent" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from feizhu_vehicle_sub_sery
    where brand_name like CONCAT('%', #{searchContent}, '%')
    or vehicle_sery like CONCAT('%', #{searchContent}, '%')
  </select>
</mapper>
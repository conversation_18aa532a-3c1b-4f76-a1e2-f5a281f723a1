<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.RentChannelPriceMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.RentChannelPrice">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="rent_base_id" jdbcType="BIGINT" property="rentBaseId" />
    <result column="rent_calendar_id" jdbcType="BIGINT" property="rentCalendarId" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="price" jdbcType="INTEGER" property="price" />
    <result column="price_weekend" jdbcType="INTEGER" property="priceWeekend" />
    <result column="latest_booking_time" jdbcType="DECIMAL" property="latestBookingTime" />
    <result column="least_rental_time" jdbcType="SMALLINT" property="leastRentalTime" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="latest_booking_unit" jdbcType="TINYINT" property="latestBookingUnit" />
    <result column="price_monday_sunday" jdbcType="VARCHAR" property="priceMondaySunday" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, rent_base_id, rent_calendar_id, merchant_id, store_id, vehicle_model_id, price, 
    price_weekend, latest_booking_time, least_rental_time, channel_id, deleted, last_ver, 
    create_time, op_time, op_user_id, latest_booking_unit, price_monday_sunday
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.RentChannelPriceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from rent_channel_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from rent_channel_price
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from rent_channel_price
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.RentChannelPriceExample">
    delete from rent_channel_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.RentChannelPrice">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into rent_channel_price (rent_base_id, rent_calendar_id, merchant_id, 
      store_id, vehicle_model_id, price, 
      price_weekend, latest_booking_time, least_rental_time, 
      channel_id, deleted, last_ver, 
      create_time, op_time, op_user_id, 
      latest_booking_unit, price_monday_sunday)
    values (#{rentBaseId,jdbcType=BIGINT}, #{rentCalendarId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, 
      #{storeId,jdbcType=BIGINT}, #{vehicleModelId,jdbcType=BIGINT}, #{price,jdbcType=INTEGER}, 
      #{priceWeekend,jdbcType=INTEGER}, #{latestBookingTime,jdbcType=DECIMAL}, #{leastRentalTime,jdbcType=SMALLINT}, 
      #{channelId,jdbcType=BIGINT}, #{deleted,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER}, 
      #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}, 
      #{latestBookingUnit,jdbcType=TINYINT}, #{priceMondaySunday,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.RentChannelPrice">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into rent_channel_price
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="rentBaseId != null">
        rent_base_id,
      </if>
      <if test="rentCalendarId != null">
        rent_calendar_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="priceWeekend != null">
        price_weekend,
      </if>
      <if test="latestBookingTime != null">
        latest_booking_time,
      </if>
      <if test="leastRentalTime != null">
        least_rental_time,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="latestBookingUnit != null">
        latest_booking_unit,
      </if>
      <if test="priceMondaySunday != null">
        price_monday_sunday,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="rentBaseId != null">
        #{rentBaseId,jdbcType=BIGINT},
      </if>
      <if test="rentCalendarId != null">
        #{rentCalendarId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="price != null">
        #{price,jdbcType=INTEGER},
      </if>
      <if test="priceWeekend != null">
        #{priceWeekend,jdbcType=INTEGER},
      </if>
      <if test="latestBookingTime != null">
        #{latestBookingTime,jdbcType=DECIMAL},
      </if>
      <if test="leastRentalTime != null">
        #{leastRentalTime,jdbcType=SMALLINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="latestBookingUnit != null">
        #{latestBookingUnit,jdbcType=TINYINT},
      </if>
      <if test="priceMondaySunday != null">
        #{priceMondaySunday,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.RentChannelPriceExample" resultType="java.lang.Long">
    select count(*) from rent_channel_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update rent_channel_price
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.rentBaseId != null">
        rent_base_id = #{record.rentBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.rentCalendarId != null">
        rent_calendar_id = #{record.rentCalendarId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=INTEGER},
      </if>
      <if test="record.priceWeekend != null">
        price_weekend = #{record.priceWeekend,jdbcType=INTEGER},
      </if>
      <if test="record.latestBookingTime != null">
        latest_booking_time = #{record.latestBookingTime,jdbcType=DECIMAL},
      </if>
      <if test="record.leastRentalTime != null">
        least_rental_time = #{record.leastRentalTime,jdbcType=SMALLINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=BIGINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.latestBookingUnit != null">
        latest_booking_unit = #{record.latestBookingUnit,jdbcType=TINYINT},
      </if>
      <if test="record.priceMondaySunday != null">
        price_monday_sunday = #{record.priceMondaySunday,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update rent_channel_price
    set id = #{record.id,jdbcType=BIGINT},
      rent_base_id = #{record.rentBaseId,jdbcType=BIGINT},
      rent_calendar_id = #{record.rentCalendarId,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      price = #{record.price,jdbcType=INTEGER},
      price_weekend = #{record.priceWeekend,jdbcType=INTEGER},
      latest_booking_time = #{record.latestBookingTime,jdbcType=DECIMAL},
      least_rental_time = #{record.leastRentalTime,jdbcType=SMALLINT},
      channel_id = #{record.channelId,jdbcType=BIGINT},
      deleted = #{record.deleted,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      latest_booking_unit = #{record.latestBookingUnit,jdbcType=TINYINT},
      price_monday_sunday = #{record.priceMondaySunday,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.RentChannelPrice">
    update rent_channel_price
    <set>
      <if test="rentBaseId != null">
        rent_base_id = #{rentBaseId,jdbcType=BIGINT},
      </if>
      <if test="rentCalendarId != null">
        rent_calendar_id = #{rentCalendarId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=INTEGER},
      </if>
      <if test="priceWeekend != null">
        price_weekend = #{priceWeekend,jdbcType=INTEGER},
      </if>
      <if test="latestBookingTime != null">
        latest_booking_time = #{latestBookingTime,jdbcType=DECIMAL},
      </if>
      <if test="leastRentalTime != null">
        least_rental_time = #{leastRentalTime,jdbcType=SMALLINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="latestBookingUnit != null">
        latest_booking_unit = #{latestBookingUnit,jdbcType=TINYINT},
      </if>
      <if test="priceMondaySunday != null">
        price_monday_sunday = #{priceMondaySunday,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.RentChannelPrice">
    update rent_channel_price
    set rent_base_id = #{rentBaseId,jdbcType=BIGINT},
      rent_calendar_id = #{rentCalendarId,jdbcType=BIGINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      price = #{price,jdbcType=INTEGER},
      price_weekend = #{priceWeekend,jdbcType=INTEGER},
      latest_booking_time = #{latestBookingTime,jdbcType=DECIMAL},
      least_rental_time = #{leastRentalTime,jdbcType=SMALLINT},
      channel_id = #{channelId,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      latest_booking_unit = #{latestBookingUnit,jdbcType=TINYINT},
      price_monday_sunday = #{priceMondaySunday,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into rent_channel_price
    (rent_base_id, rent_calendar_id, merchant_id, store_id, vehicle_model_id, price, 
      price_weekend, latest_booking_time, least_rental_time, channel_id, deleted, last_ver, 
      create_time, op_time, op_user_id, latest_booking_unit, price_monday_sunday)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.rentBaseId,jdbcType=BIGINT}, #{item.rentCalendarId,jdbcType=BIGINT}, #{item.merchantId,jdbcType=BIGINT}, 
        #{item.storeId,jdbcType=BIGINT}, #{item.vehicleModelId,jdbcType=BIGINT}, #{item.price,jdbcType=INTEGER}, 
        #{item.priceWeekend,jdbcType=INTEGER}, #{item.latestBookingTime,jdbcType=DECIMAL}, 
        #{item.leastRentalTime,jdbcType=SMALLINT}, #{item.channelId,jdbcType=BIGINT}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.lastVer,jdbcType=INTEGER}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, 
        #{item.opUserId,jdbcType=BIGINT}, #{item.latestBookingUnit,jdbcType=TINYINT}, #{item.priceMondaySunday,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into rent_channel_price (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'rent_base_id'.toString() == column.value">
          #{item.rentBaseId,jdbcType=BIGINT}
        </if>
        <if test="'rent_calendar_id'.toString() == column.value">
          #{item.rentCalendarId,jdbcType=BIGINT}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'price'.toString() == column.value">
          #{item.price,jdbcType=INTEGER}
        </if>
        <if test="'price_weekend'.toString() == column.value">
          #{item.priceWeekend,jdbcType=INTEGER}
        </if>
        <if test="'latest_booking_time'.toString() == column.value">
          #{item.latestBookingTime,jdbcType=DECIMAL}
        </if>
        <if test="'least_rental_time'.toString() == column.value">
          #{item.leastRentalTime,jdbcType=SMALLINT}
        </if>
        <if test="'channel_id'.toString() == column.value">
          #{item.channelId,jdbcType=BIGINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'latest_booking_unit'.toString() == column.value">
          #{item.latestBookingUnit,jdbcType=TINYINT}
        </if>
        <if test="'price_monday_sunday'.toString() == column.value">
          #{item.priceMondaySunday,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
  <update id="batchUpdate" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <foreach collection="list" item="item" separator=";">
      update rent_channel_price
      <set>
        <if test="item.rentBaseId != null">
          rent_base_id = #{item.rentBaseId,jdbcType=BIGINT},
        </if>
        <if test="item.rentCalendarId != null">
          rent_calendar_id = #{item.rentCalendarId,jdbcType=BIGINT},
        </if>
        <if test="item.merchantId != null">
          merchant_id = #{item.merchantId,jdbcType=BIGINT},
        </if>
        <if test="item.storeId != null">
          store_id = #{item.storeId,jdbcType=BIGINT},
        </if>
        <if test="item.vehicleModelId != null">
          vehicle_model_id = #{item.vehicleModelId,jdbcType=BIGINT},
        </if>
        <if test="item.price != null">
          price = #{item.price,jdbcType=INTEGER},
        </if>
        <if test="item.priceWeekend != null">
          price_weekend = #{item.priceWeekend,jdbcType=INTEGER},
        </if>
        <if test="item.latestBookingTime != null">
          latest_booking_time = #{item.latestBookingTime,jdbcType=DECIMAL},
        </if>
        <if test="item.leastRentalTime != null">
          least_rental_time = #{item.leastRentalTime,jdbcType=SMALLINT},
        </if>
        <if test="item.channelId != null">
          channel_id = #{item.channelId,jdbcType=BIGINT},
        </if>
        <if test="item.deleted != null">
          deleted = #{item.deleted,jdbcType=TINYINT},
        </if>
        <if test="item.lastVer != null">
          last_ver = #{item.lastVer,jdbcType=INTEGER},
        </if>
        <if test="item.createTime != null">
          create_time = #{item.createTime,jdbcType=BIGINT},
        </if>
        <if test="item.opTime != null">
          op_time = #{item.opTime,jdbcType=BIGINT},
        </if>
        <if test="item.opUserId != null">
          op_user_id = #{item.opUserId,jdbcType=BIGINT},
        </if>
        <if test="item.latestBookingUnit != null">
          latest_booking_unit = #{item.latestBookingUnit,jdbcType=TINYINT},
        </if>
        <if test="item.priceMondaySunday != null">
          price_monday_sunday = #{item.priceMondaySunday,jdbcType=VARCHAR},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>

  </update>
</mapper>
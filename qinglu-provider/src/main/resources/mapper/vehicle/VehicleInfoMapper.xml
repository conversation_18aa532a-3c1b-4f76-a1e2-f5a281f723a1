<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.VehicleInfoMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.VehicleInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vehicle_model_id" jdbcType="BIGINT" property="vehicleModelId" />
    <result column="vehicle_model_name" jdbcType="VARCHAR" property="vehicleModelName" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="vehicle_source" jdbcType="TINYINT" property="vehicleSource" />
    <result column="vehicle_status" jdbcType="TINYINT" property="vehicleStatus" />
    <result column="vehicle_color_id" jdbcType="BIGINT" property="vehicleColorId" />
    <result column="mileage" jdbcType="INTEGER" property="mileage" />
    <result column="license" jdbcType="VARCHAR" property="license" />
    <result column="frame_num" jdbcType="VARCHAR" property="frameNum" />
    <result column="engine_num" jdbcType="VARCHAR" property="engineNum" />
    <result column="reg_date" jdbcType="VARCHAR" property="regDate" />
    <result column="usage_nature" jdbcType="TINYINT" property="usageNature" />
    <result column="yearly_inspection_period" jdbcType="VARCHAR" property="yearlyInspectionPeriod" />
    <result column="yearly_inspection_date" jdbcType="BIGINT" property="yearlyInspectionDate" />
    <result column="maintenance_interval" jdbcType="INTEGER" property="maintenanceInterval" />
    <result column="next_maintenance_mileage" jdbcType="INTEGER" property="nextMaintenanceMileage" />
    <result column="self_service_return" jdbcType="TINYINT" property="selfServiceReturn" />
    <result column="platform_sold" jdbcType="TINYINT" property="platformSold" />
    <result column="sale_status" jdbcType="TINYINT" property="saleStatus" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="ti_include_etc" jdbcType="TINYINT" property="tiIncludeEtc" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vehicle_model_id, vehicle_model_name, store_id, store_name, merchant_id, vehicle_source, 
    vehicle_status, vehicle_color_id, mileage, license, frame_num, engine_num, reg_date, 
    usage_nature, yearly_inspection_period, yearly_inspection_date, maintenance_interval, 
    next_maintenance_mileage, self_service_return, platform_sold, sale_status, owner, 
    extra, deleted, last_ver, op_user_id, create_time, op_time, ti_include_etc
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.VehicleInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.VehicleInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_info (vehicle_model_id, vehicle_model_name, 
      store_id, store_name, merchant_id, 
      vehicle_source, vehicle_status, vehicle_color_id, 
      mileage, license, frame_num, 
      engine_num, reg_date, usage_nature, 
      yearly_inspection_period, yearly_inspection_date, 
      maintenance_interval, next_maintenance_mileage, 
      self_service_return, platform_sold, sale_status, 
      owner, extra, deleted, 
      last_ver, op_user_id, create_time, 
      op_time, ti_include_etc)
    values (#{vehicleModelId,jdbcType=BIGINT}, #{vehicleModelName,jdbcType=VARCHAR}, 
      #{storeId,jdbcType=BIGINT}, #{storeName,jdbcType=VARCHAR}, #{merchantId,jdbcType=BIGINT}, 
      #{vehicleSource,jdbcType=TINYINT}, #{vehicleStatus,jdbcType=TINYINT}, #{vehicleColorId,jdbcType=BIGINT}, 
      #{mileage,jdbcType=INTEGER}, #{license,jdbcType=VARCHAR}, #{frameNum,jdbcType=VARCHAR}, 
      #{engineNum,jdbcType=VARCHAR}, #{regDate,jdbcType=VARCHAR}, #{usageNature,jdbcType=TINYINT}, 
      #{yearlyInspectionPeriod,jdbcType=VARCHAR}, #{yearlyInspectionDate,jdbcType=BIGINT}, 
      #{maintenanceInterval,jdbcType=INTEGER}, #{nextMaintenanceMileage,jdbcType=INTEGER}, 
      #{selfServiceReturn,jdbcType=TINYINT}, #{platformSold,jdbcType=TINYINT}, #{saleStatus,jdbcType=TINYINT}, 
      #{owner,jdbcType=VARCHAR}, #{extra,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, 
      #{lastVer,jdbcType=INTEGER}, #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{tiIncludeEtc,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.VehicleInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="vehicleSource != null">
        vehicle_source,
      </if>
      <if test="vehicleStatus != null">
        vehicle_status,
      </if>
      <if test="vehicleColorId != null">
        vehicle_color_id,
      </if>
      <if test="mileage != null">
        mileage,
      </if>
      <if test="license != null">
        license,
      </if>
      <if test="frameNum != null">
        frame_num,
      </if>
      <if test="engineNum != null">
        engine_num,
      </if>
      <if test="regDate != null">
        reg_date,
      </if>
      <if test="usageNature != null">
        usage_nature,
      </if>
      <if test="yearlyInspectionPeriod != null">
        yearly_inspection_period,
      </if>
      <if test="yearlyInspectionDate != null">
        yearly_inspection_date,
      </if>
      <if test="maintenanceInterval != null">
        maintenance_interval,
      </if>
      <if test="nextMaintenanceMileage != null">
        next_maintenance_mileage,
      </if>
      <if test="selfServiceReturn != null">
        self_service_return,
      </if>
      <if test="platformSold != null">
        platform_sold,
      </if>
      <if test="saleStatus != null">
        sale_status,
      </if>
      <if test="owner != null">
        owner,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="tiIncludeEtc != null">
        ti_include_etc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelName != null">
        #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="vehicleSource != null">
        #{vehicleSource,jdbcType=TINYINT},
      </if>
      <if test="vehicleStatus != null">
        #{vehicleStatus,jdbcType=TINYINT},
      </if>
      <if test="vehicleColorId != null">
        #{vehicleColorId,jdbcType=BIGINT},
      </if>
      <if test="mileage != null">
        #{mileage,jdbcType=INTEGER},
      </if>
      <if test="license != null">
        #{license,jdbcType=VARCHAR},
      </if>
      <if test="frameNum != null">
        #{frameNum,jdbcType=VARCHAR},
      </if>
      <if test="engineNum != null">
        #{engineNum,jdbcType=VARCHAR},
      </if>
      <if test="regDate != null">
        #{regDate,jdbcType=VARCHAR},
      </if>
      <if test="usageNature != null">
        #{usageNature,jdbcType=TINYINT},
      </if>
      <if test="yearlyInspectionPeriod != null">
        #{yearlyInspectionPeriod,jdbcType=VARCHAR},
      </if>
      <if test="yearlyInspectionDate != null">
        #{yearlyInspectionDate,jdbcType=BIGINT},
      </if>
      <if test="maintenanceInterval != null">
        #{maintenanceInterval,jdbcType=INTEGER},
      </if>
      <if test="nextMaintenanceMileage != null">
        #{nextMaintenanceMileage,jdbcType=INTEGER},
      </if>
      <if test="selfServiceReturn != null">
        #{selfServiceReturn,jdbcType=TINYINT},
      </if>
      <if test="platformSold != null">
        #{platformSold,jdbcType=TINYINT},
      </if>
      <if test="saleStatus != null">
        #{saleStatus,jdbcType=TINYINT},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="tiIncludeEtc != null">
        #{tiIncludeEtc,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.VehicleInfoExample" resultType="java.lang.Long">
    select count(*) from vehicle_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelId != null">
        vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleModelName != null">
        vehicle_model_name = #{record.vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleSource != null">
        vehicle_source = #{record.vehicleSource,jdbcType=TINYINT},
      </if>
      <if test="record.vehicleStatus != null">
        vehicle_status = #{record.vehicleStatus,jdbcType=TINYINT},
      </if>
      <if test="record.vehicleColorId != null">
        vehicle_color_id = #{record.vehicleColorId,jdbcType=BIGINT},
      </if>
      <if test="record.mileage != null">
        mileage = #{record.mileage,jdbcType=INTEGER},
      </if>
      <if test="record.license != null">
        license = #{record.license,jdbcType=VARCHAR},
      </if>
      <if test="record.frameNum != null">
        frame_num = #{record.frameNum,jdbcType=VARCHAR},
      </if>
      <if test="record.engineNum != null">
        engine_num = #{record.engineNum,jdbcType=VARCHAR},
      </if>
      <if test="record.regDate != null">
        reg_date = #{record.regDate,jdbcType=VARCHAR},
      </if>
      <if test="record.usageNature != null">
        usage_nature = #{record.usageNature,jdbcType=TINYINT},
      </if>
      <if test="record.yearlyInspectionPeriod != null">
        yearly_inspection_period = #{record.yearlyInspectionPeriod,jdbcType=VARCHAR},
      </if>
      <if test="record.yearlyInspectionDate != null">
        yearly_inspection_date = #{record.yearlyInspectionDate,jdbcType=BIGINT},
      </if>
      <if test="record.maintenanceInterval != null">
        maintenance_interval = #{record.maintenanceInterval,jdbcType=INTEGER},
      </if>
      <if test="record.nextMaintenanceMileage != null">
        next_maintenance_mileage = #{record.nextMaintenanceMileage,jdbcType=INTEGER},
      </if>
      <if test="record.selfServiceReturn != null">
        self_service_return = #{record.selfServiceReturn,jdbcType=TINYINT},
      </if>
      <if test="record.platformSold != null">
        platform_sold = #{record.platformSold,jdbcType=TINYINT},
      </if>
      <if test="record.saleStatus != null">
        sale_status = #{record.saleStatus,jdbcType=TINYINT},
      </if>
      <if test="record.owner != null">
        owner = #{record.owner,jdbcType=VARCHAR},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.tiIncludeEtc != null">
        ti_include_etc = #{record.tiIncludeEtc,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_info
    set id = #{record.id,jdbcType=BIGINT},
      vehicle_model_id = #{record.vehicleModelId,jdbcType=BIGINT},
      vehicle_model_name = #{record.vehicleModelName,jdbcType=VARCHAR},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      vehicle_source = #{record.vehicleSource,jdbcType=TINYINT},
      vehicle_status = #{record.vehicleStatus,jdbcType=TINYINT},
      vehicle_color_id = #{record.vehicleColorId,jdbcType=BIGINT},
      mileage = #{record.mileage,jdbcType=INTEGER},
      license = #{record.license,jdbcType=VARCHAR},
      frame_num = #{record.frameNum,jdbcType=VARCHAR},
      engine_num = #{record.engineNum,jdbcType=VARCHAR},
      reg_date = #{record.regDate,jdbcType=VARCHAR},
      usage_nature = #{record.usageNature,jdbcType=TINYINT},
      yearly_inspection_period = #{record.yearlyInspectionPeriod,jdbcType=VARCHAR},
      yearly_inspection_date = #{record.yearlyInspectionDate,jdbcType=BIGINT},
      maintenance_interval = #{record.maintenanceInterval,jdbcType=INTEGER},
      next_maintenance_mileage = #{record.nextMaintenanceMileage,jdbcType=INTEGER},
      self_service_return = #{record.selfServiceReturn,jdbcType=TINYINT},
      platform_sold = #{record.platformSold,jdbcType=TINYINT},
      sale_status = #{record.saleStatus,jdbcType=TINYINT},
      owner = #{record.owner,jdbcType=VARCHAR},
      extra = #{record.extra,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      ti_include_etc = #{record.tiIncludeEtc,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.VehicleInfo">
    update vehicle_info
    <set>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="vehicleSource != null">
        vehicle_source = #{vehicleSource,jdbcType=TINYINT},
      </if>
      <if test="vehicleStatus != null">
        vehicle_status = #{vehicleStatus,jdbcType=TINYINT},
      </if>
      <if test="vehicleColorId != null">
        vehicle_color_id = #{vehicleColorId,jdbcType=BIGINT},
      </if>
      <if test="mileage != null">
        mileage = #{mileage,jdbcType=INTEGER},
      </if>
      <if test="license != null">
        license = #{license,jdbcType=VARCHAR},
      </if>
      <if test="frameNum != null">
        frame_num = #{frameNum,jdbcType=VARCHAR},
      </if>
      <if test="engineNum != null">
        engine_num = #{engineNum,jdbcType=VARCHAR},
      </if>
      <if test="regDate != null">
        reg_date = #{regDate,jdbcType=VARCHAR},
      </if>
      <if test="usageNature != null">
        usage_nature = #{usageNature,jdbcType=TINYINT},
      </if>
      <if test="yearlyInspectionPeriod != null">
        yearly_inspection_period = #{yearlyInspectionPeriod,jdbcType=VARCHAR},
      </if>
      <if test="yearlyInspectionDate != null">
        yearly_inspection_date = #{yearlyInspectionDate,jdbcType=BIGINT},
      </if>
      <if test="maintenanceInterval != null">
        maintenance_interval = #{maintenanceInterval,jdbcType=INTEGER},
      </if>
      <if test="nextMaintenanceMileage != null">
        next_maintenance_mileage = #{nextMaintenanceMileage,jdbcType=INTEGER},
      </if>
      <if test="selfServiceReturn != null">
        self_service_return = #{selfServiceReturn,jdbcType=TINYINT},
      </if>
      <if test="platformSold != null">
        platform_sold = #{platformSold,jdbcType=TINYINT},
      </if>
      <if test="saleStatus != null">
        sale_status = #{saleStatus,jdbcType=TINYINT},
      </if>
      <if test="owner != null">
        owner = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="tiIncludeEtc != null">
        ti_include_etc = #{tiIncludeEtc,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.VehicleInfo">
    update vehicle_info
    set vehicle_model_id = #{vehicleModelId,jdbcType=BIGINT},
      vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=BIGINT},
      store_name = #{storeName,jdbcType=VARCHAR},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      vehicle_source = #{vehicleSource,jdbcType=TINYINT},
      vehicle_status = #{vehicleStatus,jdbcType=TINYINT},
      vehicle_color_id = #{vehicleColorId,jdbcType=BIGINT},
      mileage = #{mileage,jdbcType=INTEGER},
      license = #{license,jdbcType=VARCHAR},
      frame_num = #{frameNum,jdbcType=VARCHAR},
      engine_num = #{engineNum,jdbcType=VARCHAR},
      reg_date = #{regDate,jdbcType=VARCHAR},
      usage_nature = #{usageNature,jdbcType=TINYINT},
      yearly_inspection_period = #{yearlyInspectionPeriod,jdbcType=VARCHAR},
      yearly_inspection_date = #{yearlyInspectionDate,jdbcType=BIGINT},
      maintenance_interval = #{maintenanceInterval,jdbcType=INTEGER},
      next_maintenance_mileage = #{nextMaintenanceMileage,jdbcType=INTEGER},
      self_service_return = #{selfServiceReturn,jdbcType=TINYINT},
      platform_sold = #{platformSold,jdbcType=TINYINT},
      sale_status = #{saleStatus,jdbcType=TINYINT},
      owner = #{owner,jdbcType=VARCHAR},
      extra = #{extra,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      ti_include_etc = #{tiIncludeEtc,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_info
    (vehicle_model_id, vehicle_model_name, store_id, store_name, merchant_id, vehicle_source, 
      vehicle_status, vehicle_color_id, mileage, license, frame_num, engine_num, reg_date, 
      usage_nature, yearly_inspection_period, yearly_inspection_date, maintenance_interval, 
      next_maintenance_mileage, self_service_return, platform_sold, sale_status, owner, 
      extra, deleted, last_ver, op_user_id, create_time, op_time, ti_include_etc)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.vehicleModelId,jdbcType=BIGINT}, #{item.vehicleModelName,jdbcType=VARCHAR}, 
        #{item.storeId,jdbcType=BIGINT}, #{item.storeName,jdbcType=VARCHAR}, #{item.merchantId,jdbcType=BIGINT}, 
        #{item.vehicleSource,jdbcType=TINYINT}, #{item.vehicleStatus,jdbcType=TINYINT}, 
        #{item.vehicleColorId,jdbcType=BIGINT}, #{item.mileage,jdbcType=INTEGER}, #{item.license,jdbcType=VARCHAR}, 
        #{item.frameNum,jdbcType=VARCHAR}, #{item.engineNum,jdbcType=VARCHAR}, #{item.regDate,jdbcType=VARCHAR}, 
        #{item.usageNature,jdbcType=TINYINT}, #{item.yearlyInspectionPeriod,jdbcType=VARCHAR}, 
        #{item.yearlyInspectionDate,jdbcType=BIGINT}, #{item.maintenanceInterval,jdbcType=INTEGER}, 
        #{item.nextMaintenanceMileage,jdbcType=INTEGER}, #{item.selfServiceReturn,jdbcType=TINYINT}, 
        #{item.platformSold,jdbcType=TINYINT}, #{item.saleStatus,jdbcType=TINYINT}, #{item.owner,jdbcType=VARCHAR}, 
        #{item.extra,jdbcType=VARCHAR}, #{item.deleted,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER}, 
        #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, 
        #{item.tiIncludeEtc,jdbcType=TINYINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'vehicle_model_id'.toString() == column.value">
          #{item.vehicleModelId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_model_name'.toString() == column.value">
          #{item.vehicleModelName,jdbcType=VARCHAR}
        </if>
        <if test="'store_id'.toString() == column.value">
          #{item.storeId,jdbcType=BIGINT}
        </if>
        <if test="'store_name'.toString() == column.value">
          #{item.storeName,jdbcType=VARCHAR}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_source'.toString() == column.value">
          #{item.vehicleSource,jdbcType=TINYINT}
        </if>
        <if test="'vehicle_status'.toString() == column.value">
          #{item.vehicleStatus,jdbcType=TINYINT}
        </if>
        <if test="'vehicle_color_id'.toString() == column.value">
          #{item.vehicleColorId,jdbcType=BIGINT}
        </if>
        <if test="'mileage'.toString() == column.value">
          #{item.mileage,jdbcType=INTEGER}
        </if>
        <if test="'license'.toString() == column.value">
          #{item.license,jdbcType=VARCHAR}
        </if>
        <if test="'frame_num'.toString() == column.value">
          #{item.frameNum,jdbcType=VARCHAR}
        </if>
        <if test="'engine_num'.toString() == column.value">
          #{item.engineNum,jdbcType=VARCHAR}
        </if>
        <if test="'reg_date'.toString() == column.value">
          #{item.regDate,jdbcType=VARCHAR}
        </if>
        <if test="'usage_nature'.toString() == column.value">
          #{item.usageNature,jdbcType=TINYINT}
        </if>
        <if test="'yearly_inspection_period'.toString() == column.value">
          #{item.yearlyInspectionPeriod,jdbcType=VARCHAR}
        </if>
        <if test="'yearly_inspection_date'.toString() == column.value">
          #{item.yearlyInspectionDate,jdbcType=BIGINT}
        </if>
        <if test="'maintenance_interval'.toString() == column.value">
          #{item.maintenanceInterval,jdbcType=INTEGER}
        </if>
        <if test="'next_maintenance_mileage'.toString() == column.value">
          #{item.nextMaintenanceMileage,jdbcType=INTEGER}
        </if>
        <if test="'self_service_return'.toString() == column.value">
          #{item.selfServiceReturn,jdbcType=TINYINT}
        </if>
        <if test="'platform_sold'.toString() == column.value">
          #{item.platformSold,jdbcType=TINYINT}
        </if>
        <if test="'sale_status'.toString() == column.value">
          #{item.saleStatus,jdbcType=TINYINT}
        </if>
        <if test="'owner'.toString() == column.value">
          #{item.owner,jdbcType=VARCHAR}
        </if>
        <if test="'extra'.toString() == column.value">
          #{item.extra,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'ti_include_etc'.toString() == column.value">
          #{item.tiIncludeEtc,jdbcType=TINYINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>

  <select id="getVehicleInfoByLicenseIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from vehicle_info
    where deleted = 0 and license in
    <foreach item="license" collection="licenseList" separator="," open="(" close=")" index="">
      #{license}
    </foreach>
    and merchant_id = #{merchantId}
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.VehiclePurchaseMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.VehiclePurchase">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="purchase_way" jdbcType="TINYINT" property="purchaseWay" />
    <result column="purchase_amount" jdbcType="BIGINT" property="purchaseAmount" />
    <result column="vehicle_supplier" jdbcType="VARCHAR" property="vehicleSupplier" />
    <result column="lease_time" jdbcType="INTEGER" property="leaseTime" />
    <result column="monthly_rent" jdbcType="BIGINT" property="monthlyRent" />
    <result column="shareholder" jdbcType="VARCHAR" property="shareholder" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vehicle_id, purchase_way, purchase_amount, vehicle_supplier, lease_time, monthly_rent, 
    shareholder, merchant_id, deleted, last_ver, op_user_id, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.VehiclePurchaseExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_purchase
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_purchase
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_purchase
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.VehiclePurchaseExample">
    delete from vehicle_purchase
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.VehiclePurchase">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_purchase (vehicle_id, purchase_way, purchase_amount, 
      vehicle_supplier, lease_time, monthly_rent, 
      shareholder, merchant_id, deleted, 
      last_ver, op_user_id, create_time, 
      op_time)
    values (#{vehicleId,jdbcType=BIGINT}, #{purchaseWay,jdbcType=TINYINT}, #{purchaseAmount,jdbcType=BIGINT}, 
      #{vehicleSupplier,jdbcType=VARCHAR}, #{leaseTime,jdbcType=INTEGER}, #{monthlyRent,jdbcType=BIGINT}, 
      #{shareholder,jdbcType=VARCHAR}, #{merchantId,jdbcType=BIGINT}, #{deleted,jdbcType=TINYINT}, 
      #{lastVer,jdbcType=INTEGER}, #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.VehiclePurchase">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_purchase
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="purchaseWay != null">
        purchase_way,
      </if>
      <if test="purchaseAmount != null">
        purchase_amount,
      </if>
      <if test="vehicleSupplier != null">
        vehicle_supplier,
      </if>
      <if test="leaseTime != null">
        lease_time,
      </if>
      <if test="monthlyRent != null">
        monthly_rent,
      </if>
      <if test="shareholder != null">
        shareholder,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="purchaseWay != null">
        #{purchaseWay,jdbcType=TINYINT},
      </if>
      <if test="purchaseAmount != null">
        #{purchaseAmount,jdbcType=BIGINT},
      </if>
      <if test="vehicleSupplier != null">
        #{vehicleSupplier,jdbcType=VARCHAR},
      </if>
      <if test="leaseTime != null">
        #{leaseTime,jdbcType=INTEGER},
      </if>
      <if test="monthlyRent != null">
        #{monthlyRent,jdbcType=BIGINT},
      </if>
      <if test="shareholder != null">
        #{shareholder,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.VehiclePurchaseExample" resultType="java.lang.Long">
    select count(*) from vehicle_purchase
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_purchase
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="record.purchaseWay != null">
        purchase_way = #{record.purchaseWay,jdbcType=TINYINT},
      </if>
      <if test="record.purchaseAmount != null">
        purchase_amount = #{record.purchaseAmount,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleSupplier != null">
        vehicle_supplier = #{record.vehicleSupplier,jdbcType=VARCHAR},
      </if>
      <if test="record.leaseTime != null">
        lease_time = #{record.leaseTime,jdbcType=INTEGER},
      </if>
      <if test="record.monthlyRent != null">
        monthly_rent = #{record.monthlyRent,jdbcType=BIGINT},
      </if>
      <if test="record.shareholder != null">
        shareholder = #{record.shareholder,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_purchase
    set id = #{record.id,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=BIGINT},
      purchase_way = #{record.purchaseWay,jdbcType=TINYINT},
      purchase_amount = #{record.purchaseAmount,jdbcType=BIGINT},
      vehicle_supplier = #{record.vehicleSupplier,jdbcType=VARCHAR},
      lease_time = #{record.leaseTime,jdbcType=INTEGER},
      monthly_rent = #{record.monthlyRent,jdbcType=BIGINT},
      shareholder = #{record.shareholder,jdbcType=VARCHAR},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      deleted = #{record.deleted,jdbcType=TINYINT},
      last_ver = #{record.lastVer,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.VehiclePurchase">
    update vehicle_purchase
    <set>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="purchaseWay != null">
        purchase_way = #{purchaseWay,jdbcType=TINYINT},
      </if>
      <if test="purchaseAmount != null">
        purchase_amount = #{purchaseAmount,jdbcType=BIGINT},
      </if>
      <if test="vehicleSupplier != null">
        vehicle_supplier = #{vehicleSupplier,jdbcType=VARCHAR},
      </if>
      <if test="leaseTime != null">
        lease_time = #{leaseTime,jdbcType=INTEGER},
      </if>
      <if test="monthlyRent != null">
        monthly_rent = #{monthlyRent,jdbcType=BIGINT},
      </if>
      <if test="shareholder != null">
        shareholder = #{shareholder,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.VehiclePurchase">
    update vehicle_purchase
    set vehicle_id = #{vehicleId,jdbcType=BIGINT},
      purchase_way = #{purchaseWay,jdbcType=TINYINT},
      purchase_amount = #{purchaseAmount,jdbcType=BIGINT},
      vehicle_supplier = #{vehicleSupplier,jdbcType=VARCHAR},
      lease_time = #{leaseTime,jdbcType=INTEGER},
      monthly_rent = #{monthlyRent,jdbcType=BIGINT},
      shareholder = #{shareholder,jdbcType=VARCHAR},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=TINYINT},
      last_ver = #{lastVer,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_purchase
    (vehicle_id, purchase_way, purchase_amount, vehicle_supplier, lease_time, monthly_rent, 
      shareholder, merchant_id, deleted, last_ver, op_user_id, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.vehicleId,jdbcType=BIGINT}, #{item.purchaseWay,jdbcType=TINYINT}, #{item.purchaseAmount,jdbcType=BIGINT}, 
        #{item.vehicleSupplier,jdbcType=VARCHAR}, #{item.leaseTime,jdbcType=INTEGER}, #{item.monthlyRent,jdbcType=BIGINT}, 
        #{item.shareholder,jdbcType=VARCHAR}, #{item.merchantId,jdbcType=BIGINT}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.lastVer,jdbcType=INTEGER}, #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_purchase (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'vehicle_id'.toString() == column.value">
          #{item.vehicleId,jdbcType=BIGINT}
        </if>
        <if test="'purchase_way'.toString() == column.value">
          #{item.purchaseWay,jdbcType=TINYINT}
        </if>
        <if test="'purchase_amount'.toString() == column.value">
          #{item.purchaseAmount,jdbcType=BIGINT}
        </if>
        <if test="'vehicle_supplier'.toString() == column.value">
          #{item.vehicleSupplier,jdbcType=VARCHAR}
        </if>
        <if test="'lease_time'.toString() == column.value">
          #{item.leaseTime,jdbcType=INTEGER}
        </if>
        <if test="'monthly_rent'.toString() == column.value">
          #{item.monthlyRent,jdbcType=BIGINT}
        </if>
        <if test="'shareholder'.toString() == column.value">
          #{item.shareholder,jdbcType=VARCHAR}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>

  <select id="queryByVehicleIdsAndMerchantId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from vehicle_purchase
    where vehicle_id in
    <foreach item="item" index="index" collection="vehicleIds" open="(" separator="," close=")">
      #{item}
    </foreach>
    and merchant_id = #{merchantId}
    </select>
</mapper>
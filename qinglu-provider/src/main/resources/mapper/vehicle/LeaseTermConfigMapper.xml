<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.LeaseTermConfigMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.LeaseTermConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="thr_id" jdbcType="VARCHAR" property="thrId" />
    <result column="min_rent_term" jdbcType="SMALLINT" property="minRentTerm" />
    <result column="max_rent_term" jdbcType="SMALLINT" property="maxRentTerm" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="store_all" jdbcType="TINYINT" property="storeAll" />
    <result column="vehicle_all" jdbcType="TINYINT" property="vehicleAll" />
    <result column="channel_id_list" jdbcType="VARCHAR" property="channelIdList" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="source" jdbcType="TINYINT" property="source" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, start_date, end_date, thr_id, min_rent_term, max_rent_term, merchant_id, store_all, 
    vehicle_all, channel_id_list, deleted, source
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.LeaseTermConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from lease_term_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lease_term_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lease_term_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.LeaseTermConfigExample">
    delete from lease_term_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.LeaseTermConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lease_term_config (start_date, end_date, thr_id, 
      min_rent_term, max_rent_term, merchant_id, 
      store_all, vehicle_all, channel_id_list, 
      deleted, source)
    values (#{startDate,jdbcType=DATE}, #{endDate,jdbcType=DATE}, #{thrId,jdbcType=VARCHAR}, 
      #{minRentTerm,jdbcType=SMALLINT}, #{maxRentTerm,jdbcType=SMALLINT}, #{merchantId,jdbcType=BIGINT}, 
      #{storeAll,jdbcType=TINYINT}, #{vehicleAll,jdbcType=TINYINT}, #{channelIdList,jdbcType=VARCHAR}, 
      #{deleted,jdbcType=TINYINT}, #{source,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.LeaseTermConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lease_term_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="thrId != null">
        thr_id,
      </if>
      <if test="minRentTerm != null">
        min_rent_term,
      </if>
      <if test="maxRentTerm != null">
        max_rent_term,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="storeAll != null">
        store_all,
      </if>
      <if test="vehicleAll != null">
        vehicle_all,
      </if>
      <if test="channelIdList != null">
        channel_id_list,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="source != null">
        source,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="startDate != null">
        #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=DATE},
      </if>
      <if test="thrId != null">
        #{thrId,jdbcType=VARCHAR},
      </if>
      <if test="minRentTerm != null">
        #{minRentTerm,jdbcType=SMALLINT},
      </if>
      <if test="maxRentTerm != null">
        #{maxRentTerm,jdbcType=SMALLINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeAll != null">
        #{storeAll,jdbcType=TINYINT},
      </if>
      <if test="vehicleAll != null">
        #{vehicleAll,jdbcType=TINYINT},
      </if>
      <if test="channelIdList != null">
        #{channelIdList,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.LeaseTermConfigExample" resultType="java.lang.Long">
    select count(*) from lease_term_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lease_term_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.startDate != null">
        start_date = #{record.startDate,jdbcType=DATE},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=DATE},
      </if>
      <if test="record.thrId != null">
        thr_id = #{record.thrId,jdbcType=VARCHAR},
      </if>
      <if test="record.minRentTerm != null">
        min_rent_term = #{record.minRentTerm,jdbcType=SMALLINT},
      </if>
      <if test="record.maxRentTerm != null">
        max_rent_term = #{record.maxRentTerm,jdbcType=SMALLINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeAll != null">
        store_all = #{record.storeAll,jdbcType=TINYINT},
      </if>
      <if test="record.vehicleAll != null">
        vehicle_all = #{record.vehicleAll,jdbcType=TINYINT},
      </if>
      <if test="record.channelIdList != null">
        channel_id_list = #{record.channelIdList,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lease_term_config
    set id = #{record.id,jdbcType=BIGINT},
      start_date = #{record.startDate,jdbcType=DATE},
      end_date = #{record.endDate,jdbcType=DATE},
      thr_id = #{record.thrId,jdbcType=VARCHAR},
      min_rent_term = #{record.minRentTerm,jdbcType=SMALLINT},
      max_rent_term = #{record.maxRentTerm,jdbcType=SMALLINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      store_all = #{record.storeAll,jdbcType=TINYINT},
      vehicle_all = #{record.vehicleAll,jdbcType=TINYINT},
      channel_id_list = #{record.channelIdList,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      source = #{record.source,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.LeaseTermConfig">
    update lease_term_config
    <set>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=DATE},
      </if>
      <if test="thrId != null">
        thr_id = #{thrId,jdbcType=VARCHAR},
      </if>
      <if test="minRentTerm != null">
        min_rent_term = #{minRentTerm,jdbcType=SMALLINT},
      </if>
      <if test="maxRentTerm != null">
        max_rent_term = #{maxRentTerm,jdbcType=SMALLINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="storeAll != null">
        store_all = #{storeAll,jdbcType=TINYINT},
      </if>
      <if test="vehicleAll != null">
        vehicle_all = #{vehicleAll,jdbcType=TINYINT},
      </if>
      <if test="channelIdList != null">
        channel_id_list = #{channelIdList,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.LeaseTermConfig">
    update lease_term_config
    set start_date = #{startDate,jdbcType=DATE},
      end_date = #{endDate,jdbcType=DATE},
      thr_id = #{thrId,jdbcType=VARCHAR},
      min_rent_term = #{minRentTerm,jdbcType=SMALLINT},
      max_rent_term = #{maxRentTerm,jdbcType=SMALLINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      store_all = #{storeAll,jdbcType=TINYINT},
      vehicle_all = #{vehicleAll,jdbcType=TINYINT},
      channel_id_list = #{channelIdList,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      source = #{source,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into lease_term_config
    (start_date, end_date, thr_id, min_rent_term, max_rent_term, merchant_id, store_all, 
      vehicle_all, channel_id_list, deleted, source)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.startDate,jdbcType=DATE}, #{item.endDate,jdbcType=DATE}, #{item.thrId,jdbcType=VARCHAR}, 
        #{item.minRentTerm,jdbcType=SMALLINT}, #{item.maxRentTerm,jdbcType=SMALLINT}, #{item.merchantId,jdbcType=BIGINT}, 
        #{item.storeAll,jdbcType=TINYINT}, #{item.vehicleAll,jdbcType=TINYINT}, #{item.channelIdList,jdbcType=VARCHAR}, 
        #{item.deleted,jdbcType=TINYINT}, #{item.source,jdbcType=TINYINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into lease_term_config (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'start_date'.toString() == column.value">
          #{item.startDate,jdbcType=DATE}
        </if>
        <if test="'end_date'.toString() == column.value">
          #{item.endDate,jdbcType=DATE}
        </if>
        <if test="'thr_id'.toString() == column.value">
          #{item.thrId,jdbcType=VARCHAR}
        </if>
        <if test="'min_rent_term'.toString() == column.value">
          #{item.minRentTerm,jdbcType=SMALLINT}
        </if>
        <if test="'max_rent_term'.toString() == column.value">
          #{item.maxRentTerm,jdbcType=SMALLINT}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'store_all'.toString() == column.value">
          #{item.storeAll,jdbcType=TINYINT}
        </if>
        <if test="'vehicle_all'.toString() == column.value">
          #{item.vehicleAll,jdbcType=TINYINT}
        </if>
        <if test="'channel_id_list'.toString() == column.value">
          #{item.channelIdList,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'source'.toString() == column.value">
          #{item.source,jdbcType=TINYINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.HelloSubSeryMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.HelloSubSery">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="hello_id" jdbcType="VARCHAR" property="helloId" />
    <result column="brand_id" jdbcType="VARCHAR" property="brandId" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="model_year" jdbcType="INTEGER" property="modelYear" />
    <result column="model_name" jdbcType="VARCHAR" property="modelName" />
    <result column="series_id" jdbcType="VARCHAR" property="seriesId" />
    <result column="series_name" jdbcType="VARCHAR" property="seriesName" />
    <result column="displacement" jdbcType="VARCHAR" property="displacement" />
    <result column="transmission_type" jdbcType="INTEGER" property="transmissionType" />
    <result column="fuel_type" jdbcType="INTEGER" property="fuelType" />
    <result column="seat_num" jdbcType="INTEGER" property="seatNum" />
    <result column="door_num" jdbcType="INTEGER" property="doorNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="van_type" jdbcType="INTEGER" property="vanType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, hello_id, brand_id, brand_name, model_year, model_name, series_id, series_name, 
    displacement, transmission_type, fuel_type, seat_num, door_num, status, van_type
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.HelloSubSeryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from hello_sub_sery
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from hello_sub_sery
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.HelloSubSery">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into hello_sub_sery (hello_id, brand_id, brand_name, 
      model_year, model_name, series_id, 
      series_name, displacement, transmission_type, 
      fuel_type, seat_num, door_num, 
      status, van_type)
    values (#{helloId,jdbcType=VARCHAR}, #{brandId,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR}, 
      #{modelYear,jdbcType=INTEGER}, #{modelName,jdbcType=VARCHAR}, #{seriesId,jdbcType=VARCHAR}, 
      #{seriesName,jdbcType=VARCHAR}, #{displacement,jdbcType=VARCHAR}, #{transmissionType,jdbcType=INTEGER}, 
      #{fuelType,jdbcType=INTEGER}, #{seatNum,jdbcType=INTEGER}, #{doorNum,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{vanType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.HelloSubSery">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into hello_sub_sery
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="helloId != null">
        hello_id,
      </if>
      <if test="brandId != null">
        brand_id,
      </if>
      <if test="brandName != null">
        brand_name,
      </if>
      <if test="modelYear != null">
        model_year,
      </if>
      <if test="modelName != null">
        model_name,
      </if>
      <if test="seriesId != null">
        series_id,
      </if>
      <if test="seriesName != null">
        series_name,
      </if>
      <if test="displacement != null">
        displacement,
      </if>
      <if test="transmissionType != null">
        transmission_type,
      </if>
      <if test="fuelType != null">
        fuel_type,
      </if>
      <if test="seatNum != null">
        seat_num,
      </if>
      <if test="doorNum != null">
        door_num,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="vanType != null">
        van_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="helloId != null">
        #{helloId,jdbcType=VARCHAR},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="modelYear != null">
        #{modelYear,jdbcType=INTEGER},
      </if>
      <if test="modelName != null">
        #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="seriesId != null">
        #{seriesId,jdbcType=VARCHAR},
      </if>
      <if test="seriesName != null">
        #{seriesName,jdbcType=VARCHAR},
      </if>
      <if test="displacement != null">
        #{displacement,jdbcType=VARCHAR},
      </if>
      <if test="transmissionType != null">
        #{transmissionType,jdbcType=INTEGER},
      </if>
      <if test="fuelType != null">
        #{fuelType,jdbcType=INTEGER},
      </if>
      <if test="seatNum != null">
        #{seatNum,jdbcType=INTEGER},
      </if>
      <if test="doorNum != null">
        #{doorNum,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="vanType != null">
        #{vanType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.HelloSubSeryExample" resultType="java.lang.Long">
    select count(*) from hello_sub_sery
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update hello_sub_sery
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.helloId != null">
        hello_id = #{record.helloId,jdbcType=VARCHAR},
      </if>
      <if test="record.brandId != null">
        brand_id = #{record.brandId,jdbcType=VARCHAR},
      </if>
      <if test="record.brandName != null">
        brand_name = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.modelYear != null">
        model_year = #{record.modelYear,jdbcType=INTEGER},
      </if>
      <if test="record.modelName != null">
        model_name = #{record.modelName,jdbcType=VARCHAR},
      </if>
      <if test="record.seriesId != null">
        series_id = #{record.seriesId,jdbcType=VARCHAR},
      </if>
      <if test="record.seriesName != null">
        series_name = #{record.seriesName,jdbcType=VARCHAR},
      </if>
      <if test="record.displacement != null">
        displacement = #{record.displacement,jdbcType=VARCHAR},
      </if>
      <if test="record.transmissionType != null">
        transmission_type = #{record.transmissionType,jdbcType=INTEGER},
      </if>
      <if test="record.fuelType != null">
        fuel_type = #{record.fuelType,jdbcType=INTEGER},
      </if>
      <if test="record.seatNum != null">
        seat_num = #{record.seatNum,jdbcType=INTEGER},
      </if>
      <if test="record.doorNum != null">
        door_num = #{record.doorNum,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.vanType != null">
        van_type = #{record.vanType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update hello_sub_sery
    set id = #{record.id,jdbcType=BIGINT},
      hello_id = #{record.helloId,jdbcType=VARCHAR},
      brand_id = #{record.brandId,jdbcType=VARCHAR},
      brand_name = #{record.brandName,jdbcType=VARCHAR},
      model_year = #{record.modelYear,jdbcType=INTEGER},
      model_name = #{record.modelName,jdbcType=VARCHAR},
      series_id = #{record.seriesId,jdbcType=VARCHAR},
      series_name = #{record.seriesName,jdbcType=VARCHAR},
      displacement = #{record.displacement,jdbcType=VARCHAR},
      transmission_type = #{record.transmissionType,jdbcType=INTEGER},
      fuel_type = #{record.fuelType,jdbcType=INTEGER},
      seat_num = #{record.seatNum,jdbcType=INTEGER},
      door_num = #{record.doorNum,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      van_type = #{record.vanType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.HelloSubSery">
    update hello_sub_sery
    <set>
      <if test="helloId != null">
        hello_id = #{helloId,jdbcType=VARCHAR},
      </if>
      <if test="brandId != null">
        brand_id = #{brandId,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        brand_name = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="modelYear != null">
        model_year = #{modelYear,jdbcType=INTEGER},
      </if>
      <if test="modelName != null">
        model_name = #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="seriesId != null">
        series_id = #{seriesId,jdbcType=VARCHAR},
      </if>
      <if test="seriesName != null">
        series_name = #{seriesName,jdbcType=VARCHAR},
      </if>
      <if test="displacement != null">
        displacement = #{displacement,jdbcType=VARCHAR},
      </if>
      <if test="transmissionType != null">
        transmission_type = #{transmissionType,jdbcType=INTEGER},
      </if>
      <if test="fuelType != null">
        fuel_type = #{fuelType,jdbcType=INTEGER},
      </if>
      <if test="seatNum != null">
        seat_num = #{seatNum,jdbcType=INTEGER},
      </if>
      <if test="doorNum != null">
        door_num = #{doorNum,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="vanType != null">
        van_type = #{vanType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.HelloSubSery">
    update hello_sub_sery
    set hello_id = #{helloId,jdbcType=VARCHAR},
      brand_id = #{brandId,jdbcType=VARCHAR},
      brand_name = #{brandName,jdbcType=VARCHAR},
      model_year = #{modelYear,jdbcType=INTEGER},
      model_name = #{modelName,jdbcType=VARCHAR},
      series_id = #{seriesId,jdbcType=VARCHAR},
      series_name = #{seriesName,jdbcType=VARCHAR},
      displacement = #{displacement,jdbcType=VARCHAR},
      transmission_type = #{transmissionType,jdbcType=INTEGER},
      fuel_type = #{fuelType,jdbcType=INTEGER},
      seat_num = #{seatNum,jdbcType=INTEGER},
      door_num = #{doorNum,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      van_type = #{vanType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into hello_sub_sery
    (hello_id, brand_id, brand_name, model_year, model_name, series_id, series_name, 
      displacement, transmission_type, fuel_type, seat_num, door_num, status, van_type
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.helloId,jdbcType=VARCHAR}, #{item.brandId,jdbcType=VARCHAR}, #{item.brandName,jdbcType=VARCHAR}, 
        #{item.modelYear,jdbcType=INTEGER}, #{item.modelName,jdbcType=VARCHAR}, #{item.seriesId,jdbcType=VARCHAR}, 
        #{item.seriesName,jdbcType=VARCHAR}, #{item.displacement,jdbcType=VARCHAR}, #{item.transmissionType,jdbcType=INTEGER}, 
        #{item.fuelType,jdbcType=INTEGER}, #{item.seatNum,jdbcType=INTEGER}, #{item.doorNum,jdbcType=INTEGER}, 
        #{item.status,jdbcType=INTEGER}, #{item.vanType,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into hello_sub_sery (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'hello_id'.toString() == column.value">
          #{item.helloId,jdbcType=VARCHAR}
        </if>
        <if test="'brand_id'.toString() == column.value">
          #{item.brandId,jdbcType=VARCHAR}
        </if>
        <if test="'brand_name'.toString() == column.value">
          #{item.brandName,jdbcType=VARCHAR}
        </if>
        <if test="'model_year'.toString() == column.value">
          #{item.modelYear,jdbcType=INTEGER}
        </if>
        <if test="'model_name'.toString() == column.value">
          #{item.modelName,jdbcType=VARCHAR}
        </if>
        <if test="'series_id'.toString() == column.value">
          #{item.seriesId,jdbcType=VARCHAR}
        </if>
        <if test="'series_name'.toString() == column.value">
          #{item.seriesName,jdbcType=VARCHAR}
        </if>
        <if test="'displacement'.toString() == column.value">
          #{item.displacement,jdbcType=VARCHAR}
        </if>
        <if test="'transmission_type'.toString() == column.value">
          #{item.transmissionType,jdbcType=INTEGER}
        </if>
        <if test="'fuel_type'.toString() == column.value">
          #{item.fuelType,jdbcType=INTEGER}
        </if>
        <if test="'seat_num'.toString() == column.value">
          #{item.seatNum,jdbcType=INTEGER}
        </if>
        <if test="'door_num'.toString() == column.value">
          #{item.doorNum,jdbcType=INTEGER}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'van_type'.toString() == column.value">
          #{item.vanType,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
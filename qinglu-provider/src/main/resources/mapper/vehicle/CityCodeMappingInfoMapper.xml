<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.CityCodeMappingInfoMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.CityCodeMappingInfo">
    <!--@mbg.generated-->
    <!--@Table city_code_mapping_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="channel_city_id" jdbcType="BIGINT" property="channelCityId" />
    <result column="sass_city_id" jdbcType="BIGINT" property="sassCityId" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, channel, channel_city_id, sass_city_id, deleted, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.CityCodeMappingInfoExample" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from city_code_mapping_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from city_code_mapping_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from city_code_mapping_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.CityCodeMappingInfoExample">
    <!--@mbg.generated-->
    delete from city_code_mapping_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ql.rent.entity.vehicle.CityCodeMappingInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into city_code_mapping_info (channel, channel_city_id, sass_city_id, 
      deleted, create_time, op_time
      )
    values (#{channel,jdbcType=INTEGER}, #{channelCityId,jdbcType=BIGINT}, #{sassCityId,jdbcType=BIGINT}, 
      #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ql.rent.entity.vehicle.CityCodeMappingInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into city_code_mapping_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="channel != null">
        channel,
      </if>
      <if test="channelCityId != null">
        channel_city_id,
      </if>
      <if test="sassCityId != null">
        sass_city_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="channelCityId != null">
        #{channelCityId,jdbcType=BIGINT},
      </if>
      <if test="sassCityId != null">
        #{sassCityId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.CityCodeMappingInfoExample" resultType="java.lang.Long">
    <!--@mbg.generated-->
    select count(*) from city_code_mapping_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--@mbg.generated-->
    update city_code_mapping_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=INTEGER},
      </if>
      <if test="record.channelCityId != null">
        channel_city_id = #{record.channelCityId,jdbcType=BIGINT},
      </if>
      <if test="record.sassCityId != null">
        sass_city_id = #{record.sassCityId,jdbcType=BIGINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--@mbg.generated-->
    update city_code_mapping_info
    set id = #{record.id,jdbcType=BIGINT},
      channel = #{record.channel,jdbcType=INTEGER},
      channel_city_id = #{record.channelCityId,jdbcType=BIGINT},
      sass_city_id = #{record.sassCityId,jdbcType=BIGINT},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.CityCodeMappingInfo">
    <!--@mbg.generated-->
    update city_code_mapping_info
    <set>
      <if test="channel != null">
        channel = #{channel,jdbcType=INTEGER},
      </if>
      <if test="channelCityId != null">
        channel_city_id = #{channelCityId,jdbcType=BIGINT},
      </if>
      <if test="sassCityId != null">
        sass_city_id = #{sassCityId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.CityCodeMappingInfo">
    <!--@mbg.generated-->
    update city_code_mapping_info
    set channel = #{channel,jdbcType=INTEGER},
      channel_city_id = #{channelCityId,jdbcType=BIGINT},
      sass_city_id = #{sassCityId,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
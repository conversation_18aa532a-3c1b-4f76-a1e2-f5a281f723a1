<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.VehicleSeryFileMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.VehicleSeryFile">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sub_sery_id" jdbcType="BIGINT" property="subSeryId" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="ctrip_url" jdbcType="VARCHAR" property="ctripUrl" />
    <result column="sort" jdbcType="TINYINT" property="sort" />
    <result column="file_type" jdbcType="TINYINT" property="fileType" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, sub_sery_id, url, ctrip_url, sort, file_type, deleted, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.VehicleSeryFileExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from vehicle_sery_file
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_sery_file
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_sery_file
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.VehicleSeryFileExample">
    delete from vehicle_sery_file
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.VehicleSeryFile">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_sery_file (sub_sery_id, url, ctrip_url, 
      sort, file_type, deleted, 
      create_time, op_time)
    values (#{subSeryId,jdbcType=BIGINT}, #{url,jdbcType=VARCHAR}, #{ctripUrl,jdbcType=VARCHAR}, 
      #{sort,jdbcType=TINYINT}, #{fileType,jdbcType=TINYINT}, #{deleted,jdbcType=TINYINT}, 
      #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.VehicleSeryFile">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into vehicle_sery_file
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="subSeryId != null">
        sub_sery_id,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="ctripUrl != null">
        ctrip_url,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="fileType != null">
        file_type,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="subSeryId != null">
        #{subSeryId,jdbcType=BIGINT},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="ctripUrl != null">
        #{ctripUrl,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=TINYINT},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.VehicleSeryFileExample" resultType="java.lang.Long">
    select count(*) from vehicle_sery_file
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_sery_file
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.subSeryId != null">
        sub_sery_id = #{record.subSeryId,jdbcType=BIGINT},
      </if>
      <if test="record.url != null">
        url = #{record.url,jdbcType=VARCHAR},
      </if>
      <if test="record.ctripUrl != null">
        ctrip_url = #{record.ctripUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=TINYINT},
      </if>
      <if test="record.fileType != null">
        file_type = #{record.fileType,jdbcType=TINYINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_sery_file
    set id = #{record.id,jdbcType=BIGINT},
      sub_sery_id = #{record.subSeryId,jdbcType=BIGINT},
      url = #{record.url,jdbcType=VARCHAR},
      ctrip_url = #{record.ctripUrl,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=TINYINT},
      file_type = #{record.fileType,jdbcType=TINYINT},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.VehicleSeryFile">
    update vehicle_sery_file
    <set>
      <if test="subSeryId != null">
        sub_sery_id = #{subSeryId,jdbcType=BIGINT},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="ctripUrl != null">
        ctrip_url = #{ctripUrl,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=TINYINT},
      </if>
      <if test="fileType != null">
        file_type = #{fileType,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.VehicleSeryFile">
    update vehicle_sery_file
    set sub_sery_id = #{subSeryId,jdbcType=BIGINT},
      url = #{url,jdbcType=VARCHAR},
      ctrip_url = #{ctripUrl,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=TINYINT},
      file_type = #{fileType,jdbcType=TINYINT},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_sery_file
    (sub_sery_id, url, ctrip_url, sort, file_type, deleted, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.subSeryId,jdbcType=BIGINT}, #{item.url,jdbcType=VARCHAR}, #{item.ctripUrl,jdbcType=VARCHAR}, 
        #{item.sort,jdbcType=TINYINT}, #{item.fileType,jdbcType=TINYINT}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into vehicle_sery_file (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'sub_sery_id'.toString() == column.value">
          #{item.subSeryId,jdbcType=BIGINT}
        </if>
        <if test="'url'.toString() == column.value">
          #{item.url,jdbcType=VARCHAR}
        </if>
        <if test="'ctrip_url'.toString() == column.value">
          #{item.ctripUrl,jdbcType=VARCHAR}
        </if>
        <if test="'sort'.toString() == column.value">
          #{item.sort,jdbcType=TINYINT}
        </if>
        <if test="'file_type'.toString() == column.value">
          #{item.fileType,jdbcType=TINYINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
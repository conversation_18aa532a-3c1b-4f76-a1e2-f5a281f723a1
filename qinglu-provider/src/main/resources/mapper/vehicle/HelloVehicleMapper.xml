<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.HelloVehicleMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.HelloVehicle">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="vehicle_sery" jdbcType="VARCHAR" property="vehicleSery" />
    <result column="vehicle_model_name" jdbcType="VARCHAR" property="vehicleModelName" />
    <result column="vehicle_year_style" jdbcType="VARCHAR" property="vehicleYearStyle" />
    <result column="displacement" jdbcType="VARCHAR" property="displacement" />
    <result column="gearbox" jdbcType="VARCHAR" property="gearbox" />
    <result column="fuel_form" jdbcType="VARCHAR" property="fuelForm" />
    <result column="seat_num" jdbcType="SMALLINT" property="seatNum" />
    <result column="doors" jdbcType="TINYINT" property="doors" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, code, brand, vehicle_sery, vehicle_model_name, vehicle_year_style, displacement, 
    gearbox, fuel_form, seat_num, doors
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.HelloVehicleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from hello_vehicle
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from hello_vehicle
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from hello_vehicle
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.vehicle.HelloVehicleExample">
    delete from hello_vehicle
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.HelloVehicle">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into hello_vehicle (code, brand, vehicle_sery, 
      vehicle_model_name, vehicle_year_style, displacement, 
      gearbox, fuel_form, seat_num, 
      doors)
    values (#{code,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{vehicleSery,jdbcType=VARCHAR}, 
      #{vehicleModelName,jdbcType=VARCHAR}, #{vehicleYearStyle,jdbcType=VARCHAR}, #{displacement,jdbcType=VARCHAR}, 
      #{gearbox,jdbcType=VARCHAR}, #{fuelForm,jdbcType=VARCHAR}, #{seatNum,jdbcType=SMALLINT}, 
      #{doors,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.HelloVehicle">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into hello_vehicle
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="code != null">
        code,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="vehicleSery != null">
        vehicle_sery,
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name,
      </if>
      <if test="vehicleYearStyle != null">
        vehicle_year_style,
      </if>
      <if test="displacement != null">
        displacement,
      </if>
      <if test="gearbox != null">
        gearbox,
      </if>
      <if test="fuelForm != null">
        fuel_form,
      </if>
      <if test="seatNum != null">
        seat_num,
      </if>
      <if test="doors != null">
        doors,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSery != null">
        #{vehicleSery,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleYearStyle != null">
        #{vehicleYearStyle,jdbcType=VARCHAR},
      </if>
      <if test="displacement != null">
        #{displacement,jdbcType=VARCHAR},
      </if>
      <if test="gearbox != null">
        #{gearbox,jdbcType=VARCHAR},
      </if>
      <if test="fuelForm != null">
        #{fuelForm,jdbcType=VARCHAR},
      </if>
      <if test="seatNum != null">
        #{seatNum,jdbcType=SMALLINT},
      </if>
      <if test="doors != null">
        #{doors,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.HelloVehicleExample" resultType="java.lang.Long">
    select count(*) from hello_vehicle
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update hello_vehicle
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null">
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleSery != null">
        vehicle_sery = #{record.vehicleSery,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleModelName != null">
        vehicle_model_name = #{record.vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleYearStyle != null">
        vehicle_year_style = #{record.vehicleYearStyle,jdbcType=VARCHAR},
      </if>
      <if test="record.displacement != null">
        displacement = #{record.displacement,jdbcType=VARCHAR},
      </if>
      <if test="record.gearbox != null">
        gearbox = #{record.gearbox,jdbcType=VARCHAR},
      </if>
      <if test="record.fuelForm != null">
        fuel_form = #{record.fuelForm,jdbcType=VARCHAR},
      </if>
      <if test="record.seatNum != null">
        seat_num = #{record.seatNum,jdbcType=SMALLINT},
      </if>
      <if test="record.doors != null">
        doors = #{record.doors,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update hello_vehicle
    set id = #{record.id,jdbcType=BIGINT},
      code = #{record.code,jdbcType=VARCHAR},
      brand = #{record.brand,jdbcType=VARCHAR},
      vehicle_sery = #{record.vehicleSery,jdbcType=VARCHAR},
      vehicle_model_name = #{record.vehicleModelName,jdbcType=VARCHAR},
      vehicle_year_style = #{record.vehicleYearStyle,jdbcType=VARCHAR},
      displacement = #{record.displacement,jdbcType=VARCHAR},
      gearbox = #{record.gearbox,jdbcType=VARCHAR},
      fuel_form = #{record.fuelForm,jdbcType=VARCHAR},
      seat_num = #{record.seatNum,jdbcType=SMALLINT},
      doors = #{record.doors,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.HelloVehicle">
    update hello_vehicle
    <set>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSery != null">
        vehicle_sery = #{vehicleSery,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleYearStyle != null">
        vehicle_year_style = #{vehicleYearStyle,jdbcType=VARCHAR},
      </if>
      <if test="displacement != null">
        displacement = #{displacement,jdbcType=VARCHAR},
      </if>
      <if test="gearbox != null">
        gearbox = #{gearbox,jdbcType=VARCHAR},
      </if>
      <if test="fuelForm != null">
        fuel_form = #{fuelForm,jdbcType=VARCHAR},
      </if>
      <if test="seatNum != null">
        seat_num = #{seatNum,jdbcType=SMALLINT},
      </if>
      <if test="doors != null">
        doors = #{doors,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.HelloVehicle">
    update hello_vehicle
    set code = #{code,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      vehicle_sery = #{vehicleSery,jdbcType=VARCHAR},
      vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      vehicle_year_style = #{vehicleYearStyle,jdbcType=VARCHAR},
      displacement = #{displacement,jdbcType=VARCHAR},
      gearbox = #{gearbox,jdbcType=VARCHAR},
      fuel_form = #{fuelForm,jdbcType=VARCHAR},
      seat_num = #{seatNum,jdbcType=SMALLINT},
      doors = #{doors,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into hello_vehicle
    (code, brand, vehicle_sery, vehicle_model_name, vehicle_year_style, displacement, 
      gearbox, fuel_form, seat_num, doors)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.code,jdbcType=VARCHAR}, #{item.brand,jdbcType=VARCHAR}, #{item.vehicleSery,jdbcType=VARCHAR}, 
        #{item.vehicleModelName,jdbcType=VARCHAR}, #{item.vehicleYearStyle,jdbcType=VARCHAR}, 
        #{item.displacement,jdbcType=VARCHAR}, #{item.gearbox,jdbcType=VARCHAR}, #{item.fuelForm,jdbcType=VARCHAR}, 
        #{item.seatNum,jdbcType=SMALLINT}, #{item.doors,jdbcType=TINYINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into hello_vehicle (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'code'.toString() == column.value">
          #{item.code,jdbcType=VARCHAR}
        </if>
        <if test="'brand'.toString() == column.value">
          #{item.brand,jdbcType=VARCHAR}
        </if>
        <if test="'vehicle_sery'.toString() == column.value">
          #{item.vehicleSery,jdbcType=VARCHAR}
        </if>
        <if test="'vehicle_model_name'.toString() == column.value">
          #{item.vehicleModelName,jdbcType=VARCHAR}
        </if>
        <if test="'vehicle_year_style'.toString() == column.value">
          #{item.vehicleYearStyle,jdbcType=VARCHAR}
        </if>
        <if test="'displacement'.toString() == column.value">
          #{item.displacement,jdbcType=VARCHAR}
        </if>
        <if test="'gearbox'.toString() == column.value">
          #{item.gearbox,jdbcType=VARCHAR}
        </if>
        <if test="'fuel_form'.toString() == column.value">
          #{item.fuelForm,jdbcType=VARCHAR}
        </if>
        <if test="'seat_num'.toString() == column.value">
          #{item.seatNum,jdbcType=SMALLINT}
        </if>
        <if test="'doors'.toString() == column.value">
          #{item.doors,jdbcType=TINYINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
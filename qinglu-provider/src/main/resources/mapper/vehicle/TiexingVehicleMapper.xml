<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.TiexingVehicleMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.TiexingVehicle">
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="vehicle_code" jdbcType="VARCHAR" property="vehicleCode" />
    <result column="vehicle_img" jdbcType="VARCHAR" property="vehicleImg" />
    <result column="vehicle_name" jdbcType="VARCHAR" property="vehicleName" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="configuration" jdbcType="VARCHAR" property="configuration" />
    <result column="door_count" jdbcType="VARCHAR" property="doorCount" />
    <result column="emission" jdbcType="VARCHAR" property="emission" />
    <result column="fuel_type" jdbcType="VARCHAR" property="fuelType" />
    <result column="passenger_num" jdbcType="VARCHAR" property="passengerNum" />
    <result column="transmission_type" jdbcType="VARCHAR" property="transmissionType" />
    <result column="year" jdbcType="VARCHAR" property="year" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="STATUS" jdbcType="TINYINT" property="status" />
    <result column="LAST_VER" jdbcType="BIGINT" property="lastVer" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    ID, vehicle_code, vehicle_img, vehicle_name, brand_name, category, configuration, 
    door_count, emission, fuel_type, passenger_num, transmission_type, year, op_user_id, 
    create_time, op_time, STATUS, LAST_VER
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.TiexingVehicleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from tiexing_vehicle
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tiexing_vehicle
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.TiexingVehicle">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tiexing_vehicle (vehicle_code, vehicle_img, vehicle_name, 
      brand_name, category, configuration, 
      door_count, emission, fuel_type, 
      passenger_num, transmission_type, year, 
      op_user_id, create_time, op_time, 
      STATUS, LAST_VER)
    values (#{vehicleCode,jdbcType=VARCHAR}, #{vehicleImg,jdbcType=VARCHAR}, #{vehicleName,jdbcType=VARCHAR}, 
      #{brandName,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, #{configuration,jdbcType=VARCHAR}, 
      #{doorCount,jdbcType=VARCHAR}, #{emission,jdbcType=VARCHAR}, #{fuelType,jdbcType=VARCHAR}, 
      #{passengerNum,jdbcType=VARCHAR}, #{transmissionType,jdbcType=VARCHAR}, #{year,jdbcType=VARCHAR}, 
      #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, 
      #{status,jdbcType=TINYINT}, #{lastVer,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.TiexingVehicle">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tiexing_vehicle
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="vehicleCode != null">
        vehicle_code,
      </if>
      <if test="vehicleImg != null">
        vehicle_img,
      </if>
      <if test="vehicleName != null">
        vehicle_name,
      </if>
      <if test="brandName != null">
        brand_name,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="configuration != null">
        configuration,
      </if>
      <if test="doorCount != null">
        door_count,
      </if>
      <if test="emission != null">
        emission,
      </if>
      <if test="fuelType != null">
        fuel_type,
      </if>
      <if test="passengerNum != null">
        passenger_num,
      </if>
      <if test="transmissionType != null">
        transmission_type,
      </if>
      <if test="year != null">
        year,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="lastVer != null">
        LAST_VER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="vehicleCode != null">
        #{vehicleCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleImg != null">
        #{vehicleImg,jdbcType=VARCHAR},
      </if>
      <if test="vehicleName != null">
        #{vehicleName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="configuration != null">
        #{configuration,jdbcType=VARCHAR},
      </if>
      <if test="doorCount != null">
        #{doorCount,jdbcType=VARCHAR},
      </if>
      <if test="emission != null">
        #{emission,jdbcType=VARCHAR},
      </if>
      <if test="fuelType != null">
        #{fuelType,jdbcType=VARCHAR},
      </if>
      <if test="passengerNum != null">
        #{passengerNum,jdbcType=VARCHAR},
      </if>
      <if test="transmissionType != null">
        #{transmissionType,jdbcType=VARCHAR},
      </if>
      <if test="year != null">
        #{year,jdbcType=VARCHAR},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.TiexingVehicleExample" resultType="java.lang.Long">
    select count(*) from tiexing_vehicle
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tiexing_vehicle
    <set>
      <if test="record.id != null">
        ID = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleCode != null">
        vehicle_code = #{record.vehicleCode,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleImg != null">
        vehicle_img = #{record.vehicleImg,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleName != null">
        vehicle_name = #{record.vehicleName,jdbcType=VARCHAR},
      </if>
      <if test="record.brandName != null">
        brand_name = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.configuration != null">
        configuration = #{record.configuration,jdbcType=VARCHAR},
      </if>
      <if test="record.doorCount != null">
        door_count = #{record.doorCount,jdbcType=VARCHAR},
      </if>
      <if test="record.emission != null">
        emission = #{record.emission,jdbcType=VARCHAR},
      </if>
      <if test="record.fuelType != null">
        fuel_type = #{record.fuelType,jdbcType=VARCHAR},
      </if>
      <if test="record.passengerNum != null">
        passenger_num = #{record.passengerNum,jdbcType=VARCHAR},
      </if>
      <if test="record.transmissionType != null">
        transmission_type = #{record.transmissionType,jdbcType=VARCHAR},
      </if>
      <if test="record.year != null">
        year = #{record.year,jdbcType=VARCHAR},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        STATUS = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        LAST_VER = #{record.lastVer,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tiexing_vehicle
    set ID = #{record.id,jdbcType=BIGINT},
      vehicle_code = #{record.vehicleCode,jdbcType=VARCHAR},
      vehicle_img = #{record.vehicleImg,jdbcType=VARCHAR},
      vehicle_name = #{record.vehicleName,jdbcType=VARCHAR},
      brand_name = #{record.brandName,jdbcType=VARCHAR},
      category = #{record.category,jdbcType=VARCHAR},
      configuration = #{record.configuration,jdbcType=VARCHAR},
      door_count = #{record.doorCount,jdbcType=VARCHAR},
      emission = #{record.emission,jdbcType=VARCHAR},
      fuel_type = #{record.fuelType,jdbcType=VARCHAR},
      passenger_num = #{record.passengerNum,jdbcType=VARCHAR},
      transmission_type = #{record.transmissionType,jdbcType=VARCHAR},
      year = #{record.year,jdbcType=VARCHAR},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      STATUS = #{record.status,jdbcType=TINYINT},
      LAST_VER = #{record.lastVer,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.TiexingVehicle">
    update tiexing_vehicle
    <set>
      <if test="vehicleCode != null">
        vehicle_code = #{vehicleCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleImg != null">
        vehicle_img = #{vehicleImg,jdbcType=VARCHAR},
      </if>
      <if test="vehicleName != null">
        vehicle_name = #{vehicleName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        brand_name = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="configuration != null">
        configuration = #{configuration,jdbcType=VARCHAR},
      </if>
      <if test="doorCount != null">
        door_count = #{doorCount,jdbcType=VARCHAR},
      </if>
      <if test="emission != null">
        emission = #{emission,jdbcType=VARCHAR},
      </if>
      <if test="fuelType != null">
        fuel_type = #{fuelType,jdbcType=VARCHAR},
      </if>
      <if test="passengerNum != null">
        passenger_num = #{passengerNum,jdbcType=VARCHAR},
      </if>
      <if test="transmissionType != null">
        transmission_type = #{transmissionType,jdbcType=VARCHAR},
      </if>
      <if test="year != null">
        year = #{year,jdbcType=VARCHAR},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        LAST_VER = #{lastVer,jdbcType=BIGINT},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.TiexingVehicle">
    update tiexing_vehicle
    set vehicle_code = #{vehicleCode,jdbcType=VARCHAR},
      vehicle_img = #{vehicleImg,jdbcType=VARCHAR},
      vehicle_name = #{vehicleName,jdbcType=VARCHAR},
      brand_name = #{brandName,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      configuration = #{configuration,jdbcType=VARCHAR},
      door_count = #{doorCount,jdbcType=VARCHAR},
      emission = #{emission,jdbcType=VARCHAR},
      fuel_type = #{fuelType,jdbcType=VARCHAR},
      passenger_num = #{passengerNum,jdbcType=VARCHAR},
      transmission_type = #{transmissionType,jdbcType=VARCHAR},
      year = #{year,jdbcType=VARCHAR},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      STATUS = #{status,jdbcType=TINYINT},
      LAST_VER = #{lastVer,jdbcType=BIGINT}
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into tiexing_vehicle
    (vehicle_code, vehicle_img, vehicle_name, brand_name, category, configuration, door_count, 
      emission, fuel_type, passenger_num, transmission_type, year, op_user_id, create_time, 
      op_time, STATUS, LAST_VER)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.vehicleCode,jdbcType=VARCHAR}, #{item.vehicleImg,jdbcType=VARCHAR}, #{item.vehicleName,jdbcType=VARCHAR}, 
        #{item.brandName,jdbcType=VARCHAR}, #{item.category,jdbcType=VARCHAR}, #{item.configuration,jdbcType=VARCHAR}, 
        #{item.doorCount,jdbcType=VARCHAR}, #{item.emission,jdbcType=VARCHAR}, #{item.fuelType,jdbcType=VARCHAR}, 
        #{item.passengerNum,jdbcType=VARCHAR}, #{item.transmissionType,jdbcType=VARCHAR}, 
        #{item.year,jdbcType=VARCHAR}, #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.status,jdbcType=TINYINT}, #{item.lastVer,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into tiexing_vehicle (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'vehicle_code'.toString() == column.value">
          #{item.vehicleCode,jdbcType=VARCHAR}
        </if>
        <if test="'vehicle_img'.toString() == column.value">
          #{item.vehicleImg,jdbcType=VARCHAR}
        </if>
        <if test="'vehicle_name'.toString() == column.value">
          #{item.vehicleName,jdbcType=VARCHAR}
        </if>
        <if test="'brand_name'.toString() == column.value">
          #{item.brandName,jdbcType=VARCHAR}
        </if>
        <if test="'category'.toString() == column.value">
          #{item.category,jdbcType=VARCHAR}
        </if>
        <if test="'configuration'.toString() == column.value">
          #{item.configuration,jdbcType=VARCHAR}
        </if>
        <if test="'door_count'.toString() == column.value">
          #{item.doorCount,jdbcType=VARCHAR}
        </if>
        <if test="'emission'.toString() == column.value">
          #{item.emission,jdbcType=VARCHAR}
        </if>
        <if test="'fuel_type'.toString() == column.value">
          #{item.fuelType,jdbcType=VARCHAR}
        </if>
        <if test="'passenger_num'.toString() == column.value">
          #{item.passengerNum,jdbcType=VARCHAR}
        </if>
        <if test="'transmission_type'.toString() == column.value">
          #{item.transmissionType,jdbcType=VARCHAR}
        </if>
        <if test="'year'.toString() == column.value">
          #{item.year,jdbcType=VARCHAR}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'STATUS'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'LAST_VER'.toString() == column.value">
          #{item.lastVer,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
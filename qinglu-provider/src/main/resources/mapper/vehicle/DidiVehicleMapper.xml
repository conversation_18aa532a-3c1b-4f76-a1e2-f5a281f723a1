<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.vehicle.DidiVehicleMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.vehicle.DidiVehicle">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="model_id" jdbcType="VARCHAR" property="modelId" />
    <result column="model_name" jdbcType="VARCHAR" property="modelName" />
    <result column="nickname" jdbcType="VARCHAR" property="nickname" />
    <result column="brand_id" jdbcType="VARCHAR" property="brandId" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="series_id" jdbcType="VARCHAR" property="seriesId" />
    <result column="series_name" jdbcType="VARCHAR" property="seriesName" />
    <result column="style_year" jdbcType="VARCHAR" property="styleYear" />
    <result column="guide_price" jdbcType="BIGINT" property="guidePrice" />
    <result column="group_code" jdbcType="VARCHAR" property="groupCode" />
    <result column="fuel_type" jdbcType="INTEGER" property="fuelType" />
    <result column="transmission_type" jdbcType="INTEGER" property="transmissionType" />
    <result column="displacement" jdbcType="VARCHAR" property="displacement" />
    <result column="passenger_no" jdbcType="INTEGER" property="passengerNo" />
    <result column="luggage_no" jdbcType="INTEGER" property="luggageNo" />
    <result column="door_no" jdbcType="INTEGER" property="doorNo" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="STATUS" jdbcType="TINYINT" property="status" />
    <result column="LAST_VER" jdbcType="BIGINT" property="lastVer" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.ql.rent.entity.vehicle.DidiVehicleWithBLOBs">
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="img_url" jdbcType="LONGVARCHAR" property="imgUrl" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, model_id, model_name, nickname, brand_id, brand_name, series_id, series_name, 
    style_year, guide_price, group_code, fuel_type, transmission_type, displacement, 
    passenger_no, luggage_no, door_no, op_user_id, create_time, op_time, STATUS, LAST_VER
  </sql>
  <sql id="Blob_Column_List">
    description, img_url
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.ql.rent.entity.vehicle.DidiVehicleExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from didi_vehicle
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.ql.rent.entity.vehicle.DidiVehicleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from didi_vehicle
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from didi_vehicle
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.vehicle.DidiVehicleWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into didi_vehicle (model_id, model_name, nickname, 
      brand_id, brand_name, series_id, 
      series_name, style_year, guide_price, 
      group_code, fuel_type, transmission_type, 
      displacement, passenger_no, luggage_no, 
      door_no, op_user_id, create_time, 
      op_time, STATUS, LAST_VER, 
      description, img_url)
    values (#{modelId,jdbcType=VARCHAR}, #{modelName,jdbcType=VARCHAR}, #{nickname,jdbcType=VARCHAR}, 
      #{brandId,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR}, #{seriesId,jdbcType=VARCHAR}, 
      #{seriesName,jdbcType=VARCHAR}, #{styleYear,jdbcType=VARCHAR}, #{guidePrice,jdbcType=BIGINT}, 
      #{groupCode,jdbcType=VARCHAR}, #{fuelType,jdbcType=INTEGER}, #{transmissionType,jdbcType=INTEGER}, 
      #{displacement,jdbcType=VARCHAR}, #{passengerNo,jdbcType=INTEGER}, #{luggageNo,jdbcType=INTEGER}, 
      #{doorNo,jdbcType=INTEGER}, #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, #{lastVer,jdbcType=BIGINT}, 
      #{description,jdbcType=LONGVARCHAR}, #{imgUrl,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.vehicle.DidiVehicleWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into didi_vehicle
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="modelId != null">
        model_id,
      </if>
      <if test="modelName != null">
        model_name,
      </if>
      <if test="nickname != null">
        nickname,
      </if>
      <if test="brandId != null">
        brand_id,
      </if>
      <if test="brandName != null">
        brand_name,
      </if>
      <if test="seriesId != null">
        series_id,
      </if>
      <if test="seriesName != null">
        series_name,
      </if>
      <if test="styleYear != null">
        style_year,
      </if>
      <if test="guidePrice != null">
        guide_price,
      </if>
      <if test="groupCode != null">
        group_code,
      </if>
      <if test="fuelType != null">
        fuel_type,
      </if>
      <if test="transmissionType != null">
        transmission_type,
      </if>
      <if test="displacement != null">
        displacement,
      </if>
      <if test="passengerNo != null">
        passenger_no,
      </if>
      <if test="luggageNo != null">
        luggage_no,
      </if>
      <if test="doorNo != null">
        door_no,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="lastVer != null">
        LAST_VER,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="imgUrl != null">
        img_url,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="modelId != null">
        #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="modelName != null">
        #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="seriesId != null">
        #{seriesId,jdbcType=VARCHAR},
      </if>
      <if test="seriesName != null">
        #{seriesName,jdbcType=VARCHAR},
      </if>
      <if test="styleYear != null">
        #{styleYear,jdbcType=VARCHAR},
      </if>
      <if test="guidePrice != null">
        #{guidePrice,jdbcType=BIGINT},
      </if>
      <if test="groupCode != null">
        #{groupCode,jdbcType=VARCHAR},
      </if>
      <if test="fuelType != null">
        #{fuelType,jdbcType=INTEGER},
      </if>
      <if test="transmissionType != null">
        #{transmissionType,jdbcType=INTEGER},
      </if>
      <if test="displacement != null">
        #{displacement,jdbcType=VARCHAR},
      </if>
      <if test="passengerNo != null">
        #{passengerNo,jdbcType=INTEGER},
      </if>
      <if test="luggageNo != null">
        #{luggageNo,jdbcType=INTEGER},
      </if>
      <if test="doorNo != null">
        #{doorNo,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="imgUrl != null">
        #{imgUrl,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.vehicle.DidiVehicleExample" resultType="java.lang.Long">
    select count(*) from didi_vehicle
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update didi_vehicle
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.modelId != null">
        model_id = #{record.modelId,jdbcType=VARCHAR},
      </if>
      <if test="record.modelName != null">
        model_name = #{record.modelName,jdbcType=VARCHAR},
      </if>
      <if test="record.nickname != null">
        nickname = #{record.nickname,jdbcType=VARCHAR},
      </if>
      <if test="record.brandId != null">
        brand_id = #{record.brandId,jdbcType=VARCHAR},
      </if>
      <if test="record.brandName != null">
        brand_name = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.seriesId != null">
        series_id = #{record.seriesId,jdbcType=VARCHAR},
      </if>
      <if test="record.seriesName != null">
        series_name = #{record.seriesName,jdbcType=VARCHAR},
      </if>
      <if test="record.styleYear != null">
        style_year = #{record.styleYear,jdbcType=VARCHAR},
      </if>
      <if test="record.guidePrice != null">
        guide_price = #{record.guidePrice,jdbcType=BIGINT},
      </if>
      <if test="record.groupCode != null">
        group_code = #{record.groupCode,jdbcType=VARCHAR},
      </if>
      <if test="record.fuelType != null">
        fuel_type = #{record.fuelType,jdbcType=INTEGER},
      </if>
      <if test="record.transmissionType != null">
        transmission_type = #{record.transmissionType,jdbcType=INTEGER},
      </if>
      <if test="record.displacement != null">
        displacement = #{record.displacement,jdbcType=VARCHAR},
      </if>
      <if test="record.passengerNo != null">
        passenger_no = #{record.passengerNo,jdbcType=INTEGER},
      </if>
      <if test="record.luggageNo != null">
        luggage_no = #{record.luggageNo,jdbcType=INTEGER},
      </if>
      <if test="record.doorNo != null">
        door_no = #{record.doorNo,jdbcType=INTEGER},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        STATUS = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.lastVer != null">
        LAST_VER = #{record.lastVer,jdbcType=BIGINT},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.imgUrl != null">
        img_url = #{record.imgUrl,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update didi_vehicle
    set id = #{record.id,jdbcType=BIGINT},
      model_id = #{record.modelId,jdbcType=VARCHAR},
      model_name = #{record.modelName,jdbcType=VARCHAR},
      nickname = #{record.nickname,jdbcType=VARCHAR},
      brand_id = #{record.brandId,jdbcType=VARCHAR},
      brand_name = #{record.brandName,jdbcType=VARCHAR},
      series_id = #{record.seriesId,jdbcType=VARCHAR},
      series_name = #{record.seriesName,jdbcType=VARCHAR},
      style_year = #{record.styleYear,jdbcType=VARCHAR},
      guide_price = #{record.guidePrice,jdbcType=BIGINT},
      group_code = #{record.groupCode,jdbcType=VARCHAR},
      fuel_type = #{record.fuelType,jdbcType=INTEGER},
      transmission_type = #{record.transmissionType,jdbcType=INTEGER},
      displacement = #{record.displacement,jdbcType=VARCHAR},
      passenger_no = #{record.passengerNo,jdbcType=INTEGER},
      luggage_no = #{record.luggageNo,jdbcType=INTEGER},
      door_no = #{record.doorNo,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      STATUS = #{record.status,jdbcType=TINYINT},
      LAST_VER = #{record.lastVer,jdbcType=BIGINT},
      description = #{record.description,jdbcType=LONGVARCHAR},
      img_url = #{record.imgUrl,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update didi_vehicle
    set id = #{record.id,jdbcType=BIGINT},
      model_id = #{record.modelId,jdbcType=VARCHAR},
      model_name = #{record.modelName,jdbcType=VARCHAR},
      nickname = #{record.nickname,jdbcType=VARCHAR},
      brand_id = #{record.brandId,jdbcType=VARCHAR},
      brand_name = #{record.brandName,jdbcType=VARCHAR},
      series_id = #{record.seriesId,jdbcType=VARCHAR},
      series_name = #{record.seriesName,jdbcType=VARCHAR},
      style_year = #{record.styleYear,jdbcType=VARCHAR},
      guide_price = #{record.guidePrice,jdbcType=BIGINT},
      group_code = #{record.groupCode,jdbcType=VARCHAR},
      fuel_type = #{record.fuelType,jdbcType=INTEGER},
      transmission_type = #{record.transmissionType,jdbcType=INTEGER},
      displacement = #{record.displacement,jdbcType=VARCHAR},
      passenger_no = #{record.passengerNo,jdbcType=INTEGER},
      luggage_no = #{record.luggageNo,jdbcType=INTEGER},
      door_no = #{record.doorNo,jdbcType=INTEGER},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      STATUS = #{record.status,jdbcType=TINYINT},
      LAST_VER = #{record.lastVer,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.vehicle.DidiVehicleWithBLOBs">
    update didi_vehicle
    <set>
      <if test="modelId != null">
        model_id = #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="modelName != null">
        model_name = #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        nickname = #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="brandId != null">
        brand_id = #{brandId,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        brand_name = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="seriesId != null">
        series_id = #{seriesId,jdbcType=VARCHAR},
      </if>
      <if test="seriesName != null">
        series_name = #{seriesName,jdbcType=VARCHAR},
      </if>
      <if test="styleYear != null">
        style_year = #{styleYear,jdbcType=VARCHAR},
      </if>
      <if test="guidePrice != null">
        guide_price = #{guidePrice,jdbcType=BIGINT},
      </if>
      <if test="groupCode != null">
        group_code = #{groupCode,jdbcType=VARCHAR},
      </if>
      <if test="fuelType != null">
        fuel_type = #{fuelType,jdbcType=INTEGER},
      </if>
      <if test="transmissionType != null">
        transmission_type = #{transmissionType,jdbcType=INTEGER},
      </if>
      <if test="displacement != null">
        displacement = #{displacement,jdbcType=VARCHAR},
      </if>
      <if test="passengerNo != null">
        passenger_no = #{passengerNo,jdbcType=INTEGER},
      </if>
      <if test="luggageNo != null">
        luggage_no = #{luggageNo,jdbcType=INTEGER},
      </if>
      <if test="doorNo != null">
        door_no = #{doorNo,jdbcType=INTEGER},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=TINYINT},
      </if>
      <if test="lastVer != null">
        LAST_VER = #{lastVer,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="imgUrl != null">
        img_url = #{imgUrl,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.ql.rent.entity.vehicle.DidiVehicleWithBLOBs">
    update didi_vehicle
    set model_id = #{modelId,jdbcType=VARCHAR},
      model_name = #{modelName,jdbcType=VARCHAR},
      nickname = #{nickname,jdbcType=VARCHAR},
      brand_id = #{brandId,jdbcType=VARCHAR},
      brand_name = #{brandName,jdbcType=VARCHAR},
      series_id = #{seriesId,jdbcType=VARCHAR},
      series_name = #{seriesName,jdbcType=VARCHAR},
      style_year = #{styleYear,jdbcType=VARCHAR},
      guide_price = #{guidePrice,jdbcType=BIGINT},
      group_code = #{groupCode,jdbcType=VARCHAR},
      fuel_type = #{fuelType,jdbcType=INTEGER},
      transmission_type = #{transmissionType,jdbcType=INTEGER},
      displacement = #{displacement,jdbcType=VARCHAR},
      passenger_no = #{passengerNo,jdbcType=INTEGER},
      luggage_no = #{luggageNo,jdbcType=INTEGER},
      door_no = #{doorNo,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      STATUS = #{status,jdbcType=TINYINT},
      LAST_VER = #{lastVer,jdbcType=BIGINT},
      description = #{description,jdbcType=LONGVARCHAR},
      img_url = #{imgUrl,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.vehicle.DidiVehicle">
    update didi_vehicle
    set model_id = #{modelId,jdbcType=VARCHAR},
      model_name = #{modelName,jdbcType=VARCHAR},
      nickname = #{nickname,jdbcType=VARCHAR},
      brand_id = #{brandId,jdbcType=VARCHAR},
      brand_name = #{brandName,jdbcType=VARCHAR},
      series_id = #{seriesId,jdbcType=VARCHAR},
      series_name = #{seriesName,jdbcType=VARCHAR},
      style_year = #{styleYear,jdbcType=VARCHAR},
      guide_price = #{guidePrice,jdbcType=BIGINT},
      group_code = #{groupCode,jdbcType=VARCHAR},
      fuel_type = #{fuelType,jdbcType=INTEGER},
      transmission_type = #{transmissionType,jdbcType=INTEGER},
      displacement = #{displacement,jdbcType=VARCHAR},
      passenger_no = #{passengerNo,jdbcType=INTEGER},
      luggage_no = #{luggageNo,jdbcType=INTEGER},
      door_no = #{doorNo,jdbcType=INTEGER},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      STATUS = #{status,jdbcType=TINYINT},
      LAST_VER = #{lastVer,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into didi_vehicle
    (model_id, model_name, nickname, brand_id, brand_name, series_id, series_name, style_year, 
      guide_price, group_code, fuel_type, transmission_type, displacement, passenger_no, 
      luggage_no, door_no, op_user_id, create_time, op_time, STATUS, LAST_VER, description, 
      img_url)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.modelId,jdbcType=VARCHAR}, #{item.modelName,jdbcType=VARCHAR}, #{item.nickname,jdbcType=VARCHAR}, 
        #{item.brandId,jdbcType=VARCHAR}, #{item.brandName,jdbcType=VARCHAR}, #{item.seriesId,jdbcType=VARCHAR}, 
        #{item.seriesName,jdbcType=VARCHAR}, #{item.styleYear,jdbcType=VARCHAR}, #{item.guidePrice,jdbcType=BIGINT}, 
        #{item.groupCode,jdbcType=VARCHAR}, #{item.fuelType,jdbcType=INTEGER}, #{item.transmissionType,jdbcType=INTEGER}, 
        #{item.displacement,jdbcType=VARCHAR}, #{item.passengerNo,jdbcType=INTEGER}, #{item.luggageNo,jdbcType=INTEGER}, 
        #{item.doorNo,jdbcType=INTEGER}, #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.status,jdbcType=TINYINT}, #{item.lastVer,jdbcType=BIGINT}, 
        #{item.description,jdbcType=LONGVARCHAR}, #{item.imgUrl,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into didi_vehicle (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'model_id'.toString() == column.value">
          #{item.modelId,jdbcType=VARCHAR}
        </if>
        <if test="'model_name'.toString() == column.value">
          #{item.modelName,jdbcType=VARCHAR}
        </if>
        <if test="'nickname'.toString() == column.value">
          #{item.nickname,jdbcType=VARCHAR}
        </if>
        <if test="'brand_id'.toString() == column.value">
          #{item.brandId,jdbcType=VARCHAR}
        </if>
        <if test="'brand_name'.toString() == column.value">
          #{item.brandName,jdbcType=VARCHAR}
        </if>
        <if test="'series_id'.toString() == column.value">
          #{item.seriesId,jdbcType=VARCHAR}
        </if>
        <if test="'series_name'.toString() == column.value">
          #{item.seriesName,jdbcType=VARCHAR}
        </if>
        <if test="'style_year'.toString() == column.value">
          #{item.styleYear,jdbcType=VARCHAR}
        </if>
        <if test="'guide_price'.toString() == column.value">
          #{item.guidePrice,jdbcType=BIGINT}
        </if>
        <if test="'group_code'.toString() == column.value">
          #{item.groupCode,jdbcType=VARCHAR}
        </if>
        <if test="'fuel_type'.toString() == column.value">
          #{item.fuelType,jdbcType=INTEGER}
        </if>
        <if test="'transmission_type'.toString() == column.value">
          #{item.transmissionType,jdbcType=INTEGER}
        </if>
        <if test="'displacement'.toString() == column.value">
          #{item.displacement,jdbcType=VARCHAR}
        </if>
        <if test="'passenger_no'.toString() == column.value">
          #{item.passengerNo,jdbcType=INTEGER}
        </if>
        <if test="'luggage_no'.toString() == column.value">
          #{item.luggageNo,jdbcType=INTEGER}
        </if>
        <if test="'door_no'.toString() == column.value">
          #{item.doorNo,jdbcType=INTEGER}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'STATUS'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'LAST_VER'.toString() == column.value">
          #{item.lastVer,jdbcType=BIGINT}
        </if>
        <if test="'description'.toString() == column.value">
          #{item.description,jdbcType=LONGVARCHAR}
        </if>
        <if test="'img_url'.toString() == column.value">
          #{item.imgUrl,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
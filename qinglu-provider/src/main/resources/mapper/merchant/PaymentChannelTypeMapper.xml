<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.merchant.PaymentChannelTypeMapper">
    
    <resultMap id="BaseResultMap" type="com.ql.rent.entity.merchant.PaymentChannelType">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="status" property="status"/>
        <result column="icon" property="icon"/>
        <result column="remark" property="remark"/>
    </resultMap>
    
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT * FROM payment_channel_type
        WHERE deleted = 0 
        ORDER BY sort_order ASC
    </select>
    
</mapper> 
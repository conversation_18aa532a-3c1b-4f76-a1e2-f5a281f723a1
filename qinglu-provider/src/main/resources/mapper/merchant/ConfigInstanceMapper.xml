<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.merchant.ConfigInstanceMapper">
    <resultMap id="BaseResultMap" type="com.ql.rent.entity.merchant.ConfigInstance">
        <id column="id" property="id" />
        <result column="template_id" property="templateId" />
        <result column="business_id" property="businessId" />
        <result column="instance_name" property="instanceName" />
        <result column="status" property="status" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="op_time" property="opTime" />
        <result column="op_user_id" property="opUserId" />
    </resultMap>

    <insert id="insert" parameterType="com.ql.rent.entity.merchant.ConfigInstance" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO config_instance (
            template_id, business_id, instance_name, status,
            create_time, create_user_id, op_time, op_user_id
        ) VALUES (
            #{templateId}, #{businessId}, #{instanceName}, #{status},
            #{createTime}, #{createUserId}, #{opTime}, #{opUserId}
        )
    </insert>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT *
        FROM config_instance
        WHERE id = #{id}
        AND deleted = 0
    </select>

    <select id="selectByBusinessId" resultMap="BaseResultMap">
        SELECT *
        FROM config_instance
        WHERE business_id = #{businessId}
        AND deleted = 0
        LIMIT 1
    </select>

    <select id="selectByBusinessIds" resultMap="BaseResultMap">
        SELECT 
            *
        FROM config_instance
        WHERE business_id IN
        <foreach collection="businessIds" item="businessId" open="(" separator="," close=")">
            #{businessId}
        </foreach>
        AND deleted = 0
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT *
        FROM config_instance
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
    </select>
</mapper> 
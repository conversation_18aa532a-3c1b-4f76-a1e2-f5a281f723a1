<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.merchant.VerifyPaymentAccountLogMapper">
    <resultMap id="BaseResultMap" type="com.ql.rent.entity.merchant.VerifyPaymentAccountLog">
        <id column="id" property="id" />
        <result column="verify_no" property="verifyNo" />
        <result column="payment_channel_id" property="paymentChannelId" />
        <result column="verify_amount" property="verifyAmount" />
        <result column="verify_code" property="verifyCode" />
        <result column="verify_status" property="verifyStatus" />
        <result column="auto_verify" property="autoVerify" />
        <result column="pay_time" property="payTime" />
        <result column="verify_time" property="verifyTime" />
        <result column="verify_operator" property="verifyOperator" />
        <result column="verify_result" property="verifyResult" />
        <result column="expire_time" property="expireTime" />
        <result column="callback_status" property="callbackStatus" />
        <result column="callback_time" property="callbackTime" />
        <result column="callback_data" property="callbackData" />
        <result column="remark" property="remark" />
    </resultMap>

    <insert id="insert" parameterType="com.ql.rent.entity.merchant.VerifyPaymentAccountLog" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO verify_payment_account_log (
            verify_no, payment_channel_id, verify_amount, verify_code,
            verify_status, auto_verify, expire_time,
            create_time, create_user_id, op_time, op_user_id
        ) VALUES (
            #{verifyNo}, #{paymentChannelId}, #{verifyAmount}, #{verifyCode},
            #{verifyStatus}, #{autoVerify}, #{expireTime},
            #{createTime}, #{createUserId}, #{opTime}, #{opUserId}
        )
    </insert>

    <select id="selectByVerifyNo" resultMap="BaseResultMap">
        SELECT *
        FROM verify_payment_account_log
        WHERE verify_no = #{verifyNo} AND deleted = 0
    </select>

    <update id="updateVerifyStatus">
        UPDATE verify_payment_account_log
        SET verify_status = #{verifyStatus},
            verify_result = #{verifyResult},
            verify_time = #{verifyTime},
            op_time = #{verifyTime}
        WHERE verify_no = #{verifyNo} AND deleted = 0
    </update>
</mapper> 
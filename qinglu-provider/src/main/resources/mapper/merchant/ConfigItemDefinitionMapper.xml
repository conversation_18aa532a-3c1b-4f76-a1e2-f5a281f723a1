<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.merchant.ConfigItemDefinitionMapper">
    
    <resultMap id="BaseResultMap" type="com.ql.rent.entity.merchant.ConfigItemDefinition">
        <id column="id" property="id"/>
        <result column="template_id" property="templateId"/>
        <result column="item_code" property="itemCode"/>
        <result column="item_name" property="itemName"/>
        <result column="item_type" property="itemType"/>
        <result column="is_required" property="required"/>
        <result column="is_encrypted" property="encrypted"/>
        <result column="validation_rules" property="validationRules"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="status" property="status"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="op_time" property="opTime"/>
    </resultMap>
    
    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM config_item_definition WHERE id = #{id} AND deleted = 0
    </select>
    
    <select id="selectByTemplateIdAndItemCode" resultMap="BaseResultMap">
        SELECT * FROM config_item_definition 
        WHERE template_id = #{templateId} 
        AND item_code = #{itemCode} 
        AND deleted = 0
    </select>
    
    <select id="selectByTemplateId" resultMap="BaseResultMap">
        SELECT * FROM config_item_definition 
        WHERE template_id = #{templateId} 
        AND deleted = 0
    </select>
    
    <select id="selectList" resultMap="BaseResultMap">
        SELECT * FROM config_item_definition 
        WHERE deleted = 0
        <if test="templateId != null">
            AND template_id = #{templateId}
        </if>
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>
    
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO config_item_definition (
            template_id, item_code, item_name, item_type, 
            required, encrypted, status, deleted,
            create_time, op_time
        ) VALUES (
            #{templateId}, #{itemCode}, #{itemName}, #{itemType},
            #{required}, #{encrypted}, #{status}, #{deleted},
            #{createTime}, #{opTime}
        )
    </insert>
    
    <update id="update">
        UPDATE config_item_definition
        SET item_name = #{itemName},
            item_type = #{itemType},
            required = #{required},
            encrypted = #{encrypted},
            op_time = #{opTime}
        WHERE id = #{id} AND deleted = 0
    </update>
    
    <update id="updateStatus">
        UPDATE config_item_definition
        SET status = #{status},
            op_time = #{opTime}
        WHERE id = #{id} AND deleted = 0
    </update>
    
    <select id="selectByTemplateCodeAndItemCode" resultMap="BaseResultMap">
        SELECT * FROM config_item_definition 
        WHERE template_code = #{templateCode} 
        AND item_code = #{itemCode} 
        AND deleted = 0
    </select>
    
    <select id="selectByTemplateIds" resultMap="BaseResultMap">
        SELECT *
        FROM config_item_definition
        WHERE template_id IN
        <foreach collection="templateIds" item="templateId" open="(" separator="," close=")">
            #{templateId}
        </foreach>
        AND deleted = 0
    </select>
    
</mapper> 
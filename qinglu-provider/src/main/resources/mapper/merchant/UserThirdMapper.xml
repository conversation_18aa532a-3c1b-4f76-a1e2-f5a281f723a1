<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.merchant.UserThirdMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.merchant.UserThird">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="third_type" jdbcType="TINYINT" property="thirdType" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="union_id" jdbcType="VARCHAR" property="unionId" />
    <result column="open_id" jdbcType="VARCHAR" property="openId" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="gender" jdbcType="TINYINT" property="gender" />
    <result column="logo_url" jdbcType="VARCHAR" property="logoUrl" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, third_type, user_id, union_id, open_id, nick_name, mobile, gender, logo_url, 
    deleted, op_user_id, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.merchant.UserThirdExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from user_third
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_third
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from user_third
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.merchant.UserThirdExample">
    delete from user_third
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.merchant.UserThird">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_third (third_type, user_id, union_id, 
      open_id, nick_name, mobile, 
      gender, logo_url, deleted, 
      op_user_id, create_time, op_time
      )
    values (#{thirdType,jdbcType=TINYINT}, #{userId,jdbcType=BIGINT}, #{unionId,jdbcType=VARCHAR}, 
      #{openId,jdbcType=VARCHAR}, #{nickName,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, 
      #{gender,jdbcType=TINYINT}, #{logoUrl,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, 
      #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.merchant.UserThird">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_third
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="thirdType != null">
        third_type,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="unionId != null">
        union_id,
      </if>
      <if test="openId != null">
        open_id,
      </if>
      <if test="nickName != null">
        nick_name,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="logoUrl != null">
        logo_url,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="thirdType != null">
        #{thirdType,jdbcType=TINYINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="unionId != null">
        #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="openId != null">
        #{openId,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=TINYINT},
      </if>
      <if test="logoUrl != null">
        #{logoUrl,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.merchant.UserThirdExample" resultType="java.lang.Long">
    select count(*) from user_third
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update user_third
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.thirdType != null">
        third_type = #{record.thirdType,jdbcType=TINYINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.unionId != null">
        union_id = #{record.unionId,jdbcType=VARCHAR},
      </if>
      <if test="record.openId != null">
        open_id = #{record.openId,jdbcType=VARCHAR},
      </if>
      <if test="record.nickName != null">
        nick_name = #{record.nickName,jdbcType=VARCHAR},
      </if>
      <if test="record.mobile != null">
        mobile = #{record.mobile,jdbcType=VARCHAR},
      </if>
      <if test="record.gender != null">
        gender = #{record.gender,jdbcType=TINYINT},
      </if>
      <if test="record.logoUrl != null">
        logo_url = #{record.logoUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update user_third
    set id = #{record.id,jdbcType=BIGINT},
      third_type = #{record.thirdType,jdbcType=TINYINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      union_id = #{record.unionId,jdbcType=VARCHAR},
      open_id = #{record.openId,jdbcType=VARCHAR},
      nick_name = #{record.nickName,jdbcType=VARCHAR},
      mobile = #{record.mobile,jdbcType=VARCHAR},
      gender = #{record.gender,jdbcType=TINYINT},
      logo_url = #{record.logoUrl,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.merchant.UserThird">
    update user_third
    <set>
      <if test="thirdType != null">
        third_type = #{thirdType,jdbcType=TINYINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="unionId != null">
        union_id = #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="openId != null">
        open_id = #{openId,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        nick_name = #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=TINYINT},
      </if>
      <if test="logoUrl != null">
        logo_url = #{logoUrl,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.merchant.UserThird">
    update user_third
    set third_type = #{thirdType,jdbcType=TINYINT},
      user_id = #{userId,jdbcType=BIGINT},
      union_id = #{unionId,jdbcType=VARCHAR},
      open_id = #{openId,jdbcType=VARCHAR},
      nick_name = #{nickName,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=TINYINT},
      logo_url = #{logoUrl,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into user_third
    (third_type, user_id, union_id, open_id, nick_name, mobile, gender, logo_url, deleted, 
      op_user_id, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.thirdType,jdbcType=TINYINT}, #{item.userId,jdbcType=BIGINT}, #{item.unionId,jdbcType=VARCHAR}, 
        #{item.openId,jdbcType=VARCHAR}, #{item.nickName,jdbcType=VARCHAR}, #{item.mobile,jdbcType=VARCHAR}, 
        #{item.gender,jdbcType=TINYINT}, #{item.logoUrl,jdbcType=VARCHAR}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into user_third (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'third_type'.toString() == column.value">
          #{item.thirdType,jdbcType=TINYINT}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=BIGINT}
        </if>
        <if test="'union_id'.toString() == column.value">
          #{item.unionId,jdbcType=VARCHAR}
        </if>
        <if test="'open_id'.toString() == column.value">
          #{item.openId,jdbcType=VARCHAR}
        </if>
        <if test="'nick_name'.toString() == column.value">
          #{item.nickName,jdbcType=VARCHAR}
        </if>
        <if test="'mobile'.toString() == column.value">
          #{item.mobile,jdbcType=VARCHAR}
        </if>
        <if test="'gender'.toString() == column.value">
          #{item.gender,jdbcType=TINYINT}
        </if>
        <if test="'logo_url'.toString() == column.value">
          #{item.logoUrl,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.merchant.MerchantSettingMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.merchant.MerchantSetting">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="domain_api" jdbcType="VARCHAR" property="domainApi" />
    <result column="domain_saas" jdbcType="VARCHAR" property="domainSaas" />
    <result column="saas_background" jdbcType="VARCHAR" property="saasBackground" />
    <result column="saas_logo" jdbcType="VARCHAR" property="saasLogo" />
    <result column="saas_web_icon" jdbcType="VARCHAR" property="saasWebIcon" />
    <result column="saas_menu_icon" jdbcType="VARCHAR" property="saasMenuIcon" />
    <result column="show_privacy" jdbcType="TINYINT" property="showPrivacy" />
    <result column="show_qr" jdbcType="TINYINT" property="showQr" />
    <result column="show_reg" jdbcType="TINYINT" property="showReg" />
    <result column="icp" jdbcType="VARCHAR" property="icp" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, domain_api, domain_saas, saas_background, saas_logo, saas_web_icon, 
    saas_menu_icon, show_privacy, show_qr, show_reg, icp, ext, deleted, op_user_id, create_time, 
    op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.merchant.MerchantSettingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from merchant_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_setting
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.merchant.MerchantSetting">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into merchant_setting (merchant_id, domain_api, domain_saas, 
      saas_background, saas_logo, saas_web_icon, 
      saas_menu_icon, show_privacy, show_qr, 
      show_reg, icp, ext, 
      deleted, op_user_id, create_time, 
      op_time)
    values (#{merchantId,jdbcType=BIGINT}, #{domainApi,jdbcType=VARCHAR}, #{domainSaas,jdbcType=VARCHAR}, 
      #{saasBackground,jdbcType=VARCHAR}, #{saasLogo,jdbcType=VARCHAR}, #{saasWebIcon,jdbcType=VARCHAR}, 
      #{saasMenuIcon,jdbcType=VARCHAR}, #{showPrivacy,jdbcType=TINYINT}, #{showQr,jdbcType=TINYINT}, 
      #{showReg,jdbcType=TINYINT}, #{icp,jdbcType=VARCHAR}, #{ext,jdbcType=VARCHAR}, 
      #{deleted,jdbcType=TINYINT}, #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.merchant.MerchantSetting">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into merchant_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="domainApi != null">
        domain_api,
      </if>
      <if test="domainSaas != null">
        domain_saas,
      </if>
      <if test="saasBackground != null">
        saas_background,
      </if>
      <if test="saasLogo != null">
        saas_logo,
      </if>
      <if test="saasWebIcon != null">
        saas_web_icon,
      </if>
      <if test="saasMenuIcon != null">
        saas_menu_icon,
      </if>
      <if test="showPrivacy != null">
        show_privacy,
      </if>
      <if test="showQr != null">
        show_qr,
      </if>
      <if test="showReg != null">
        show_reg,
      </if>
      <if test="icp != null">
        icp,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="domainApi != null">
        #{domainApi,jdbcType=VARCHAR},
      </if>
      <if test="domainSaas != null">
        #{domainSaas,jdbcType=VARCHAR},
      </if>
      <if test="saasBackground != null">
        #{saasBackground,jdbcType=VARCHAR},
      </if>
      <if test="saasLogo != null">
        #{saasLogo,jdbcType=VARCHAR},
      </if>
      <if test="saasWebIcon != null">
        #{saasWebIcon,jdbcType=VARCHAR},
      </if>
      <if test="saasMenuIcon != null">
        #{saasMenuIcon,jdbcType=VARCHAR},
      </if>
      <if test="showPrivacy != null">
        #{showPrivacy,jdbcType=TINYINT},
      </if>
      <if test="showQr != null">
        #{showQr,jdbcType=TINYINT},
      </if>
      <if test="showReg != null">
        #{showReg,jdbcType=TINYINT},
      </if>
      <if test="icp != null">
        #{icp,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.merchant.MerchantSettingExample" resultType="java.lang.Long">
    select count(*) from merchant_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update merchant_setting
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.domainApi != null">
        domain_api = #{record.domainApi,jdbcType=VARCHAR},
      </if>
      <if test="record.domainSaas != null">
        domain_saas = #{record.domainSaas,jdbcType=VARCHAR},
      </if>
      <if test="record.saasBackground != null">
        saas_background = #{record.saasBackground,jdbcType=VARCHAR},
      </if>
      <if test="record.saasLogo != null">
        saas_logo = #{record.saasLogo,jdbcType=VARCHAR},
      </if>
      <if test="record.saasWebIcon != null">
        saas_web_icon = #{record.saasWebIcon,jdbcType=VARCHAR},
      </if>
      <if test="record.saasMenuIcon != null">
        saas_menu_icon = #{record.saasMenuIcon,jdbcType=VARCHAR},
      </if>
      <if test="record.showPrivacy != null">
        show_privacy = #{record.showPrivacy,jdbcType=TINYINT},
      </if>
      <if test="record.showQr != null">
        show_qr = #{record.showQr,jdbcType=TINYINT},
      </if>
      <if test="record.showReg != null">
        show_reg = #{record.showReg,jdbcType=TINYINT},
      </if>
      <if test="record.icp != null">
        icp = #{record.icp,jdbcType=VARCHAR},
      </if>
      <if test="record.ext != null">
        ext = #{record.ext,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update merchant_setting
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      domain_api = #{record.domainApi,jdbcType=VARCHAR},
      domain_saas = #{record.domainSaas,jdbcType=VARCHAR},
      saas_background = #{record.saasBackground,jdbcType=VARCHAR},
      saas_logo = #{record.saasLogo,jdbcType=VARCHAR},
      saas_web_icon = #{record.saasWebIcon,jdbcType=VARCHAR},
      saas_menu_icon = #{record.saasMenuIcon,jdbcType=VARCHAR},
      show_privacy = #{record.showPrivacy,jdbcType=TINYINT},
      show_qr = #{record.showQr,jdbcType=TINYINT},
      show_reg = #{record.showReg,jdbcType=TINYINT},
      icp = #{record.icp,jdbcType=VARCHAR},
      ext = #{record.ext,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.merchant.MerchantSetting">
    update merchant_setting
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="domainApi != null">
        domain_api = #{domainApi,jdbcType=VARCHAR},
      </if>
      <if test="domainSaas != null">
        domain_saas = #{domainSaas,jdbcType=VARCHAR},
      </if>
      <if test="saasBackground != null">
        saas_background = #{saasBackground,jdbcType=VARCHAR},
      </if>
      <if test="saasLogo != null">
        saas_logo = #{saasLogo,jdbcType=VARCHAR},
      </if>
      <if test="saasWebIcon != null">
        saas_web_icon = #{saasWebIcon,jdbcType=VARCHAR},
      </if>
      <if test="saasMenuIcon != null">
        saas_menu_icon = #{saasMenuIcon,jdbcType=VARCHAR},
      </if>
      <if test="showPrivacy != null">
        show_privacy = #{showPrivacy,jdbcType=TINYINT},
      </if>
      <if test="showQr != null">
        show_qr = #{showQr,jdbcType=TINYINT},
      </if>
      <if test="showReg != null">
        show_reg = #{showReg,jdbcType=TINYINT},
      </if>
      <if test="icp != null">
        icp = #{icp,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.merchant.MerchantSetting">
    update merchant_setting
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      domain_api = #{domainApi,jdbcType=VARCHAR},
      domain_saas = #{domainSaas,jdbcType=VARCHAR},
      saas_background = #{saasBackground,jdbcType=VARCHAR},
      saas_logo = #{saasLogo,jdbcType=VARCHAR},
      saas_web_icon = #{saasWebIcon,jdbcType=VARCHAR},
      saas_menu_icon = #{saasMenuIcon,jdbcType=VARCHAR},
      show_privacy = #{showPrivacy,jdbcType=TINYINT},
      show_qr = #{showQr,jdbcType=TINYINT},
      show_reg = #{showReg,jdbcType=TINYINT},
      icp = #{icp,jdbcType=VARCHAR},
      ext = #{ext,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into merchant_setting
    (merchant_id, domain_api, domain_saas, saas_background, saas_logo, saas_web_icon, 
      saas_menu_icon, show_privacy, show_qr, show_reg, icp, ext, deleted, op_user_id, 
      create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.domainApi,jdbcType=VARCHAR}, #{item.domainSaas,jdbcType=VARCHAR}, 
        #{item.saasBackground,jdbcType=VARCHAR}, #{item.saasLogo,jdbcType=VARCHAR}, #{item.saasWebIcon,jdbcType=VARCHAR}, 
        #{item.saasMenuIcon,jdbcType=VARCHAR}, #{item.showPrivacy,jdbcType=TINYINT}, #{item.showQr,jdbcType=TINYINT}, 
        #{item.showReg,jdbcType=TINYINT}, #{item.icp,jdbcType=VARCHAR}, #{item.ext,jdbcType=VARCHAR}, 
        #{item.deleted,jdbcType=TINYINT}, #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into merchant_setting (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'domain_api'.toString() == column.value">
          #{item.domainApi,jdbcType=VARCHAR}
        </if>
        <if test="'domain_saas'.toString() == column.value">
          #{item.domainSaas,jdbcType=VARCHAR}
        </if>
        <if test="'saas_background'.toString() == column.value">
          #{item.saasBackground,jdbcType=VARCHAR}
        </if>
        <if test="'saas_logo'.toString() == column.value">
          #{item.saasLogo,jdbcType=VARCHAR}
        </if>
        <if test="'saas_web_icon'.toString() == column.value">
          #{item.saasWebIcon,jdbcType=VARCHAR}
        </if>
        <if test="'saas_menu_icon'.toString() == column.value">
          #{item.saasMenuIcon,jdbcType=VARCHAR}
        </if>
        <if test="'show_privacy'.toString() == column.value">
          #{item.showPrivacy,jdbcType=TINYINT}
        </if>
        <if test="'show_qr'.toString() == column.value">
          #{item.showQr,jdbcType=TINYINT}
        </if>
        <if test="'show_reg'.toString() == column.value">
          #{item.showReg,jdbcType=TINYINT}
        </if>
        <if test="'icp'.toString() == column.value">
          #{item.icp,jdbcType=VARCHAR}
        </if>
        <if test="'ext'.toString() == column.value">
          #{item.ext,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.merchant.PaymentChannelMapper">
    
    <!-- 添加 BaseResultMap -->
    <resultMap id="BaseResultMap" type="com.ql.rent.entity.merchant.PaymentChannel">
        <id column="id" property="id"/>
        <result column="legal_entity_id" property="legalEntityId"/>
        <result column="payment_channel_type_code" property="paymentChannelTypeCode"/>
        <result column="payment_channel_account" property="paymentChannelAccount"/>
        <result column="payment_channel_name" property="paymentChannelName"/>
        <result column="verify_status" property="verifyStatus"/>
        <result column="last_verify_time" property="lastVerifyTime"/>
        <result column="verify_fail_count" property="verifyFailCount"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="deleted" property="deleted"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="op_user_id" property="opUserId"/>
        <result column="op_time" property="opTime"/>
    </resultMap>
    
    <!-- 修改所有查询方法使用 resultMap -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM payment_channel WHERE id = #{id} AND deleted = 0
    </select>
    
    <!-- PaymentChannelVO 的 resultMap -->
    <resultMap id="VOResultMap" type="com.ql.rent.vo.merchant.PaymentChannelVO">
        <id column="id" property="paymentChannelId"/>
        <result column="payment_channel_type_code" property="paymentChannelTypeCode"/>
        <result column="payment_channel_account" property="paymentChannelAccount"/>
        <result column="payment_channel_name" property="paymentChannelName"/>
        <result column="legal_entity_id" property="legalEntityId"/>
        <result column="status" property="status"/>
        <result column="verify_status" property="verifyStatus"/>
        <result column="create_time" property="createTime"/>
    </resultMap>
    
    <select id="selectPage" resultMap="VOResultMap">
        SELECT * FROM payment_channel
        <where>
            deleted = 0
            <if test="legalEntityId != null">AND legal_entity_id = #{legalEntityId}</if>
            <if test="paymentChannelTypeCode != null">AND payment_channel_type_code = #{paymentChannelTypeCode}</if>
            <if test="paymentChannelName != null and paymentChannelName != ''">
                AND payment_channel_name LIKE CONCAT('%', #{paymentChannelName}, '%')
            </if>
            <if test="status != null">AND status = #{status}</if>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <update id="update">
        UPDATE payment_channel
        SET verify_status = #{verifyStatus},
            verify_fail_count = #{verifyFailCount},
            last_verify_time = #{lastVerifyTime},
            payment_channel_name = #{paymentChannelName},
            payment_channel_type_code = #{paymentChannelTypeCode},
            status = #{status},
            remark = #{remark},
            op_time = #{opTime}
        WHERE id = #{id} AND deleted = 0
    </update>
    
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO payment_channel (
            payment_channel_name, payment_channel_type_code,
            legal_entity_id, template_code, status, verify_status, remark,
            verify_fail_count, last_verify_time, create_time, op_time, deleted
        ) VALUES (
            #{paymentChannelName}, #{paymentChannelTypeCode},
            #{legalEntityId}, #{templateCode}, #{status}, #{verifyStatus}, #{remark},
            #{verifyFailCount}, #{lastVerifyTime}, #{createTime}, #{opTime}, 0
        )
    </insert>
    
    <select id="countTotal" resultType="long">
        SELECT COUNT(*)
        FROM payment_channel
        <where>
            deleted = 0
            <if test="legalEntityId != null">AND legal_entity_id = #{legalEntityId}</if>
            <if test="paymentChannelTypeCode != null">AND payment_channel_type_code = #{paymentChannelTypeCode}</if>
            <if test="paymentChannelName != null and paymentChannelName != ''">
                AND payment_channel_name LIKE CONCAT('%', #{paymentChannelName}, '%')
            </if>
            <if test="status != null">AND status = #{status}</if>
        </where>
    </select>
    
    <select id="selectLegalEntityIdsByMerchantId" resultType="java.util.Map">
        SELECT 
            pc.legal_entity_id as legalEntityId,
            le.entity_name as entityName,
            pc.payment_channel_type_code as paymentChannelTypeCode,
            mpcb.payment_channel_id as paymentChannelId,
            mpcb.status as status,
            mpcb.discount as discount
        FROM payment_channel pc
        INNER JOIN merchant_payment_channel_bind mpcb 
            ON pc.id = mpcb.payment_channel_id
        INNER JOIN legal_entity le
            ON pc.legal_entity_id = le.id
        WHERE mpcb.merchant_id = #{merchantId}
            AND pc.deleted = 0
            AND mpcb.deleted = 0
            AND le.deleted = 0
    </select>
    
    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT 
           *
        FROM payment_channel
        <where>
            deleted = 0
            <if test="channelIds != null and channelIds.size() > 0">
                AND id IN
                <foreach collection="channelIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    
</mapper> 
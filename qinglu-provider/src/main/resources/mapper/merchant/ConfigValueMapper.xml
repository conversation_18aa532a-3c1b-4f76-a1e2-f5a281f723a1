<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.merchant.ConfigValueMapper">
    <resultMap id="BaseResultMap" type="com.ql.rent.entity.merchant.ConfigValue">
        <id column="id" property="id" />
        <result column="instance_id" property="instanceId" />
        <result column="item_id" property="itemId" />
        <result column="config_value" property="configValue" />
        <result column="encrypted_value" property="encryptedValue" />
        <result column="status" property="status" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="op_time" property="opTime" />
        <result column="op_user_id" property="opUserId" />
    </resultMap>

    <insert id="insert" parameterType="com.ql.rent.entity.merchant.ConfigValue" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO config_value (
            instance_id, item_id, config_value, encrypted_value, status,
            create_time, create_user_id, op_time, op_user_id
        ) VALUES (
            #{instanceId}, #{itemId}, #{configValue}, #{encryptedValue}, #{status},
            #{createTime}, #{createUserId}, #{opTime}, #{opUserId}
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO config_value (
            instance_id, item_id, config_value, encrypted_value, status,
            create_time, create_user_id, op_time, op_user_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.instanceId}, #{item.itemId}, #{item.configValue}, #{item.encryptedValue}, #{item.status},
            #{item.createTime}, #{item.createUserId}, #{item.opTime}, #{item.opUserId})
        </foreach>
    </insert>

    <update id="update" parameterType="com.ql.rent.entity.merchant.ConfigValue">
        UPDATE config_value
        SET config_value = #{configValue},
            encrypted_value = #{encryptedValue},
            status = #{status},
            op_time = #{opTime},
            op_user_id = #{opUserId}
        WHERE id = #{id}
        AND deleted = 0
    </update>

    <select id="selectByInstanceId" resultMap="BaseResultMap">
        SELECT *
        FROM config_value
        WHERE instance_id = #{instanceId}
        AND deleted = 0
        ORDER BY id ASC
    </select>

    <select id="selectByInstanceIdAndItemId" resultMap="BaseResultMap">
        SELECT *
        FROM config_value
        WHERE instance_id = #{instanceId}
        AND item_id = #{itemId}
        AND deleted = 0
        LIMIT 1
    </select>

    <update id="updateStatus">
        UPDATE config_value
        SET status = #{status},
            op_time = #{opTime}
        WHERE id = #{id}
        AND deleted = 0
    </update>

    <select id="selectConfigItems" resultType="com.ql.rent.vo.merchant.PaymentChannelDetailVO$ConfigItemVO">
        SELECT 
            d.item_code as itemCode,
            d.item_name as itemName,
            CASE 
                WHEN d.is_encrypted = 1 THEN v.encrypted_value 
                ELSE v.config_value 
            END AS itemValue
        FROM config_value v
        INNER JOIN config_item_definition d ON v.item_id = d.id
        WHERE v.instance_id = #{instanceId}
        AND v.status = 1
        AND d.status = 1
    </select>

    <select id="selectConfigItemsByInstanceIds" resultType="com.ql.rent.vo.merchant.ConfigItemInstanceVO">
        SELECT 
            cv.instance_id AS instanceId,
            cid.item_code AS itemCode,
            CASE 
                WHEN cid.is_encrypted = 1 THEN cv.encrypted_value 
                ELSE cv.config_value 
            END AS itemValue,
            cid.item_name AS itemName,
            cid.item_type AS itemType
        FROM config_value cv
        INNER JOIN config_item_definition cid ON cv.item_id = cid.id
        WHERE cv.instance_id IN
        <foreach collection="instanceIds" item="instanceId" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
        AND cv.deleted = 0
        ORDER BY cv.instance_id, cid.sort_order
    </select>

    <select id="selectByConfigValue" resultMap="BaseResultMap">
        SELECT *
        FROM config_value
        WHERE config_value = #{configValue}
        AND deleted = 0
    </select>
</mapper> 
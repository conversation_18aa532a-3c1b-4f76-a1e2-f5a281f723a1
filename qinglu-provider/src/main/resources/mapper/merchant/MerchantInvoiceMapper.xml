<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.merchant.MerchantInvoiceMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.merchant.MerchantInvoice">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="invoice_type" jdbcType="TINYINT" property="invoiceType" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="uscc" jdbcType="VARCHAR" property="uscc" />
    <result column="open_bank" jdbcType="VARCHAR" property="openBank" />
    <result column="open_account" jdbcType="VARCHAR" property="openAccount" />
    <result column="register_address" jdbcType="VARCHAR" property="registerAddress" />
    <result column="register_phone" jdbcType="VARCHAR" property="registerPhone" />
    <result column="receive_name" jdbcType="VARCHAR" property="receiveName" />
    <result column="receive_phone" jdbcType="VARCHAR" property="receivePhone" />
    <result column="receive_province_id" jdbcType="BIGINT" property="receiveProvinceId" />
    <result column="receive_city_id" jdbcType="BIGINT" property="receiveCityId" />
    <result column="receive_area_id" jdbcType="BIGINT" property="receiveAreaId" />
    <result column="receive_address" jdbcType="VARCHAR" property="receiveAddress" />
    <result column="receive_mail" jdbcType="VARCHAR" property="receiveMail" />
    <result column="last_ver" jdbcType="BIGINT" property="lastVer" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, invoice_type, title, uscc, open_bank, open_account, register_address, 
    register_phone, receive_name, receive_phone, receive_province_id, receive_city_id, 
    receive_area_id, receive_address, receive_mail, last_ver, create_time, op_time, op_user_id
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.merchant.MerchantInvoiceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from merchant_invoice
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_invoice
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_invoice
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.merchant.MerchantInvoiceExample">
    delete from merchant_invoice
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.merchant.MerchantInvoice">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into merchant_invoice (merchant_id, invoice_type, title, 
      uscc, open_bank, open_account, 
      register_address, register_phone, receive_name, 
      receive_phone, receive_province_id, receive_city_id, 
      receive_area_id, receive_address, receive_mail, 
      last_ver, create_time, op_time, 
      op_user_id)
    values (#{merchantId,jdbcType=BIGINT}, #{invoiceType,jdbcType=TINYINT}, #{title,jdbcType=VARCHAR}, 
      #{uscc,jdbcType=VARCHAR}, #{openBank,jdbcType=VARCHAR}, #{openAccount,jdbcType=VARCHAR}, 
      #{registerAddress,jdbcType=VARCHAR}, #{registerPhone,jdbcType=VARCHAR}, #{receiveName,jdbcType=VARCHAR}, 
      #{receivePhone,jdbcType=VARCHAR}, #{receiveProvinceId,jdbcType=BIGINT}, #{receiveCityId,jdbcType=BIGINT}, 
      #{receiveAreaId,jdbcType=BIGINT}, #{receiveAddress,jdbcType=VARCHAR}, #{receiveMail,jdbcType=VARCHAR}, 
      #{lastVer,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT}, 
      #{opUserId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.merchant.MerchantInvoice">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into merchant_invoice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="invoiceType != null">
        invoice_type,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="uscc != null">
        uscc,
      </if>
      <if test="openBank != null">
        open_bank,
      </if>
      <if test="openAccount != null">
        open_account,
      </if>
      <if test="registerAddress != null">
        register_address,
      </if>
      <if test="registerPhone != null">
        register_phone,
      </if>
      <if test="receiveName != null">
        receive_name,
      </if>
      <if test="receivePhone != null">
        receive_phone,
      </if>
      <if test="receiveProvinceId != null">
        receive_province_id,
      </if>
      <if test="receiveCityId != null">
        receive_city_id,
      </if>
      <if test="receiveAreaId != null">
        receive_area_id,
      </if>
      <if test="receiveAddress != null">
        receive_address,
      </if>
      <if test="receiveMail != null">
        receive_mail,
      </if>
      <if test="lastVer != null">
        last_ver,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="uscc != null">
        #{uscc,jdbcType=VARCHAR},
      </if>
      <if test="openBank != null">
        #{openBank,jdbcType=VARCHAR},
      </if>
      <if test="openAccount != null">
        #{openAccount,jdbcType=VARCHAR},
      </if>
      <if test="registerAddress != null">
        #{registerAddress,jdbcType=VARCHAR},
      </if>
      <if test="registerPhone != null">
        #{registerPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveName != null">
        #{receiveName,jdbcType=VARCHAR},
      </if>
      <if test="receivePhone != null">
        #{receivePhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveProvinceId != null">
        #{receiveProvinceId,jdbcType=BIGINT},
      </if>
      <if test="receiveCityId != null">
        #{receiveCityId,jdbcType=BIGINT},
      </if>
      <if test="receiveAreaId != null">
        #{receiveAreaId,jdbcType=BIGINT},
      </if>
      <if test="receiveAddress != null">
        #{receiveAddress,jdbcType=VARCHAR},
      </if>
      <if test="receiveMail != null">
        #{receiveMail,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        #{lastVer,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.merchant.MerchantInvoiceExample" resultType="java.lang.Long">
    select count(*) from merchant_invoice
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update merchant_invoice
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.invoiceType != null">
        invoice_type = #{record.invoiceType,jdbcType=TINYINT},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.uscc != null">
        uscc = #{record.uscc,jdbcType=VARCHAR},
      </if>
      <if test="record.openBank != null">
        open_bank = #{record.openBank,jdbcType=VARCHAR},
      </if>
      <if test="record.openAccount != null">
        open_account = #{record.openAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.registerAddress != null">
        register_address = #{record.registerAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.registerPhone != null">
        register_phone = #{record.registerPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveName != null">
        receive_name = #{record.receiveName,jdbcType=VARCHAR},
      </if>
      <if test="record.receivePhone != null">
        receive_phone = #{record.receivePhone,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveProvinceId != null">
        receive_province_id = #{record.receiveProvinceId,jdbcType=BIGINT},
      </if>
      <if test="record.receiveCityId != null">
        receive_city_id = #{record.receiveCityId,jdbcType=BIGINT},
      </if>
      <if test="record.receiveAreaId != null">
        receive_area_id = #{record.receiveAreaId,jdbcType=BIGINT},
      </if>
      <if test="record.receiveAddress != null">
        receive_address = #{record.receiveAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveMail != null">
        receive_mail = #{record.receiveMail,jdbcType=VARCHAR},
      </if>
      <if test="record.lastVer != null">
        last_ver = #{record.lastVer,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update merchant_invoice
    set id = #{record.id,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      invoice_type = #{record.invoiceType,jdbcType=TINYINT},
      title = #{record.title,jdbcType=VARCHAR},
      uscc = #{record.uscc,jdbcType=VARCHAR},
      open_bank = #{record.openBank,jdbcType=VARCHAR},
      open_account = #{record.openAccount,jdbcType=VARCHAR},
      register_address = #{record.registerAddress,jdbcType=VARCHAR},
      register_phone = #{record.registerPhone,jdbcType=VARCHAR},
      receive_name = #{record.receiveName,jdbcType=VARCHAR},
      receive_phone = #{record.receivePhone,jdbcType=VARCHAR},
      receive_province_id = #{record.receiveProvinceId,jdbcType=BIGINT},
      receive_city_id = #{record.receiveCityId,jdbcType=BIGINT},
      receive_area_id = #{record.receiveAreaId,jdbcType=BIGINT},
      receive_address = #{record.receiveAddress,jdbcType=VARCHAR},
      receive_mail = #{record.receiveMail,jdbcType=VARCHAR},
      last_ver = #{record.lastVer,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.merchant.MerchantInvoice">
    update merchant_invoice
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="invoiceType != null">
        invoice_type = #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="uscc != null">
        uscc = #{uscc,jdbcType=VARCHAR},
      </if>
      <if test="openBank != null">
        open_bank = #{openBank,jdbcType=VARCHAR},
      </if>
      <if test="openAccount != null">
        open_account = #{openAccount,jdbcType=VARCHAR},
      </if>
      <if test="registerAddress != null">
        register_address = #{registerAddress,jdbcType=VARCHAR},
      </if>
      <if test="registerPhone != null">
        register_phone = #{registerPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveName != null">
        receive_name = #{receiveName,jdbcType=VARCHAR},
      </if>
      <if test="receivePhone != null">
        receive_phone = #{receivePhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveProvinceId != null">
        receive_province_id = #{receiveProvinceId,jdbcType=BIGINT},
      </if>
      <if test="receiveCityId != null">
        receive_city_id = #{receiveCityId,jdbcType=BIGINT},
      </if>
      <if test="receiveAreaId != null">
        receive_area_id = #{receiveAreaId,jdbcType=BIGINT},
      </if>
      <if test="receiveAddress != null">
        receive_address = #{receiveAddress,jdbcType=VARCHAR},
      </if>
      <if test="receiveMail != null">
        receive_mail = #{receiveMail,jdbcType=VARCHAR},
      </if>
      <if test="lastVer != null">
        last_ver = #{lastVer,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.merchant.MerchantInvoice">
    update merchant_invoice
    set merchant_id = #{merchantId,jdbcType=BIGINT},
      invoice_type = #{invoiceType,jdbcType=TINYINT},
      title = #{title,jdbcType=VARCHAR},
      uscc = #{uscc,jdbcType=VARCHAR},
      open_bank = #{openBank,jdbcType=VARCHAR},
      open_account = #{openAccount,jdbcType=VARCHAR},
      register_address = #{registerAddress,jdbcType=VARCHAR},
      register_phone = #{registerPhone,jdbcType=VARCHAR},
      receive_name = #{receiveName,jdbcType=VARCHAR},
      receive_phone = #{receivePhone,jdbcType=VARCHAR},
      receive_province_id = #{receiveProvinceId,jdbcType=BIGINT},
      receive_city_id = #{receiveCityId,jdbcType=BIGINT},
      receive_area_id = #{receiveAreaId,jdbcType=BIGINT},
      receive_address = #{receiveAddress,jdbcType=VARCHAR},
      receive_mail = #{receiveMail,jdbcType=VARCHAR},
      last_ver = #{lastVer,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into merchant_invoice
    (merchant_id, invoice_type, title, uscc, open_bank, open_account, register_address, 
      register_phone, receive_name, receive_phone, receive_province_id, receive_city_id, 
      receive_area_id, receive_address, receive_mail, last_ver, create_time, op_time, 
      op_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.merchantId,jdbcType=BIGINT}, #{item.invoiceType,jdbcType=TINYINT}, #{item.title,jdbcType=VARCHAR}, 
        #{item.uscc,jdbcType=VARCHAR}, #{item.openBank,jdbcType=VARCHAR}, #{item.openAccount,jdbcType=VARCHAR}, 
        #{item.registerAddress,jdbcType=VARCHAR}, #{item.registerPhone,jdbcType=VARCHAR}, 
        #{item.receiveName,jdbcType=VARCHAR}, #{item.receivePhone,jdbcType=VARCHAR}, #{item.receiveProvinceId,jdbcType=BIGINT}, 
        #{item.receiveCityId,jdbcType=BIGINT}, #{item.receiveAreaId,jdbcType=BIGINT}, #{item.receiveAddress,jdbcType=VARCHAR}, 
        #{item.receiveMail,jdbcType=VARCHAR}, #{item.lastVer,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into merchant_invoice (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'invoice_type'.toString() == column.value">
          #{item.invoiceType,jdbcType=TINYINT}
        </if>
        <if test="'title'.toString() == column.value">
          #{item.title,jdbcType=VARCHAR}
        </if>
        <if test="'uscc'.toString() == column.value">
          #{item.uscc,jdbcType=VARCHAR}
        </if>
        <if test="'open_bank'.toString() == column.value">
          #{item.openBank,jdbcType=VARCHAR}
        </if>
        <if test="'open_account'.toString() == column.value">
          #{item.openAccount,jdbcType=VARCHAR}
        </if>
        <if test="'register_address'.toString() == column.value">
          #{item.registerAddress,jdbcType=VARCHAR}
        </if>
        <if test="'register_phone'.toString() == column.value">
          #{item.registerPhone,jdbcType=VARCHAR}
        </if>
        <if test="'receive_name'.toString() == column.value">
          #{item.receiveName,jdbcType=VARCHAR}
        </if>
        <if test="'receive_phone'.toString() == column.value">
          #{item.receivePhone,jdbcType=VARCHAR}
        </if>
        <if test="'receive_province_id'.toString() == column.value">
          #{item.receiveProvinceId,jdbcType=BIGINT}
        </if>
        <if test="'receive_city_id'.toString() == column.value">
          #{item.receiveCityId,jdbcType=BIGINT}
        </if>
        <if test="'receive_area_id'.toString() == column.value">
          #{item.receiveAreaId,jdbcType=BIGINT}
        </if>
        <if test="'receive_address'.toString() == column.value">
          #{item.receiveAddress,jdbcType=VARCHAR}
        </if>
        <if test="'receive_mail'.toString() == column.value">
          #{item.receiveMail,jdbcType=VARCHAR}
        </if>
        <if test="'last_ver'.toString() == column.value">
          #{item.lastVer,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.merchant.MerchantInfoMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.merchant.MerchantInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="name_short" jdbcType="VARCHAR" property="nameShort" />
    <result column="merchant_type" jdbcType="TINYINT" property="merchantType" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="uscc" jdbcType="VARCHAR" property="uscc" />
    <result column="validity_start" jdbcType="VARCHAR" property="validityStart" />
    <result column="validity_end" jdbcType="VARCHAR" property="validityEnd" />
    <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
    <result column="link_name" jdbcType="VARCHAR" property="linkName" />
    <result column="legal_name" jdbcType="VARCHAR" property="legalName" />
    <result column="legal_phone" jdbcType="VARCHAR" property="legalPhone" />
    <result column="legal_id_no" jdbcType="VARCHAR" property="legalIdNo" />
    <result column="reg_source" jdbcType="VARCHAR" property="regSource" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="is_test" jdbcType="TINYINT" property="isTest" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, phone, name, name_short, merchant_type, address, uscc, validity_start, validity_end, 
    country_code, link_name, legal_name, legal_phone, legal_id_no, reg_source, deleted, 
    is_test, op_user_id, create_time, op_time,  tag
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.merchant.MerchantInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from merchant_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.merchant.MerchantInfoExample">
    delete from merchant_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.merchant.MerchantInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into merchant_info (phone, name, name_short, 
      merchant_type, address, uscc, 
      validity_start, validity_end, country_code, 
      link_name, legal_name, legal_phone, 
      legal_id_no, reg_source, deleted, 
      is_test, op_user_id, create_time, 
      op_time)
    values (#{phone,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{nameShort,jdbcType=VARCHAR}, 
      #{merchantType,jdbcType=TINYINT}, #{address,jdbcType=VARCHAR}, #{uscc,jdbcType=VARCHAR}, 
      #{validityStart,jdbcType=VARCHAR}, #{validityEnd,jdbcType=VARCHAR}, #{countryCode,jdbcType=VARCHAR}, 
      #{linkName,jdbcType=VARCHAR}, #{legalName,jdbcType=VARCHAR}, #{legalPhone,jdbcType=VARCHAR}, 
      #{legalIdNo,jdbcType=VARCHAR}, #{regSource,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, 
      #{isTest,jdbcType=TINYINT}, #{opUserId,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.merchant.MerchantInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into merchant_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="phone != null">
        phone,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="nameShort != null">
        name_short,
      </if>
      <if test="merchantType != null">
        merchant_type,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="uscc != null">
        uscc,
      </if>
      <if test="validityStart != null">
        validity_start,
      </if>
      <if test="validityEnd != null">
        validity_end,
      </if>
      <if test="countryCode != null">
        country_code,
      </if>
      <if test="linkName != null">
        link_name,
      </if>
      <if test="legalName != null">
        legal_name,
      </if>
      <if test="legalPhone != null">
        legal_phone,
      </if>
      <if test="legalIdNo != null">
        legal_id_no,
      </if>
      <if test="regSource != null">
        reg_source,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="isTest != null">
        is_test,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameShort != null">
        #{nameShort,jdbcType=VARCHAR},
      </if>
      <if test="merchantType != null">
        #{merchantType,jdbcType=TINYINT},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="uscc != null">
        #{uscc,jdbcType=VARCHAR},
      </if>
      <if test="validityStart != null">
        #{validityStart,jdbcType=VARCHAR},
      </if>
      <if test="validityEnd != null">
        #{validityEnd,jdbcType=VARCHAR},
      </if>
      <if test="countryCode != null">
        #{countryCode,jdbcType=VARCHAR},
      </if>
      <if test="linkName != null">
        #{linkName,jdbcType=VARCHAR},
      </if>
      <if test="legalName != null">
        #{legalName,jdbcType=VARCHAR},
      </if>
      <if test="legalPhone != null">
        #{legalPhone,jdbcType=VARCHAR},
      </if>
      <if test="legalIdNo != null">
        #{legalIdNo,jdbcType=VARCHAR},
      </if>
      <if test="regSource != null">
        #{regSource,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="isTest != null">
        #{isTest,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.merchant.MerchantInfoExample" resultType="java.lang.Long">
    select count(*) from merchant_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update merchant_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.nameShort != null">
        name_short = #{record.nameShort,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantType != null">
        merchant_type = #{record.merchantType,jdbcType=TINYINT},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.uscc != null">
        uscc = #{record.uscc,jdbcType=VARCHAR},
      </if>
      <if test="record.validityStart != null">
        validity_start = #{record.validityStart,jdbcType=VARCHAR},
      </if>
      <if test="record.validityEnd != null">
        validity_end = #{record.validityEnd,jdbcType=VARCHAR},
      </if>
      <if test="record.countryCode != null">
        country_code = #{record.countryCode,jdbcType=VARCHAR},
      </if>
      <if test="record.linkName != null">
        link_name = #{record.linkName,jdbcType=VARCHAR},
      </if>
      <if test="record.legalName != null">
        legal_name = #{record.legalName,jdbcType=VARCHAR},
      </if>
      <if test="record.legalPhone != null">
        legal_phone = #{record.legalPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.legalIdNo != null">
        legal_id_no = #{record.legalIdNo,jdbcType=VARCHAR},
      </if>
      <if test="record.regSource != null">
        reg_source = #{record.regSource,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.isTest != null">
        is_test = #{record.isTest,jdbcType=TINYINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update merchant_info
    set id = #{record.id,jdbcType=BIGINT},
      phone = #{record.phone,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      name_short = #{record.nameShort,jdbcType=VARCHAR},
      merchant_type = #{record.merchantType,jdbcType=TINYINT},
      address = #{record.address,jdbcType=VARCHAR},
      uscc = #{record.uscc,jdbcType=VARCHAR},
      validity_start = #{record.validityStart,jdbcType=VARCHAR},
      validity_end = #{record.validityEnd,jdbcType=VARCHAR},
      country_code = #{record.countryCode,jdbcType=VARCHAR},
      link_name = #{record.linkName,jdbcType=VARCHAR},
      legal_name = #{record.legalName,jdbcType=VARCHAR},
      legal_phone = #{record.legalPhone,jdbcType=VARCHAR},
      legal_id_no = #{record.legalIdNo,jdbcType=VARCHAR},
      reg_source = #{record.regSource,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      is_test = #{record.isTest,jdbcType=TINYINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.merchant.MerchantInfo">
    update merchant_info
    <set>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameShort != null">
        name_short = #{nameShort,jdbcType=VARCHAR},
      </if>
      <if test="merchantType != null">
        merchant_type = #{merchantType,jdbcType=TINYINT},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="uscc != null">
        uscc = #{uscc,jdbcType=VARCHAR},
      </if>
      <if test="validityStart != null">
        validity_start = #{validityStart,jdbcType=VARCHAR},
      </if>
      <if test="validityEnd != null">
        validity_end = #{validityEnd,jdbcType=VARCHAR},
      </if>
      <if test="countryCode != null">
        country_code = #{countryCode,jdbcType=VARCHAR},
      </if>
      <if test="linkName != null">
        link_name = #{linkName,jdbcType=VARCHAR},
      </if>
      <if test="legalName != null">
        legal_name = #{legalName,jdbcType=VARCHAR},
      </if>
      <if test="legalPhone != null">
        legal_phone = #{legalPhone,jdbcType=VARCHAR},
      </if>
      <if test="legalIdNo != null">
        legal_id_no = #{legalIdNo,jdbcType=VARCHAR},
      </if>
      <if test="regSource != null">
        reg_source = #{regSource,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="isTest != null">
        is_test = #{isTest,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.merchant.MerchantInfo">
    update merchant_info
    set phone = #{phone,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      name_short = #{nameShort,jdbcType=VARCHAR},
      merchant_type = #{merchantType,jdbcType=TINYINT},
      address = #{address,jdbcType=VARCHAR},
      uscc = #{uscc,jdbcType=VARCHAR},
      validity_start = #{validityStart,jdbcType=VARCHAR},
      validity_end = #{validityEnd,jdbcType=VARCHAR},
      country_code = #{countryCode,jdbcType=VARCHAR},
      link_name = #{linkName,jdbcType=VARCHAR},
      legal_name = #{legalName,jdbcType=VARCHAR},
      legal_phone = #{legalPhone,jdbcType=VARCHAR},
      legal_id_no = #{legalIdNo,jdbcType=VARCHAR},
      reg_source = #{regSource,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      is_test = #{isTest,jdbcType=TINYINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into merchant_info
    (phone, name, name_short, merchant_type, address, uscc, validity_start, validity_end, 
      country_code, link_name, legal_name, legal_phone, legal_id_no, reg_source, deleted, 
      is_test, op_user_id, create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.phone,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.nameShort,jdbcType=VARCHAR}, 
        #{item.merchantType,jdbcType=TINYINT}, #{item.address,jdbcType=VARCHAR}, #{item.uscc,jdbcType=VARCHAR}, 
        #{item.validityStart,jdbcType=VARCHAR}, #{item.validityEnd,jdbcType=VARCHAR}, #{item.countryCode,jdbcType=VARCHAR}, 
        #{item.linkName,jdbcType=VARCHAR}, #{item.legalName,jdbcType=VARCHAR}, #{item.legalPhone,jdbcType=VARCHAR}, 
        #{item.legalIdNo,jdbcType=VARCHAR}, #{item.regSource,jdbcType=VARCHAR}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.isTest,jdbcType=TINYINT}, #{item.opUserId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.opTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into merchant_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'phone'.toString() == column.value">
          #{item.phone,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'name_short'.toString() == column.value">
          #{item.nameShort,jdbcType=VARCHAR}
        </if>
        <if test="'merchant_type'.toString() == column.value">
          #{item.merchantType,jdbcType=TINYINT}
        </if>
        <if test="'address'.toString() == column.value">
          #{item.address,jdbcType=VARCHAR}
        </if>
        <if test="'uscc'.toString() == column.value">
          #{item.uscc,jdbcType=VARCHAR}
        </if>
        <if test="'validity_start'.toString() == column.value">
          #{item.validityStart,jdbcType=VARCHAR}
        </if>
        <if test="'validity_end'.toString() == column.value">
          #{item.validityEnd,jdbcType=VARCHAR}
        </if>
        <if test="'country_code'.toString() == column.value">
          #{item.countryCode,jdbcType=VARCHAR}
        </if>
        <if test="'link_name'.toString() == column.value">
          #{item.linkName,jdbcType=VARCHAR}
        </if>
        <if test="'legal_name'.toString() == column.value">
          #{item.legalName,jdbcType=VARCHAR}
        </if>
        <if test="'legal_phone'.toString() == column.value">
          #{item.legalPhone,jdbcType=VARCHAR}
        </if>
        <if test="'legal_id_no'.toString() == column.value">
          #{item.legalIdNo,jdbcType=VARCHAR}
        </if>
        <if test="'reg_source'.toString() == column.value">
          #{item.regSource,jdbcType=VARCHAR}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'is_test'.toString() == column.value">
          #{item.isTest,jdbcType=TINYINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
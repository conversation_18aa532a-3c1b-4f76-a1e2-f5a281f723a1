<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.merchant.LegalEntityMapper">
    
    <resultMap id="BaseResultMap" type="com.ql.rent.entity.merchant.LegalEntity">
        <id column="id" property="id" />
        <result column="entity_name" property="entityName" />
        <result column="entity_type" property="entityType" />
        <result column="business_license" property="businessLicense" />
        <result column="legal_person" property="legalPerson" />
        <result column="tax_code" property="taxCode" />
        <result column="contact_name" property="contactName" />
        <result column="contact_phone" property="contactPhone" />
        <result column="contact_email" property="contactEmail" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="address" property="address" />
        <result column="status" property="status" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="op_time" property="opTime" />
        <result column="op_user_id" property="opUserId" />
    </resultMap>

    <sql id="Base_Column_List">
        id, entity_name, entity_type, business_license, legal_person, tax_code,
        contact_name, contact_phone, contact_email, province, city, address,
        status, deleted, create_time, create_user_id, op_time, op_user_id
    </sql>

    <insert id="insert" parameterType="com.ql.rent.entity.merchant.LegalEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO legal_entity (
            entity_name, entity_type, business_license, legal_person, tax_code,
            contact_name, contact_phone, contact_email, province, city, address,
            status, deleted, create_time, create_user_id, op_time, op_user_id
        ) VALUES (
            #{entityName}, #{entityType}, #{businessLicense}, #{legalPerson}, #{taxCode},
            #{contactName}, #{contactPhone}, #{contactEmail}, #{province}, #{city}, #{address},
            #{status}, #{deleted}, #{createTime}, #{createUserId}, #{opTime}, #{opUserId}
        )
    </insert>

    <update id="update" parameterType="com.ql.rent.entity.merchant.LegalEntity">
        UPDATE legal_entity
        <set>
            <if test="entityName != null">entity_name = #{entityName},</if>
            <if test="entityType != null">entity_type = #{entityType},</if>
            <if test="businessLicense != null">business_license = #{businessLicense},</if>
            <if test="legalPerson != null">legal_person = #{legalPerson},</if>
            <if test="taxCode != null">tax_code = #{taxCode},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null">contact_email = #{contactEmail},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="address != null">address = #{address},</if>
            <if test="status != null">status = #{status},</if>
            <if test="opTime != null">op_time = #{opTime},</if>
            <if test="opUserId != null">op_user_id = #{opUserId}</if>
        </set>
        WHERE id = #{id} AND deleted = 0
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM legal_entity
        WHERE id = #{id} AND deleted = 0
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM legal_entity
        <where>
            deleted = 0
            <if test="entityName != null">AND entity_name LIKE CONCAT('%', #{entityName}, '%')</if>
            <if test="entityType != null">AND entity_type = #{entityType}</if>
            <if test="status != null">AND status = #{status}</if>
            <if test="startTime != null">AND create_time >= #{startTime}</if>
            <if test="endTime != null">AND create_time &lt;= #{endTime}</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <update id="updateStatus">
        UPDATE legal_entity
        SET status = #{status},
            op_time = #{opTime}
        WHERE id = #{id} AND deleted = 0
    </update>

    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM legal_entity
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
    </select>
</mapper> 
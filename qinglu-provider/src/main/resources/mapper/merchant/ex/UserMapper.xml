<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.merchant.ex.UserMapper">
    <select id="selectMaxId"  resultType="java.lang.Long">
        select max(id) from sys_user
    </select>

    <select id="userIds" resultType="java.lang.Long">
        select id as id from sys_user where (login_name = #{loginName}
        <if test="mobile != null and mobile.length > 0">
            or mobile = #{mobile}
        </if>
        ) and deleted = 0
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.merchant.RoyaltyRelationMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.merchant.RoyaltyRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="account" jdbcType="VARCHAR" property="account" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="royalty_rate" jdbcType="DECIMAL" property="royaltyRate" />
    <result column="third_source" jdbcType="INTEGER" property="thirdSource" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, type, account, name, royalty_rate, third_source, status, merchant_id, deleted, 
    op_user_id, create_time, op_time
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.merchant.RoyaltyRelationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from royalty_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from royalty_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.ql.rent.entity.merchant.RoyaltyRelation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into royalty_relation (type, account, name, 
      royalty_rate, third_source, status, 
      merchant_id, deleted, op_user_id, 
      create_time, op_time)
    values (#{type,jdbcType=TINYINT}, #{account,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{royaltyRate,jdbcType=DECIMAL}, #{thirdSource,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, 
      #{merchantId,jdbcType=BIGINT}, #{deleted,jdbcType=TINYINT}, #{opUserId,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{opTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.merchant.RoyaltyRelation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into royalty_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        type,
      </if>
      <if test="account != null">
        account,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="royaltyRate != null">
        royalty_rate,
      </if>
      <if test="thirdSource != null">
        third_source,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="royaltyRate != null">
        #{royaltyRate,jdbcType=DECIMAL},
      </if>
      <if test="thirdSource != null">
        #{thirdSource,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.merchant.RoyaltyRelationExample" resultType="java.lang.Long">
    select count(*) from royalty_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update royalty_relation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=TINYINT},
      </if>
      <if test="record.account != null">
        account = #{record.account,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.royaltyRate != null">
        royalty_rate = #{record.royaltyRate,jdbcType=DECIMAL},
      </if>
      <if test="record.thirdSource != null">
        third_source = #{record.thirdSource,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update royalty_relation
    set id = #{record.id,jdbcType=BIGINT},
      type = #{record.type,jdbcType=TINYINT},
      account = #{record.account,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      royalty_rate = #{record.royaltyRate,jdbcType=DECIMAL},
      third_source = #{record.thirdSource,jdbcType=INTEGER},
      status = #{record.status,jdbcType=TINYINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      deleted = #{record.deleted,jdbcType=TINYINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.merchant.RoyaltyRelation">
    update royalty_relation
    <set>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="account != null">
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="royaltyRate != null">
        royalty_rate = #{royaltyRate,jdbcType=DECIMAL},
      </if>
      <if test="thirdSource != null">
        third_source = #{thirdSource,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.merchant.RoyaltyRelation">
    update royalty_relation
    set type = #{type,jdbcType=TINYINT},
      account = #{account,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      royalty_rate = #{royaltyRate,jdbcType=DECIMAL},
      third_source = #{thirdSource,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=TINYINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into royalty_relation
    (type, account, name, royalty_rate, third_source, status, merchant_id, deleted, op_user_id, 
      create_time, op_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.type,jdbcType=TINYINT}, #{item.account,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, 
        #{item.royaltyRate,jdbcType=DECIMAL}, #{item.thirdSource,jdbcType=INTEGER}, #{item.status,jdbcType=TINYINT}, 
        #{item.merchantId,jdbcType=BIGINT}, #{item.deleted,jdbcType=TINYINT}, #{item.opUserId,jdbcType=BIGINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into royalty_relation (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=TINYINT}
        </if>
        <if test="'account'.toString() == column.value">
          #{item.account,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'royalty_rate'.toString() == column.value">
          #{item.royaltyRate,jdbcType=DECIMAL}
        </if>
        <if test="'third_source'.toString() == column.value">
          #{item.thirdSource,jdbcType=INTEGER}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>
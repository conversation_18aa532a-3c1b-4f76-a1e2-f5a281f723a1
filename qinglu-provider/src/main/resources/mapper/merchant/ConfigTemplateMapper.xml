<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.merchant.ConfigTemplateMapper">
    <resultMap id="BaseResultMap" type="com.ql.rent.entity.merchant.ConfigTemplate">
        <id column="id" property="id" />
        <result column="template_code" property="templateCode" />
        <result column="template_name" property="templateName" />
        <result column="description" property="description" />
        <result column="status" property="status" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="op_time" property="opTime" />
        <result column="op_user_id" property="opUserId" />
    </resultMap>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT *
        FROM config_template
        WHERE id = #{id}
        AND deleted = 0
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        SELECT *
        FROM config_template
        WHERE deleted = 0
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY template_code ASC
    </select>

    <select id="selectByTemplateCode" resultMap="BaseResultMap">
        SELECT *
        FROM config_template
        WHERE template_code = #{templateCode}
        AND deleted = 0
        LIMIT 1
    </select>
</mapper> 
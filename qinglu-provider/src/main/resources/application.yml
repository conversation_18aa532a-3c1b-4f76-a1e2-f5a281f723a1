spring:
  name: qinglu-server
  main:
    allow-circular-references: true
  profiles:
    active: ${profile.env}
    include: default,jdbc,redis,api
  thymeleaf:
    prefix: classpath:/static/
  security:
    oauth:
      resource:
        id: resource_password_id
        client:
          id: 1
          secret: 123
  lifecycle:
    timeout-per-shutdown-phase: 30s

otel:
  resource:
    attributes:
      service:
        name: ${spring.name}
      deployment.environment: ${profile.env}
      service.namespace: ${profile.env}
  instrumentation.jdbc.enabled: ${otel.instrumentation.jdbc.enabled}
  instrumentation.jedis.enabled: false
  instrumentation.lettuce.enabled: false
  exporter:
    otlp:
      enabled: ${otlp.enabled}
      endpoint: ${otlp.endpoint}
      protocol: grpc
      compression: gzip

server:
  port: ${service.port}
  servlet:
    context-path: /api
  tomcat:
    # 最大等待数
    max-threads: 300
    # 最大线程数
    accept-count: 300
  shutdown: graceful

api:
  ribbon:
    listOfServers: ${api.ribbon.listOfServers}

collector-api:
  ribbon:
    listOfServers: ${collector-api.ribbon.listOfServers}

ctrip-api:
  ribbon:
    listOfServers: ${ctrip-api.ribbon.listOfServers}
#    沙箱环境请求线上 限价数据
ctrip-api-test:
  ribbon:
    listOfServers: ${ctrip-api-test.ribbon.listOfServers}

ctrip-api-sp:
  ribbon:
    listOfServers: ${ctrip-api-sp.ribbon.listOfServers}

ctrip-contract-api:
  ribbon:
    listOfServers: ${ctrip-contract-api.ribbon.listOfServers}

ctrip-contractV2-api:
  ribbon:
    listOfServers: ${ctrip-contractV2-api.ribbon.listOfServers}

open-api:
  ribbon:
    listOfServers: ${open-api.ribbon.listOfServers}

didi-api:
  ribbon:
    listOfServers: ${didi.ribbon.listOfServers}

wx-robot-api:
  ribbon:
    listOfServers: ${wx-robot-api.ribbon.listOfServers}

car:
  ribbon:
    listOfServers: ${car.ribbon.listOfServers}

zzc-api:
  ribbon:
    listOfServers: ${zzc.ribbon.listOfServers}

tc-api:
  ribbon:
    listOfServers: ${tc-api.ribbon.listOfServers}

wuKong-api:
  ribbon:
    listOfServers: ${wuKong-api.ribbon.listOfServers}

tx-api:
  ribbon:
    listOfServers: ${tx-api.ribbon.listOfServers}

sz-api:
  ribbon:
    listOfServers: http://shenzhou-car-api:8080

saas-self-etc-api:
  ribbon:
    listOfServers: ${saas-self-etc-api.ribbon.listOfServers}


fin-link:
  ribbon:
    listOfServers: ${fin-link.ribbon.listOfServers}
management:
  otlp:
    metrics:
      export:
        domain: domain
        businessCode: businessCode
        resourceAttributes:
          service:
            name: ${spring.name}
          deployment: ${profile.env}
        enabled: ${otlp.enabled}
        url: http://otel-collector:4318/v1/metrics

---
spring:
  config:
    activate:
      on-profile: prod

  rocketmq:
    nameServer: ${rocketmq.nameServer}
  #   阿里云 RocketMQ 账号密码配置
    accessKey: ${rocketmq.accessKey}
    secretKey: ${rocketmq.secretKey}
    namespace: ${rocketmq.namespace}
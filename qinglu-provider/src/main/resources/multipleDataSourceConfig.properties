## 门店系统数据库配置
spring.datasource.store.url=${spring.datasource.store.url}
spring.datasource.store.username=${spring.datasource.store.username}
spring.datasource.store.password=${spring.datasource.store.password}
store.mybatis.mapper-locations=classpath:/mapper/store/**/*.xml


## 通用系统数据库配置
spring.datasource.common.url=${spring.datasource.common.url}
spring.datasource.common.username=${spring.datasource.common.username}
spring.datasource.common.password=${spring.datasource.common.password}
common.mybatis.mapper-locations=classpath:/mapper/common/**/*.xml

## ???????????
## 通用系统数据库配置 从库
spring.datasource.slave.common.url=${spring.datasource.slave.common.url}
spring.datasource.slave.common.username=${spring.datasource.slave.common.username}
spring.datasource.slave.common.password=${spring.datasource.slave.common.password}
slave.common.mybatis.mapper-locations=classpath:/mapper/slave/common/**/*.xml


## 订单系统数据库配置
spring.datasource.trade.url=${spring.datasource.trade.url}
spring.datasource.trade.username=${spring.datasource.trade.username}
spring.datasource.trade.password=${spring.datasource.trade.password}
trade.mybatis.mapper-locations=classpath:/mapper/trade/**/*.xml


## 从库订单系统数据库配置
spring.datasource.slave.trade.url=${spring.datasource.slave.trade.url}
spring.datasource.slave.trade.username=${spring.datasource.slave.trade.username}
spring.datasource.slave.trade.password=${spring.datasource.slave.trade.password}
slave.trade.mybatis.mapper-locations=classpath:/mapper/slave/trade/**/*.xml


## 车辆系统数据库配置
spring.datasource.vehicle.url=${spring.datasource.vehicle.url}
spring.datasource.vehicle.username=${spring.datasource.vehicle.username}
spring.datasource.vehicle.password=${spring.datasource.vehicle.password}
vehicle.mybatis.mapper-locations=classpath:/mapper/vehicle/**/*.xml

## 车辆系统数据库配置
spring.datasource.slave.vehicle.url=${spring.datasource.slave.vehicle.url}
spring.datasource.slave.vehicle.username=${spring.datasource.slave.vehicle.username}
spring.datasource.slave.vehicle.password=${spring.datasource.slave.vehicle.password}
slave.vehicle.mybatis.mapper-locations=classpath:/mapper/slave/vehicle/**/*.xml


## 商家、用户管理、权限系统数据库配置
spring.datasource.merchant.url=${spring.datasource.merchant.url}
spring.datasource.merchant.username=${spring.datasource.merchant.username}
spring.datasource.merchant.password=${spring.datasource.merchant.password}
merchant.mybatis.mapper-locations=classpath:/mapper/merchant/**/*.xml


## 支付系统数据库配置
spring.datasource.bill.url=${spring.datasource.bill.url}
spring.datasource.bill.username=${spring.datasource.bill.username}
spring.datasource.bill.password=${spring.datasource.bill.password}
bill.mybatis.mapper-locations=classpath:/mapper/bill/**/*.xml

## 至于别的数据库性能参数（如连接池大小、最大连接时间等），目前使用的是 application-jdbc的中的参数
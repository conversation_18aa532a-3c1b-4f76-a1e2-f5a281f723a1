package com.ql.rent.provider.store;

import com.alibaba.fastjson.JSON;
import com.ql.enums.SmsTemplateEnum;
import com.ql.rent.AbstractTest;
import com.ql.rent.common.IRedisService;
import com.ql.rent.common.ISendService;
import com.ql.rent.constant.RedisConstant;
import com.ql.rent.param.store.StoreAccountInfoParam;
import com.ql.rent.param.store.StoreAccountInfoQueryParam;
import com.ql.rent.param.store.StoreAccountWithdrawRecordQueryParam;
import com.ql.rent.service.store.IStoreAccountInfoService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.store.StoreAccountInfoVo;
import com.ql.rent.vo.store.StoreAccountWtithdrawVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.test.annotation.Rollback;

import javax.annotation.Resource;

/**
 * 门店账户信息服务测试类
 * 
 * <AUTHOR>
 */
@Slf4j
public class StoreAccountInfoServiceImplTest extends AbstractTest {

    @Resource
    private IStoreAccountInfoService storeAccountInfoService;

    private static final Long userId = 1L;

    @Resource
    private ISendService sendService;

    @Resource
    private IRedisService redisService;


    @Test
    public void testList() {
        String phone = "***********";
        Result<Boolean> result = sendService.sendSmsCode(phone, SmsTemplateEnum.ACCOUNT_BALANCE,
                RedisConstant.CodeRedisKey.SEND_SMS_ACCOUNT_BALANCE);
        if (ResultUtil.isResultNotSuccess(result)) {
            throw new BizException(result.getMessage());
        }
    }

    @Test
    public void testCode() {
        String phone = "***********";
        String code = (String) redisService.get(RedisConstant.CodeRedisKey.SEND_SMS_ACCOUNT_BALANCE + phone);
        System.out.println(code);

    }


    @Test
    @Rollback
    public void testAdd() {
        log.info("=== 测试新增门店账户信息 ===");
        
        StoreAccountInfoParam info = new StoreAccountInfoParam();
//        info.setLoginNo("test001");
//        info.setEmail("<EMAIL>");
//        info.setSelfEmployed(true);
//        info.setCertificateNo("123456789012345678");
//        info.setCertificateType("身份证");
//        info.setCorporateName("测试公司");
//        info.setCorporateCertType("营业执照");
//        info.setCorporateCertId("123456789012345678");
//        info.setIndustryId("IT001");
//        info.setCardNo("6222021234567890123");
//        info.setBankName("中国银行");
//        info.setDepositBankProvince("浙江省");
//        info.setDepositBankCity("杭州市");
//        info.setDepositBankName("中国银行杭州分行");
//        info.setStatus(1);
//        info.setMerchantId(1L);
//        info.setStoreId(1L);
//        info.setContractNo("*********");
//        info.setCustomerName("张三");

        String Email = "<EMAIL>";//企业开户邮箱
        boolean SelfEmployed = false;//是否个体户 企业为false，不传默认为false
        String CorporateName ="吕在渔";//法人姓名
        String CorporateCertType = "ID";//法人证件类型：身份证-ID，港澳通行证-HONG_KONG_AND_MACAO_PASS、台湾同胞来往内地通行证-TAIWAN_TRAVEL_PERMIT、护照-PASSPORT
        String CorporateCertId = "620123196611303215";//法人证件号码
        String CorporateMobile = "***********";//法人手机号
        String IndustryId="9999";
        String BankName="中国民生银行";//银行名称
        String DepositBankProvince ="浙江省";//开户行省份
        String DepositBankCity = "杭州市";//开户行城市
        String DepositBankName = "西湖支行";//开户支行名称

        //个人开户
        String MobileNo="***********";//手机号

        //公共
        String CardNo="*************";//卡号 个人为对私卡号，企业为对公卡号
        String CustomerName ="杭州简效科技有限公司";//客户名称,当为企业/个体时为企业名称
        String CertificateNo =" 91330106MA2KC2Q64N";//证件号，当为个人时上送身份证号，企业时上送营业执照号


        // 构建开户请求

        //企业
        info.setCustomerName(CustomerName);//个人：客户名称与持卡人姓名一致 企业：商户名称（营业执照上的名称）
        info.setCertificateNo(CertificateNo);//个人：身份证号码，企业：营业执照证件号码
        info.setCardNo(CardNo);//个人：对私卡号，企业：对公卡号，（个体可绑法人对私卡号）

        info.setEmail(Email);
        info.setSelfEmployed(SelfEmployed);
        info.setCorporateName(CorporateName);
        info.setCorporateCertType(CorporateCertType);
        info.setCorporateCertId(CorporateCertId);
        info.setIndustryId(IndustryId);
        info.setBankName(BankName);
        info.setDepositBankProvince(DepositBankProvince);
        info.setDepositBankCity(DepositBankCity);
        info.setDepositBankName(DepositBankName);
        info.setCertificateType("LICENSE");
        info.setStoreId(7199L);
        info.setMerchantId(61L);
        info.setCardUserName("李选");
        info.setCorporateMobile("***********");
        String infoStr = JSON.toJSONString(info);
        log.info("新增门店账户信息: {}", infoStr);

        LoginVo loginVo = new LoginVo();
        loginVo.setUserId(1L);
        loginVo.setIsAdmin((byte) 1);
        loginVo.setParentId(-1l);

        int result = storeAccountInfoService.add(info,loginVo);
        log.info("新增结果: {}", result);
        
        assert result > 0 : "新增失败";
    }

    @Test
    @Rollback
    public void testAdd1() {
        log.info("=== 测试新增门店账户信息 ===");

        StoreAccountInfoParam info = new StoreAccountInfoParam();
//        info.setLoginNo("test001");
//        info.setEmail("<EMAIL>");
//        info.setSelfEmployed(true);
//        info.setCertificateNo("123456789012345678");
//        info.setCertificateType("身份证");
//        info.setCorporateName("测试公司");
//        info.setCorporateCertType("营业执照");
//        info.setCorporateCertId("123456789012345678");
//        info.setIndustryId("IT001");
//        info.setCardNo("6222021234567890123");
//        info.setBankName("中国银行");
//        info.setDepositBankProvince("浙江省");
//        info.setDepositBankCity("杭州市");
//        info.setDepositBankName("中国银行杭州分行");
//        info.setStatus(1);
//        info.setMerchantId(1L);
//        info.setStoreId(1L);
//        info.setContractNo("*********");
//        info.setCustomerName("张三");

        String Email = "<EMAIL>";//企业开户邮箱
        boolean SelfEmployed = false;//是否个体户 企业为false，不传默认为false
        String CorporateName ="吕在渔";//法人姓名
        String CorporateCertType = "ID";//法人证件类型：身份证-ID，港澳通行证-HONG_KONG_AND_MACAO_PASS、台湾同胞来往内地通行证-TAIWAN_TRAVEL_PERMIT、护照-PASSPORT
        String CorporateCertId = "620123196611303215";//法人证件号码
        String CorporateMobile = "***********";//法人手机号
        String IndustryId="9999";
        String BankName="中国银行";//银行名称
        String DepositBankProvince ="浙江省";//开户行省份
        String DepositBankCity = "杭州市";//开户行城市
        String DepositBankName = "西湖支行";//开户支行名称

        //个人开户
        String MobileNo="***********";//手机号

        //公共
        String CardNo="*************";//卡号 个人为对私卡号，企业为对公卡号
        String CustomerName ="杭州简效科技有限公司";//客户名称,当为企业/个体时为企业名称
        String CertificateNo =" 91330106MA2KC2Q64N";//证件号，当为个人时上送身份证号，企业时上送营业执照号


        // 构建开户请求

        //企业
        info.setCustomerName(CustomerName);//个人：客户名称与持卡人姓名一致 企业：商户名称（营业执照上的名称）
        info.setCertificateNo(CertificateNo);//个人：身份证号码，企业：营业执照证件号码
        info.setCardNo(CardNo);//个人：对私卡号，企业：对公卡号，（个体可绑法人对私卡号）

        info.setEmail(Email);
        info.setSelfEmployed(SelfEmployed);
        info.setCorporateName(CorporateName);
        info.setCorporateCertType(CorporateCertType);
        info.setCorporateCertId(CorporateCertId);
        info.setIndustryId(IndustryId);
        info.setBankName(BankName);
        info.setDepositBankProvince(DepositBankProvince);
        info.setDepositBankCity(DepositBankCity);
        info.setDepositBankName(DepositBankName);
        info.setCertificateType("LICENSE");
        info.setStoreId(7199L);
        info.setMerchantId(61L);
        info.setCardUserName("李选");
        info.setCorporateMobile("***********");
        String infoStr = JSON.toJSONString(info);
        log.info("新增门店账户信息: {}", infoStr);

        LoginVo loginVo = new LoginVo();
        loginVo.setUserId(1L);
        loginVo.setIsAdmin((byte) 1);
        loginVo.setParentId(-1l);

        int result = storeAccountInfoService.add(info,loginVo);
        log.info("新增结果: {}", result);

        assert result > 0 : "新增失败";
    }

    @Test
    @Rollback
    public void testUpdate() {
        log.info("=== 测试更新门店账户信息 ===");

        StoreAccountInfoQueryParam queryParam = new StoreAccountInfoQueryParam();
        queryParam.setPageIndex(1);
        queryParam.setPageSize(10);
        LoginVo loginVo = new LoginVo();
        loginVo.setUserId(1L);
        PageListVo<StoreAccountInfoVo> pageListVo = storeAccountInfoService.pageList(queryParam,loginVo);

        // 更新数据
        StoreAccountInfoParam updateInfo = new StoreAccountInfoParam();
        updateInfo.setId(pageListVo.getList().get(0).getId());
        updateInfo.setEmail("<EMAIL>");
        updateInfo.setCustomerName("李四(已更新)");
        updateInfo.setStatus(2);
        updateInfo.setStoreId(4707L);

        int updateResult = storeAccountInfoService.update(updateInfo,loginVo);
        log.info("更新结果: {}", updateResult);
        
        assert updateResult > 0 : "更新失败";
    }

    @Test
    public void testGetById() {
        log.info("=== 测试根据ID查询门店账户信息 ===");

        StoreAccountInfoQueryParam param = new StoreAccountInfoQueryParam();
        LoginVo loginVo = new LoginVo();
        loginVo.setUserId(1L);

        PageListVo<StoreAccountInfoVo> result = storeAccountInfoService.pageList(param,loginVo);
        Long id = result.getList().get(0).getId();
        StoreAccountInfoVo result1 = storeAccountInfoService.getById(id);
        
        if (result != null) {
            log.info("查询结果: {}", JSON.toJSONString(result));
            assert result1.getId().equals(id) : "ID不匹配";
        } else {
            log.info("未找到ID为{}的记录", id);
        }
    }

    @Test
    @Rollback
    public void testDeleteById() {
        Long userId = 1L;

        Long id = 30L;
        LoginVo loginVo = new LoginVo();
        loginVo.setUserId(1L);

        int result = storeAccountInfoService.deleteById(id,loginVo);
        log.info("删除结果: {}", result);
    }

    @Test
    public void testPageList() {
        log.info("=== 测试分页查询门店账户信息 ===");
        
        StoreAccountInfoQueryParam param = new StoreAccountInfoQueryParam();
        param.setPageIndex(1);
        param.setPageSize(10);
        param.setMerchantId(0L);
//        param.setLoginNo("test");
        LoginVo loginVo = new LoginVo();
        loginVo.setUserId(1L);
        PageListVo<StoreAccountInfoVo> result = storeAccountInfoService.pageList(param,loginVo);
        
        log.info("分页查询结果: {}", JSON.toJSONString(result));
        assert result != null : "分页查询失败";
        assert result.getCount() >= 0 : "总数不能为负数";
    }

    @Test
    public void testPageListWithEmptyResult() {
        log.info("=== 测试分页查询（无结果） ===");
        
        StoreAccountInfoQueryParam param = new StoreAccountInfoQueryParam();
        param.setPageIndex(1);
        param.setPageSize(10);
        param.setLoginNo("不存在的登录号");
        LoginVo loginVo = new LoginVo();
        loginVo.setUserId(1L);
        
        PageListVo<StoreAccountInfoVo > result = storeAccountInfoService.pageList(param,loginVo);
        
        log.info("分页查询结果: {}", JSON.toJSONString(result));
        assert result != null : "分页查询失败";
        assert result.getCount() == 0 : "应该没有结果";
        assert result.getList().isEmpty() : "列表应该为空";
    }

    @Test
    public void withdraw() {
        Long id = 1L;
        LoginVo loginVo = new LoginVo();
        loginVo.setUserId(1L);
        StoreAccountWtithdrawVo vo = storeAccountInfoService.withdraw(id,loginVo);
    }

    @Test
    public void isOpenAccount() {
        Long id = 7199L;
        boolean result = storeAccountInfoService.isOpenAccount(id);
        Assert.assertTrue(result);
    }

    @Test
    public void testGetByStoreId() {
        String errorMsg = null;
        String loginNo = "STLN46658";
        Integer state = 0;
        String contractNo = "1111";
        int result = storeAccountInfoService.updateState(errorMsg,loginNo,state,contractNo);
    }

    @Test
    public void testWithdrawRecordPageList() {
        log.info("=== 测试分页查询门店提现记录 ===");
        
        // 构建查询参数
        StoreAccountWithdrawRecordQueryParam param = new StoreAccountWithdrawRecordQueryParam();
        param.setPageSize(10);
        param.setOffset(0);
        // 可以设置具体的查询条件
         param.setStoreId(4707L); // 指定门店ID
        // param.setState((byte) 1); // 查询成功状态的记录
        // param.setStartTime(System.currentTimeMillis() - 30 * 24 * 60 * 60 * 1000L); // 最近30天
        // param.setEndTime(System.currentTimeMillis());
        
        try {
            PageListVo<StoreAccountWtithdrawVo> result = storeAccountInfoService.withdrawRecordPageList(param);
            
            log.info("查询提现记录结果: {}", JSON.toJSONString(result));
            
            // 断言验证
            assert result != null : "查询结果不能为空";
            assert result.getCount() >= 0 : "总数不能为负数";
            assert result.getList() != null : "列表不能为空";
            
            // 如果有数据，验证数据结构
            if (!result.getList().isEmpty()) {
                StoreAccountWtithdrawVo firstRecord = result.getList().get(0);
                log.info("第一条记录详情: {}", JSON.toJSONString(firstRecord));
                
                // 验证必要字段
                assert firstRecord.getTransSerialNo() != null : "交易流水号不能为空";
                // 状态字段是byte类型，验证其存在性
                log.info("提现状态: {}", firstRecord.getState());
                assert firstRecord.getCreateTime() != null : "创建时间不能为空";
            }
            
            log.info("测试通过：共查询到 {} 条提现记录", result.getCount());
            
        } catch (Exception e) {
            log.error("测试查询提现记录失败", e);
            throw e;
        }
    }

    @Test
    public void testWithdrawRecordPageListWithConditions() {
        log.info("=== 测试带条件的分页查询门店提现记录 ===");
        
        // 构建带查询条件的参数
        StoreAccountWithdrawRecordQueryParam param = new StoreAccountWithdrawRecordQueryParam();
        param.setPageSize(5);
        param.setOffset(0);
        param.setStoreId(59L); // 指定门店ID
//        param.setState((byte) 1); // 只查询成功状态的记录
//
//        // 设置时间范围：最近7天
//        long endTime = System.currentTimeMillis();
//        long startTime = endTime - 7 * 24 * 60 * 60 * 1000L;
//        param.setStartTime(startTime);
//        param.setEndTime(endTime);
        
        // 构建登录信息
//        LoginVo loginVo = new LoginVo();
//        loginVo.setUserId(1L);
//        loginVo.setMerchantId(61L);
        
        try {
            PageListVo<StoreAccountWtithdrawVo> result = storeAccountInfoService.withdrawRecordPageList(param);
            
            log.info("带条件查询提现记录结果: {}", JSON.toJSONString(result));
            
            // 断言验证
            assert result != null : "查询结果不能为空";
            assert result.getCount() >= 0 : "总数不能为负数";
            

            log.info("带条件测试通过：共查询到 {} 条符合条件的提现记录", result.getCount());
            
        } catch (Exception e) {
            log.error("测试带条件查询提现记录失败", e);
            throw e;
        }
    }

    @Test
    public void testWithdrawRecordPageListEmpty() {
        log.info("=== 测试查询不存在的提现记录 ===");
        
        // 构建一个肯定查不到数据的查询条件
        StoreAccountWithdrawRecordQueryParam param = new StoreAccountWithdrawRecordQueryParam();
        param.setPageSize(10);
        param.setOffset(0);
        param.setStoreId(999999L); // 不存在的门店ID
        param.setTransSerialNo("NOT_EXISTS_SERIAL_NO"); // 不存在的流水号
        
        // 构建登录信息
        LoginVo loginVo = new LoginVo();
        loginVo.setUserId(1L);
        loginVo.setMerchantId(61L);
        
        try {
            PageListVo<StoreAccountWtithdrawVo> result = storeAccountInfoService.withdrawRecordPageList(param);
            
            log.info("空结果查询: {}", JSON.toJSONString(result));
            
            // 断言验证
            assert result != null : "查询结果不能为空";
            assert result.getCount() == 0 : "应该没有查询到任何记录";
            assert result.getList().isEmpty() : "结果列表应该为空";
            
            log.info("空结果测试通过：确认查询不到任何记录");
            
        } catch (Exception e) {
            log.error("测试空结果查询失败", e);
            throw e;
        }
    }
}
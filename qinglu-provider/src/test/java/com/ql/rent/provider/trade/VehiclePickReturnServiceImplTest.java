package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.ql.dto.open.request.PlatformBaseRequest;
import com.ql.rent.AbstractTest;
import com.ql.rent.api.aggregate.remote.api.APIClient;
import com.ql.rent.api.aggregate.remote.vo.request.OrderInfoStatusCallBackReq;
import com.ql.rent.api.aggregate.remote.vo.response.OrderInfoCallbackV2Resp;
import com.ql.rent.enums.trade.OrderDeductionTypeEnum;
import com.ql.rent.param.trade.SelfPickReturnConfirmVO;
import com.ql.rent.param.trade.VehiclePickReturnParam;
import com.ql.rent.service.trade.IThirdSelfPickReturnService;
import com.ql.rent.service.trade.IVehiclePickReturnService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.trade.VehiclePickReturnAttVO;
import com.ql.rent.vo.trade.VehicleReturnExpenseItemVO;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

public class VehiclePickReturnServiceImplTest extends AbstractTest {

    @Resource
    private IVehiclePickReturnService vehiclePickReturnService;

    @Resource
    private IThirdSelfPickReturnService thirdSelfPickReturnService;

    @Resource
    private APIClient apiClient;

    @Test
    public void test() {
        PlatformBaseRequest platformBaseRequest = new PlatformBaseRequest();
        platformBaseRequest.setChannelId(4L);
        platformBaseRequest.setMerchantId(9L);

        OrderInfoStatusCallBackReq thirdRequest = new OrderInfoStatusCallBackReq();
        thirdRequest.setActualPickupTime(
            DateUtil.getFormatDateStr(new Date(), DateUtil.yyyyMMddHHmmss));
//        thirdRequest.setVendorCode("13073");
        thirdRequest.setOperateSerialNumber("1111");
        thirdRequest.setCtripOrderId("21417709666");
        platformBaseRequest.setData(thirdRequest);
        OrderInfoCallbackV2Resp callbackResp = apiClient.pickedUp(platformBaseRequest);
        System.out.println(callbackResp);
    }

    @Test
    public void pickUpVehicle() {

//        Long opUserId = 100L;
//        VehiclePickReturnParam param = new VehiclePickReturnParam();
//        param.setOrderId(1L);
//        param.setPrTime(new Date());
//        param.setOilLiter(2);
//        param.setMileage(2000);
//        VehiclePickReturnAttVO att = new VehiclePickReturnAttVO();
//        att.setAttUrl("attUrl");
//        param.setAttList(Collections.singletonList(att));
//        Result<Integer> result = vehiclePickReturnService.pickUpVehicle(param, 100L, 10L);
//        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void returnVehicle() {
        VehiclePickReturnParam param = new VehiclePickReturnParam();
        param.setOrderId(1L);
        param.setOilLiter(1);
        param.setMileage(3000);
        param.setDeductionPayType(OrderDeductionTypeEnum.UNDERLINE.getType());
        param.setRefundPayType(OrderDeductionTypeEnum.UNDERLINE.getType());
        param.setPrTime(new Date(System.currentTimeMillis() + 24L * 3600));
        VehiclePickReturnAttVO att = new VehiclePickReturnAttVO();
        att.setAttUrl("returnUrl");
        att.setAttType((byte)1);
        param.setAttList(Collections.singletonList(att));

        VehicleReturnExpenseItemVO decution = new VehicleReturnExpenseItemVO();
        decution.setExpenseItemPropId(1L);
        decution.setExpenseAmount(2000L);
        decution.setItemType((byte)1);
        VehicleReturnExpenseItemVO refund = new VehicleReturnExpenseItemVO();
        refund.setExpenseItemPropId(2L);
        refund.setExpenseAmount(3000L);
        param.setReturnDeductionItemList(Collections.singletonList(decution));
        param.setReturnRefundItemList(Collections.singletonList(refund));

        LoginVo loginVo = new LoginVo();
        loginVo.setUserId(100L);
        loginVo.setMerchantId(10L);
        Result<Integer> result = vehiclePickReturnService.returnVehicle(param, loginVo);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void getOrderPickReturn() {
    }

    @Test
    public void scheduledCar() {
        LoginVo loginVo = new LoginVo();
        loginVo.setMerchantId(45L);
        loginVo.setUserId(0L);
        SelfPickReturnConfirmVO param = new SelfPickReturnConfirmVO();
        param.setOrderId(124690L);
        param.setStartTime(System.currentTimeMillis());
        param.setCarProblem("");
        param.setCarRemark("123");
        param.setPositionRemark("123213");
        List<SelfPickReturnConfirmVO.SelfPickReturnAttVO> att = new ArrayList<>();
        SelfPickReturnConfirmVO.SelfPickReturnAttVO att1 = new SelfPickReturnConfirmVO.SelfPickReturnAttVO();
        att1.setType((byte) 1);
        att1.setUrl("https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1711532058635_LO62nANz.jpg");
        att.add(att1);
        SelfPickReturnConfirmVO.SelfPickReturnAttVO att2 = new SelfPickReturnConfirmVO.SelfPickReturnAttVO();
        att2.setType((byte) 2);
        att2.setUrl("https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1711532058635_LO62nANz.jpg");
        att.add(att2);
        SelfPickReturnConfirmVO.SelfPickReturnAttVO att3 = new SelfPickReturnConfirmVO.SelfPickReturnAttVO();
        att3.setType((byte) 3);
        att3.setUrl("https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1711532058635_LO62nANz.jpg");
        att.add(att3);
        SelfPickReturnConfirmVO.SelfPickReturnAttVO att4 = new SelfPickReturnConfirmVO.SelfPickReturnAttVO();
        att4.setType((byte) 4);
        att4.setUrl("https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1711532058635_LO62nANz.jpg");
        att.add(att4);
        SelfPickReturnConfirmVO.SelfPickReturnAttVO att5 = new SelfPickReturnConfirmVO.SelfPickReturnAttVO();
        att5.setType((byte) 5);
        att5.setUrl("https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1711532058635_LO62nANz.jpg");
        att.add(att5);
        SelfPickReturnConfirmVO.SelfPickReturnAttVO att6 = new SelfPickReturnConfirmVO.SelfPickReturnAttVO();
        att6.setType((byte) 6);
        att6.setUrl("https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1711532058635_LO62nANz.jpg");
        att.add(att6);
        SelfPickReturnConfirmVO.SelfPickReturnAttVO att7 = new SelfPickReturnConfirmVO.SelfPickReturnAttVO();
        att7.setType((byte) 7);
        att7.setUrl("https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1711532058635_LO62nANz.jpg");
        att.add(att7);
        SelfPickReturnConfirmVO.SelfPickReturnAttVO att8 = new SelfPickReturnConfirmVO.SelfPickReturnAttVO();
        att8.setType((byte) 8);
        att8.setUrl("https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1711532058635_LO62nANz.jpg");
        att.add(att8);
        SelfPickReturnConfirmVO.SelfPickReturnAttVO att11 = new SelfPickReturnConfirmVO.SelfPickReturnAttVO();
        att11.setType((byte) 11);
        att11.setUrl("https://qinglu-file-private-dev.oss-cn-shanghai.aliyuncs.com/uimg/1711532058635_LO62nANz.jpg");
        att.add(att11);
        param.setAtt(att);

//        SelfPickReturnConfirmVO selfPickReturnConfirmVO = JSON.parseObject("",
//                SelfPickReturnConfirmVO.class);
        thirdSelfPickReturnService.scheduledCar(param, loginVo);
    }
}
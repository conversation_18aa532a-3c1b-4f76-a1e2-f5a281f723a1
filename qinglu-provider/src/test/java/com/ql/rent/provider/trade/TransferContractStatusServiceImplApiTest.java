package com.ql.rent.provider.trade;

import com.ql.rent.param.trade.ContractListRequest;
import com.ql.rent.param.trade.SubmitContractRequest;
import com.ql.rent.param.trade.TransferContractStatusParam;
import com.ql.rent.param.trade.VehicleConflictCheckRequest;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.trade.ContractListResponse;
import com.ql.rent.vo.trade.SubmitContractResponse;
import com.ql.rent.vo.trade.TransferContractStatusVo;
import com.ql.rent.vo.trade.VehicleConflictCheckResponse;
import com.ql.rent.vo.vehicle.VehicleInfoVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TransferContractStatusServiceImpl新增API方法的单元测试
 *
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@ExtendWith(MockitoExtension.class)
class TransferContractStatusServiceImplApiTest {

    @Mock
    private IVehicleInfoService vehicleInfoService;

    @InjectMocks
    private TransferContractStatusServiceImpl transferContractStatusService;

    private Long merchantId;
    private SubmitContractRequest submitContractRequest;
    private ContractListRequest contractListRequest;
    private VehicleConflictCheckRequest vehicleConflictCheckRequest;

    @BeforeEach
    void setUp() {
        merchantId = 12345L;
        
        // 初始化提交合同请求
        submitContractRequest = new SubmitContractRequest();
        submitContractRequest.setOrderId(67890L);
        submitContractRequest.setBeginTime(new Date());
        submitContractRequest.setEndTime(new Date(System.currentTimeMillis() + 86400000)); // 1天后
        submitContractRequest.setVehicleId(11111L);
        
        // 初始化合同列表请求
        contractListRequest = new ContractListRequest();
        contractListRequest.setOrderId(67890L);
        contractListRequest.setPageNum(1);
        contractListRequest.setPageSize(20);
        
        // 初始化车辆冲突检查请求
        vehicleConflictCheckRequest = new VehicleConflictCheckRequest();
        vehicleConflictCheckRequest.setOrderId(67890L);
        vehicleConflictCheckRequest.setTargetLicense("京A12345");
        vehicleConflictCheckRequest.setPickupTime(new Date());
    }

    @Test
    void testSubmitContractByApi_Success() {
        // Given - 使用spy来模拟部分方法
        TransferContractStatusServiceImpl spyService = spy(transferContractStatusService);
        doReturn(ResultUtil.successResult(true))
                .when(spyService).submitContract(anyLong(), any(Date.class), any(Date.class), anyLong());

        // When
        Result<SubmitContractResponse> result = spyService.submitContractByApi(merchantId, submitContractRequest);

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getModel());
        assertEquals("0000", result.getModel().getPostCode());
        assertEquals("上报成功", result.getModel().getPostMsg());
        assertEquals(Integer.valueOf(0), result.getModel().getStatus());
        
        verify(spyService).submitContract(
                eq(submitContractRequest.getOrderId()),
                eq(submitContractRequest.getBeginTime()),
                eq(submitContractRequest.getEndTime()),
                eq(submitContractRequest.getVehicleId())
        );
    }

    @Test
    void testSubmitContractByApi_Failure() {
        // Given
        TransferContractStatusServiceImpl spyService = spy(transferContractStatusService);
        doReturn(ResultUtil.failResult("上报失败"))
                .when(spyService).submitContract(anyLong(), any(Date.class), any(Date.class), anyLong());

        // When
        Result<SubmitContractResponse> result = spyService.submitContractByApi(merchantId, submitContractRequest);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("上报失败", result.getMessage());
    }

    @Test
    void testGetContractListByApi_Success() {
        // Given
        TransferContractStatusServiceImpl spyService = spy(transferContractStatusService);
        List<TransferContractStatusVo> mockList = new ArrayList<>();
        doReturn(ResultUtil.successResult(mockList))
                .when(spyService).getTransferContractStatus(any(TransferContractStatusParam.class));

        // When
        Result<ContractListResponse> result = spyService.getContractListByApi(merchantId, contractListRequest);

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getModel());
        assertNotNull(result.getModel().getList());
        assertEquals(Long.valueOf(0), result.getModel().getTotal());
    }

    @Test
    void testCheckVehicleConflict_Success() {
        // Given
        VehicleInfoVO vehicleInfo = new VehicleInfoVO();
        vehicleInfo.setId(11111L);
        vehicleInfo.setLicense("京A12345");
        vehicleInfo.setMerchantId(merchantId);
        
        when(vehicleInfoService.vehicleInfoByLicense(eq(merchantId), eq("京A12345")))
                .thenReturn(ResultUtil.successResult(vehicleInfo));

        // When
        Result<VehicleConflictCheckResponse> result = transferContractStatusService.checkVehicleConflict(merchantId, vehicleConflictCheckRequest);

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getModel());
        assertFalse(result.getModel().getHasStockConflict());
        assertFalse(result.getModel().getHasContractConflict());
        assertNotNull(result.getModel().getContractConflictOrderList());
        assertEquals(vehicleInfo, result.getModel().getTargetVehicleInfo());
        
        verify(vehicleInfoService).vehicleInfoByLicense(eq(merchantId), eq("京A12345"));
    }

    @Test
    void testCheckVehicleConflict_VehicleNotFound() {
        // Given
        when(vehicleInfoService.vehicleInfoByLicense(eq(merchantId), eq("京A12345")))
                .thenReturn(ResultUtil.failResult("车辆不存在"));

        // When
        Result<VehicleConflictCheckResponse> result = transferContractStatusService.checkVehicleConflict(merchantId, vehicleConflictCheckRequest);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("车辆不存在或不属于该商家", result.getMessage());
    }

    @Test
    void testCheckVehicleConflict_Exception() {
        // Given
        when(vehicleInfoService.vehicleInfoByLicense(eq(merchantId), eq("京A12345")))
                .thenThrow(new RuntimeException("系统异常"));

        // When
        Result<VehicleConflictCheckResponse> result = transferContractStatusService.checkVehicleConflict(merchantId, vehicleConflictCheckRequest);

        // Then
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("检查失败"));
    }
}

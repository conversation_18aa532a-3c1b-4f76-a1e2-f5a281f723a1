package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.ql.dto.ApiResultResp;
import com.ql.dto.fundauth.RefundForFreezeResult;
import com.ql.dto.fundauth.TradeCloseForFreezeResult;
import com.ql.dto.fundauth.TradePayForFreezeResult;
import com.ql.rent.AbstractTest;
import com.ql.rent.api.aggregate.dto.OrderDetailExtraDTO;
import com.ql.rent.api.aggregate.model.dto.DailyPriceDTO;
import com.ql.rent.api.aggregate.model.remote.ctrip.dto.SDActualFeeDTO;
import com.ql.rent.common.IRedisService;
import com.ql.rent.component.PlatformBiz;
import com.ql.rent.dao.trade.OrderInfoMapper;
import com.ql.rent.dao.trade.OrderInstalmentMapper;
import com.ql.rent.dao.trade.OrderMemberMapper;
import com.ql.rent.dao.trade.OrderSnapshotMapper;
import com.ql.rent.dao.vehicle.AddedServiceMapper;
import com.ql.rent.dto.trade.OrderDetailMsgDTO;
import com.ql.rent.entity.third.GaodeAddressDTO;
import com.ql.rent.entity.trade.*;
import com.ql.rent.enums.trade.OrderMsgTypeEnum;
import com.ql.rent.enums.trade.ServiceItemListV2Vo;
import com.ql.rent.enums.trade.ServiceItemListVo;
import com.ql.rent.param.fundauth.RefundForFreezeParam;
import com.ql.rent.param.fundauth.TradeCloseForFreezeParam;
import com.ql.rent.param.fundauth.TradePayForFreezeParam;
import com.ql.rent.param.trade.OrderInfoParam;
import com.ql.rent.param.trade.VehicleModelPriceCalQueryParam;
import com.ql.rent.param.trade.VehicleModelPriceCalQueryV2Param;
import com.ql.rent.provider.fundauth.AliMiniProgramFundAuthHandler;
import com.ql.rent.provider.store.StoreInfoServiceImpl;
import com.ql.rent.provider.third.gaode.GaodeComponent;
import com.ql.rent.service.price.IAddedServiceSettingService;
import com.ql.rent.service.slave.IOrderSlaveService;
import com.ql.rent.service.trade.IOrderService;
import com.ql.rent.service.trade.IOrderTagService;
import com.ql.rent.service.trade.IVehiclePickReturnService;
import com.ql.rent.service.vehicle.IThirdVehicleService;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.price.AddedServiceSettingVo;
import com.ql.rent.vo.price.InsuranceServicePriceVo;
import com.ql.rent.vo.trade.*;
import com.ql.rent.vo.vehicle.VehicleInfoTagVO;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Geometry;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * @auther musi
 * @date 2022/10/25 22:19
 */
public class OrderServiceImplTest extends AbstractTest {

    @Resource
    private IOrderService orderService;
    @Resource
    private IOrderSlaveService orderSlaveService;
    @Resource
    private IThirdVehicleService thirdVehicleService;

    @Resource
    private AddedServiceMapper addedServiceMapper;

    @Resource
    private IVehiclePickReturnService vehiclePickReturnService;

    @Resource
    private OrderComponent orderComponent;

    @Resource
    private IVehicleInfoService vehicleInfoService;

    @Resource
    private OrderInfoMapper orderInfoMapper;

    @Resource
    private OrderInstalmentMapper orderInstalmentMapper;

    @Resource
    private GaodeComponent gaodeComponent;
    @Resource
    private OrderSnapshotMapper orderSnapshotMapper;
    @Resource
    private OrderMemberMapper orderMemberMapper;
    @Resource
    private IOrderTagService orderTagService;

    @Resource
    private OrderMsgProducer orderMsgProducer;
    @Resource
    private IAddedServiceSettingService addedServiceSettingService;
    @Resource
    private PlatformBiz platformBiz;
    @Resource
    private AliMiniProgramFundAuthHandler aliMiniProgramFundAuthHandler;
    @Resource
    private IRedisService redisService;

    @Test
    public void testOrderMsg() {
        orderMsgProducer.sendOrderMsg(116204L, OrderMsgTypeEnum.PICKUP);
    }

    @Test
    public void testGis() {
//        OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(1034L);
        String pickupGis = null;
        String retuenGis = null;
//        pickupGis = String.format("point(%s %s)", 120.432314, 30.233228);
//        retuenGis = String.format("point(%s %s)", 120.432314, 30.236505);
//        orderInfo.setPickupAddrGisStr(pickupGis);
//        orderInfo.setReturnAddrGisStr(retuenGis);

//        if (orderInfoMapper.updateByPrimaryKeySelective(orderInfo) < 1) {
//            throw new BizException("更新坐标失败");
//        }

        Object value = redisService.get("open_big_dev_self_check_stock:hello_15");
        System.out.println(JSON.toJSONString(value));
    }

//    @Test
//    public void fixData() {
//        OrderInfoExample example = new OrderInfoExample();
////        example.createCriteria().andMerchantIdEqualTo(3L);
//        List<OrderInfo> list = orderInfoMapper.selectByExample(example);
//        if (CollectionUtils.isNotEmpty(list)) {
//            for (OrderInfo orderInfo : list) {
//                if (orderInfo.getPickupAddrType() == 0 || orderInfo.getReturnAddrType() == 0) {
//                    orderInfo.setPickupAddrType((byte) 1);
//                    orderInfo.setReturnAddrType((byte) 1);
//                    orderInfoMapper.updateByPrimaryKey(orderInfo);
//                }
//                if (StringUtils.isNotBlank(orderInfo.getExtra())) {
//                    orderInfoMapper.deleteByPrimaryKey(orderInfo.getId());
//                }
//            }
//        }
//        OrderSnapshotExample orderSnapshotExample = new OrderSnapshotExample();
//        orderSnapshotExample.createCriteria().andOrderIdGreaterThan(2511L);
//        List<OrderSnapshot> orderSnapshotList = orderSnapshotMapper.selectByExample(orderSnapshotExample);
//        if (CollectionUtils.isNotEmpty(orderSnapshotList)) {
//            for (OrderSnapshot orderSnapshot : orderSnapshotList) {
//                if (orderSnapshot.getSnapshotType().intValue() == OrderSnapshotTypeEnum.THIRD_TAG.getType().intValue()
//                        || orderSnapshot.getSnapshotType().intValue() == OrderSnapshotTypeEnum.REPTILE_SERVICES.getType().intValue()) {
//                    orderSnapshotMapper.deleteByPrimaryKey(orderSnapshot.getId());
//                }
//            }
//        }
//    }

    @Test
    public void testInitGisAddress() {
        OrderInfoParam param = new OrderInfoParam();
        param.setPageSize(3000);
        param.setPageIndex(1);
//        param.setId(1034L);
        List<OrderInfo> orderInfoList = orderInfoMapper.selectByQuery(param);
        for (OrderInfo orderInfo : orderInfoList) {
            Double pickupLongitude = null;
            Double pickupLatitude = null;
            Double returnLongitude = null;
            Double returnLatitude = null;
            if (orderInfo.getPickupAddrGis() != null) {
                try {
                    Geometry pickGeometry = StoreInfoServiceImpl.getGeometryByBytes(orderInfo.getPickupAddrGis());
                    if (pickGeometry != null) {
                        pickupLongitude = pickGeometry.getCentroid().getX();
                        pickupLatitude = pickGeometry.getCentroid().getY();
                    }
                } catch (Exception e) {
                    System.out.println("获取高德坐标失败," + orderInfo.getId());
                    continue;
                }
            }
            if (orderInfo.getReturnAddrGis() != null) {
                try {
                    Geometry returnGeometry = StoreInfoServiceImpl.getGeometryByBytes(orderInfo.getReturnAddrGis());
                    if (returnGeometry != null) {
                        returnLongitude = returnGeometry.getCentroid().getX();
                        returnLatitude = returnGeometry.getCentroid().getY();
                    }
                } catch (Exception e) {
                    System.out.println("获取高德坐标失败," + orderInfo.getId());
                    continue;
                }
            }
            if (StringUtils.isBlank(orderInfo.getPickupGisAddr()) && StringUtils.isBlank(orderInfo.getReturnAddrGisStr())) {
                orderComponent.updateGaodeAddress(orderInfo, pickupLongitude, pickupLatitude, returnLongitude, returnLatitude);
                System.out.println("获取高德坐标成功," + orderInfo.getId());
            }
        }
        System.out.println("更新坐标成功");
    }

    @Test
    public void testGaode() {
        GaodeAddressDTO gaodeAddressDTO = gaodeComponent.regeo(120.432314, 30.236505);
        // 东莞
//        GaodeAddressDTO gaodeAddressDTO = gaodeComponent.regeo(113.74, 23.02);
        System.out.println(gaodeAddressDTO);
    }

    @Test
    public  void testCreateOrder() {
        CreateOrderVo createOrderVo = new CreateOrderVo();
//        createOrderVo.setVehicleModelId(001l);
//        createOrderVo.setVehicleName("大众");
//        createOrderVo.setVehicleNo("001");
//        createOrderVo.setPickupAddr("上海滩1号");
//        createOrderVo.setPickupStoreId(6l);
//        createOrderVo.setReturnStoreId(6l);
//        LongLatVo longLatVo = new LongLatVo();
//        longLatVo.setLatitude(109.2);
//        longLatVo.setLongitude(109.2);
//        createOrderVo.setPickupLongLatVo(longLatVo);
//        createOrderVo.setPickupAddrType((byte) 1);
//        createOrderVo.setPickupCityId(22l);
//        createOrderVo.setReturnAddr("上海滩1号");
//        createOrderVo.setReturnLongLatVo(longLatVo);
//        createOrderVo.setReturnAddrType((byte) 1);
//        createOrderVo.setReturnCityId(22l);
//        createOrderVo.setReturnDate(new Date().getTime());
//        createOrderVo.setPickupDate(new Date().getTime());
//
//        CreateOrdermemberVo createOrdermemberVo = new CreateOrdermemberVo();
//        createOrdermemberVo.setDocNo("4307231995655524");
//        createOrdermemberVo.setMobile("15877771111");
//        createOrdermemberVo.setDocType((byte)1);
//        createOrdermemberVo.setName("慕斯");
//        createOrdermemberVo.setOrderSource(OrderSourceEnum.OFFLINE.getSource());
//        createOrdermemberVo.setRemark("可能会晚一会儿到");
//        createOrderVo.setCreateOrdermemberVo(createOrdermemberVo);
//        VehicleModelPriceAbbrVo vehicleModelPriceAbbrVo = new VehicleModelPriceAbbrVo();
//        vehicleModelPriceAbbrVo.setVehicleModelId(001l);
//        vehicleModelPriceAbbrVo.setTotalAmount(10000);
//        List<ServiceItemAmountVo> list = new ArrayList<>();
//        ServiceItemAmountVo serviceItemAmountVo = new ServiceItemAmountVo();
//        serviceItemAmountVo.setAmount(1);
//        serviceItemAmountVo.setIncludeInTotalAmount(1);
//        serviceItemAmountVo.setQuantity(Double.valueOf(1));
//        serviceItemAmountVo.setId(1L);
//        serviceItemAmountVo.setName("XXX服务");
//        serviceItemAmountVo.setType((byte) 1);
//        serviceItemAmountVo.setOnCharge((byte) 1);
//        serviceItemAmountVo.setPrice(542);
//        serviceItemAmountVo.setUnit("xxx单位");
//        serviceItemAmountVo.setCount(65);
//        serviceItemAmountVo.setDescription("XXXXXXXX");
//        serviceItemAmountVo.setRequired((byte) 1);
//        list.add(serviceItemAmountVo);
//        ServiceItemAmountVo serviceItemAmountVo1 = new ServiceItemAmountVo();
//        serviceItemAmountVo1.setAmount(1);
//        serviceItemAmountVo1.setIncludeInTotalAmount(1);
//        serviceItemAmountVo1.setQuantity(Double.valueOf(1));
//        serviceItemAmountVo1.setId(2L);
//        serviceItemAmountVo1.setName("XXX服务");
//        serviceItemAmountVo1.setType((byte) 2);
//        serviceItemAmountVo1.setOnCharge((byte) 1);
//        serviceItemAmountVo1.setPrice(542);
//        serviceItemAmountVo1.setUnit("个");
//        serviceItemAmountVo1.setCount(62);
//        serviceItemAmountVo1.setDescription("XXXXXXXX");
//        serviceItemAmountVo1.setRequired((byte) 1);
//        list.add(serviceItemAmountVo1);
//        vehicleModelPriceAbbrVo.setServiceItemAmountList(list);
//        createOrderVo.setVehicleModelPriceAbbrVo(vehicleModelPriceAbbrVo);
        String str = "{\"orderType\":1,\"createOrdermemberVo\":{\"name\":\"麻油\",\"mobile\":\"18072990264\",\"docType\":\"1\",\"docNo\":\"18072990264\",\"orderSource\":\"1\",\"pickupDate\":1676400000000,\"payMode\":1,\"everyTimeAmount\":\"6000\",\"isMonth\":0,\"paymentCycle\":\"12\",\"remark\":\"test\"},\"pickupAddr\":\"\",\"pickupAddrType\":\"1\",\"pickupDate\":1676400000000,\"pickupStoreId\":2,\"returnAddr\":\"\",\"returnAddrType\":\"1\",\"returnDate\":1704038400000,\"returnStoreId\":2,\"vehicleModelId\":12,\"vehicleModelPriceAbbrVo\":{\"addedList\":[],\"insuranceList\":[],\"serviceItemAmountList\":[{\"amountStr\":\"52600.00\",\"totalAmountStr\":\"47881.50\",\"detailStr\":\"￥150.00/天 共319.21天\",\"priceStr\":\"150.00\",\"includeInTotalAmountStr\":\"0.00\",\"id\":0,\"settingId\":null,\"code\":null,\"onHighestPrice\":null,\"type\":0,\"name\":\"租车费\",\"onCharge\":null,\"price\":15000,\"unit\":\"天\",\"count\":null,\"description\":null,\"required\":null,\"quantity\":319.21,\"amount\":5260000,\"includeInTotalAmount\":null}],\"tagList\":[\"车辆标签\"],\"totalAmountStr\":\"52600.00\",\"vehicleId\":113,\"vehicleModelId\":12,\"vehicleModelName\":\"经济型 12-西雅特 LEON 2012款 1.8TSI FR+\",\"vehicleNo\":\"沪CFQ0219\",\"totalAmount\":5260000},\"serviceItemAmountList\":[{\"amountStr\":\"52600.00\",\"totalAmountStr\":\"47881.50\",\"detailStr\":\"￥150.00/天 共319.21天\",\"priceStr\":\"150.00\",\"includeInTotalAmountStr\":\"0.00\",\"id\":0,\"settingId\":null,\"code\":null,\"onHighestPrice\":null,\"type\":0,\"name\":\"租车费\",\"onCharge\":null,\"price\":15000,\"unit\":\"天\",\"count\":null,\"description\":null,\"required\":null,\"quantity\":319.21,\"amount\":5260000,\"includeInTotalAmount\":null}],\"vehicleNo\":\"沪CFQ0219\",\"vehicleName\":\"经济型 12-西雅特 LEON 2012款 1.8TSI FR+\",\"vehicleId\":113,\"longOrderTotalAmount\":5260000,\"orderInstalmentConfigVo\":{\"payMode\":1,\"isMonth\":0,\"firstPayDay\":1676400000000,\"pickupDate\":1676400000000,\"returnDate\":1704038400000,\"everyTimeAmount\":600000,\"paymentCycle\":12},\"orderInstalmentVoList\":[{\"id\":null,\"instalmentNo\":1,\"firstPayDay\":1676390400000,\"payDay\":\"2023-02-15\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":2,\"firstPayDay\":1676390400000,\"payDay\":\"2023-02-27\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":3,\"firstPayDay\":1676390400000,\"payDay\":\"2023-03-11\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":4,\"firstPayDay\":1676390400000,\"payDay\":\"2023-03-23\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":5,\"firstPayDay\":1676390400000,\"payDay\":\"2023-04-04\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":6,\"firstPayDay\":1676390400000,\"payDay\":\"2023-04-16\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":7,\"firstPayDay\":1676390400000,\"payDay\":\"2023-04-28\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":8,\"firstPayDay\":1676390400000,\"payDay\":\"2023-05-10\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":9,\"firstPayDay\":1676390400000,\"payDay\":\"2023-05-22\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":10,\"firstPayDay\":1676390400000,\"payDay\":\"2023-06-03\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":11,\"firstPayDay\":1676390400000,\"payDay\":\"2023-06-15\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":12,\"firstPayDay\":1676390400000,\"payDay\":\"2023-06-27\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":13,\"firstPayDay\":1676390400000,\"payDay\":\"2023-07-09\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":14,\"firstPayDay\":1676390400000,\"payDay\":\"2023-07-21\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":15,\"firstPayDay\":1676390400000,\"payDay\":\"2023-08-02\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":16,\"firstPayDay\":1676390400000,\"payDay\":\"2023-08-14\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":17,\"firstPayDay\":1676390400000,\"payDay\":\"2023-08-26\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":18,\"firstPayDay\":1676390400000,\"payDay\":\"2023-09-07\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":19,\"firstPayDay\":1676390400000,\"payDay\":\"2023-09-19\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":20,\"firstPayDay\":1676390400000,\"payDay\":\"2023-10-01\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":21,\"firstPayDay\":1676390400000,\"payDay\":\"2023-10-13\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":22,\"firstPayDay\":1676390400000,\"payDay\":\"2023-10-25\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":23,\"firstPayDay\":1676390400000,\"payDay\":\"2023-11-06\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":24,\"firstPayDay\":1676390400000,\"payDay\":\"2023-11-18\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":25,\"firstPayDay\":1676390400000,\"payDay\":\"2023-11-30\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":26,\"firstPayDay\":1676390400000,\"payDay\":\"2023-12-12\",\"planAmount\":600000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false},{\"id\":null,\"instalmentNo\":27,\"firstPayDay\":1676390400000,\"payDay\":\"2023-12-24\",\"planAmount\":450000,\"actualAmount\":null,\"currentNeedAmount\":null,\"instalmentStatus\":null,\"remark\":null,\"current\":false}]}";
        createOrderVo = JSON.parseObject(str, CreateOrderVo.class);
        createOrderVo.setMerchantId(1L);
        LoginVo loginVo = new LoginVo();
        Result<Long> result = orderService.createOrder(createOrderVo, loginVo);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public  void testSaveOrderRemark() {
        Result<Boolean> result = orderService.saveOrderRemark(1l, null, "备注", null,1l);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public  void testGetOrderRemarkList() {
        Result<List<OrderRemarkVo>> result = orderService.getOrderRemarkList(1l);
        System.out.println(JSON.toJSONString(result));

        String str = "{\"createOrdermemberVo\":{\"name\":\"慕斯\",\"mobile\":\"13738060450\",\"docType\":1,\"docNo\":\"330211199212044011\",\"orderSource\":1,\"remark\":\"\"},\"pickupAddr\":\"\",\"pickupAddrType\":1,\"pickupDate\":1669534667000,\"pickupStoreId\":1000,\"returnAddr\":\"\",\"returnAddrType\":1,\"returnDate\":1669621067000,\"returnStoreId\":1000,\"vehicleModelId\":19,\"vehicleModelPriceAbbrVo\":{\"vehicleId\":43,\"vehicleModelId\":19,\"vehicleModelName\":\"19-华泰3系奇瑞QQ3\",\"tagList\":[\"车辆标签\"],\"vehicleNo\":\"浙B豫A99RR9\",\"totalAmount\":981400,\"serviceItemAmountList\":[{\"id\":0,\"code\":null,\"onHighestPrice\":null,\"type\":0,\"name\":\"租车费\",\"onCharge\":null,\"price\":11000,\"unit\":\"天\",\"count\":null,\"description\":null,\"required\":null,\"quantity\":6,\"amount\":77000,\"includeInTotalAmount\":null},{\"id\":5,\"code\":\"02001\",\"onHighestPrice\":1,\"type\":2,\"name\":\"基本保障服务费\",\"onCharge\":1,\"price\":121200,\"unit\":\"天\",\"count\":null,\"description\":\"\",\"required\":1,\"quantity\":6,\"amount\":848400,\"includeInTotalAmount\":null},{\"id\":6,\"code\":\"02002\",\"onHighestPrice\":0,\"type\":2,\"name\":\"无忧尊享服务费\",\"onCharge\":1,\"price\":7000,\"unit\":\"天\",\"count\":null,\"description\":\"\",\"required\":0,\"quantity\":6,\"amount\":49000,\"includeInTotalAmount\":null},{\"id\":1,\"code\":\"03002\",\"onHighestPrice\":null,\"type\":3,\"name\":\"儿童座椅\",\"onCharge\":1,\"price\":5000,\"unit\":\"次\",\"count\":1,\"description\":\"\",\"required\":0,\"quantity\":1,\"amount\":5000,\"includeInTotalAmount\":null},{\"id\":4,\"code\":\"03001\",\"onHighestPrice\":null,\"type\":3,\"name\":\"手续费\",\"onCharge\":1,\"price\":2000,\"unit\":\"次\",\"count\":1,\"description\":\"\",\"required\":1,\"quantity\":1,\"amount\":2000,\"includeInTotalAmount\":null}],\"addedList\":[{\"id\":1,\"code\":\"03002\",\"onHighestPrice\":null,\"type\":3,\"name\":\"儿童座椅\",\"onCharge\":1,\"price\":5000,\"unit\":\"次\",\"count\":1,\"description\":\"\",\"required\":0,\"quantity\":1,\"amount\":5000,\"includeInTotalAmount\":null},{\"id\":4,\"code\":\"03001\",\"onHighestPrice\":null,\"type\":3,\"name\":\"手续费\",\"onCharge\":1,\"price\":2000,\"unit\":\"次\",\"count\":1,\"description\":\"\",\"required\":1,\"quantity\":1,\"amount\":2000,\"includeInTotalAmount\":null}],\"insuranceList\":[{\"id\":5,\"code\":\"02001\",\"onHighestPrice\":1,\"type\":2,\"name\":\"基本保障服务费\",\"onCharge\":1,\"price\":121200,\"unit\":\"天\",\"count\":null,\"description\":\"\",\"required\":1,\"quantity\":6,\"amount\":848400,\"includeInTotalAmount\":null},{\"id\":6,\"code\":\"02002\",\"onHighestPrice\":0,\"type\":2,\"name\":\"无忧尊享服务费\",\"onCharge\":1,\"price\":7000,\"unit\":\"天\",\"count\":null,\"description\":\"\",\"required\":0,\"quantity\":6,\"amount\":49000,\"includeInTotalAmount\":null}]},\"serviceItemAmountList\":[{\"id\":0,\"code\":null,\"onHighestPrice\":null,\"type\":0,\"name\":\"租车费\",\"onCharge\":null,\"price\":11000,\"unit\":\"天\",\"count\":null,\"description\":null,\"required\":null,\"quantity\":6,\"amount\":77000,\"includeInTotalAmount\":null},{\"id\":5,\"code\":\"02001\",\"onHighestPrice\":1,\"type\":2,\"name\":\"基本保障服务费\",\"onCharge\":1,\"price\":121200,\"unit\":\"天\",\"count\":null,\"description\":\"\",\"required\":1,\"quantity\":6,\"amount\":848400,\"includeInTotalAmount\":null},{\"id\":6,\"code\":\"02002\",\"onHighestPrice\":0,\"type\":2,\"name\":\"无忧尊享服务费\",\"onCharge\":1,\"price\":7000,\"unit\":\"天\",\"count\":null,\"description\":\"\",\"required\":0,\"quantity\":6,\"amount\":49000,\"includeInTotalAmount\":null},{\"id\":1,\"code\":\"03002\",\"onHighestPrice\":null,\"type\":3,\"name\":\"儿童座椅\",\"onCharge\":1,\"price\":5000,\"unit\":\"次\",\"count\":1,\"description\":\"\",\"required\":0,\"quantity\":1,\"amount\":5000,\"includeInTotalAmount\":null},{\"id\":4,\"code\":\"03001\",\"onHighestPrice\":null,\"type\":3,\"name\":\"手续费\",\"onCharge\":1,\"price\":2000,\"unit\":\"次\",\"count\":1,\"description\":\"\",\"required\":1,\"quantity\":1,\"amount\":2000,\"includeInTotalAmount\":null}],\"vehicleNo\":\"浙B豫A99RR9\",\"vehicleName\":\"19-华泰3系奇瑞QQ3\"}";
        CreateOrderVo createOrderVo = JSON.parseObject(str, CreateOrderVo.class);
        createOrderVo.setMerchantId(1L);
        LoginVo loginVo = new LoginVo();
        Result<Long> longResult = orderService.createOrder(createOrderVo, loginVo);
        System.out.println(JSON.toJSONString(longResult));
    }

    @Test
    public  void testGetOrderInfoList() {
        OrderInfoParam orderInfoParam = new OrderInfoParam();
//        orderInfoParam.setPageIndex(1);
//        orderInfoParam.setPageSize(20);
//        orderInfoParam.setMerchantId(2L);
//        orderInfoParam.setSortType("asc");
//        orderInfoParam.setSortField("pickup_date");
//        orderInfoParam.setStartPickupTime(1672582830000L);
//        orderInfoParam.setEndPickupTime(1674224430000L);
//        orderInfoParam.setPickupOrReturnStatusList(Arrays.asList((byte)7));
//        orderInfoParam.setName("慕");
//        orderInfoParam.setVehicleNo("A222");
//        orderInfoParam.setDriverUserId(1008L);
//        orderInfoParam.setOrderSourceList(Arrays.asList((byte)2));
//        orderInfoParam.setOrderId("83");
//        orderInfoParam.setOrderStatusList(Arrays.asList((byte)1,(byte)2,(byte)3,(byte)4,(byte)5,(byte)6,(byte)7,(byte)8));
//        orderInfoParam.setVehicleNo("1");
//        orderInfoParam.setName("慕斯");
        orderInfoParam.setMerchantId(Long.valueOf(1));
        orderInfoParam.setSortType("desc");

        LoginVo loginVo = new LoginVo();
        loginVo.setLoginName("merchant10");
        loginVo.setMerchantId(3L);

//        String a = "{\n" +
//                "  \"pageIndex\": 1,\n" +
//                "  \"pageSize\": 5000,\n" +
//                "  \"sortField\": \"create_time\",\n" +
//                "  \"sortType\": \"desc\",\n" +
//                "  \"orderType\": 0\n" +
//                "}";
//        orderInfoParam = JSON.parseObject(a, OrderInfoParam.class);
//        orderInfoParam.setOrderType((byte) 0);
//        orderInfoParam.setMerchantId(2L);
////        orderInfoParam.setActualEndPickupTime(1682564478000L);
////        orderInfoParam.setActualStartPickupTime(1680318078000L);
////        orderInfoParam.setLongOrderPayStatusList(Arrays.asList((byte)1));
////        orderInfoParam.setOrderId("39");
//        Result<OrderInfoListVo> result = orderService.getOrderList(orderInfoParam, loginVo);
//        System.out.println(JSON.toJSONString(result));


        orderInfoParam.setPageSize(20);
        orderInfoParam.setPageIndex(1);

        orderInfoParam = JSON.parseObject("{\n" +
                "  \"orderStatusList\": [\n" +
                "    3,\n" +
                "    4,\n" +
                "    5,\n" +
                "    6,\n" +
                "    8\n" +
                "  ],\n" +
                "  \"orderId\": \"1702815\",\n" +
                "  \"pageIndex\": 1,\n" +
                "  \"pageSize\": 10,\n" +
                "  \"pickupOrReturnStatusList\": [\n" +
                "    \"7\"\n" +
                "  ],\n" +
                "  \"sortField\": \"create_time\",\n" +
                "  \"sortType\": \"desc\"\n" +
                "}", OrderInfoParam.class);

        orderInfoParam.setMerchantId(3L);
        orderInfoParam.setOrderId("1702815");
        orderInfoParam.setOrderType((byte) 0);

        Result<OrderInfoListVo> result = orderService.getOrderListV2(orderInfoParam, loginVo);
        System.out.println(JSON.toJSONString(result));

//        Result<Map<Long, OrderFeeVo>> mapResult = orderService.getOrderExcelList(result.getModel().getCurrentList());
//        System.out.println(JSON.toJSONString(mapResult));
    }


    @Test
    public void testGetOrderList2() {
        OrderInfoParam orderInfoParam = JSON.parseObject("{\n" +
                "  \"orderStatusList\": [\n" +
                "    3,\n" +
                "    4,\n" +
                "    5,\n" +
                "    6,\n" +
                "    8\n" +
                "  ],\n" +
                "  \"orderId\": \"1702815\",\n" +
                "  \"pageIndex\": 1,\n" +
                "  \"pageSize\": 10,\n" +
                "  \"pickupOrReturnStatusList\": [\n" +
                "    \"7\"\n" +
                "  ],\n" +
                "  \"sortField\": \"create_time\",\n" +
                "  \"sortType\": \"desc\"\n" +
                "}", OrderInfoParam.class);

        orderInfoParam.setMerchantId(3l);
        orderInfoParam.setOrderType((byte) 0);
        orderInfoParam.setId(1702815L);

        Result result = orderService.getOrderListV2(orderInfoParam, null);
        System.out.println(result);
    }

    @Test
    public  void testGetOrderBill() {
        Result<OrderBillDetailVo> result = orderService.getOrderBillDetail(1704270L, null);
        Integer mainAmount = result.getModel().getMainOrderPayAmount();
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public  void testGetOrderDetail() {
//        Result<OrderDetailVo> result = orderService.getOrderDetail(1703117L);


        OrderDetailMsgDTO orderDetailMsgDTO = orderService.getOrderDetailMsg(1704842L);

        System.out.println(JSON.toJSONString(orderDetailMsgDTO));
    }

    @Test
    public  void testGetOrderExcelList() {
        Result<OrderInfoVo> result = orderService.getOrderInfo(1703505L);
        Result<Map<Long, OrderFeeVo>> result1 = orderService.getOrderExcelList(Arrays.asList(result.getModel()));
        System.out.println(JSON.toJSONString(result1));
    }



    @Test
    public  void testPriceCalForSaas() {
        String json = "{\"pickupStoreId\":1000,\"returnStoreId\":1000,\"pickupStoreType\":0,\"returnStoreType\":1,\"vehicleModelId\":18,\"merchantId\":10,\"serviceItemList\":[{\"id\":3,\"code\":\"03001\",\"onHighestPrice\":null,\"type\":3,\"name\":\"手续费\",\"onCharge\":1,\"price\":0,\"unit\":\"次\",\"count\":null,\"description\":\"\",\"required\":1},{\"id\":2,\"code\":\"02002\",\"onHighestPrice\":0,\"type\":2,\"name\":\"无忧尊享服务费\",\"onCharge\":1,\"price\":1200,\"unit\":\"天\",\"count\":null,\"description\":\"\",\"required\":0}],\"pickupDate\":1667738497932,\"returnDate\":1668343299498}";
        VehicleModelPriceCalQueryParam priceCalVo = JSON.parseObject(json, VehicleModelPriceCalQueryParam.class);
        Result<List<VehiclePriceAbbrVo>> result = orderService.priceCalForSaas(priceCalVo, new HashMap<>());
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public  void testCancelRule() {
        Result<List<OrderCancelRuleVo>> result = orderService.getCancelRule(9583L, null, null, null, false);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }


    @Test
    public  void testGetRerentOrderList() {
        Result<List<RerentOrderVo>> result = orderService.getRerentOrderList(14L);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public  void testGetOrderServiceList() {
        Result<List<ServiceItemAmountVo>> result = orderService.getRerentServiceList(32097419L);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public  void testGetAddedServiceList() {
        Result<List<ServiceItemAmountVo>> result = orderService.getAddedServiceList(10038L);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public  void testRerentPrecalculate() {

        Result<OrderInfoVo> orderInfoVoResult = orderService.getOrderInfo(1704156L);
        Result<VehicleModelPriceAbbrVo> result = orderService.rerentPrecalculateForThird(orderInfoVoResult.getModel().getId(), 1744945200000L, orderInfoVoResult.getModel().getOrderSource().longValue(), false);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public  void testRerentPrecalculateForSaas() {
        Result result = orderService.rerentPrecalculateForSaas(1704141L, 1743552047310L);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }


    @Test
    public  void testCreateRerentOrder() {
//        Result<Long> result = orderService.createRerentOrder(117084L, 1701853255328L, false, (byte) 0, false,0, 12L);
//        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public  void testAddOrderAddedServiceItem() {
        Result<Boolean> result = orderService.addOrderAddedServiceItem(117223L, null, Arrays.asList(219L),12L, null);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }


    @Test
    public  void testGetServiceDetail() {
        Result<InsuranceServicePriceVo> result = orderService.getServiceDetail(null, 28L);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public  void testCancelRerentOrder() {
        Result<Boolean> result = orderService.cancelRerentOrder(8L, 0L, false);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public  void testSelectModelServiceItemListVo() {
        ServiceItemListVo vo =
                thirdVehicleService.selectModelServiceItemListVo(10L, 1000L, 1L, null, null, null);
        System.out.println(JSON.toJSONString(vo, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public  void testGetServiceAll() {
        Result<ServiceItemListV2Vo> serviceAll = orderService.getServiceAll(10L);
        System.out.println(JSON.toJSONString(serviceAll, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public  void testCancelOrder() {
        Result result = orderService.cancelOrder(7750L, 0, 0L);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }


    @Test
    public  void testGetModelAndServiceNum() {
        List<Map<String, Object>>  result = addedServiceMapper.getModelAndServiceNum(1000L, Arrays.asList(18L, 19L), Arrays.asList(1L, 2L));
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public  void testNewPriceCalForSaas() {
        VehicleModelPriceCalQueryV2Param param = new VehicleModelPriceCalQueryV2Param();
        param = JSON.parseObject("{\n" +
                "  \"pickupStoreId\": 2,\n" +
                "  \"returnStoreId\": 2,\n" +
                "  \"vehicleModelIdList\": [\n" +
                "    12,\n" +
                "  ],\n" +
                "  \"insuranceIdList\": [\n" +
                "    4\n" +
                "    5\n" +
                "  ],\n" +
                "  \"addedIdList\": [\n" +
                "    3\n" +
                "  ],\n" +
                "  \"pickupStoreType\": 1,\n" +
                "  \"returnStoreType\": 1,\n" +
                "  \"pickupDate\": 1701529200854,\n" +
                "  \"returnDate\": 1701615600854\n" +
                "}", VehicleModelPriceCalQueryV2Param.class);
        param.setMerchantId(1L);
        Result result = orderService.newPriceCalForSaas(param);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public  void testAddedList() {
        Result<List<AddedServiceSettingVo>> addedResult = addedServiceSettingService.addedList(45L);
        System.out.println(JSON.toJSONString(addedResult, SerializerFeature.DisableCircularReferenceDetect));
    }


    @Test
    public  void testGetPickupDepositPolicy() {
        Result result = orderService.getPickupDeposit(45L);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public  void testGetOrderPickReturn() {
        Result result = vehiclePickReturnService.getOrderPickReturn(1702927L);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public  void testListVehicleModelAndTag() {
        Result<List<VehicleInfoTagVO>> vehicleInfoTagVOResult = vehicleInfoService.listVehicleModelAndTag(Arrays.asList(67L));
        System.out.println(JSON.toJSONString(vehicleInfoTagVOResult, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public  void testFixOrderMemberDate() {
        Result<Boolean> result = orderService.fixOrderMemberDate();
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public void testFinish() {
        BigDecimal otherFee = new BigDecimal(-150);
        BigDecimal rerentFee = new BigDecimal(50);
        BigDecimal insuranceFee = new BigDecimal(50);
        BigDecimal pickupFee = new BigDecimal(50);
        BigDecimal returnFee = new BigDecimal(50);

        if (otherFee.intValue() <= 0) {
            BigDecimal marginFee = otherFee;
            otherFee = BigDecimal.valueOf(0);
            rerentFee = rerentFee.add(marginFee);
            if (rerentFee.intValue() <= 0) {
                marginFee = rerentFee;
                rerentFee = BigDecimal.valueOf(0);
                insuranceFee = insuranceFee.add(marginFee);
                if (insuranceFee.intValue() <= 0) {
                    marginFee = insuranceFee;
                    insuranceFee = BigDecimal.valueOf(0);
                    pickupFee = pickupFee.add(marginFee);
                    if (pickupFee.intValue() <= 0) {
                        marginFee = pickupFee;
                        pickupFee = BigDecimal.valueOf(0);
                        returnFee = returnFee.add(marginFee);
                    }
                }
            }
        }
        List<SDActualFeeDTO> sdActualFeeDTOList = new ArrayList<>();
        SDActualFeeDTO otherFeeDTO = new SDActualFeeDTO();
        otherFeeDTO.setName("其他费");
        otherFeeDTO.setQuantity(1F);
        otherFeeDTO.setType(0);
        otherFeeDTO.setUnitPrice(otherFee);
        otherFeeDTO.setTotalPrice(otherFee);
        sdActualFeeDTOList.add(otherFeeDTO);

        SDActualFeeDTO rerentDTO = new SDActualFeeDTO();
        rerentDTO.setName("租车费");
        rerentDTO.setQuantity(1F);
        rerentDTO.setType(0);
        rerentDTO.setUnitPrice(rerentFee);
        rerentDTO.setTotalPrice(rerentFee);
        sdActualFeeDTOList.add(rerentDTO);

        SDActualFeeDTO insuranceDTO = new SDActualFeeDTO();
        insuranceDTO.setName("保险费");
        insuranceDTO.setQuantity(1F);
        insuranceDTO.setType(0);
        insuranceDTO.setUnitPrice(insuranceFee);
        insuranceDTO.setTotalPrice(insuranceFee);
        sdActualFeeDTOList.add(insuranceDTO);

        SDActualFeeDTO pickupDTO = new SDActualFeeDTO();
        pickupDTO.setName("送车上门费");
        pickupDTO.setQuantity(1F);
        pickupDTO.setType(0);
        pickupDTO.setUnitPrice(pickupFee);
        pickupDTO.setTotalPrice(pickupFee);
        sdActualFeeDTOList.add(pickupDTO);

        SDActualFeeDTO returnDTO = new SDActualFeeDTO();
        returnDTO.setName("上门取车费");
        returnDTO.setQuantity(1F);
        returnDTO.setType(0);
        returnDTO.setUnitPrice(returnFee);
        returnDTO.setTotalPrice(returnFee);
        sdActualFeeDTOList.add(returnDTO);
        System.out.println(JSON.toJSONString(sdActualFeeDTOList));
        System.out.println(otherFee.add(rerentFee).add(returnFee).add(pickupFee).add(insuranceFee));
    }


    @Test
    public  void testInitInstalment() {
        OrderInstalmentConfigVo orderInstalmentConfigVo = new OrderInstalmentConfigVo();

        orderInstalmentConfigVo.setEveryTimeAmount(300000);
        String start = "2023-05-09";
        String end = "2023-06-01";
        String firstPayDay = "2023-05-09";

        orderInstalmentConfigVo.setPickupDate(DateUtil.getFormatDate(start, DateUtil.yyyyMMdd).getTime());
        orderInstalmentConfigVo.setReturnDate(DateUtil.getFormatDate(end, DateUtil.yyyyMMdd).getTime());
        orderInstalmentConfigVo.setFirstPayDay(DateUtil.getFormatDate(firstPayDay, DateUtil.yyyyMMdd).getTime());
        orderInstalmentConfigVo.setPaymentCycle(10);
        orderInstalmentConfigVo.setIsMonth((byte) 1);
        // 付款方式 0:全款;1:周期结算
        orderInstalmentConfigVo.setPayMode((byte) 1);

        Result result = orderService.initInstalment(orderInstalmentConfigVo);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public void testInstalmentList() {

        Result result = orderService.instalmentList(8391L);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));

//        result = orderService.getInstalmentPay(895L);
//        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));

    }


    @Test
    public void testGetInstalmentPay() {
        Result result = orderService.getInstalmentPay(908L);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));

    }

    @Test
    public void testSaveInstalmentPay() {
        Result result = orderService.saveInstalmentPay(907L, 323L, 260000, "", 1L);
        System.out.println(JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public void testInitPlanAmount() {
        OrderInstalmentExample orderInstalmentExample = new OrderInstalmentExample();
        orderInstalmentExample.createCriteria().andIdLessThan(310L);
        List<OrderInstalment> list = orderInstalmentMapper.selectByExample(orderInstalmentExample);
        list.forEach(item -> {
            if (item.getInitPlanAmount() == 0) {
                item.setInitPlanAmount(item.getPlanAmount());
                orderInstalmentMapper.updateByPrimaryKey(item);
                System.out.println("id=" + item.getId() + "，更新成功");
            }
        });
    }

    @Test
    public void testGetOrderTagList() {
        Result<List<OrderTagVO>> result = orderTagService.getOrderTagList(8442l, 1L);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testSaveOrderTag() {
        Result<Boolean> result = orderTagService.saveOrderTag("先欠着", 1L);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testSaveOrderTagRelation() {
        Result<Boolean> result = orderTagService.saveOrderTagRelation(1L, null, 1L);
        System.out.println(JSON.toJSONString(result));
    }


    @Test
    public void testGetTagRelationList() {
        Result result = orderTagService.getTagRelationList(1L, 1L);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testGetOrderTagMapBytagIds() {
        Result<Map<Long, List<OrderTagVO>>> result = orderTagService.getOrderTagMapBytagIds( Arrays.asList(2l), 1L);
        System.out.println(JSON.toJSONString(result));
    }



    @Test
    public void testAdjustOrderAmount(){
        Result<Boolean> result = orderService.adjustOrderAmount(1L, (byte) 0, 100, 1L);
        System.out.println(result);

}

    @Test
    public void testGenerateOrderNo(){

        String s = "取车购买";
        OrderDetailExtraDTO detailExtraDTO = JSON.parseObject(s, OrderDetailExtraDTO.class);
        System.out.println(detailExtraDTO);

        Long start = System.currentTimeMillis();
        System.out.println("开始=" + start);
        for (int i = 0; i < 100; i++) {
            String no = orderComponent.generateOrderNo();
            System.out.println(no);
        }
        Long end = System.currentTimeMillis();
        System.out.println("结束=" + end + "，耗时=" + (end - start));
    }

    @Test
    public void testGetRentPriceVo(){
        List<DailyPriceDTO> s = JSON.parseArray("[{\"date\":\"2023-07-29\",\"hour\":23,\"partDailyPrice\":6800,\"per\":1.0,\"price\":6800},{\"date\":\"2023-07-30\",\"hour\":1,\"partDailyPrice\":0,\"per\":0.0,\"price\":6800}]", DailyPriceDTO.class);
        RentalPriceVo rentalPriceVo = orderComponent.getRentPriceVo(s);
        System.out.println(JSON.toJSONString(rentalPriceVo));
    }

    @Test
    public void test7() {
        OrderInfoParam param = new OrderInfoParam();

        LoginVo loginVo = new LoginVo();
        loginVo.setLoginName("merchant10");
        loginVo.setMerchantId(16L);
        param = JSON.parseObject("{\n" +
                "  \"orderStatusList\": [\n" +
                "    3,\n" +
                "    4,\n" +
                "    5,\n" +
                "    6\n" +
                "  ],\n" +
                "  \"orderId\": \"1704429\",\n" +
                "  \"pageIndex\": 1,\n" +
                "  \"pageSize\": 10,\n" +
                "  \"pickupOrReturnStatusList\": [\n" +
                "    \"7\"\n" +
                "  ],\n" +
                "  \"sortField\": \"create_time\",\n" +
                "  \"sortType\": \"desc\"\n" +
                "}", OrderInfoParam.class);
        param.setMerchantId(45L);
        Result<OrderInfoListVo> result = orderSlaveService.getOrderList(param, loginVo);
//        Result<Map<Long, OrderFeeVo>> mapResult = orderService.getOrderExcelList(result.getModel().getCurrentList());
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void refundOrder() {
        orderComponent.refundOrder(1690512L, null, 0L, 0L);
    }

    @Test
    public void depositRefundAll() {
        ApiResultResp apiResultResp = platformBiz.depositRefundAll(1690611L, 0L);
        System.out.println(JSON.toJSONString(apiResultResp));
    }
    @Test
    public void tradePayForFreeze() {
        TradePayForFreezeParam param = new TradePayForFreezeParam();
        param.setPayNo("20472483cfc74e39bfe443e67de481d5");
        param.setTotalAmount(100L);
        param.setDone(false);
        param.setSubject("test11");
        param.setAuthNo("2024112010002001290527699248");
        param.setMerchantPay(1);
        param.setMerchantId(3L);
        TradePayForFreezeResult result = aliMiniProgramFundAuthHandler.tradePayForFreeze(param);
        System.out.println(JSON.toJSONString(result));
    }
    @Test
    public void tradeCloseForFreeze() {
        TradeCloseForFreezeParam param = new TradeCloseForFreezeParam();
        param.setPayNo("bc8b1f8fe0a4474fa732ebce12bf6ccd");
        param.setMerchantPay(1);
        param.setMerchantId(3L);
        TradeCloseForFreezeResult result = aliMiniProgramFundAuthHandler.tradeCloseForFreeze(param);
        System.out.println(JSON.toJSONString(result));
    }
    @Test
    public void refundForFreeze() {
        RefundForFreezeParam param = new RefundForFreezeParam();
        param.setRefundNo("1232123Test");
        param.setMerchantPay(1);
        param.setMerchantId(3L);
        param.setThirdSouceNo("2024112022001459291451074930");
        param.setAmount(100L);
        param.setTradeNo("FUNDAUTH_20472483cfc74e39bfe443e67de481d5");
        RefundForFreezeResult result = aliMiniProgramFundAuthHandler.refundForFreeze(param);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void updateOrderPayStatus() {
        Result<Boolean> result = orderService.updateOrderPayStatus(1, 1, 100, "1232123Test");
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testRefundOrder() {
        orderComponent.cancelOrderForPay(1706100L, 17100L, 0L, 4707L);
    }


}

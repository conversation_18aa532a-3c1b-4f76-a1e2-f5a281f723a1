package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.ql.rent.AbstractTest;
import com.ql.rent.param.trade.ThirdAllocationTaskParam;
import com.ql.rent.param.trade.ThirdAllocationTaskQueryParam;
import com.ql.rent.service.trade.IThirdAllocationTaskService;
import com.ql.rent.util.FormatUtil;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.trade.ThirdAllocationTaskVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.test.annotation.Rollback;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 分账任务服务测试类
 *
 * <AUTHOR>
 */
@Slf4j
public class ThirdAllocationTaskServiceImplTest extends AbstractTest {

    private static final Long userId = 1L;
    @Resource
    private IThirdAllocationTaskService thirdAllocationTaskService;

    @Test
    @Rollback
    public void testAdd() {
        log.info("=== 测试新增分账任务 ===");

        ThirdAllocationTaskParam param = new ThirdAllocationTaskParam();
        param.setBizType((byte) 0); // 宝付
        param.setTotalAmount(new BigDecimal("10500.00"));
        param.setStatus((byte) 3); // 处理中
        param.setRemark("测试分账任务");
        param.setAllocationTime("2024-01-15");
        param.setTransOrderNo("TEST-TRANS-001");
        param.setOrderNo("TEST-ORDER-001");
        param.setErrorMsg("测试错误信息");

        String paramStr = JSON.toJSONString(param);
        log.info("新增分账任务参数: {}", paramStr);

        int result = thirdAllocationTaskService.add(param, userId);
        log.info("新增结果: {}", result);

        assert result > 0 : "新增失败";
    }

    @Test
    @Rollback
    public void testUpdate() {
        log.info("=== 测试更新分账任务 ===");

        // 先查询现有数据
        ThirdAllocationTaskQueryParam queryParam = new ThirdAllocationTaskQueryParam();
        queryParam.setPageIndex(1);
        queryParam.setPageSize(1);
        PageListVo<ThirdAllocationTaskVo> pageListVo = thirdAllocationTaskService.pageList(queryParam);

        if (pageListVo.getList().isEmpty()) {
            log.info("没有现有数据，跳过更新测试");
            return;
        }

        // 更新数据
        ThirdAllocationTaskParam updateParam = new ThirdAllocationTaskParam();
        updateParam.setId(pageListVo.getList().get(0).getId());
        updateParam.setStatus((byte) 1); // 已完成
        updateParam.setRemark("已更新备注");

        int updateResult = thirdAllocationTaskService.update(updateParam, userId);
        log.info("更新结果: {}", updateResult);

        assert updateResult > 0 : "更新失败";
    }

    @Test
    public void testGetById() {
        log.info("=== 测试根据ID查询分账任务 ===");

        // 先查询现有数据
        ThirdAllocationTaskQueryParam queryParam = new ThirdAllocationTaskQueryParam();
        queryParam.setPageIndex(1);
        queryParam.setPageSize(1);
        PageListVo<ThirdAllocationTaskVo> pageListVo = thirdAllocationTaskService.pageList(queryParam);

        if (pageListVo.getList().isEmpty()) {
            log.info("没有现有数据，跳过查询测试");
            return;
        }

        Long id = pageListVo.getList().get(0).getId();
        ThirdAllocationTaskVo result = thirdAllocationTaskService.getById(id);

        if (result != null) {
            log.info("查询结果: {}", JSON.toJSONString(result));
            assert result.getId().equals(id) : "ID不匹配";
        } else {
            log.info("未找到ID为{}的记录", id);
        }
    }

    @Test
    public void testPageList() {
        log.info("=== 测试分页查询分账任务 ===");

        ThirdAllocationTaskQueryParam param = new ThirdAllocationTaskQueryParam();
        param.setPageIndex(1);
        param.setPageSize(10);

        PageListVo<ThirdAllocationTaskVo> result = thirdAllocationTaskService.pageList(param);

        log.info("分页查询结果: {}", JSON.toJSONString(result));
        assert result != null : "分页查询失败";
        assert result.getCount() >= 0 : "总数不能为负数";
    }

    @Test
    public void testPageListWithBizType() {
        log.info("=== 测试按业务类型分页查询 ===");

        ThirdAllocationTaskQueryParam param = new ThirdAllocationTaskQueryParam();
        param.setPageIndex(1);
        param.setPageSize(10);

        PageListVo<ThirdAllocationTaskVo> result = thirdAllocationTaskService.pageList(param);

        log.info("按业务类型分页查询结果: {}", JSON.toJSONString(result));
        assert result != null : "分页查询失败";
    }

    @Test
    public void testPageListWithStatus() {
        log.info("=== 测试按状态分页查询 ===");

        ThirdAllocationTaskQueryParam param = new ThirdAllocationTaskQueryParam();
        param.setPageIndex(1);
        param.setPageSize(10);

        PageListVo<ThirdAllocationTaskVo> result = thirdAllocationTaskService.pageList(param);

        log.info("按状态分页查询结果: {}", JSON.toJSONString(result));
        assert result != null : "分页查询失败";
    }

    @Test
    public void testPageListWithTransOrderNo() {
        log.info("=== 测试按商户订单号分页查询 ===");

        ThirdAllocationTaskQueryParam param = new ThirdAllocationTaskQueryParam();
        param.setPageIndex(1);
        param.setPageSize(10);

        PageListVo<ThirdAllocationTaskVo> result = thirdAllocationTaskService.pageList(param);

        log.info("按商户订单号分页查询结果: {}", JSON.toJSONString(result));
        assert result != null : "分页查询失败";
    }

    @Test
    public void testPageListWithOrderNo() {
        log.info("=== 测试按商户交易订单号分页查询 ===");

        ThirdAllocationTaskQueryParam param = new ThirdAllocationTaskQueryParam();
        param.setPageIndex(1);
        param.setPageSize(10);

        PageListVo<ThirdAllocationTaskVo> result = thirdAllocationTaskService.pageList(param);

        log.info("按商户交易订单号分页查询结果: {}", JSON.toJSONString(result));
        assert result != null : "分页查询失败";
    }

    @Test
    public void testPageListWithAllocationTime() {
        log.info("=== 测试按分账日期分页查询 ===");

        ThirdAllocationTaskQueryParam param = new ThirdAllocationTaskQueryParam();
        param.setPageIndex(1);
        param.setPageSize(10);

        PageListVo<ThirdAllocationTaskVo> result = thirdAllocationTaskService.pageList(param);

        log.info("按分账日期分页查询结果: {}", JSON.toJSONString(result));
        assert result != null : "分页查询失败";
    }

    @Test
    public void testPageListWithEmptyResult() {
        log.info("=== 测试分页查询（无结果） ===");

        ThirdAllocationTaskQueryParam param = new ThirdAllocationTaskQueryParam();
        param.setPageIndex(1);
        param.setPageSize(10);

        PageListVo<ThirdAllocationTaskVo> result = thirdAllocationTaskService.pageList(param);

        log.info("分页查询结果: {}", JSON.toJSONString(result));
        assert result != null : "分页查询失败";
        assert result.getCount() == 0 : "应该没有结果";
        assert result.getList().isEmpty() : "列表应该为空";
    }

    @Test
    public void testPageListWithPagination() {
        log.info("=== 测试分页参数 ===");

        ThirdAllocationTaskQueryParam param = new ThirdAllocationTaskQueryParam();
        param.setPageIndex(2);
        param.setPageSize(5);

        PageListVo<ThirdAllocationTaskVo> result = thirdAllocationTaskService.pageList(param);

        log.info("分页查询结果: {}", JSON.toJSONString(result));
        assert result != null : "分页查询失败";
        assert result.getList().size() <= 5 : "页大小应该不超过5";
    }

    @Test
    @Rollback
    public void testAddWithNullFields() {
        log.info("=== 测试新增分账任务（部分字段为空） ===");

        ThirdAllocationTaskParam param = new ThirdAllocationTaskParam();
        param.setBizType((byte) 0);
        param.setTotalAmount(new BigDecimal("500.00"));
        param.setStatus((byte) 0);
        // 不设置remark，测试null值处理
        param.setAllocationTime("2024-01-16");
        param.setTransOrderNo("TEST-TRANS-002");
        param.setOrderNo("TEST-ORDER-002");
        param.setErrorMsg("更新后的错误信息");

        String paramStr = JSON.toJSONString(param);
        log.info("新增分账任务参数: {}", paramStr);

        int result = thirdAllocationTaskService.add(param, userId);
        log.info("新增结果: {}", result);

        assert result > 0 : "新增失败";
    }

    @Test
    public void testPageListWithErrorMsg() {
        log.info("=== 测试按错误信息分页查询 ===");

        ThirdAllocationTaskQueryParam param = new ThirdAllocationTaskQueryParam();
        param.setPageIndex(1);
        param.setPageSize(10);

        PageListVo<ThirdAllocationTaskVo> result = thirdAllocationTaskService.pageList(param);

        log.info("按错误信息分页查询结果: {}", JSON.toJSONString(result));
        assert result != null : "分页查询失败";
    }

    /**
     * 测试分账任务定时执行（正常流程）
     */
    @Test
    @Rollback
    public void testDoAddAllocationTask_normal() throws Exception {
        log.info("=== 测试分账任务定时执行 ===");
        String allocationTime = "2025-08-07";
        try {
//            String addTransOrderNo = "ql-add-ONO-" + FormatUtil.CreateAeskey(30);
//            String subTransOrderNo = "ql-sub-ONO-" + FormatUtil.CreateAeskey(30);

            String addTransOrderNo = "ql-add-ONO-" + FormatUtil.CreateAeskey(10);

            thirdAllocationTaskService.doAddAllocationTask(allocationTime, addTransOrderNo);
            // 查询主表
            ThirdAllocationTaskQueryParam queryParam = new ThirdAllocationTaskQueryParam();
            PageListVo<ThirdAllocationTaskVo> pageListVo = thirdAllocationTaskService.pageList(queryParam);
            log.info("分账任务主表数据: {}", JSON.toJSONString(pageListVo));
            assert pageListVo.getCount() > 0 : "主表无新增数据";
            // 可进一步查询明细表，断言明细数量、状态等
        } catch (Exception e) {
            log.error("分账任务执行异常", e);
            assert false : "分账任务执行异常";
        }
    }


    @Test
    @Rollback
    public void testDoSubAllocationTask_normal() throws Exception {
        log.info("=== 测试分账任务定时执行 ===");
        String allocationTime = "2025-08-07";
        try {
//            String addTransOrderNo = "ql-add-ONO-" + FormatUtil.CreateAeskey(30);
//            String subTransOrderNo = "ql-sub-ONO-" + FormatUtil.CreateAeskey(30);

            String subTransOrderNo = "ql-sub-ONO-" + FormatUtil.CreateAeskey(10);

            thirdAllocationTaskService.doSubtractAllocationTask(allocationTime, subTransOrderNo);
            // 查询主表
            ThirdAllocationTaskQueryParam queryParam = new ThirdAllocationTaskQueryParam();
            PageListVo<ThirdAllocationTaskVo> pageListVo = thirdAllocationTaskService.pageList(queryParam);
            log.info("分账任务主表数据: {}", JSON.toJSONString(pageListVo));
            assert pageListVo.getCount() > 0 : "主表无新增数据";
            // 可进一步查询明细表，断言明细数量、状态等
        } catch (Exception e) {
            log.error("分账任务执行异常", e);
            assert false : "分账任务执行异常";
        }
    }

    /**
     * 测试分账任务定时执行-空参数异常
     */
//    @Test
//    public void testDoAllocationTask_withBlankParam() {
//        log.info("=== 测试分账任务定时执行-空参数 ===");
//        try {
//            thirdAllocationTaskService.doAllocationTask("");
//            assert false : "应抛出异常";
//        } catch (Exception e) {
//            log.info("捕获到预期异常: {}", e.getMessage());
//            assert e.getMessage().contains("分账时间不能为空");
//        }
//    }
} 
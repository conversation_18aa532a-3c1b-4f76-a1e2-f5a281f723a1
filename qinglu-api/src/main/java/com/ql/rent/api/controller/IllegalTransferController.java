package com.ql.rent.api.controller;

import com.ql.rent.param.trade.ContractListRequest;
import com.ql.rent.param.trade.SubmitContractRequest;
import com.ql.rent.param.trade.VehicleConflictCheckRequest;
import com.ql.rent.service.trade.IIllegalTransferService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.trade.ContractListResponse;
import com.ql.rent.vo.trade.SubmitContractResponse;
import com.ql.rent.vo.trade.VehicleConflictCheckResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 违章转移控制器
 *
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@RestController
@RequestMapping("/illegal_transfer/v1")
@Api(tags = "违章转移相关接口")
@Slf4j
@Validated
public class IllegalTransferController {

    @Resource
    private IIllegalTransferService illegalTransferService;

    /**
     * 手动上报违章转移合同
     */
    @PostMapping("/contract/submit")
    @ApiOperation("手动上报违章转移合同")
    public Result<SubmitContractResponse> submitContract(
            @ApiParam(value = "商家ID", required = true) @RequestHeader("merchantId") Long merchantId,
            @ApiParam(value = "上报请求参数", required = true) @Valid @RequestBody SubmitContractRequest request) {
        
        try {
            log.info("手动上报违章转移合同接口调用, merchantId: {}, request: {}", merchantId, request);
            
            if (merchantId == null) {
                return ResultUtil.failResult("商家ID不能为空");
            }
            
            return illegalTransferService.submitContract(merchantId, request);
            
        } catch (Exception e) {
            log.error("手动上报违章转移合同接口异常, merchantId: {}, request: {}", merchantId, request, e);
            return ResultUtil.failResult("系统异常: " + e.getMessage());
        }
    }

    /**
     * 查询违章转移合同列表
     */
    @PostMapping("/contract/list")
    @ApiOperation("查询违章转移合同列表")
    public Result<ContractListResponse> getContractList(
            @ApiParam(value = "商家ID", required = true) @RequestHeader("merchantId") Long merchantId,
            @ApiParam(value = "查询请求参数", required = true) @Valid @RequestBody ContractListRequest request) {
        
        try {
            log.info("查询违章转移合同列表接口调用, merchantId: {}, request: {}", merchantId, request);
            
            if (merchantId == null) {
                return ResultUtil.failResult("商家ID不能为空");
            }
            
            return illegalTransferService.getContractList(merchantId, request);
            
        } catch (Exception e) {
            log.error("查询违章转移合同列表接口异常, merchantId: {}, request: {}", merchantId, request, e);
            return ResultUtil.failResult("系统异常: " + e.getMessage());
        }
    }

    /**
     * 检查车辆改排后库存冲突
     */
    @PostMapping("/check_vehicle_conflict")
    @ApiOperation("检查车辆改排后库存冲突")
    public Result<VehicleConflictCheckResponse> checkVehicleConflict(
            @ApiParam(value = "商家ID", required = true) @RequestHeader("merchantId") Long merchantId,
            @ApiParam(value = "检查请求参数", required = true) @Valid @RequestBody VehicleConflictCheckRequest request) {
        
        try {
            log.info("检查车辆改排后库存冲突接口调用, merchantId: {}, request: {}", merchantId, request);
            
            if (merchantId == null) {
                return ResultUtil.failResult("商家ID不能为空");
            }
            
            return illegalTransferService.checkVehicleConflict(merchantId, request);
            
        } catch (Exception e) {
            log.error("检查车辆改排后库存冲突接口异常, merchantId: {}, request: {}", merchantId, request, e);
            return ResultUtil.failResult("系统异常: " + e.getMessage());
        }
    }
}

package com.ql.rent.api.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ql.rent.param.trade.ContractListRequest;
import com.ql.rent.param.trade.SubmitContractRequest;
import com.ql.rent.param.trade.VehicleConflictCheckRequest;
import com.ql.rent.service.trade.ITransferContractStatusService;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.trade.ContractListResponse;
import com.ql.rent.vo.trade.SubmitContractResponse;
import com.ql.rent.vo.trade.VehicleConflictCheckResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.Date;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 违章转移控制器单元测试
 *
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@WebMvcTest(IllegalTransferController.class)
class IllegalTransferControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ITransferContractStatusService transferContractStatusService;

    @Autowired
    private ObjectMapper objectMapper;

    private Long merchantId;
    private SubmitContractRequest submitContractRequest;
    private ContractListRequest contractListRequest;
    private VehicleConflictCheckRequest vehicleConflictCheckRequest;

    @BeforeEach
    void setUp() {
        merchantId = 12345L;
        
        // 初始化提交合同请求
        submitContractRequest = new SubmitContractRequest();
        submitContractRequest.setOrderId(67890L);
        submitContractRequest.setBeginTime(new Date());
        submitContractRequest.setEndTime(new Date(System.currentTimeMillis() + 86400000)); // 1天后
        submitContractRequest.setVehicleId(11111L);
        
        // 初始化合同列表请求
        contractListRequest = new ContractListRequest();
        contractListRequest.setOrderId(67890L);
        contractListRequest.setPageNum(1);
        contractListRequest.setPageSize(20);
        
        // 初始化车辆冲突检查请求
        vehicleConflictCheckRequest = new VehicleConflictCheckRequest();
        vehicleConflictCheckRequest.setOrderId(67890L);
        vehicleConflictCheckRequest.setTargetLicense("京A12345");
        vehicleConflictCheckRequest.setPickupTime(new Date());
    }

    @Test
    void testSubmitContract_Success() throws Exception {
        // Given
        SubmitContractResponse response = new SubmitContractResponse();
        response.setPostCode("0000");
        response.setPostMsg("上报成功");
        response.setStatus(0);
        
        when(transferContractStatusService.submitContractByApi(eq(merchantId), any(SubmitContractRequest.class)))
                .thenReturn(ResultUtil.successResult(response));

        // When & Then
        mockMvc.perform(post("/illegal_transfer/v1/contract/submit")
                        .header("merchantId", merchantId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(submitContractRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.model.postCode").value("0000"))
                .andExpect(jsonPath("$.model.postMsg").value("上报成功"))
                .andExpect(jsonPath("$.model.status").value(0));
    }

    @Test
    void testSubmitContract_MissingMerchantId() throws Exception {
        // When & Then
        mockMvc.perform(post("/illegal_transfer/v1/contract/submit")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(submitContractRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("商家ID不能为空"));
    }

    @Test
    void testSubmitContract_ValidationError() throws Exception {
        // Given - 创建无效请求（缺少必填字段）
        SubmitContractRequest invalidRequest = new SubmitContractRequest();
        // orderId为null，应该触发验证错误

        // When & Then
        mockMvc.perform(post("/illegal_transfer/v1/contract/submit")
                        .header("merchantId", merchantId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest()); // 验证错误通常返回400
    }

    @Test
    void testGetContractList_Success() throws Exception {
        // Given
        ContractListResponse response = new ContractListResponse();
        response.setTotal(0L);
        response.setList(new ArrayList<>());
        
        when(transferContractStatusService.getContractListByApi(eq(merchantId), any(ContractListRequest.class)))
                .thenReturn(ResultUtil.successResult(response));

        // When & Then
        mockMvc.perform(post("/illegal_transfer/v1/contract/list")
                        .header("merchantId", merchantId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(contractListRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.model.total").value(0))
                .andExpect(jsonPath("$.model.list").isArray());
    }

    @Test
    void testCheckVehicleConflict_Success() throws Exception {
        // Given
        VehicleConflictCheckResponse response = new VehicleConflictCheckResponse();
        response.setHasStockConflict(false);
        response.setHasContractConflict(false);
        response.setContractConflictOrderList(new ArrayList<>());
        
        when(transferContractStatusService.checkVehicleConflict(eq(merchantId), any(VehicleConflictCheckRequest.class)))
                .thenReturn(ResultUtil.successResult(response));

        // When & Then
        mockMvc.perform(post("/illegal_transfer/v1/check_vehicle_conflict")
                        .header("merchantId", merchantId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(vehicleConflictCheckRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.model.hasStockConflict").value(false))
                .andExpect(jsonPath("$.model.hasContractConflict").value(false))
                .andExpect(jsonPath("$.model.contractConflictOrderList").isArray());
    }

    @Test
    void testCheckVehicleConflict_ServiceFailure() throws Exception {
        // Given
        when(transferContractStatusService.checkVehicleConflict(eq(merchantId), any(VehicleConflictCheckRequest.class)))
                .thenReturn(ResultUtil.failResult("车辆不存在"));

        // When & Then
        mockMvc.perform(post("/illegal_transfer/v1/check_vehicle_conflict")
                        .header("merchantId", merchantId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(vehicleConflictCheckRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("车辆不存在"));
    }

    @Test
    void testCheckVehicleConflict_ValidationError() throws Exception {
        // Given - 创建无效请求（缺少必填字段）
        VehicleConflictCheckRequest invalidRequest = new VehicleConflictCheckRequest();
        // orderId为null，应该触发验证错误

        // When & Then
        mockMvc.perform(post("/illegal_transfer/v1/check_vehicle_conflict")
                        .header("merchantId", merchantId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest()); // 验证错误通常返回400
    }
}
